## 业务系统2.0


### 交易参数配置

StoreConfig与MerchantConfig与VendorConfig里面的json配置结构一致,参数取值先后为store merchant vendor, 区别在于， 不同的表里面的所需的配置项不一样。

* store_daily_max_sum_of_trans StoreConfig独有
* merchant_daily_max_sum_of_trans MerchantConfig独有
* weixin_city_goods_tag, weixin_city_sub_mch_id VendorConfig独有

#### system_config里面的配置

system_config里面存放系统的一些配置,结构如下

```

  name VARCHAR(64) NOT NULL COMMENT '名字',
  desc VARCHAR(100) NOT NULL COMMENT '描述',
  content BLOB NULL COMMENT '内容（JSON，非JSON）',

```

* provider支持的收款通道以及交易模式配置在name为 provider.payways 的记录里面，content为json格式
   结构为：provider:{payway:[sub_payway]}


```
{
      "1001": {
          "2": ["1", "2", "3"],
          "3": ["1", "2", "3"]
      },
      "1002": {
        "2": ["1", "2", "3"],
        "3": ["1", "2", "3"]
      }

  }

```

* 收款通道不支持试用商户的配置name为informal.forbidden.payways 的几率里面， content为json格式
    结构为 payway:errorMessage or payway:subPayway:errorMessage
 
```
{
      "1": "此商户未开通支付宝",
      "2": "此商户未开通支付宝",
      "4": "百度钱包业务升级暂时无法付款，请使用其他支付方式",
      "6": "此商户未开通QQ钱包"
  }


```




#### merchant_config 配置
当商户有自己的特殊配置， 比如有自定义的费率，有自定义的限额，  需要关闭一些交易通道时，需要在merchant_config里面写入自己的配置

merchant_config有如下主要字段:

```
   merchant_id  '商户id'
   payway    '收款通道 支付方式，1：支付宝1.0；2：支付宝2.0；3：微信；4：百付宝；5：京东钱包；6：QQ钱包；7：ApplePay；8：三星支付；9：小米支付；10：华为支付；11：翼支付；12：苏宁易钱包；13：银联云闪付（AndroidPay）；14银联钱包',
   b2c_formal    'b扫c是否正式商户 0:否  1:是 ',
   b2c_satus    'b扫c 是否开通关闭 0:关闭 1：开通',
   b2c_fee_rate   'b扫c 费率',
   c2b_formal    'c扫b是否正式商户 0:否  1:是 ',
   c2b_fee_rate   'c扫b 费率',
   c2b_status    'c扫b是否开通关闭 0:关闭 1：开通',
   wap_formal    'wap是否正式商户 0:否  1:是 ',
   wap_status    'wap 是否开通关闭 0:关闭 1：开通',
   wap_fee_rate   'wap 费率',
   mini_formal
   mini_status
   mini_fee_rate
   provider      '支付通道 INT 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉'
   params   '配置参数（JSON）',

```
  merchant_config一个商户在数据库中可能会有多条记录， 但是每一种收款通道只有一条记录，取值先后顺序为 payway不为空的记录， payway为空的记录
  如果费率， 是否开通， 是否正式商户 以及params里面的其他值 为空(null),那么表示此值没有设置，不会覆盖掉默认的值。
  对接的第3方支付通道，参数写入在payway为null的记录里面。如果某些收款通道的试用交易要走某个支付通道，则provider字段记录为对应的支付通道值。
  当payway为null， 表示配置的参数对所有的收款通道都适用, params里面配置的结构为：

```
 {
   "store_daily_max_sum_of_trans": "234234",
   "merchant_daily_max_sum_of_trans": "23423"
 }

```

  当payway为具体的某一通道时， 配置的参数只对其收款通道适用 如果是正式商户， 不支持c扫b, b扫c配置不同的交易参数。
 如果是某一通道为正式，那么params字段按照下面的来配置


* payway为支付宝v1时， b扫c与c扫b为正式那么配置alipay_v1_trade_params, wap为正式那么配置alipay_wap_trade_params  params配置


```
{
  "alipay_v1_trade_params": {
      "partner": "2088811682011464",
      "app_key": "at41iy1d8sg3z42vp96vuh943kcmmec2"
    },

    "alipay_wap_trade_params": {
        "app_auth_token": ""
    }
}

```




* payway为支付宝v2时，b扫c与c扫b为正式那么配置alipay_v2_trade_params params配置, wap为正式那么配置alipay_wap_v2_trade_params params配置
app_auth_token 可不配置

```
{
  "alipay_v2_trade_params": {
      "app_auth_token": "",
      "auth_app_id":""
   },
   "alipay_wap_v2_trade_params": {
         "app_auth_token": "",
         "auth_app_id":""
   }
}

```

* payway为微信时， b扫c与c扫b为正式那么配置weixin_trade_params, wap为正式那么配置weixin_wap_trade_params mini为正式，那么配置weixin_mini_trade_params params配置


```
{
  "weixin_trade_params": {
      "weixin_sub_appid": "xx",
      "weixin_sub_mch_id": "xx"
    },
    "weixin_wap_trade_params": {
      "weixin_sub_appid": "xx",
      "weixin_sub_mch_id": "xx",
      "weixin_sub_appsecret":"xx"
    },
    "weixin_mini_trade_params": {
          "weixin_sub_appid": "xx",
          "weixin_sub_mch_id": "xx"
    }
}

```

* payway为京东时，b扫c与c扫b为正式那么配置jd_trade_params params配置


```
{
  "jd_trade_params": {
      "merchant_no": "xxx",
      "merchant_key": "xxx"
    }
}

```

* payway为百付宝时，b扫c与c扫b为正式那么配置baifubao_trade_params params配置

```
{
 "baifubao_trade_params": {
     "baifubao_sp_no": "**********",
     "baifubao_sp_key": "xxS"
   }

}


```
* payway为QQ钱包时，b扫c与c扫b为正式那么配置qq_trade_params params配置

```
{
 "qq_trade_params": {
     "qq_merchant_id": "xxS"
   }

}
```

* payway为NFC ， 没有正式

```
{
 "nfc_trade_params": {
     
   }

}
```

* 兴业银行支付通道的交易参数配置在payway为null的记录里面

```
"cibbank_trade_params": {
      "cibbank_mch_id": ""
  }

```

* 中信银行支付通道的交易参数配置在payway为null的记录里面

```
"citicbank_trade_params": {
    "citicbank_mch_id": ""
  },


```

* 拉卡拉支付通道的交易参数配置在payway为null的记录里面

```

"lakala_trade_params": {
      "lakala_merc_id": null,
      "lakala_term_id": null,
  }

```

#### 微信活动参数goods_tag设置
merchant_config_custom表里面插入type为7的记录。可以配置到门店与商户
对应xxx_value配置为goods_tag值





#### store_config

门店配置 配置门店限额

params字段结构为

```
{
   "store_daily_max_sum_of_trans": "234234",
}


```


#### solicitor_config

推广者的配置

* 当商户入网时， 商户的配置是由推广者的配置复制而来。推广者的配置与商户配置结构方法 merchant_config完全一样。
* 推广者配置没有正式商户的配置项








