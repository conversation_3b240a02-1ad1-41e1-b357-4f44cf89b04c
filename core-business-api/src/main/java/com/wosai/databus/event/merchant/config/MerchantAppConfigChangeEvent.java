package com.wosai.databus.event.merchant.config;

import com.wosai.databus.event.AbstractEvent;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 多业务商户费率变更事件
 * @date 2024-04-11
 */
public class MerchantAppConfigChangeEvent extends MerchantConfigEvent {
    public static final String EVENT_TYPE = "MERCHANT_APP_CONFIG_CHANGE";

    public static String eventType() {
        return EVENT_TYPE;
    }

    static {
        AbstractEvent.registerType(module(), objectType(), eventType(), MerchantAppConfigChangeEvent.class);
    }

    Map<String, Object> before;
    Map<String, Object> after;

    public MerchantAppConfigChangeEvent() {
        setEventType(eventType());
    }

    public MerchantAppConfigChangeEvent(Map<String, Object> before, Map<String, Object> after) {
        setEventType(eventType());
        this.before = before;
        this.after = after;
    }

    public Map<String, Object> getBefore() {
        return before;
    }

    public void setBefore(Map<String, Object> before) {
        this.before = before;
    }

    public Map<String, Object> getAfter() {
        return after;
    }

    public void setAfter(Map<String, Object> after) {
        this.after = after;
    }
}
