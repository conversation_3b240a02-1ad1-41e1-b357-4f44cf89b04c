package com.wosai.databus.event.merchant.config;


import com.wosai.databus.event.AbstractEvent;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/11/13.
 */
public class MerchantBankAccountChangeEvent extends AbstractMerchantBankAccountEvent {
    public static final String EVENT_TYPE = "CORE_BUSINESS_MERCHANT_BANK_ACCOUNT_CHANGE";

    public static String eventType() {
        return EVENT_TYPE;
    }

    static {
        AbstractEvent.registerType(module(), objectType(), eventType(), MerchantBankAccountChangeEvent.class);
    }

    Map<String,Object> data;

    public MerchantBankAccountChangeEvent() {
        setEventType(eventType());
    }

    public MerchantBankAccountChangeEvent(Map<String, Object> data) {
        setEventType(eventType());
        this.data = data;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
