package com.wosai.databus.event.merchant.config;

import com.wosai.databus.event.AbstractEvent;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/14.
 */
public class MerchantConfigChangeEvent extends MerchantConfigEvent {
    public static final String EVENT_TYPE = "MERCHANT_CONFIG_CHANGE";


    public static String eventType() {
        return EVENT_TYPE;
    }

    static {
        AbstractEvent.registerType(module(), objectType(), eventType(), MerchantConfigChangeEvent.class);
    }

    Map<String,Object> before;
    Map<String,Object> after;

    public MerchantConfigChangeEvent() {
        setEventType(eventType());
    }

    public MerchantConfigChangeEvent(Map<String, Object> before, Map<String, Object> after) {
        setEventType(eventType());
        this.before = before;
        this.after = after;
    }

    public Map<String, Object> getBefore() {
        return before;
    }

    public void setBefore(Map<String, Object> before) {
        this.before = before;
    }

    public Map<String, Object> getAfter() {
        return after;
    }

    public void setAfter(Map<String, Object> after) {
        this.after = after;
    }
}
