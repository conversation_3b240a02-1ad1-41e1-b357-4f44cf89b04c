package com.wosai.upay.core.bean.model;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;

public class ProviderAbility {
    /**
     * 主键ID
     */
    private long id;

    /**
     * 支付通道
     */
    private Integer provider;

    /**
     * 支付源
     */
    private int payway;

    /**
     * 支付方式
     */
    @JsonProperty("sub_payway")
    private List<Integer> subPayway;

    /**
     * 是否直连
     */
    @JsonProperty("is_formal")
    private boolean isFormal = false;

    /**
     * 是否银行通道
     */
    @JsonProperty("is_bank_channel")
    private boolean isBankChannel = false;

    /**
     * 结算通道
     */
    @JsonProperty("clearance_provider")
    private Integer clearanceProvider;

    /**
     * 是否支持余额
     */
    @JsonProperty("support_wallet")
    private boolean supportWallet = false;

    /**
     * 是否支持分账
     */
    @JsonProperty("support_profit")
    private boolean supportProfit = false;

    /**
     * 是否支持预授权
     */
    @JsonProperty("support_deposit")
    private boolean supportDeposit = false;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public int getPayway() {
        return payway;
    }

    public void setPayway(int payway) {
        this.payway = payway;
    }

    public List<Integer> getSubPayway() {
        return subPayway;
    }

    public void setSubPayway(List<Integer> subPayway) {
        this.subPayway = subPayway;
    }

    @JsonProperty("is_formal")
    public boolean isFormal() {
        return isFormal;
    }

    public void setFormal(boolean isFormal) {
        this.isFormal = isFormal;
    }

    public Integer getClearanceProvider() {
        return clearanceProvider;
    }

    public void setClearanceProvider(Integer clearanceProvider) {
        this.clearanceProvider = clearanceProvider;
    }

    public boolean isSupportWallet() {
        return supportWallet;
    }

    public void setSupportWallet(boolean supportWallet) {
        this.supportWallet = supportWallet;
    }

    public boolean isSupportProfit() {
        return supportProfit;
    }

    public void setSupportProfit(boolean supportProfit) {
        this.supportProfit = supportProfit;
    }

    public boolean isSupportDeposit() {
        return supportDeposit;
    }

    public void setSupportDeposit(boolean supportDeposit) {
        this.supportDeposit = supportDeposit;
    }

    @JsonProperty("is_bank_channel")
    public boolean isBankChannel() {
        return isBankChannel;
    }

    public void setBankChannel(boolean isBankChannel) {
        this.isBankChannel = isBankChannel;
    }

    public static ProviderAbility fromMap(Map<String, Object> providerAbilityMap) {
        ProviderAbility ability = new ProviderAbility();
        ability.setId(MapUtil.getLongValue(providerAbilityMap, DaoConstants.ID));
        ability.setProvider(MapUtil.getInteger(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.PROVIDER));
        ability.setPayway(MapUtil.getIntValue(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.PAYWAY));
        ability.setSubPayway((List)MapUtil.getObject(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.SUB_PAYWAY));
        ability.setClearanceProvider(MapUtil.getInteger(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.CLEARANCE_PROVIDER));
        ability.setFormal(MapUtil.getBoolean(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.IS_FORMAL, false));
        ability.setBankChannel(MapUtil.getBoolean(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.IS_BANK_CHANNEL, false));
        ability.setSupportWallet(MapUtil.getBoolean(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.SUPPORT_WALLET, false));
        ability.setSupportProfit(MapUtil.getBoolean(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.SUPPORT_PROFIT, false));
        ability.setSupportDeposit(MapUtil.getBoolean(providerAbilityMap, com.wosai.upay.core.model.ProviderAbility.SUPPORT_DEPOSIT, false));
        return ability;
    }
}
