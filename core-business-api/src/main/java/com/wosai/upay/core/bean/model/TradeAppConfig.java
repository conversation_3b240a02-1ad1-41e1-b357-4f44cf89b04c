package com.wosai.upay.core.bean.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/12/13.
 */
public class TradeAppConfig {

    /**
     * 商品名称取值规则类型  1:默认规则, 2:门店名称,  3:自定义
     */
    public static final int SUBJECT_RULE_DEFAULT = 1;
    public static final int SUBJECT_RULE_STORE_NAME = 2;
    public static final int SUBJECT_RULE_CUSTOM = 3;


    public static final String KEY_TRADE_APP = "trade_app";
    public static final String KEY_BIZ_MODELS = "biz_models";
    public static final String KEY_PAY_PATHS = "pay_paths";
    public static final String KEY_BIZ_MODEL_TRADE_APP = "biz_model_trade_app";
    public static final String KEY_SUBJECT_RULE_TYPE = "subject_rule_type";
    public static final String KEY_SUBJECT_RULE = "subject_rule";
    public static final String KEY_TRANSACTION_JUMP_URL = "transaction_jump_url";


    /**
     * 应用id
     */
    @JsonProperty("trade_app")
    private int tradeApp;

    /**
     * 应用申请的业务模式
     */
    @JsonProperty("biz_models")
    private Set<String> bizModels;

    /**
     * 应用申请的支付路径
     */
    @JsonProperty("pay_paths")
    private Set<String> payPaths;

    /**
     * 业务模式与应用的映射配置
     */
    @JsonProperty("biz_model_trade_app")
    private Map<String, Integer> bizModelTradeApp;

    /**
     * 商品名称取值规则类型
     */
    @JsonProperty("subject_rule_type")
    private Integer subjectRuleType;

    /**
     * 商品名称取值规则配置
     */
    @JsonProperty("subject_rule")
    private Map<String, String> subjectRule;

    /**
     * 业务方订单详情url跳转地址
     */
    @JsonProperty("transaction_jump_url")
    private String transactionJumpUrl;

    public int getTradeApp() {
        return tradeApp;
    }

    public void setTradeApp(int tradeApp) {
        this.tradeApp = tradeApp;
    }

    public Set<String> getBizModels() {
        return bizModels;
    }

    public void setBizModels(Set<String> bizModels) {
        this.bizModels = bizModels;
    }

    public Set<String> getPayPaths() {
        return payPaths;
    }

    public void setPayPaths(Set<String> payPaths) {
        this.payPaths = payPaths;
    }

    public Map<String, Integer> getBizModelTradeApp() {
        return bizModelTradeApp;
    }

    public void setBizModelTradeApp(Map<String, Integer> bizModelTradeApp) {
        this.bizModelTradeApp = bizModelTradeApp;
    }

    public String getTransactionJumpUrl() {
        return transactionJumpUrl;
    }

    public void setTransactionJumpUrl(String transactionJumpUrl) {
        this.transactionJumpUrl = transactionJumpUrl;
    }

    public Integer getSubjectRuleType() {
        return subjectRuleType;
    }

    public void setSubjectRuleType(Integer subjectRuleType) {
        this.subjectRuleType = subjectRuleType;
    }

    public Map<String, String> getSubjectRule() {
        return subjectRule;
    }

    public void setSubjectRule(Map<String, String> subjectRule) {
        this.subjectRule = subjectRule;
    }
}
