package com.wosai.upay.core.bean.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR> Date: 2022/3/8 Time: 3:56 下午
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TradeExtConfigContentModel {

    @JsonProperty("term_id")
    private String termId;

    /**
     * 终端报备信息
     */
    @JsonProperty("term_info")
    private TermInfo termInfo;

    @JsonProperty("limit_payer")
    private Boolean limitPayer;

    @JsonProperty("store_scene_switch")
    private Boolean storeSceneSwitch;

    public TradeExtConfigContentModel() {
    }

    public String getTermId() {
        return termId;
    }

    public void setTermId(String termId) {
        this.termId = termId;
    }

    public TermInfo getTermInfo() {
        return termInfo;
    }

    public void setTermInfo(TermInfo termInfo) {
        this.termInfo = termInfo;
    }

    public Boolean getLimitPayer() {
        return limitPayer;
    }

    public void setLimitPayer(Boolean limitPayer) {
        this.limitPayer = limitPayer;
    }

    public Boolean getStoreSceneSwitch() {
        return storeSceneSwitch;
    }

    public void setStoreSceneSwitch(Boolean storeSceneSwitch) {
        this.storeSceneSwitch = storeSceneSwitch;
    }
}
