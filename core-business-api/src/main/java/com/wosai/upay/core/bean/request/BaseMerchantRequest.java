package com.wosai.upay.core.bean.request;


import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/7/27 10:48
 */

public class BaseMerchantRequest {

    @NotBlank(message = "商户id不能为空")
    private String merchant_id;

    public BaseMerchantRequest(){}

    public BaseMerchantRequest(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }
}