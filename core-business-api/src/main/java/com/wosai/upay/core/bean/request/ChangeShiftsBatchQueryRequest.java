package com.wosai.upay.core.bean.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class ChangeShiftsBatchQueryRequest extends ChangeShiftsQueryRequest {
    public static final int ORDER_BY_CTIME_DESC = 1;
    public static final int ORDER_BY_CTIME_ASC = 2;
    public static final int ORDER_BY_END_DATE_DESC = 3;
    public static final int ORDER_BY_END_DATE_ASC = 4;

    public static final int TIMERANGE_START_TIME = 1;
    public static final int TIMERANGE_END_TIME = 2;
    public static final int TIMERANGE_TRADE_TIME = 3;

    /**
     * 开始时间
     */
    @JsonProperty("start_date")
    private Long startDate;

    /**
     * 结束时间
     */
    @JsonProperty("end_date")
    private Long endDate;

    /**
     * 是否返回未签退批次
     */
    @JsonProperty("return_uncheckout")
    private Boolean returnUnCheckout;

    /**
     * 商户Id
     */
    @JsonProperty("cs_merchant_id")
    private String csMerchantId;

    /**
     * 门店Id列表
     */
    @JsonProperty("cs_store_Ids")
    private List<String> csStoreIds;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 是否为交易收银员查询
     */
    @JsonProperty("trade_cashier_query")
    private Boolean tradeCashierQuery;

    /**
     * 时间范围查询方式
     */
    private Integer timeRange;

    /**
     *页数
     */
    private Integer page;

    /**
     * 每页显示数量
     */
    @JsonProperty("page_size")
    private Integer pageSize;
    /**
     * 排序方式， 3：ctime倒序 4：ctime正序
     */
    @JsonProperty("order_by")
    private Integer orderBy;

    public ChangeShiftsBatchQueryRequest(String terminalSn, String cashDeskId, String csStoreId, String batchSn, Boolean useBatchSn) {
        super(terminalSn, cashDeskId, csStoreId, batchSn, useBatchSn);
    }

    public ChangeShiftsBatchQueryRequest() {
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(Integer orderBy) {
        this.orderBy = orderBy;
    }

    public Boolean getReturnUnCheckout() {
        return returnUnCheckout;
    }

    public void setReturnUnCheckout(Boolean returnUnCheckout) {
        this.returnUnCheckout = returnUnCheckout;
    }

    public String getCsMerchantId() {
        return csMerchantId;
    }

    public void setCsMerchantId(String csMerchantId) {
        this.csMerchantId = csMerchantId;
    }

    public Boolean getTradeCashierQuery() {
        return tradeCashierQuery;
    }

    public void setTradeCashierQuery(Boolean tradeCashierQuery) {
        this.tradeCashierQuery = tradeCashierQuery;
    }

    public List<String> getCsStoreIds() {
        return csStoreIds;
    }

    public void setCsStoreIds(List<String> csStoreIds) {
        this.csStoreIds = csStoreIds;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(Integer timeRange) {
        this.timeRange = timeRange;
    }

    public static void check(ChangeShiftsBatchQueryRequest request) {
        if (request == null) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
        if (request.getTerminalSn() == null 
                && request.getCashDeskId() == null 
                && request.getCsStoreId() == null
                && request.getCsMerchantId() == null) {
            throw new CoreInvalidParameterException("终端号、收银台编号、门店id和商户id不能都为空");
        }
        if (request.getPageSize() == null) {
            request.setPageSize(10);
        } else if (request.getPageSize() > 100 && (request.getTradeCashierQuery() == null || !request.getTradeCashierQuery())) {
            throw new CoreInvalidParameterException("分页数不能大于100");
        } 
        if (request.getTradeCashierQuery() != null 
                && request.getTradeCashierQuery()) {
            if (request.getCsMerchantId() == null) {
                throw new CoreInvalidParameterException("按商户查询时，商户id不能为空");
            }
            if (request.getStartDate() == null) {
                throw new CoreInvalidParameterException("按商户查询时，起始时间不能为空");
            }
        }
        if (request.getPage() == null) {
            request.setPage(1);
        }
        if (request.getOrderBy() != null 
                && request.getOrderBy() != ORDER_BY_CTIME_DESC
                && request.getOrderBy() != ORDER_BY_CTIME_ASC
                && request.getOrderBy() != ORDER_BY_END_DATE_ASC
                && request.getOrderBy() != ORDER_BY_END_DATE_DESC) {
            throw new CoreInvalidParameterException("排序方式错误");
        }
        if (request.getTimeRange() != null 
                && request.getTimeRange() != TIMERANGE_END_TIME
                && request.getTimeRange() != TIMERANGE_START_TIME
                && request.getTimeRange() != TIMERANGE_TRADE_TIME) {
            throw new CoreInvalidParameterException("时间范围查询方式错误");
        }
    }
}
