package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class ChangeShiftsCashierQueryInfo {
    /**
     * 交易时间
     */
    private long ctime;

    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 终端id
     */
    @JsonProperty("terminal_id")
    private String terminalId;

    /**
     * 收银台id
     */
    @JsonProperty("cash_desk_id")
    private String cashDeskId;

    public ChangeShiftsCashierQueryInfo() {
    }

    public ChangeShiftsCashierQueryInfo(long ctime, String merchantId, String terminalId, String cashDeskId) {
        this.ctime = ctime;
        this.merchantId = merchantId;
        this.terminalId = terminalId;
        this.cashDeskId = cashDeskId;
    }

    public long getCtime() {
        return ctime;
    }

    public void setCtime(long ctime) {
        this.ctime = ctime;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public static void check(ChangeShiftsCashierQueryInfo request) {
        if (request == null) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
        if (request.getTerminalId() == null && request.getCashDeskId() == null) {
            throw new CoreInvalidParameterException("终端号和收银台编号不能同时都为空");
        }
    }
}
