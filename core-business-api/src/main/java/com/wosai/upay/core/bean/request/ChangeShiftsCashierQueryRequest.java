package com.wosai.upay.core.bean.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class ChangeShiftsCashierQueryRequest {
    @JsonProperty("cashier_query_infos")
    List<ChangeShiftsCashierQueryInfo> cashierQueryInfos;

    public void setCashierQueryInfos(List<ChangeShiftsCashierQueryInfo> cashierQueryInfos) {
        this.cashierQueryInfos = cashierQueryInfos;
    }

    public List<ChangeShiftsCashierQueryInfo> getCashierQueryInfos(){
        return this.cashierQueryInfos;
    }

    public static void  check(ChangeShiftsCashierQueryRequest request) {
        if (request == null || request.getCashierQueryInfos() == null || request.getCashierQueryInfos().isEmpty()) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
        for (ChangeShiftsCashierQueryInfo info : request.getCashierQueryInfos()) {
            ChangeShiftsCashierQueryInfo.check(info);
        }
    }
}