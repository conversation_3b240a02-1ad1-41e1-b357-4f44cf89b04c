package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class ChangeShiftsCheckInRequest {
    @JsonProperty("terminal_sn")
    private String terminalSn;

    /**
     * 收银台Id
     */
    @JsonProperty("cash_desk_id")
    private String cashDeskId;

    /**
     * 是否接入收银台
     */
    @JsonProperty("access_cash_desk")
    private boolean accessCashDesk = false;

    public ChangeShiftsCheckInRequest(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public ChangeShiftsCheckInRequest() {
    }

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public boolean isAccessCashDesk() {
        return accessCashDesk;
    }

    public void setAccessCashDesk(boolean accessCashDesk) {
        this.accessCashDesk = accessCashDesk;
    }

    public static void check(ChangeShiftsCheckInRequest request) {
        if (request == null) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
        if(StringUtil.isEmpty(request.getTerminalSn()) && StringUtil.isEmpty(request.getCashDeskId())) {
            throw new CoreInvalidParameterException("终端号或收银台不能为空");
        }
    }
}
