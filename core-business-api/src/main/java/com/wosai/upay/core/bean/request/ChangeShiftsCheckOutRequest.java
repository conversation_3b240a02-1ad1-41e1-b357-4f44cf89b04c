package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class ChangeShiftsCheckOutRequest {
    /**
     * 终端号
     */
    @JsonProperty("terminal_sn")
    private String terminalSn;

    /**
     * 批次号
     */
    @JsonProperty("batch_sn")
    private String batchSn;

    /**
     * 收银员编号
     */
    @JsonProperty("cashier_no")
    private String cashierNo;

    /**
     * 收银员id
     */
    @JsonProperty("cashier_id")
    private String cashierId;

    /**
     * 收银台Id
     */
    @JsonProperty("cash_desk_id")
    private String cashDeskId;

    /**
     * 是否接入收银台
     */
    @JsonProperty("access_cash_desk")
    private boolean accessCashDesk = false;

    public ChangeShiftsCheckOutRequest(String terminalSn, String batchSn, String cashierNo) {
        this.terminalSn = terminalSn;
        this.batchSn = batchSn;
        this.cashierNo = cashierNo;
    }

    public ChangeShiftsCheckOutRequest() {
    }
    
    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getBatchSn() {
        return batchSn;
    }

    public void setBatchSn(String batchSn) {
        this.batchSn = batchSn;
    }

    public String getCashierNo() {
        return cashierNo;
    }

    public void setCashierNo(String cashierNo) {
        this.cashierNo = cashierNo;
    }

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public boolean isAccessCashDesk() {
        return accessCashDesk;
    }
    
    public void setAccessCashDesk(boolean accessCashDesk) {
        this.accessCashDesk = accessCashDesk;
    }

    public String getCashierId() {
        return cashierId;
    }

    public void setCashierId(String cashierId) {
        this.cashierId = cashierId;
    }

    public static void check(ChangeShiftsCheckOutRequest request) {
        if (request == null) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
        if(StringUtil.isEmpty(request.getTerminalSn()) && StringUtil.isEmpty(request.getCashDeskId())) {
            throw new CoreInvalidParameterException("终端号或收银台不能为空");
        }
    }
}
