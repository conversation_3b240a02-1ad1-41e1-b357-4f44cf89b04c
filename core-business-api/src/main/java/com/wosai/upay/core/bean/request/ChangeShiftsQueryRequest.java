package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class ChangeShiftsQueryRequest {

    /**
     * 批次号
     */
    @JsonProperty("batch_sn")
    private String batchSn;

    /**
     * 指定批次号查询，只有在终端查询时生效
     */
    @JsonProperty("use_batch_sn")
    private Boolean useBatchSn;

    /**
     * 终端号
     */
    @JsonProperty("terminal_sn")
    private String terminalSn;

    /**
     * 收银台Id
     */
    @JsonProperty("cash_desk_id")
    private String cashDeskId;

    /**
     * 门店Id
     */
    @JsonProperty("cs_store_id")
    private String csStoreId;

    /**
     * 是否接入收银台
     */
    @JsonProperty("access_cash_desk")
    private boolean accessCashDesk = false;

    public ChangeShiftsQueryRequest(String terminalSn, String cashDeskId, String csStoreId, String batchSn, Boolean useBatchSn) {
        this.terminalSn = terminalSn;
        this.cashDeskId = cashDeskId;
        this.csStoreId = csStoreId;
        this.batchSn = batchSn;
        this.useBatchSn = useBatchSn;
    }

    public ChangeShiftsQueryRequest() {
    }

    public String getBatchSn() {
        return batchSn;
    }

    public void setBatchSn(String batchSn) {
        this.batchSn = batchSn;
    }

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public Boolean getUseBatchSn() {
        return useBatchSn;
    }

    public void setUseBatchSn(Boolean useBatchSn) {
        this.useBatchSn = useBatchSn;
    }

    public String getCsStoreId() {
        return csStoreId;
    }

    public void setCsStoreId(String csStoreId) {
        this.csStoreId = csStoreId;
    }

    public boolean isAccessCashDesk() {
        return accessCashDesk;
    }

    public void setAccessCashDesk(boolean accessCashDesk) {
        this.accessCashDesk = accessCashDesk;
    }

    public static void check(ChangeShiftsQueryRequest request) {
        if (request.getTerminalSn() == null && request.getCashDeskId() == null && request.getCsStoreId() == null) {
            throw new CoreInvalidParameterException("终端号、收银台编号和门店id不能都为空");
        }
    }

}
