package com.wosai.upay.core.bean.request;

import com.wosai.upay.common.validation.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * Description: This is a description of the class.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/7/25
 */
@Data
public class GetAnalyzedMerchantAppConfigRequest {
    /**
     * 商户号
     */
    @NotEmpty(message = "merchantId为空")
    private String merchantId;
    /**
     * payway列表
     */
    @NotEmpty(message = "payWayList为空")
    private List<Integer> payWayList;
    /**
     * 多业务ID列表
     */
    private List<String> appIdList;
    /**
     * subPayWay
     */
    private Integer subPayWay;


    public int[] payWayListToArray() {
        int[] array = new int[payWayList.size()];
        for (int i = 0; i < payWayList.size(); i++) {
            array[i] = payWayList.get(i);
        }
        return array;
    }
}
