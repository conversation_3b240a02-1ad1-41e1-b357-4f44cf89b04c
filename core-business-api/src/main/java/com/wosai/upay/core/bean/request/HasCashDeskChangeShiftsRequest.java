package com.wosai.upay.core.bean.request;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class HasCashDeskChangeShiftsRequest {
    @JsonProperty("terminal_sns")
    private List<String> terminalSns;

    public List<String> getTerminalSns() {
        return terminalSns;
    }

    public void setTerminalSns(List<String> terminalSns) {
        this.terminalSns = terminalSns;
    }

    public static void check(HasCashDeskChangeShiftsRequest request) {
        if(request == null) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
        if(request.getTerminalSns() == null || request.getTerminalSns().isEmpty()) {
            throw new CoreInvalidParameterException("终端号不能为空");
        }
    }

}