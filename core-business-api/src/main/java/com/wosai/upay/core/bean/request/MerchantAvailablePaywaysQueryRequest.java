package com.wosai.upay.core.bean.request;

import java.util.List;

/**
 * <AUTHOR> Date: 2022/12/13 Time: 3:08 PM
 */
public class MerchantAvailablePaywaysQueryRequest {

    private String merchantId;

    private Integer subPayway;

    private List<Integer> assignPayways;


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getSubPayway() {
        return subPayway;
    }

    public void setSubPayway(Integer subPayway) {
        this.subPayway = subPayway;
    }

    public List<Integer> getAssignPayways() {
        return assignPayways;
    }

    public void setAssignPayways(List<Integer> assignPayways) {
        this.assignPayways = assignPayways;
    }

    @Override
    public String toString() {
        return "MerchantAvailablePaywaysQueryRequest{" +
                "merchantId='" + merchantId + '\'' +
                ", subPayway=" + subPayway +
                ", assignPayways=" + assignPayways +
                '}';
    }
}
