package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OpLogCreateRequest {
    @JsonProperty("platform_code")
    private String platformCode;

    @JsonProperty("op_user_id")
    private String opUserId;

    @JsonProperty("op_user_name")
    private String opUserName;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("outer_scene_trace_id")
    private String outerSceneTraceId;

}
