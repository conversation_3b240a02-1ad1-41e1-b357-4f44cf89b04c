package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.exception.CoreInvalidParameterException;

/**
 * <AUTHOR> Date: 2022/3/8 Time: 11:30 上午
 */
public class TradeExtConfigCreateRequest extends BaseTradeExtRequest {

    /**
     * 序列号
     */
    private String sn;

    /**
     * 序列号类型，0：商户、1：门店、2：终端、3：支付通道商户号、4：门店号:支付源子商户号、5：商户号:支付源子商户号
     */
    @JsonProperty("sn_type")
    private Integer snType;

    /**
     * 通道
     */
    private Integer provider;

    /**
     * 配置内容
     */
    private TradeExtConfigContentModel content;


    public TradeExtConfigCreateRequest() {
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getSnType() {
        return snType;
    }

    public void setSnType(Integer snType) {
        this.snType = snType;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public TradeExtConfigContentModel getContent() {
        return content;
    }

    public void setContent(TradeExtConfigContentModel content) {
        this.content = content;
    }

    public void check() {
        if (sn == null || sn.isEmpty()) {
            throw new CoreInvalidParameterException("序列号不能为空");
        }
        if (snType == null || snType < 0 || snType > 5) {
            throw new CoreInvalidParameterException("序列号类型错误");
        }
        if (content == null) {
            throw new CoreInvalidParameterException("配置内容不能为空");
        }
    }
}
