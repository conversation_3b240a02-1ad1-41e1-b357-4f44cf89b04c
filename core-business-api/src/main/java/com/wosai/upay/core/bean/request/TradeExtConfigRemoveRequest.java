package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class TradeExtConfigRemoveRequest extends BaseTradeExtRequest {
    /**
     * 商户号
     */
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 门店号
     */
    @JsonProperty("store_sn")
    private String storeSn;

    /**
     * 序列号类型，0：商户、1：门店、2：终端、3：支付通道商户号、4：门店号:支付源子商户号、5：商户号:支付源子商户号
     */
    @JsonProperty("sn_types")
    private List<Integer> snTypes;

    /**
     * 通道
     */
    private Integer provider;
    /**
     * 通道商户号
     */
    @JsonProperty("provider_mch_id")
    private String providerMchId;


    public String getMerchantSn() {
        return merchantSn;
    }

    public void setMerchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
    }

    public String getStoreSn() {
        return storeSn;
    }

    public void setStoreSn(String storeSn) {
        this.storeSn = storeSn;
    }

    public List<Integer> getSnTypes() {
        return snTypes;
    }

    public void setSnTypes(List<Integer> snTypes) {
        this.snTypes = snTypes;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public String getProviderMchId() {
        return providerMchId;
    }

    public void setProviderMchId(String providerMchId) {
        this.providerMchId = providerMchId;
    }
}
