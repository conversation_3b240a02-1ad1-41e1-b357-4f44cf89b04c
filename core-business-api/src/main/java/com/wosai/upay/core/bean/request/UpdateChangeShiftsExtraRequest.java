package com.wosai.upay.core.bean.request;

import java.util.Map;

import com.wosai.upay.core.exception.CoreInvalidParameterException;

public class UpdateChangeShiftsExtraRequest {
    private long id;
    private Map<String, Object> extra;

    public UpdateChangeShiftsExtraRequest(long id, Map<String, Object> extra){
        this.id = id;
        this.extra = extra;
    }

    public UpdateChangeShiftsExtraRequest() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public void setExtra(Map<String, Object> extra) {
        this.extra = extra;
    }

    public static void check(UpdateChangeShiftsExtraRequest request) {
        if (request == null) {
            throw new CoreInvalidParameterException("请求参数不能为空");
        }
    }
}
