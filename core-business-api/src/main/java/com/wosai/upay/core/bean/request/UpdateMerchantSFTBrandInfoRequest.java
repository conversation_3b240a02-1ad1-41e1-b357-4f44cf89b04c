package com.wosai.upay.core.bean.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class UpdateMerchantSFTBrandInfoRequest {
    @JsonProperty("merchant_sn")
    private String merchantSn;

    @JsonProperty("brand_id")
    private String brandId;

    public String getMerchantSn() {
        return merchantSn;
    }

    public void setMerchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("UpdateMerchantBrandInfoRequest [merchantSn=");
        builder.append(merchantSn);
        builder.append(", brandId=");
        builder.append(brandId);
        builder.append("]");
        return builder.toString();
    }
}