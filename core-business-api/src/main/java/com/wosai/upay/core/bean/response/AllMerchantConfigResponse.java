package com.wosai.upay.core.bean.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 所有的商户配置数据，包括基础业务和多业务
 * @date 2024-04-10
 */
@Data
public class AllMerchantConfigResponse implements Serializable {
    private static final long serialVersionUID = 6062871956362578434L;

    /**
     * 基础业务的商户配置
     */
    private List baseMerchantConfigs;
    /**
     * 多业务的商户配置
     */
    private List appMerchantConfigs;
}
