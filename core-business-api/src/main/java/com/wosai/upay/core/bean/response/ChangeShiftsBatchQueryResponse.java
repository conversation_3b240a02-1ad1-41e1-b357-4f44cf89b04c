package com.wosai.upay.core.bean.response;

import java.util.List;

public class ChangeShiftsBatchQueryResponse {
    private Long total;
    private List<ChangeShiftsInfo> records;

    public ChangeShiftsBatchQueryResponse(long count, List list) {
        this.total = count;
        this.records = list;
    }

    public ChangeShiftsBatchQueryResponse() {
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<ChangeShiftsInfo> getRecords() {
        return records;
    }

    public void setRecords(List<ChangeShiftsInfo> records) {
        this.records = records;
    }

}
