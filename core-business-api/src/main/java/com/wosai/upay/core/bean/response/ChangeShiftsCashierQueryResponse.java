package com.wosai.upay.core.bean.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ChangeShiftsCashierQueryResponse {
    @JsonProperty("change_shifts_infos")
    private List<ChangeShiftsInfo> changeShiftsInfos;

    public ChangeShiftsCashierQueryResponse() {
    }

    public ChangeShiftsCashierQueryResponse(List<ChangeShiftsInfo> changeShiftsInfos) {
        this.changeShiftsInfos = changeShiftsInfos;
    }

    public List<ChangeShiftsInfo> getChangeShiftsInfos() {
        return changeShiftsInfos;
    }

    public void setChangeShiftsInfos(List<ChangeShiftsInfo> changeShiftsInfos) {
        this.changeShiftsInfos = changeShiftsInfos;
    }
    
}
