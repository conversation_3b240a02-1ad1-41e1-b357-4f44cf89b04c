package com.wosai.upay.core.bean.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ChangeShiftsCheckInResponse {
    /**
     * 批次号
     */
    @JsonProperty("batch_sn")
    private String batchSn;
    /**
     * 签到开始时间
     */
    @JsonProperty("start_date")
    private long startDate;

    public ChangeShiftsCheckInResponse(String batchSn, long startDate){
        this.batchSn = batchSn;
        this.startDate = startDate;
    }

    public String getBatchSn() {
        return batchSn;
    }

    public void setBatchSn(String batchSn) {
        this.batchSn = batchSn;
    }

    public long getStartDate() {
        return startDate;
    }

    public void setStartDate(long startDate) {
        this.startDate = startDate;
    }

}
