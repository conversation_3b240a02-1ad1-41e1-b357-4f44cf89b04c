package com.wosai.upay.core.bean.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ChangeShiftsCheckOutResponse {
    /**
     * 批次号
     */
    @JsonProperty("batch_sn")
    private String batchSn;
    /**
     * 签到开始时间
     */
    @JsonProperty("end_date")
    private long endDate;
    /**
     * 下一批次号
     */
    @JsonProperty("next_batch_sn")
    private String nextBatchSn;

    public ChangeShiftsCheckOutResponse(String batchSn, long endDate, String nextBatchSn){
        this.batchSn = batchSn;
        this.endDate = endDate;
        this.nextBatchSn = nextBatchSn;
    }

    public String getBatchSn() {
        return batchSn;
    }

    public void setBatchSn(String batchSn) {
        this.batchSn = batchSn;
    }

    public long getEndDate() {
        return endDate;
    }

    public void setEndDate(long endDate) {
        this.endDate = endDate;
    }

    public String getNextBatchSn() {
        return nextBatchSn;
    }

    public void setNextBatchSn(String nextBatchSn) {
        this.nextBatchSn = nextBatchSn;
    }


}
