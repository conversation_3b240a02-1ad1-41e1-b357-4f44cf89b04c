package com.wosai.upay.core.bean.response;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.ChangeShifts;

public class ChangeShiftsInfo {
    private long id;
    /**
     * 商户id
     */
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * 门店id
     */
    @JsonProperty("store_id")
    private String storeId;

    /**
     * 班次类型
     */
    private Integer type;

    /**
     * 班次类型
     */
    @JsonProperty("service_id")
    private String serviceId;

    /**
     * 收银台名称，在类型为收银台时才有值
     */
    @JsonProperty("cash_desk_name")
    private String cashDeskName;

    /**
     * 批次号
     */
    @JsonProperty("batch_sn")
    private String batchSn;

    /**
     * 收银员id
     */
    @JsonProperty("cashier_id")
    private String cashierId;

    /**
     * 收银台名称，存在收银员id时才会返回值
     */
    @JsonProperty("cashier_name")
    private String cashierName;

    /**
     * 收银员手机号，存在收银员id时才会返回值
     */
    @JsonProperty("cashier_phone")
    private String cashierPhone;

    /**
     * 收银员编号
     */
    @Deprecated
    @JsonProperty("cashier_no")
    private String cashierNo;

    /**
     * 批次号
     */
    @JsonProperty("start_date")
    private Long startDate;

    /**
     * 批次号
     */
    @JsonProperty("end_date")
    private Long endDate;

    /**
     * 扩展信息
     */
    private Map<String, Object> extra;

    /**
     * 创建时间
     */
    private Long ctime;

    /**
     * 修改时间
     */
    private Long mtime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getBatchSn() {
        return batchSn;
    }

    public void setBatchSn(String batchSn) {
        this.batchSn = batchSn;
    }

    public String getCashierNo() {
        return cashierNo;
    }

    public void setCashierNo(String cashierNo) {
        this.cashierNo = cashierNo;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public void setExtra(Map<String, Object> extra) {
        this.extra = extra;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getMtime() {
        return mtime;
    }

    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }

    public String getCashierId() {
        return cashierId;
    }

    public void setCashierId(String cashierId) {
        this.cashierId = cashierId;
    }

    public String getCashDeskName() {
        return cashDeskName;
    }

    public void setCashDeskName(String cashDeskName) {
        this.cashDeskName = cashDeskName;
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public String getCashierPhone() {
        return cashierPhone;
    }

    public void setCashierPhone(String cashierPhone) {
        this.cashierPhone = cashierPhone;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public static ChangeShiftsInfo toChangeShiftsInfo(Map<String, Object> changeShiftsMap) {
        ChangeShiftsInfo response = null;
        if (changeShiftsMap != null) {
            response = new ChangeShiftsQueryResponse();
            response.setId(MapUtil.getLongValue(changeShiftsMap, DaoConstants.ID));
            response.setBatchSn(MapUtil.getString(changeShiftsMap, ChangeShifts.BATCH_SN));
            response.setCashierId(MapUtil.getString(changeShiftsMap, ChangeShifts.CASHIER_ID));
            response.setCashierNo(MapUtil.getString(changeShiftsMap, ChangeShifts.CASHIER_NO));
            response.setType(MapUtil.getInteger(changeShiftsMap, ChangeShifts.TYPE));
            response.setStartDate(MapUtil.getLong(changeShiftsMap, ChangeShifts.START_DATE));
            response.setEndDate(MapUtil.getLong(changeShiftsMap, ChangeShifts.END_DATE));
            response.setServiceId(MapUtil.getString(changeShiftsMap, ChangeShifts.SERVICE_ID));
            response.setExtra(MapUtil.getMap(changeShiftsMap, ChangeShifts.EXTRA));
            response.setMerchantId(MapUtil.getString(changeShiftsMap, ChangeShifts.MERCHANT_ID));
            response.setStoreId(MapUtil.getString(changeShiftsMap, ChangeShifts.STORE_ID));
            response.setCtime(MapUtil.getLong(changeShiftsMap, DaoConstants.CTIME));
            response.setMtime(MapUtil.getLong(changeShiftsMap, DaoConstants.MTIME));
            Object extra;
            if ((extra = changeShiftsMap.get(ChangeShifts.EXTRA)) != null && extra instanceof Map) {
                Map<String, Object> cashierInfo = MapUtil.getMap((Map)extra, ChangeShifts.EXTRA_CASHIER_INFO);
                if (cashierInfo != null) {
                    response.setCashierName(MapUtil.getString(cashierInfo, ChangeShifts.CASHIER_NAME));
                    response.setCashierPhone(MapUtil.getString(cashierInfo, ChangeShifts.CASHIER_PHONE));
                }
            }
        }
        return response;
    }

}
