package com.wosai.upay.core.bean.response;

import java.util.Map;

import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.ChangeShifts;

public class ChangeShiftsQueryResponse extends ChangeShiftsInfo{
    
    public static ChangeShiftsQueryResponse toChangeShiftsQueryResponse(Map<String, Object> changeShiftsMap) {
        ChangeShiftsQueryResponse response = null;
        if (changeShiftsMap != null) {
            response = new ChangeShiftsQueryResponse();
            response.setId(MapUtil.getLongValue(changeShiftsMap, DaoConstants.ID));
            response.setBatchSn(MapUtil.getString(changeShiftsMap, ChangeShifts.BATCH_SN));
            response.setCashierId(MapUtil.getString(changeShiftsMap, ChangeShifts.CASHIER_ID));
            response.setCashierNo(MapUtil.getString(changeShiftsMap, ChangeShifts.CASHIER_NO));
            response.setType(MapUtil.getInteger(changeShiftsMap, ChangeShifts.TYPE));
            response.setStartDate(MapUtil.getLong(changeShiftsMap, ChangeShifts.START_DATE));
            response.setEndDate(MapUtil.getLong(changeShiftsMap, ChangeShifts.END_DATE));
            response.setMerchantId(MapUtil.getString(changeShiftsMap, ChangeShifts.MERCHANT_ID));
            response.setServiceId(MapUtil.getString(changeShiftsMap, ChangeShifts.SERVICE_ID));
            response.setExtra(MapUtil.getMap(changeShiftsMap, ChangeShifts.EXTRA));
            response.setCtime(MapUtil.getLong(changeShiftsMap, DaoConstants.CTIME));
            response.setMtime(MapUtil.getLong(changeShiftsMap, DaoConstants.MTIME));
            Object extra;
            if ((extra = changeShiftsMap.get(ChangeShifts.EXTRA)) != null && extra instanceof Map) {
                Map<String, Object> cashierInfo = MapUtil.getMap((Map)extra, ChangeShifts.EXTRA_CASHIER_INFO);
                if (cashierInfo != null) {
                    response.setCashierName(MapUtil.getString(cashierInfo, ChangeShifts.CASHIER_NAME));
                    response.setCashierPhone(MapUtil.getString(cashierInfo, ChangeShifts.CASHIER_PHONE));
                }
            }
        }
        return response;
    }
}
