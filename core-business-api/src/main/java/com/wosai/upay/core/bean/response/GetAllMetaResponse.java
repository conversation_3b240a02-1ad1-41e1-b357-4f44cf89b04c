package com.wosai.upay.core.bean.response;

import com.wosai.upay.core.bean.model.ProviderAbility;
import com.wosai.upay.core.meta.*;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/25.
 */
public class GetAllMetaResponse {

    private List<Payway> payways;
    private List<SubPayway> subPayways;
    private List<Provider> providers;
    private List<ClearanceProvider> clearanceProviders;
    private List<ProductFlag> productFlags;

    public GetAllMetaResponse() {
    }

    public void setPayways(List<Payway> payways) {
        this.payways = payways;
    }

    public void setSubPayways(List<SubPayway> subPayways) {
        this.subPayways = subPayways;
    }

    public void setProviders(List<Provider> providers) {
        this.providers = providers;
    }

    public void setClearanceProviders(List<ClearanceProvider> clearanceProviders) {
        this.clearanceProviders = clearanceProviders;
    }

    public void setProductFlags(List<ProductFlag> productFlags) {
        this.productFlags = productFlags;
    }

    public List<Payway> getPayways() {
        return payways;
    }

    public List<SubPayway> getSubPayways() {
        return subPayways;
    }

    public List<Provider> getProviders() {
        return providers;
    }

    public List<ClearanceProvider> getClearanceProviders() {
        return clearanceProviders;
    }

    public List<ProductFlag> getProductFlags() {
        return productFlags;
    }

    public String getPaywayNameByCode(Integer code){
        return getNameByCode(payways, code);
    }

    public String getSubPaywayNameByCode(Integer code){
        return getNameByCode(subPayways, code);
    }

    public String getProviderNameByCode(Integer code){
        return getNameByCode(providers, code);
    }

    public String getClearanceProviderNameByCode(Integer code){
        return getNameByCode(clearanceProviders, code);
    }

    public String getProductFlagNameByCode(String code){
        return getNameByCode(productFlags, code);
    }



    private <T> String getNameByCode(List<? extends Meta> metaList, T code){
        if(code == null || metaList == null){
            return null;
        }
        for (Meta meta : metaList) {
            if(Objects.equals(meta.getCode(), code)){
                return meta.getName();
            }
        }
        return null;
    }

}
