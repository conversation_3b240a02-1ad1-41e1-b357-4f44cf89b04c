package com.wosai.upay.core.bean.response;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.bean.model.ProviderAbility;

public class GetAllProviderAbilityResponse {
    @JsonProperty("provider_abilitys")
    private List<ProviderAbility> providerAbilitys;

    public List<ProviderAbility> getProviderAbilitys() {
        return providerAbilitys;
    }

    public void setProviderAbilitys(List<ProviderAbility> providerAbilitys) {
        this.providerAbilitys = providerAbilitys;
    }

}
