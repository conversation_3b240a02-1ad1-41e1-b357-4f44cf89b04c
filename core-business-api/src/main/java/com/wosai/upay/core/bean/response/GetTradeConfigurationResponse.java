package com.wosai.upay.core.bean.response;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/8/23.
 */
public class GetTradeConfigurationResponse {

    /**
     * 是否支持平台券
     */
    private boolean supportPlatformCoupon;

    /**
     * 是否直清 直连以及一些银行通道属于直清，拉卡拉、海科交易属于间清
     */
    private boolean isFormal;

    /**
     * 是否直连交易
     */
    private boolean isDirect;


    private Integer provider;

    public boolean isSupportPlatformCoupon() {
        return supportPlatformCoupon;
    }

    public void setSupportPlatformCoupon(boolean supportPlatformCoupon) {
        this.supportPlatformCoupon = supportPlatformCoupon;
    }

    public boolean isFormal() {
        return isFormal;
    }

    public void setFormal(boolean formal) {
        isFormal = formal;
    }

    public boolean isDirect() {
        return isDirect;
    }

    public void setDirect(boolean direct) {
        isDirect = direct;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }
}
