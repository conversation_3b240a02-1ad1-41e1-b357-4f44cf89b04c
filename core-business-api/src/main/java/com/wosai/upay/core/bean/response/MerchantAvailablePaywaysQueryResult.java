package com.wosai.upay.core.bean.response;

import java.util.Map;

/**
 * <AUTHOR> Date: 2022/12/13 Time: 3:09 PM
 */
public class MerchantAvailablePaywaysQueryResult {

    private Integer payway;

    private Integer provider;

    private Map<String, Object> params;

    public Integer getPayway() {
        return payway;
    }

    public void setPayway(Integer payway) {
        this.payway = payway;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return "MerchantAvailablePaywaysQueryResult{" +
                "payway=" + payway +
                ", provider=" + provider +
                ", params=" + params +
                '}';
    }
}
