package com.wosai.upay.core.bean.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;

/**
 * <AUTHOR> Date: 2022/3/8 Time: 11:44 上午
 */
public class TradeExtConfigQueryResponse {

    private String id;
    private String sn;
    @JsonProperty("sn_type")
    private int snType;
    private Integer provider;
    private TradeExtConfigContentModel content;
    private long version;


    public TradeExtConfigQueryResponse() {
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public int getSnType() {
        return snType;
    }

    public void setSnType(int snType) {
        this.snType = snType;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public TradeExtConfigContentModel getContent() {
        return content;
    }

    public void setContent(TradeExtConfigContentModel content) {
        this.content = content;
    }

    public long getVersion() {
        return version;
    }

    public void setVersion(long version) {
        this.version = version;
    }

}
