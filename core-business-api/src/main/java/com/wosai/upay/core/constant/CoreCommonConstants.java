package com.wosai.upay.core.constant;

/**
 * <AUTHOR> Date: 2019-12-19 Time: 16:22
 */
public class CoreCommonConstants {
    //商户自定义配置表统一value值
    public static final String MERCHANT_CONFIG_CUSTOM_VALUE = "value";
    //收钱吧门店号和支付宝门店号的映射
    public static final String SQB_STORE_ALIPAY_STORE_MAP = "store_id_map";
    //支付宝门店号
    public static final String ALIPAY_STORE_ID = "alipay_store_id";


    public static final String KEY_STORE_PROVINCE = "store_province";
    public static final String KEY_STORE_CITY = "store_city";
    public static final String KEY_STORE_LOGO = "store_logo";
    public static final String KEY_MERCHANT_CITY = "merchant_city";
    public static final String KEY_MERCHANT_LOGO = "merchant_logo";
    public static final String KEY_MERCHANT_BUSINESS_NAME = "merchant_business_name";
    public static final String KEY_MERCHANT_ALIAS = "merchant_alias";

    public static final String KEY_COMMON_SWITCH_TYPE = "type";
    public static final String KEY_COMMON_SWITCH_STATUS = "status";

    public static final String SUFFIX_TRADE_PARAMS = "_trade_params";

    //商户信用限额类型日/月
    public static final int CREDIT_TYPE_DAY = 1;
    public static final int CREDIT_TYPE_MONTH = 2;


    public static final String FIND_ALL_VENDOR_APP_NAME_TYPE_TERMINAL = "0";

    public static final String FIND_ALL_VENDOR_APP_NAME_TYPE_MERCHANT = "1";

    public static final String FIND_ALL_VENDOR_APP_NAME_TYPE_DEVICE_FINGERPRINT = "2";


    public static final int SINGLE = 0;

    public static final int DAY = 1;



}
