package com.wosai.upay.core.exception;

/**
 * Created by jian<PERSON> on 24/3/16.
 */
public class CoreAccountEmailDuplicateException extends CoreBizException {
    @Override
    public int getCode() {
        return CoreException.CODE_ACCOUNT_EMAIL_DUPLICATE;
    }

    public CoreAccountEmailDuplicateException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreAccountEmailDuplicateException(String message) {
        super(message);
    }
}
