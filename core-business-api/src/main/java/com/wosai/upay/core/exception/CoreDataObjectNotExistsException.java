package com.wosai.upay.core.exception;

/**
 * <AUTHOR>
 */
public class CoreDataObjectNotExistsException extends CoreBizException {
    @Override
    public int getCode() {
        return CoreException.CODE_DATA_OBJECT_NOT_EXISTS;
    }

    public CoreDataObjectNotExistsException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreDataObjectNotExistsException(String message) {
        super(message);
    }
}
