package com.wosai.upay.core.exception;

/**
 * <AUTHOR>
 */
public class CoreDatabaseCannotSetIncorrectValueException extends CoreBizException {
    @Override
    public int getCode() {
        return CoreException.CODE_CANNOT_SET_INCORRECT_VALUE;
    }

    public CoreDatabaseCannotSetIncorrectValueException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreDatabaseCannotSetIncorrectValueException(String message) {
        super(message);
    }
}
