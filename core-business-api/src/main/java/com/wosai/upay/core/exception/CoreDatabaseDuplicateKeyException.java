package com.wosai.upay.core.exception;

/**
 * <AUTHOR>
 */
public class CoreDatabaseDuplicateKeyException extends CoreBizException {
    @Override
    public int getCode() {
        return CoreException.CODE_DATABASE_DUPLICATE_KEY;
    }

    public CoreDatabaseDuplicateKeyException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreDatabaseDuplicateKeyException(String message) {
        super(message);
    }
}
