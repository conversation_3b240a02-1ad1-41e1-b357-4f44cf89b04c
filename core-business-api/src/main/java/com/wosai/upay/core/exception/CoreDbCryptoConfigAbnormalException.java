package com.wosai.upay.core.exception;

/**
 *
 */
public class CoreDbCryptoConfigAbnormalException extends CoreBizException {

    public CoreDbCryptoConfigAbnormalException(String message) {
        this(message, null);
    }

    public CoreDbCryptoConfigAbnormalException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CoreException.CODE_DB_CRYPTO_CONFIG_ABNORMAL;
    }

}
