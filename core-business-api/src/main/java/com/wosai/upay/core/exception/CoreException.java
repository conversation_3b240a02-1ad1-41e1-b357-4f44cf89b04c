package com.wosai.upay.core.exception;

import com.wosai.upay.common.exception.CommonException;

import java.util.LinkedHashMap;
import java.util.Map;

@SuppressWarnings("serial")
public abstract class CoreException extends CommonException {

    // 20*-30*之间为业务异常编码
    // 201*为开发者相关异常编码
    public static final int CODE_VENDOR_NOT_EXISTS = 20101;
    public static final int CODE_VENDOR_DEVELOPER_NOT_EXISTS = 20102;
    public static final int CODE_VENDOR_ACCESS_DENIED = 20103;
    public static final int CODE_VENDOR_STATUS_ABNORMAL = 20104;

    // 202*为推广渠道相关异常编码
    public static final int CODE_SOLICITOR_NOT_EXISTS = 20201;
    public static final int CODE_SOLICITOR_DEVELOPER_NOT_EXISTS = 20202;
    public static final int CODE_SOLICITOR_ACCESS_DENIED = 20203;
    public static final int CODE_SOLICITOR_STATUS_ABNORMAL = 20204;

    // 203*为商户相关异常编码
    public static final int CODE_MERCHANT_NOT_EXISTS = 20301;
    public static final int CODE_MERCHANT_CONFIG_ABNORMAL = 20302;
    public static final int CODE_MERCHANT_STATUS_ABNORMAL = 20303;
    public static final int CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT = 20304;
    public static final int CODE_MERCHNAT_BANKACCOUNT_NO_KEY_INFO_CHANGED = 20305;
    public static final int CODE_MERCHANT_NOT_DELETED_BANK_ACCOUNT_PRE = 20306;
    public static final int CODE_MERCHNAT_BANKACCOUNT_NO_INSERT = 20307;

    // 204*为门店相关异常编码
    public static final int CODE_STORE_NOT_EXISTS = 20401;
    public static final int CODE_STORE_NOT_ONLINE = 20402;
    public static final int CODE_STORE_STATUS_ABNORMAL = 20403;
    public static final int CODE_STORE_CONFIG_ABNORMAL = 20404;


    // 205*为终端相关异常编码
    public static final int CODE_TERMINAL_NOT_EXISTS = 20501;
    public static final int CODE_TERMINAL_NOT_ACTIVATED = 20502;
    public static final int CODE_TERMINAL_ACTIVATION_ERROR = 20503;
    public static final int CODE_TERMINAL_BIND_ERROR = 20504;
    public static final int CODE_TERMINAL_MOVE_STORE_NOT_BELONG_MERCHANT = 20505;
    public static final int CODE_TERMINAL_STATUS_ABNORMAL = 20506;
    public static final int CODE_TERMINAL_MISSING_NEXT_SECRET = 20507;
    public static final int CODE_TERMINAL_OBSOLETE_NEXT_SECRET = 20508;
    public static final int CODE_TERMINAL_ACTIVATION_CODE_GENERATION_ERROR = 20509;
    public static final int CODE_TERMINAL_CONFIG_ABNORMAL = 20510;

    // 206*为用户、账号、权限相关异常编码
    public static final int CODE_ACCOUNT_NOT_EXISTS = 20601;
    public static final int CODE_USERNAME_DUPLICATE = 20602;
    public static final int CODE_CELLPHONE_DUPLICATE = 20603;
    public static final int CODE_ACCOUNT_EMAIL_DUPLICATE = 20604;
    public static final int CODE_EXTERNAL_STATE_CLOSE_ABNORMAL = 20605; //收单机构子商户号禁用


    // 207*为其他异常编码
    public static final int CODE_CLIENT_SN_NOT_UNIQUE = 20701;
    public static final int CODE_SN_GENERATION_FAIL = 20702;
    public static final int CODE_ONLY_STATUS_DISABLED_COULD_ENABLE = 20703;
    public static final int CODE_ONLY_STATUS_ACTIVATED_COULD_DISABLE = 20704;
    public static final int CODE_DB_CRYPTO_CONFIG_ABNORMAL = 20705;
    public static final int CODE_ID_VALIDITY_TOO_SHORT = 20706;
    public static final int CODE_LICENSE_NOT_EXISTS = 20707;

    // 208*为开发者应用异常编码
    public static final int CODE_VENDOR_APP_NOT_EXITS = 20801;
    public static final int CODE_VENDOR_APP_ACCESS_DENIED = 20802;

    // 209*为集团商户异常编码
    public static final int CODE_GROUP_NOT_EXITS = 20901;
    public static final int CODE_GROUP_USER_NOT_EXITS = 20902;

    //210*为部门异常编码
    public static final int CODE_DEPARTMENT_NOT_EXITS = 21001;

    // 301&为境外币种费率配置异常编码
    public static final int CODE_CURRENCY_FEERATE_NOT_EXIST = 30101;

    // 302*为交接班异常编码
    public static final int CODE_CHANGE_SHITFS_EROR = 30201;

    public static final Map<Integer, String> CODES_DESC_MAP = new LinkedHashMap<Integer, String>() {{
        putAll(COMMON_CODES_DESC_MAP); //将常用异常取过来
        put(CODE_VENDOR_NOT_EXISTS, "开发者不存在");
        put(CODE_VENDOR_DEVELOPER_NOT_EXISTS, "开发者账户不存在");
        put(CODE_VENDOR_ACCESS_DENIED, "开发者不具备对应操作的权限");
        put(CODE_VENDOR_STATUS_ABNORMAL, "开发者已被关闭或者禁用");

        put(CODE_SOLICITOR_NOT_EXISTS, "推广渠道不存在");
        put(CODE_SOLICITOR_DEVELOPER_NOT_EXISTS, "推广渠道开发者不存在");
        put(CODE_SOLICITOR_ACCESS_DENIED, "推广渠道不具备对应操作的权限");
        put(CODE_SOLICITOR_STATUS_ABNORMAL, "推广渠道已被关闭或者禁用");

        put(CODE_MERCHANT_NOT_EXISTS, "商户不存在");
        put(CODE_MERCHANT_CONFIG_ABNORMAL, "交易参数配置异常");
        put(CODE_MERCHANT_STATUS_ABNORMAL, "商户已被关闭或者禁用");
        put(CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT, "该商户未绑定过银行卡信息");
        put(CODE_MERCHANT_NOT_DELETED_BANK_ACCOUNT_PRE, "不允许删除正在使用的银行卡");
        put(CODE_MERCHNAT_BANKACCOUNT_NO_KEY_INFO_CHANGED,"银行卡信息未做任何变更不能重新绑卡");
        put(CODE_MERCHNAT_BANKACCOUNT_NO_INSERT,"商户银行预存卡达到上限，不允许添加");


        put(CODE_STORE_NOT_EXISTS, "门店不存在");
        put(CODE_STORE_NOT_ONLINE, "门店非上线状态");
        put(CODE_STORE_STATUS_ABNORMAL, "门店已被禁用或者被关闭");
        put(CODE_STORE_CONFIG_ABNORMAL, "该门店收款功能已关闭");

        put(CODE_TERMINAL_NOT_EXISTS, "终端不存在");
        put(CODE_TERMINAL_NOT_ACTIVATED, "终端未激活");
        put(CODE_TERMINAL_ACTIVATION_ERROR, "终端激活错误");
        put(CODE_TERMINAL_BIND_ERROR, "终端绑定错误");
        put(CODE_TERMINAL_MOVE_STORE_NOT_BELONG_MERCHANT, "移机到的门店不属于终端所属商户下的门店");
        put(CODE_TERMINAL_STATUS_ABNORMAL, "终端未激活或者已被禁用");

        put(CODE_ACCOUNT_NOT_EXISTS, "账号不存在");
        put(CODE_USERNAME_DUPLICATE, "用户名重复");
        put(CODE_CELLPHONE_DUPLICATE, "手机号码重复");
        put(CODE_ACCOUNT_EMAIL_DUPLICATE, "邮箱账号重复");

        put(CODE_CLIENT_SN_NOT_UNIQUE, "(门店、终端、订单号)在外部编号商户内不唯一");
        put(CODE_SN_GENERATION_FAIL, "编号生成失败");
        put(CODE_ONLY_STATUS_DISABLED_COULD_ENABLE, "只用禁用状态才可启用/激活");

        put(CODE_VENDOR_APP_NOT_EXITS, "开发者应用不存在");
        put(CODE_VENDOR_APP_ACCESS_DENIED, "开发者应用不具备对应操作的权限");

        put(CODE_GROUP_NOT_EXITS, "集团不存在");
        put(CODE_GROUP_USER_NOT_EXITS, "集团用户不存在");

        put(CODE_DEPARTMENT_NOT_EXITS, "部门不存在");
        put(CODE_LICENSE_NOT_EXISTS,"没有此营业执照或者许可证的信息");
    }};

    public static String getCodeDesc(int code) {
        return CODES_DESC_MAP.get(code);
    }

    public CoreException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreException(String message) {
        super(message);
    }

    public CoreException(Throwable cause) {
        super(cause);
    }

}
