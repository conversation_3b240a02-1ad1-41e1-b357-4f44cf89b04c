package com.wosai.upay.core.exception;

public class CoreExternalStateException extends CoreBizException {

    public static final String PROVIDER_MCH_CLOSE_MSG = "收单机构商户号已禁用";
    public static final String SUB_MCH_ID_CLOSE_MSG = "支付源商户号已禁用";
    public static final String PROVIDER_TERMINAL_CLOSE_MSG = "收单机构终端号已禁用";



    public CoreExternalStateException(String message) {
        super(message);
    }

    public CoreExternalStateException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CoreException.CODE_EXTERNAL_STATE_CLOSE_ABNORMAL;
    }
}
