package com.wosai.upay.core.exception;

@SuppressWarnings("serial")
public class CoreGroupUserNotExistsException extends CoreBizException {

    public CoreGroupUserNotExistsException(String message) {
        this(message, null);
    }

    public CoreGroupUserNotExistsException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CoreException.CODE_GROUP_USER_NOT_EXITS;
    }

}
