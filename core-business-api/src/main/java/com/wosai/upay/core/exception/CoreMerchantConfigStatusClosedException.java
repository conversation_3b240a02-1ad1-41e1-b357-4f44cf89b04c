package com.wosai.upay.core.exception;

/**
 * Created by jian<PERSON> on 24/3/16.
 */
public class CoreMerchantConfigStatusClosedException extends CoreBizException {
    @Override
    public int getCode() {
        return CoreException.CODE_MERCHANT_CONFIG_ABNORMAL;
    }

    public CoreMerchantConfigStatusClosedException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreMerchantConfigStatusClosedException(String message) {
        super(message);
    }
}
