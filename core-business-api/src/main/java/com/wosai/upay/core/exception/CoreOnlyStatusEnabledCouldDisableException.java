package com.wosai.upay.core.exception;

/**
 * <AUTHOR>
 */
public class CoreOnlyStatusEnabledCouldDisableException extends CoreBizException {
    @Override
    public int getCode() {
        return CoreException.CODE_ONLY_STATUS_ACTIVATED_COULD_DISABLE;
    }

    public CoreOnlyStatusEnabledCouldDisableException(String message, Throwable cause) {
        super(message, cause);
    }

    public CoreOnlyStatusEnabledCouldDisableException(String message) {
        super(message);
    }
}
