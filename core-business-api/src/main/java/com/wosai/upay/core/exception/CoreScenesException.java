package com.wosai.upay.core.exception;

public class CoreScenesException extends RuntimeException{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1123123123123213L;
	private String errorScenes = null;
	private int code = 0;
	private String exceptionName = null;
	
	public CoreScenesException(String errorScenes, int code, String exceptionName){
		this.errorScenes = errorScenes;
		this.code = code;
		this.exceptionName = exceptionName;
	}

	public String getErrorScenes() {
		return errorScenes;
	}

	public void setErrorScenes(String errorScenes) {
		this.errorScenes = errorScenes;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getExceptionName() {
		return exceptionName;
	}

	public void setExceptionName(String exceptionName) {
		this.exceptionName = exceptionName;
	}
}
