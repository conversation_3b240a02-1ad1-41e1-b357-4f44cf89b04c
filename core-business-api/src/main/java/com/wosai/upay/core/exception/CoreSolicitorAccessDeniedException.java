package com.wosai.upay.core.exception;

public class CoreSolicitorAccessDeniedException extends CoreAuthException {
    public CoreSolicitorAccessDeniedException(String message) {
        super(message);
    }

    public CoreSolicitorAccessDeniedException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CODE_SOLICITOR_ACCESS_DENIED;
    }
}
