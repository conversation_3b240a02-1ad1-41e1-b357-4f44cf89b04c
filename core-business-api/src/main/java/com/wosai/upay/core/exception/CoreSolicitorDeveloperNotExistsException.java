package com.wosai.upay.core.exception;

public class CoreSolicitorDeveloperNotExistsException extends CoreBizException {
    public CoreSolicitorDeveloperNotExistsException(String message) {
        super(message);
    }

    public CoreSolicitorDeveloperNotExistsException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CODE_SOLICITOR_DEVELOPER_NOT_EXISTS;
    }
}
