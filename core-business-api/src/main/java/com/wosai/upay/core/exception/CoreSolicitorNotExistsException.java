package com.wosai.upay.core.exception;

public class CoreSolicitorNotExistsException extends CoreBizException {
    public CoreSolicitorNotExistsException(String message) {
        super(message);
    }

    public CoreSolicitorNotExistsException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CODE_SOLICITOR_NOT_EXISTS;
    }
}
