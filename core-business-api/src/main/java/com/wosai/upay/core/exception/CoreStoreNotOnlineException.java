package com.wosai.upay.core.exception;

@SuppressWarnings("serial")
public class CoreStoreNotOnlineException extends CoreBizException {

    public CoreStoreNotOnlineException(String message) {
        this(message, null);
    }

    public CoreStoreNotOnlineException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public int getCode() {
        return CoreException.CODE_STORE_NOT_ONLINE;
    }

}
