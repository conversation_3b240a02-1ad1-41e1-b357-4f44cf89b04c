package com.wosai.upay.core.model;

/**
 * Created by jian<PERSON> on 26/8/16.
 */
public class Agent {

    public static final String NAME = "name"; // VARCHAR(37) NOT NULL COMMENT '受理商名字'
    public static final String DETAILS = "details"; // VARCHAR(256) NOT NULL COMMENT '受理商详情'
    public static final String PROVIDER = "provider"; // INT(11) NULL DEFAULT NULL COMMENT '结算通道'
    public static final String PAYWAY = "payway"; // INT(11) NULL DEFAULT NULL COMMENT '支付通道'
    public static final String SUB_PAYWAY = "sub_payway"; //  INT(1) NULL DEFAULT NULL COMMENT '交易模式'
    public static final String FORMAL = "formal"; // tinyint(1) NOT NULL COMMENT '是否正式'
    public static final String IS_PRIMARY = "is_primary"; //  tinyint(1) NOT NULL COMMENT '是否默认配置'
    public static final String PARAMS = "params"; //  BLOB NULL COMMENT '配置参数（JSON）''

}
