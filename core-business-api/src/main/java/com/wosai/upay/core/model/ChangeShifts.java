package com.wosai.upay.core.model;

public class ChangeShifts {
    public static final String MERCHANT_ID = "merchant_id";
    public static final String STORE_ID = "store_id";
    public static final String TYPE = "type";
    public static final String SERVICE_ID = "service_id";
    public static final String BATCH_SN = "batch_sn";
    public static final String NEXT_BATCH_SN = "next_batch_sn";
    public static final String CASHIER_ID = "cashier_id";
    public static final String CASHIER_NO = "cashier_no";
    public static final String START_DATE = "start_date";
    public static final String END_DATE = "end_date";
    public static final String EXTRA = "extra";
    public static final String EXTRA_CASHIER_INFO = "cashier";
    public static final String CASHIER_NAME = "cashier_name";   // 收银员名称
    public static final String CASHIER_PHONE = "cashier_phone";   // 收银员手机号

    public static final String EXTRA_TRADE_INFO = "trade_info";
    public static final String CASH_DESK_NAME = "cash_desk_name";   // 收银台名称，在班次类型=收银台时才有值

    public static final int TYPE_TERMINAL = 1;      // 签到类型：终端
    public static final int TYPE_CASHDESK = 2;      // 签到类型：收银台
}
