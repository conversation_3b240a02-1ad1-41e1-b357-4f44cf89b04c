package com.wosai.upay.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Arrays;
import java.util.List;

public class ExternalExtraConfig {

    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_OPENED = 1; //开通

    public static final int SN_TYPE_PROVIDER_MCH = 1; //收单机构商户号
    public static final int SN_TYPE_SUB_MCH_ID = 2; //支付源商户号
    public static final int SN_TYPE_PROVIDER_TERMINAL = 3; //收单机构终端号

    public static final int BIZ_TYPE_TRADE = 0;
    public static final int BIZ_TYPE_SETTLEMENT = 2;
    public static final int BIZ_TYPE_REFUND = 3;

    public static final List<Integer> ALLOW_SN_TYPE = Arrays.asList(SN_TYPE_PROVIDER_MCH, SN_TYPE_PROVIDER_TERMINAL, SN_TYPE_SUB_MCH_ID);
    public static final List<Integer> ALLOW_BIZ_TYPE = Arrays.asList(BIZ_TYPE_TRADE, BIZ_TYPE_SETTLEMENT, BIZ_TYPE_REFUND);
    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '编号'
    public static final String SN_TYPE = "sn_type";
    public static final String BIZ_TYPE = "biz_type";
    public static final String STATUS = "status";
    public static final String PROVIDER = "provider";
}
