package com.wosai.upay.core.model;

/**
 * 集团.
 */
public class Group {
    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用

    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '集团编号'
    public static final String NAME = "name"; // varchar(128) NOT NULL COMMENT '集团名称'
    public static final String ALIAS = "alias"; // varchar(128) DEFAULT NULL COMMENT '集团简称'
    public static final String INDUSTRY = "industry"; // varchar(36) DEFAULT NULL
    public static final String STATUS = "status"; // int unsigned DEFAULT NULL
    public static final String MANAGER_MERCHANT_NUM = "manager_merchant_num"; // int NOT NULL DEFAULT 0 COMMENT '管理商户数'
    public static final String LONGITUDE = "longitude"; // varchar(16) DEFAULT NULL COMMENT '经度'
    public static final String LATITUDE = "latitude"; // varchar(16) DEFAULT NULL COMMENT '纬度'
    public static final String PROVINCE = "province"; // varchar(32) DEFAULT NULL COMMENT '省'
    public static final String CITY = "city"; // varchar(32) DEFAULT NULL COMMENT '市'
    public static final String DISTRICT = "district"; // varchar(32) DEFAULT NULL COMMENT '区'
    public static final String STREET_ADDRESS = "street_address"; // varchar(255) DEFAULT NULL COMMENT '街道门牌号'
    public static final String CONTACT_NAME = "contact_name"; // varchar(32) DEFAULT NULL COMMENT '联系人姓名'
    public static final String CONTACT_PHONE = "contact_phone"; // varchar(32) DEFAULT NULL COMMENT '联系固定电话号码'
    public static final String CONTACT_CELLPHONE = "contact_cellphone"; // varchar(32) DEFAULT NULL COMMENT '联系移动电话号码'
    public static final String CLIENT_SN = "client_sn"; // varchar(50) DEFAULT NULL COMMENT '集团外部集团号 '
    public static final String VENDOR_ID = "vendor_id"; // varchar(37) DEFAULT NULL COMMENT '服务商UUID'
    public static final String SOLICITOR_ID = "solicitor_id"; // varchar(37) DEFAULT NULL COMMENT '推广者UUID'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段'

}
