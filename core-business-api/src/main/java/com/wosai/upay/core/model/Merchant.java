package com.wosai.upay.core.model;

public class Merchant {
    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用
    public static final int BANK_ACCOUNT_VERIFY_STATUS_NO_MATEIRAL = -1;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_NOT = 0;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_INPROGRESS = 1;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_SUCC = 2;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_FAIL = 3;

    public static final int WITHDRAW_MODE_NORMAL = 1;
    public static final int WITHDRAW_MODE_AUTO = 2;

    public static final String MERCHANT_TYPE = "merchant_type"; // tinyint(4)  DEFAULT '-1'  COMMENT '个人0、个体1、组织2',
    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '商户可见的商户号'
    public static final String NAME = "name"; // varchar(128) NOT NULL COMMENT '商户名'
    public static final String ALIAS = "alias"; // varchar(128) DEFAULT NULL COMMENT '商户别名/常用名'
    public static final String BUSINESS_NAME = "business_name"; // varchar(128) DEFAULT NULL COMMENT '商户经营名称'
    public static final String INDUSTRY = "industry"; // varchar(36) DEFAULT NULL
    public static final String STATUS = "status"; // int unsigned DEFAULT NULL
    public static final String RANK = "rank"; // int DEFAULT NULL COMMENT '商户信用等级，初始值从推广者继承过来。'
    public static final String WITHDRAW_MODE = "withdraw_mode"; // int NOT NULL DEFAULT 1 COMMENT '1：人工提现；2：智能提现，其他值根据业务定义'
    public static final String LONGITUDE = "longitude"; // varchar(16) DEFAULT NULL COMMENT '经度'
    public static final String LATITUDE = "latitude"; // varchar(16) DEFAULT NULL COMMENT '纬度'
    public static final String COUNTRY = "country";   // varchar(3) DEFAULT 'CHN' COMMENT '国家代码,遵循ISO 3166-1 。默认为中国两位字母代码CN'
    public static final String PROVINCE = "province"; // varchar(32) DEFAULT NULL COMMENT '省'
    public static final String CITY = "city"; // varchar(32) DEFAULT NULL COMMENT '市'
    public static final String DISTRICT = "district"; // varchar(32) DEFAULT NULL COMMENT '区'
    public static final String STREET_ADDRESS = "street_address"; // varchar(255) DEFAULT NULL COMMENT '街道门牌号'
    public static final String STREET_ADDRESS_DESC = "street_address_desc"; // varchar(255) DEFAULT NULL COMMENT '街道地址备注说明'
    public static final String CONTACT_NAME = "contact_name"; // varchar(32) DEFAULT NULL COMMENT '联系人姓名'
    public static final String CONTACT_PHONE = "contact_phone"; // varchar(32) DEFAULT NULL COMMENT '联系固定电话号码'
    public static final String CONTACT_CELLPHONE = "contact_cellphone"; // varchar(32) DEFAULT NULL COMMENT '联系移动电话号码'
    public static final String CONTACT_EMAIL = "contact_email"; // varchar(64) DEFAULT NULL COMMENT '联系邮箱'
    public static final String CONCAT_ID_CARD_FRONT_PHOTO = "concat_id_card_front_photo"; //varchar(346) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联系人身份证正面照片',
    public static final String CONCAT_IDENTITY = "concat_identity"; //varchar(168) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联系人身份证号'

    public static final String LEGAL_PERSON_TYPE = "legal_person_type"; // int DEFAULT NULL COMMENT '法人类型 1:个人 2:企业'
    public static final String LEGAL_PERSON_NAME = "legal_person_name"; // varchar(100) DEFAULT NULL COMMENT '法人姓名'
    public static final String LEGAL_PERSON_ID_TYPE = "legal_person_id_type"; // int(11) NULL DEFAULT 1 COMMENT '法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；'
    public static final String LEGAL_PERSON_ID_NUMBER = "legal_person_id_number"; // varchar(100) DEFAULT NULL COMMENT '法人证件号码'
    public static final String LEGAL_PERSON_ID_CARD_FRONT_PHOTO = "legal_person_id_card_front_photo"; // varchar(255) DEFAULT NULL COMMENT '法人身份证正面照'
    public static final String LEGAL_PERSON_ID_CARD_BACK_PHOTO = "legal_person_id_card_back_photo"; // varchar(255) DEFAULT NULL COMMENT '法人身份证反面照'
    public static final String LEGAL_PERSON_REGISTER_NO = "legal_person_register_no"; // varchar(100) DEFAULT NULL COMMENT '营业执照注册号/个体户注册号'
    public static final String BUSINESS_LICENSE_PHOTO = "business_license_photo"; // varchar(255) NULL COMMENT '营业执照照片'
    public static final String BUSINESS = "business"; // varchar(255) DEFAULT NULL COMMENT '经营内容'
    public static final String CURRENCY = "currency"; // varchar(10) DEFAULT 'CNY' COMMENT '商户交易币种。符合ISO 4217标准的三位字母代码, 默认为人民币CNY'
    public static final String OWNER_NAME = "owner_name"; // varchar(50) DEFAULT NULL COMMENT '所有人姓名'
    public static final String OWNER_CELLPHONE = "owner_cellphone"; // varchar(32) DEFAULT NULL COMMENT '所有人手机'
    public static final String CUSTOMER_PHONE = "customer_phone"; // varchar(32) DEFAULT NULL COMMENT '所有人手机'
    public static final String BANK_ACCOUNT_VERIFY_STATUS = "bank_account_verify_status"; // INT(11) NOT NULL DEFAULT -1 COMMENT '卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败'
    public static final String CLIENT_SN = "client_sn"; // varchar(50) DEFAULT NULL COMMENT '商户外部商户号 '
    public static final String VENDOR_ID = "vendor_id"; // varchar(37) DEFAULT NULL COMMENT '服务商UUID'
    public static final String SOLICITOR_ID = "solicitor_id"; // varchar(37) DEFAULT NULL COMMENT '推广者UUID'
    public static final String PLATFORM = "platform"; // int NOT NULL DEFAULT '0' COMMENT '商户所属平台platform: 0: 收钱吧 1: 拉卡拉'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段'
    public static final String LOGO = "logo"; // varchar(255) DEFAULT NULL COMMENT '门店码LOGO'
    public static final String DISTRICT_CODE = "district_code";
    public static final String BINDED_STORE_ID = "binded_store_id";


}
