package com.wosai.upay.core.model;

import java.util.Arrays;
import java.util.List;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/11/10.
 */
public class MerchantAudit {
    public static final int STATUS_MATERIAL_NOT_SUBMIT = 0;//资料未上传
    public static final int STATUS_PENDING = 1;//待处理
    public static final int STATUS_IN_PROCESS = 2;//处理中
    public static final int STATUS_PASSED = 3;//审核通过
    public static final int STATUS_REJECTED = 4;//审核驳回
    /**
     * 定义的状态码集合
     */
    public static final List<Integer> STATUS_LIST = Arrays.asList(STATUS_MATERIAL_NOT_SUBMIT, STATUS_PENDING, STATUS_IN_PROCESS, STATUS_PASSED, STATUS_REJECTED);

    public static final int OPERATOR_SUBMITTER = 1;
    public static final int OPERATOR_AUDITOR = 2;

    public static final int PLATFORM_ISV = 1;
    public static final int PLATFORM_OSP = 2;
    public static final int PLATFORM_CRM = 3;

    public static final String MERCHANT_ID = "merchant_id";
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String MERCHANT_NAME = "merchant_name";
    public static final String MERCHANT_PROVINCE = "merchant_province";
    public static final String MERCHANT_CITY = "merchant_city";
    public static final String MERCHANT_DISTRICT = "merchant_district";
    public static final String MERCHANT_STREET_ADDRESS = "merchant_street_address";
    public static final String MERCHANT_CONTACT_NAME = "merchant_contact_name";
    public static final String MERCHANT_CONTACT_CELLPHONE = "merchant_contact_cellphone";
    public static final String MERCHANT_OWNER_NAME = "merchant_owner_name";
    public static final String MERCHANT_OWNER_CELLPHONE = "merchant_owner_cellphone";
    public static final String MERCHANT_SOLICITOR_ID = "merchant_solicitor_id";
    public static final String MERCHANT_SOLICITOR_NAME = "merchant_solicitor_name";
    public static final String MERCHANT_CTIME = "merchant_ctime";
    public static final String SUBMITTER = "submitter";
    public static final String SUBMIT_PLATFORM = "submit_platform";
    public static final String SUBMIT_ORGANIZATION_PATH = "submit_organization_path";
    public static final String AUDITOR = "auditor";
    public static final String AUDIT_PLATFORM = "audit_platform";
    public static final String STATUS = "status";//真实性审核总状态
    public static final String BUSINESS_LICENSE_STATUS = "business_license_status";//营业执照审核状态
    public static final String STORE_PHOTO_STATUS = "store_photo_status";//门店状态
    public static final String BASE_INFO_STATUS = "base_info_status";//商户基本信息状态
    public static final String STATUS_CHANGE_DETAILS = "status_change_details";
    public static final String BRAND_PHOTO = "brand_photo";
    public static final String INDOOR_MATERIAL_PHOTO = "indoor_material_photo";
    public static final String OUTDOOR_MATERIAL_PHOTO = "outdoor_material_photo";
    public static final String OTHER_PHOTO = "other_photo";
    public static final String AUDIT_PHOTO = "audit_photo";//审批截图
    public static final String REMARK = "remark";
    public static final String DESCRIPTION = "description";
    public static final String EXTRA = "extra";
    public static final String OPERATOR = "operator";
    public static final String OPERATOR_TYPE = "operator_type";
    public static final String OPERATE_PLATFORM = "operator_platform";

    //提供给 api 使用的字段，调用方将照片相关的数据（包括url，poi）放在一个字段提交
    public static final String BRAND = "brand";
    public static final String INDOOR_MATERIAL = "indoor_material";
    public static final String OUTDOOR_MATERIAL = "outdoor_material";
    public static final String OTHER = "other";

}
