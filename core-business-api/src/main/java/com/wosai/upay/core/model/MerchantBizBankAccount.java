package com.wosai.upay.core.model;

/**
 *
 */
public class MerchantBizBankAccount {
    public static final int VERIFY_STATUS_NOT = 0;
    public static final int VERIFY_STATUS_INPROGRESS = 1;
    public static final int VERIFY_STATUS_SUCC = 2;
    public static final int VERIFY_STATUS_FAIL = 3;

    public static final int DEFAULT_STATUS_TRUE = 1;
    public static final int DEFAULT_STATUS_FALSE = 0;

    public static final String MERCHANT_ID = "merchant_id"; // varchar(45) DEFAULT NULL
    public static final String TYPE = "type"; // int DEFAULT NULL COMMENT '账户类型：1：个人账户；2：企业账户'
    public static final String BIZ = "biz"; // varchar(16) NOT NULL DEFAULT '' COMMENT '业务方'
    public static final String HOLDER = "holder"; // varchar(45) DEFAULT NULL COMMENT '账户持有人名称'
    public static final String ID_TYPE = "id_type"; // int(11) NULL DEFAULT 1 COMMENT '账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；'
    public static final String IDENTITY = "identity"; // varchar(45) DEFAULT NULL COMMENT '账户持有人证件编号'
    public static final String TAX_PAYER_ID = "tax_payer_id"; // varchar(45) DEFAULT NULL COMMENT '工商税务号'
    public static final String NUMBER = "number"; // varchar(45) DEFAULT NULL COMMENT '账号'
    public static final String VERIFY_STATUS = "verify_status"; // INT(11) NOT NULL DEFAULT '0' COMMENT '卡号(账号)真实性验证状态 0未验证 1 验证中 2验证有效 3验证失败'
    public static final String BANK_NAME = "bank_name"; // varchar(45) DEFAULT NULL COMMENT '开户银行名称'
    public static final String BRANCH_NAME = "branch_name"; // varchar(45) DEFAULT NULL COMMENT '分支行名称'
    public static final String CARD_VALIDITY = "card_validity"; //银行卡有效期
    public static final String CLEARING_NUMBER = "clearing_number"; //清算行号
    public static final String OPENING_NUMBER = "opening_number"; //开户行号

    public static final String CHANGE_EXTRA = "change_extra"; //银行卡变更时使用的extra字段


    public static final String CITY = "city"; // varchar(32) DEFAULT NULL COMMENT '分支行所在城市'
    public static final String CELLPHONE = "cellphone"; // varchar(32) DEFAULT NULL COMMENT '和账号绑定的手机号'
    public static final String HOLDER_ID_CARD_ADDRESS = "holder_id_card_address"; // VARCHAR(255) DEFAULT NULL COMMENT '身份证住址'
    public static final String HOLDER_ID_CARD_ISSUING_AUTHORITY = "holder_id_card_issuing_authority"; // VARCHAR(255) DEFAULT NULL COMMENT '身份证签发机关'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段'
    public static final String CHANGE_TIME = "change_time"; // bigint(20) NULL DEFAULT NULL COMMENT '换卡时间'
    public static final String HOLDER_ID_FRONT_PHOTO = "holder_id_front_photo";//varchar(255) DEFAULT NULL COMMENT '开户人证件照正面照片'
    public static final String HOLDER_ID_BACK_PHOTO = "holder_id_back_photo";//varchar(255) DEFAULT NULL COMMENT '开户人证件照背面照片'
    public static final String HOLDER_ID_FRONT_OCR_STATUS = "holder_id_front_ocr_status";  // int(11) DEFAULT '0' COMMENT '状态: 0-待认证;1 - 认证成功 2-认证失败; | 身份证正面OCR认证状态',
    public static final String HOLDER_ID_BACK_OCR_STATUS = "holder_id_back_ocr_status";   // int(11) DEFAULT '0' COMMENT '状态: 0-待认证;1 - 认证成功 2-认证失败; | 身份证反面OCR认证状态',
    public static final String HOLDER_ID_STATUS = "holder_id_status";  //int(11) DEFAULT '0' COMMENT '状态: 0-待认证;1 - 认证成功 2-认证失败; | 身份证OCR认证状态',
    public static final String BANK_CARD_IMAGE = "bank_card_image";//varchar(255) DEFAULT NULL COMMENT '对私：表示银行卡照片，对公：表示开户许可证'
    public static final String TRANSFER_VOUCHER = "transfer_voucher";//varchar(255) DEFAULT NULL COMMENT '转账凭证'
    public static final String ID_VALIDITY = "id_validity";//varchar(128) DEFAULT NULL COMMENT '证件有效期'
    public static final String LETTER_OF_AUTHORIZATION = "letter_of_authorization";//varchar(255) DEFAULT NULL COMMENT '营业执照授权函'
    public static final String HAND_LETTER_OF_AUTHORIZATION = "hand_letter_of_authorization";//varchar(512) DEFAULT NULL COMMENT '辅助证明材料，最多三张图片url，用逗号隔开'
    public static final String BANK_CARD_STATUS = "bank_card_status";  //int(11) DEFAULT '-1' COMMENT '银行卡照片/开户许可证审核状态 -1: 未提交; 0: 待认证; 1: 认证成功; 2: 认证失败;',

    public static final String DEFAULT_STATUS = "default_status"; // int(1) DEFAULT '0' COMMENT '是否为当前商户正在使用的银行卡 0:否 1:是'
    public static final String LEGAL_PERSON_NAME = "legal_person_name";  //varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '法人姓名',
    public static final String LEGAL_PERSON_REGISTER_NO = "legal_person_register_no";  //varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '营业执照注册号/个体户注册号'
    public static final String BUSINESS_LICENSE_PHOTO = "business_license_photo";  //营业执照照片
    public static final String BUSINESS_NAME = "business_name";  //商户经营名称

}
