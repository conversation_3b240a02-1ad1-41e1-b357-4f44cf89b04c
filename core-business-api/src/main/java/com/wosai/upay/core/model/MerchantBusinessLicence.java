package com.wosai.upay.core.model;

import java.util.Arrays;
import java.util.List;

/**
 * Created by hzq on 19/8/26.
 */
public class MerchantBusinessLicence {

    public static final String MERCHANT_ID = "merchant_id";
    public static final String TYPE = "type";
    public static final String PHOTO = "photo";
    public static final String NUMBER = "number";
    public static final String NAME = "name";
    public static final String VALIDITY = "validity";
    public static final String ADDRESS = "address";
    public static final String LETTER_OF_AUTHORIZATION = "letter_of_authorization";
    public static final String TRADE_LICENSE = "trade_license";
    public static final String TRADE_LICENSE_LIST = "trade_license_list";
    public static final String LEGAL_PERSON_ID_TYPE = "legal_person_id_type";
    public static final String LEGAL_PERSON_ID_CARD_FRONT_PHOTO = "legal_person_id_card_front_photo";
    public static final String LEGAL_PERSON_ID_CARD_BACK_PHOTO = "legal_person_id_card_back_photo";
    public static final String LEGAL_PERSON_NAME = "legal_person_name";
    public static final String LEGAL_PERSON_ID_NUMBER = "legal_person_id_number";
    public static final String ID_VALIDITY = "id_validity";
    public static final String BUSINESS_SCOPE = "business_scope";
    public static final String LEGAL_PERSON_ID_CARD_ADDRESS = "legal_person_id_card_address"; // VARCHAR(255) DEFAULT NULL COMMENT '身份证住址'
    public static final String LEGAL_PERSON_ID_CARD_ISSUING_AUTHORITY = "legal_person_id_card_issuing_authority"; // VARCHAR(255) DEFAULT NULL COMMENT '身份证签发机关'


    public static final List<String> PHOTOS = Arrays.asList(PHOTO, LEGAL_PERSON_ID_CARD_FRONT_PHOTO, LEGAL_PERSON_ID_CARD_BACK_PHOTO);

}
