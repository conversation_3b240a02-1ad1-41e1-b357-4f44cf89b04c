package com.wosai.upay.core.model;

public class MerchantConfigCustom {
    public static final int TYPE_WEIXIN_SUB_MCH_ID = 1;
    public static final int TYPE_JD_MERCHANT_NO = 2;
    public static final int TYPE_CIBBANK_MCH_ID_WEIXIN = 3;
    public static final int TYPE_CIBBANK_MCH_ID_ALIPAY = 4;
    public static final int TYPE_CITICBANK_MCH_ID_WEIXIN = 5;
    public static final int TYPE_CITICBANK_MCH_ID_ALIPAY = 6;
    public static final int TYPE_WEIXIN_GOODS_TAG = 7;
    public static final int TYPE_ALIPAY_SELLER_ID = 8;
    public static final int TYPE_ALIPAY_STORE_ID = 9;
    public static final int TYPE_ALIPAY_EDU_SCHOOL_ID = 10;

    public static final String TYPE = "type"; // tinyint NOT NULL COMMENT '类型 1: 微信子商户号 2:京东商户号 '
    public static final String MERCHANT_ID = "merchant_id"; // varchar(37) NOT NULL COMMENT '商户id'
    public static final String STORE_ID = "store_id"; // varchar(37) DEFAULT NULL COMMENT '门店id'
    public static final String B2C_VALUE = "b2c_value"; // varchar(64) DEFAULT NULL COMMENT 'b扫c交易的值'
    public static final String B2C_AGENT_NAME = "b2c_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String C2B_VALUE = "c2b_value"; // varchar(64) DEFAULT NULL COMMENT 'c扫b交易的值'
    public static final String C2B_AGENT_NAME = "c2b_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String WAP_VALUE = "wap_value"; // varchar(64) DEFAULT NULL COMMENT 'wap支付的值'
    public static final String WAP_AGENT_NAME = "wap_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String MINI_VALUE = "mini_value"; // varchar(64) DEFAULT NULL COMMENT '预留字段'
    public static final String MINI_AGENT_NAME = "mini_agent_name"; // varchar(64) DEFAULT NULL COMMENT '预留字段'
    public static final String APP_VALUE = "app_value"; // varchar(64) DEFAULT NULL COMMENT '预留字段'
    public static final String APP_AGENT_NAME = "app_agent_name"; // varchar(64) DEFAULT NULL COMMENT '预留字段'
    public static final String H5_VALUE = "h5_value"; // varchar(64) DEFAULT NULL COMMENT '预留字段'
    public static final String H5_AGENT_NAME = "h5_agent_name"; // varchar(64) DEFAULT NULL COMMENT '预留字段'
}
