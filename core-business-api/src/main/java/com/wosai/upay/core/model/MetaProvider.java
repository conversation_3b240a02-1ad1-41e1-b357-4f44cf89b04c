package com.wosai.upay.core.model;

/**
 * <AUTHOR>
 * @Date 2024/4/15、14:28
 **/

public class MetaProvider {

    public static final int SUPPORT = 1;

    public static final int FORBID = 0;

    //不进行限制，第一优先级
    public static final int NOT_LIMIT = 2;

    public static final String NAME = "name";

    public static final String FLAG = "flag";

    public static final String TRADE_SUPPORT_1000 = "trade_support_1000";

    // 收单机构
    public static final String ACQUIRE_NAME = "acquire_name";
    /**
     * 是否支持扫码预授权
     */
    public static final String SUPPORT_DEPOSIT = "support_deposit";
    /**
     * 是否跳过分账强校验
     */
    public static final String PROFIT_SHARING_SKIP_CHECK = "profit_sharing_skip_check";

    /**
     * 交易参数key
     */
    public static final String TRADE_PARAMS_KEY = "trade_params_key";

    /**
     * 支持的payway
     */
    public static final String SUPPORT_PAYWAYS = "support_payways";

}
