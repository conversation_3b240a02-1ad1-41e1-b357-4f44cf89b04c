package com.wosai.upay.core.model;

import com.wosai.upay.common.dao.DaoConstants;

import java.util.*;

public class OpLog {

    // 终端启动与关闭
    public static final String TERMINAL_TEMPLATE_CODE = "G00T6N4GGR30";
    public static final String TERMINAL_TABLE_NAME = "terminal#";


    public static final List<String> FIXED_TERMINAL_KEY_LIST = Arrays.asList(Terminal.SN);
    public static final List<String> TERMINAL_STATUS_LIST = Arrays.asList(Terminal.STATUS);

    public static final Map<String, Map<String, String>> TERMINAL_DESC_MAP = new HashMap() {
        {
            put(Terminal.STATUS, new HashMap<String, String>() {
                {
                    put(Terminal.STATUS_UNACTIVATED + "", "未激活");
                    put(Terminal.STATUS_DISABLED + "", "已禁用");
                    put(Terminal.STATUS_ACTIVATED + "", "已激活");

                }
            });
        }
    };


    // 更新商户信息
    public static final String MERCHANT_TEMPLATE_CODE = "74N4SGESGBMG";
    public static final String MERCHANT_TABLE_NAME = "merchant#";

    public static final List<String> FIXED_MERCHANT_KEY_LIST = Arrays.asList(DaoConstants.ID);

    public static final List<String> MERCHANT_CHANGE_KEY_LIST = Arrays.asList(
            Merchant.NAME, Merchant.ALIAS, Merchant.BUSINESS_NAME, Merchant.INDUSTRY, Merchant.STATUS,
            Merchant.COUNTRY, Merchant.PROVINCE, Merchant.CITY, Merchant.DISTRICT, Merchant.STREET_ADDRESS,
            Merchant.CONTACT_NAME, Merchant.CONTACT_PHONE, Merchant.CONTACT_CELLPHONE, Merchant.CONTACT_EMAIL, Merchant.CUSTOMER_PHONE, Merchant.WITHDRAW_MODE
    );

    public static final Map<String, Map<String, String>> MERCHANT_DESC_MAP = new HashMap() {
        {
            put(Merchant.STATUS, new HashMap<String, String>() {
                {
                    put(Merchant.STATUS_CLOSED + "", "关闭");
                    put(Merchant.STATUS_ENABLED + "", "启用");
                    put(Merchant.STATUS_DISABLED + "", "禁用");
                }
            });
            put(Merchant.WITHDRAW_MODE, new HashMap<String, String>() {
                {
                    put(Merchant.WITHDRAW_MODE_NORMAL + "", "人工结算");

                    put(Merchant.WITHDRAW_MODE_AUTO + "", "自动结算");
                }
            });
        }
    };


    // 修改门店信息
    public static final String STORE_TEMPLATE_CODE = "40QLW6TBINBW";
    public static final String STORE_TABLE_NAME = "store#";


    public static final List<String> STORE_CHANGE_KEY_LIST = Arrays.asList(Store.NAME, Store.INDUSTRY,
            Store.STATUS, Store.PROVINCE, Store.CITY, Store.DISTRICT, Store.STREET_ADDRESS,
            Store.CONTACT_NAME, Store.CONTACT_PHONE, Store.CONTACT_CELLPHONE, Store.CONTACT_EMAIL
    );


    public static final Map<String, Map<String, String>> STORE_DESC_MAP = new HashMap() {
        {
            put(Store.STATUS, new HashMap<String, String>() {
                {
                    put(Store.STATUS_CLOSED + "", "关闭");
                    put(Store.STATUS_ENABLED + "", "启用");
                    put(Store.STATUS_DISABLED + "", "禁用");
                }
            });
        }
    };


    // 修改商户配置
    public static final String MERCHANT_CONFIG_TEMPLATE_CODE = "FNQ8AGWIEOSF";
    public static final String MERCHANT_CONFIG_TABLE_NAME = "merchant_config#";

    public static final List<String> FIXED_MERCHANT_CONFIG_KEY_LIST = Arrays.asList(MerchantConfig.MERCHANT_ID);

    public static final List<String> MERCHANT_CONFIG_CHANGE_KEY_LIST = Arrays.asList(
            MerchantConfig.B2C_STATUS,
            MerchantConfig.B2C_FEE_RATE,
            MerchantConfig.C2B_STATUS,
            MerchantConfig.C2B_FEE_RATE,
            MerchantConfig.WAP_STATUS,
            MerchantConfig.WAP_FEE_RATE,
            MerchantConfig.MINI_STATUS,
            MerchantConfig.MINI_FEE_RATE,
            MerchantConfig.H5_STATUS,
            MerchantConfig.H5_FEE_RATE,
            MerchantConfig.APP_STATUS,
            MerchantConfig.APP_FEE_RATE,
            MerchantConfig.EXTEND2_STATUS,
            MerchantConfig.EXTEND2_FEE_RATE,
            MerchantConfig.PARAMS,
            MerchantConfig.PAYWAY,
            MerchantConfig.PROVIDER,
            MerchantConfig.B2C_FORMAL,
            MerchantConfig.B2C_AGENT_NAME,
            MerchantConfig.C2B_FORMAL,
            MerchantConfig.C2B_AGENT_NAME,
            MerchantConfig.WAP_FORMAL,
            MerchantConfig.WAP_AGENT_NAME,
            MerchantConfig.MINI_FORMAL,
            MerchantConfig.MINI_AGENT_NAME,
            MerchantConfig.H5_FORMAL,
            MerchantConfig.H5_AGENT_NAME,
            MerchantConfig.APP_FORMAL,
            MerchantConfig.APP_AGENT_NAME,
            MerchantConfig.EXTEND2_FORMAL,
            MerchantConfig.EXTEND2_AGENT_NAME
    );

    public static final Map<String, Map<String, String>> MERCHANT_CONFIG_DESC_MAP = new HashMap() {
        {

            put(MerchantConfig.B2C_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });

            put(MerchantConfig.C2B_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });

            put(MerchantConfig.WAP_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(MerchantConfig.MINI_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(MerchantConfig.H5_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });

            put(MerchantConfig.APP_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });

        }
    };


    // 修改商户配置
    public static final String MERCHANT_CONFIG_PARAMS_TEMPLATE_CODE = "GNFHGLOQ4UGB";
    public static final String MERCHANT_CONFIG_PARAMS_TABLE_NAME = "merchant_config#params#";

    public static final List<String> FIXED_MERCHANT_CONFIG_PARAMS_KEY_LIST = new ArrayList<>();
    public static final List<String> MERCHANT_CONFIG_PARAMS_CHANGE_KEY_LIST = Arrays.asList(
            TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, OpLog.SINGLE_TRANSACTION_LIMIT
    );


    public static final String HISTORY_TRADE_REFUND_FLAG_TEMPLATE_CODE = "FAG1GBESHTHN";
    public static final String HISTORY_TRADE_REFUND_FLAG_TABLE_NAME = "merchant_config#params#";

    public static final List<String> FIXED_HISTORY_TRADE_REFUND_FLAG_KEY_LIST = new ArrayList<>();
    public static final List<String> HISTORY_TRADE_REFUND_FLAG_CHANGE_KEY_LIST = Arrays.asList(
            TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG
    );


    /**
     * 云闪付单笔限额
     */
    public final static String SINGLE_TRANSACTION_LIMIT = "single_transaction_limit";


    // 修改商户配置
    public static final String COMMON_SWITCH_TEMPLATE_CODE = "8EF6GOSUH417";
    public static final String MERCHANT_CONFIG_COMMON_SWITCH_TABLE_NAME = "common_switch#";

    public static final List<String> MERCHANT_CONFIG_COMMON_SWITCH_CHANGE_KEY_LIST = Arrays.asList(
            "status", "account_phone"
    );


    // 修改商户业务方交易配置表
    public static final String MERCHANT_APP_CONFIG_TEMPLATE_CODE = "50GHWKPT8VCH";
    public static final String MERCHANT_APP_CONFIG_TABLE_NAME = "merchant_app_config#";

    public static final List<String> FIXED_MERCHANT__APP_CONFIG_KEY_LIST = new ArrayList<>();

    public static final List<String> MERCHANT__APP_CONFIG_CHANGE_KEY_LIST = Arrays.asList(
            MerchantAppConfig.B2C_FORMAL,
            MerchantAppConfig.B2C_STATUS,
            MerchantAppConfig.B2C_FEE_RATE,
            MerchantAppConfig.B2C_AGENT_NAME,
            MerchantAppConfig.C2B_FORMAL,
            MerchantAppConfig.C2B_STATUS,
            MerchantAppConfig.C2B_FEE_RATE,
            MerchantAppConfig.C2B_AGENT_NAME,
            MerchantAppConfig.WAP_FORMAL,
            MerchantAppConfig.WAP_STATUS,
            MerchantAppConfig.WAP_FEE_RATE,
            MerchantAppConfig.WAP_AGENT_NAME,
            MerchantAppConfig.MINI_FORMAL,
            MerchantAppConfig.MINI_STATUS,
            MerchantAppConfig.MINI_FEE_RATE,
            MerchantAppConfig.MINI_AGENT_NAME,
            MerchantAppConfig.PARAMS,
            MerchantAppConfig.PAYWAY,
            MerchantAppConfig.PROVIDER,
            MerchantAppConfig.APP_ID
    );


    public static final Map<String, Map<String, String>> MERCHANT_APP_CONFIG_DESC_MAP = new HashMap() {
        {

            put(MerchantConfig.B2C_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });

            put(MerchantConfig.C2B_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });

            put(MerchantConfig.WAP_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(MerchantConfig.MINI_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(MerchantConfig.B2C_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });

            put(MerchantConfig.C2B_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });


            put(MerchantConfig.WAP_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });

            put(MerchantConfig.MINI_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });

        }
    };


    public static final String VENDOR_TEMPLATE_CODE = "4EKHBQQ42IKH";
    public static final String VENDOR_TABLE_NAME = "vendor#";
    public static final List<String> VENDOR_CHANGE_KEY_LIST = Arrays.asList(
            Vendor.NAME,
            Vendor.SN,
            Vendor.CELLPHONE,
            Vendor.CONTACT_NAME,
            Vendor.CONTACT_PHONE,
            Vendor.CONTACT_CELLPHONE,
            Vendor.CONTACT_EMAIL,
            Vendor.CONTACT_ADDRESS,
            Vendor.STATUS);

    public static final Map<String, Map<String, String>> VENDOR_DESC_MAP = new HashMap() {
        {
            put(TerminalActivationCode.STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "正常");
                    put("2", "禁用");
                }
            });
        }
    };

    public static final String TERMINAL_CONFIG_TEMPLATE_CODE = "A6HDR31LTSE8";
    public static final String TERMINAL_CONFIG_TABLE_NAME = "terminal_config#";
    public static final List<String> TERMINAL_CONFIG_CHANGE_KEY_LIST = Arrays.asList(
            TerminalConfig.PAYWAY,
            TerminalConfig.B2C_FORMAL,
            TerminalConfig.B2C_STATUS,
            TerminalConfig.B2C_FEE_RATE,
            TerminalConfig.B2C_AGENT_NAME,
            TerminalConfig.C2B_FORMAL,
            TerminalConfig.C2B_STATUS,
            TerminalConfig.C2B_FEE_RATE,
            TerminalConfig.C2B_AGENT_NAME,
            TerminalConfig.WAP_FORMAL,
            TerminalConfig.WAP_STATUS,
            TerminalConfig.WAP_FEE_RATE,
            TerminalConfig.WAP_AGENT_NAME,
            TerminalConfig.MINI_FORMAL,
            TerminalConfig.MINI_STATUS,
            TerminalConfig.MINI_FEE_RATE,
            TerminalConfig.MINI_AGENT_NAME,
            TerminalConfig.PROVIDER,
            TerminalConfig.PARAMS,
            TerminalConfig.LADDER_STATUS
    );

    public static final Map<String, Map<String, String>> TERMINAL_CONFIG_DESC_MAP = new HashMap() {
        {
            put(TerminalConfig.B2C_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(TerminalConfig.C2B_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(TerminalConfig.WAP_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(TerminalConfig.MINI_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(TerminalConfig.H5_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(TerminalConfig.APP_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(TerminalConfig.EXTEND2_STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开通");
                }
            });
            put(MerchantConfig.B2C_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });

            put(MerchantConfig.C2B_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });


            put(MerchantConfig.WAP_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });

            put(MerchantConfig.MINI_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });
            put(MerchantConfig.H5_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });
            put(MerchantConfig.APP_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }

            });
            put(MerchantConfig.EXTEND2_FORMAL, new HashMap<String, String>() {
                {
                    put("0", "否");
                    put("1", "是");
                }
            });
        }
    };

    public static final String ACTIVATION_CODE_TEMPLATE_CODE = "8H3WK35FH5SN";
    public static final String ACTIVATION_CODE_TABLE_NAME = "terminal_activation_code#";
    public static final List<String> FIXED_ACTIVATION_CODE_KEY_LIST = new ArrayList<>();
    public static final List<String> ACTIVATION_CODE_CHANGE_KEY_LIST = Arrays.asList(
            TerminalActivationCode.CODE,
            TerminalActivationCode.DEFAULT_TERMINAL_NAME,
            TerminalActivationCode.STATUS,
            TerminalActivationCode.USAGE_LIMITS,
            TerminalActivationCode.REMAINING,
            TerminalActivationCode.EXPIRE_TIME,
            TerminalActivationCode.MERCHANT_ID,
            TerminalActivationCode.STORE_ID

    );

    public static final Map<String, Map<String, String>> ACTIVATION_CODE_DESC_MAP = new HashMap() {
        {
            put(TerminalActivationCode.STATUS, new HashMap<String, String>() {
                {
                    put("1", "ACTIVE");
                    put("2", "EXPIRED");
                    put("3", "USED");
                }
            });
        }
    };


    public static final String CURRENCY_FEE_RATE_TEMPLATE_CODE = "WR7SK3SLORTF";
    public static final String CURRENCY_FEE_RATE_TABLE_NAME = "currency_feerate_mapping#";
    public static final List<String> CURRENCY_FEE_RATE_CHANGE_KEY_LIST = Arrays.asList(
            CurrencyFeerate.PAYWAY,
            CurrencyFeerate.SUB_PAYWAY,
            CurrencyFeerate.CURRENCY_CODES_PAIRS,
            CurrencyFeerate.FEE_RATE,
            CurrencyFeerate.STATUS,
            CurrencyFeerate.REMARK
    );

    public static final Map<String, Map<String, String>> CURRENCY_FEE_RATE_DESC_MAP = new HashMap() {
        {
            put(CurrencyFeerate.STATUS, new HashMap<String, String>() {
                {
                    put("0", "关闭");
                    put("1", "开启");
                }
            });
            put(CurrencyFeerate.SUB_PAYWAY, new HashMap<String, String>() {
                {
                    put("1", "B扫C");
                    put("2", "C扫B");
                    put("3", "WAP支付");
                    put("4", "小程序支付");
                    put("5", "APP支付");
                    put("6", "H5支付");
                }

            });
        }
    };




    public static final String MERCHANT_CONFIG_CUSTOM_TEMPLATE_CODE = "HWK2LV4W506L";

    public static final String MERCHANT_CONFIG_CUSTOM_TABLE_NAME = "merchant_config_custom#";


    public static final List<String> MERCHANT_CONFIG_CUSTOM_CHANGE_KEY_LIST = Arrays.asList(
            MerchantConfigCustom.MERCHANT_ID,
            MerchantConfigCustom.TYPE,
            MerchantConfigCustom.STORE_ID,
            MerchantConfigCustom.B2C_VALUE,
            MerchantConfigCustom.B2C_AGENT_NAME,
            MerchantConfigCustom.C2B_VALUE,
            MerchantConfigCustom.C2B_AGENT_NAME,
            MerchantConfigCustom.WAP_VALUE,
            MerchantConfigCustom.WAP_AGENT_NAME,
            MerchantConfigCustom.MINI_VALUE,
            MerchantConfigCustom.MINI_AGENT_NAME
    );
















}








