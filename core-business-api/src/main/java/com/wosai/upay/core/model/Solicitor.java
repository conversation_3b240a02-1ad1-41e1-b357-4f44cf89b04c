package com.wosai.upay.core.model;

public class Solicitor {
    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用
    public static final int BANK_ACCOUNT_VERIFY_STATUS_NO_MATEIRAL = -1;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_NOT = 0;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_INPROGRESS = 1;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_SUCC = 2;
    public static final int BANK_ACCOUNT_VERIFY_STATUS_FAIL = 3;

    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '推广者可见的编号'
    public static final String NAME = "name"; // varchar(128) DEFAULT NULL COMMENT '推广者名称'
    public static final String CATEGORY = "category"; // int DEFAULT NULL
    public static final String STATUS = "status"; // int DEFAULT NULL
    public static final String CELLPHONE = "cellphone"; // varchar(32) DEFAULT NULL
    public static final String CONTACT_NAME = "contact_name"; // varchar(32) DEFAULT NULL COMMENT '联系人姓名'
    public static final String CONTACT_PHONE = "contact_phone"; // varchar(32) DEFAULT NULL COMMENT '联系固定电话号码'
    public static final String CONTACT_CELLPHONE = "contact_cellphone"; // varchar(32) DEFAULT NULL COMMENT '联系移动电话号码'
    public static final String CONTACT_EMAIL = "contact_email"; // varchar(64) DEFAULT NULL COMMENT '联系邮箱'
    public static final String CONTACT_ADDRESS = "contact_address"; // varchar(255) DEFAULT NULL COMMENT '联系地址'
    public static final String PROVINCE = "province"; // varchar(32) DEFAULT NULL COMMENT '省'
    public static final String CITY = "city"; // varchar(32) DEFAULT NULL COMMENT '市'
    public static final String DISTRICT = "district"; // varchar(32) DEFAULT NULL COMMENT '区'
    public static final String STREET_ADDRESS = "street_address"; // varchar(255) DEFAULT NULL COMMENT '街道门牌号'
    public static final String BANK_ACCOUNT_VERIFY_STATUS = "bank_account_verify_status"; // INT(11) NOT NULL DEFAULT -1 COMMENT '卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段 address email remark '

}
