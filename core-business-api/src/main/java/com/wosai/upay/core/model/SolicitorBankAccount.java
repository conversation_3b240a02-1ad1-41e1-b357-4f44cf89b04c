package com.wosai.upay.core.model;

public class SolicitorBankAccount {
    public static final int VERIFY_STATUS_NOT = 0;
    public static final int VERIFY_STATUS_INPROGRESS = 1;
    public static final int VERIFY_STATUS_SUCC = 2;
    public static final int VERIFY_STATUS_FAIL = 3;

    public static final String SOLICITOR_ID = "solicitor_id"; // varchar(45) DEFAULT NULL
    public static final String TYPE = "type"; // int DEFAULT NULL COMMENT '账户类型：1：个人账户；2：企业账户'
    public static final String HOLDER = "holder"; // varchar(45) DEFAULT NULL COMMENT '账户持有人名称'
    public static final String ID_TYPE = "id_type"; // int(11) NULL DEFAULT 1 COMMENT '账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；'
    public static final String IDENTITY = "identity"; // varchar(45) DEFAULT NULL COMMENT '账户持有人证件编号'
    public static final String TAX_PAYER_ID = "tax_payer_id"; // varchar(45) DEFAULT NULL COMMENT '工商税务号'
    public static final String NUMBER = "number"; // varchar(45) DEFAULT NULL COMMENT '账号'
    public static final String VERIFY_STATUS = "verify_status"; // INT(11) NOT NULL DEFAULT '0' COMMENT '卡号(账号)真实性验证状态 0未验证 1 验证中 2验证有效 3验证失败'
    public static final String BANK_NAME = "bank_name"; // varchar(45) DEFAULT NULL COMMENT '开户银行名称'
    public static final String BRANCH_NAME = "branch_name"; // varchar(45) DEFAULT NULL COMMENT '分支行名称'
    public static final String CITY = "city"; // varchar(32) DEFAULT NULL COMMENT '分支行所在城市'
    public static final String CELLPHONE = "cellphone"; // varchar(32) DEFAULT NULL COMMENT '和账号绑定的手机号'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段'

}
