package com.wosai.upay.core.model;

public class SolicitorDeveloper {

    public static final String SOLICITOR_ID = "solicitor_id"; // varchar(37) NOT NULL
    public static final String SOLICITOR_SN = "solicitor_sn"; // varchar(32) NOT NULL
    public static final String APP_KEY = "app_key"; // varchar(64) DEFAULT NULL COMMENT '开发者密钥'
    public static final String PUBLIC_KEY = "public_key"; // blob DEFAULT NULL COMMENT '开发者公钥'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL

}
