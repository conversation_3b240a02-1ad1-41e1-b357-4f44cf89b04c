package com.wosai.upay.core.model;

/**
 * Created by xuchma<PERSON> on 16/12/8.
 */
public class SqbProviderSync {
    public static final int OPT_TYPE_INSERT = 0;
    public static final int OPT_TYPE_UPDATE = 0;
    public static final int PROCESS_STATUS_WAITING = 0;
    public static final int PROCESS_STATUS_FINISH = 1;
    public static final int SYNC_STATUS_FAIL = 0;
    public static final int SYNC_STATUS_SUCCESS = 1;

    public static final String MERCHANT_SN = "merchant_sn";
    public static final String OPT_TYPE = "opt_type";
    public static final String PROVIDER = "provider";
    public static final String PROCESS_STATUS = "process_status";
    public static final String SYNC_STATUS = "sync_status";
    public static final String RET_MSG = "ret_msg";
}
