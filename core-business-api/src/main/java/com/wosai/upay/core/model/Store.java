package com.wosai.upay.core.model;

public class Store {

    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用

    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '对应1.0中的wosai_store_id'
    public static final String NAME = "name"; // varchar(128) DEFAULT NULL
    public static final String INDUSTRY = "industry"; // varchar(36) DEFAULT NULL
    public static final String STATUS = "status"; // int DEFAULT NULL
    public static final String RANK = "rank"; // int DEFAULT NULL COMMENT '信用等级'
    public static final String LONGITUDE = "longitude"; // varchar(16) DEFAULT NULL COMMENT '经度'
    public static final String LATITUDE = "latitude"; // varchar(16) DEFAULT NULL COMMENT '纬度'
    public static final String PROVINCE = "province"; // varchar(32) DEFAULT NULL
    public static final String CITY = "city"; // varchar(32) DEFAULT NULL
    public static final String DISTRICT = "district"; // varchar(32) DEFAULT NULL
    public static final String STREET_ADDRESS = "street_address"; // varchar(255) DEFAULT NULL
    public static final String STREET_ADDRESS_DESC = "street_address_desc"; // varchar(255) DEFAULT NULL COMMENT '街道地址备注说明'
    public static final String LOGO = "logo"; // varchar(255) DEFAULT NULL COMMENT '门店码LOGO'
    public static final String CONTACT_NAME = "contact_name"; // varchar(32) DEFAULT NULL COMMENT '联系人姓名'
    public static final String CONTACT_PHONE = "contact_phone"; // varchar(32) DEFAULT NULL COMMENT '联系固定电话号码'
    public static final String CONTACT_CELLPHONE = "contact_cellphone"; // varchar(32) DEFAULT NULL COMMENT '联系移动电话号码'
    public static final String CONTACT_EMAIL = "contact_email"; // varchar(64) DEFAULT NULL COMMENT '联系邮箱'
    public static final String CLIENT_SN = "client_sn"; // varchar(50) DEFAULT NULL COMMENT '商户外部门店号 '
    public static final String MERCHANT_ID = "merchant_id"; // varchar(37) DEFAULT NULL
    public static final String SOLICITOR_ID = "solicitor_id"; // varchar(37) DEFAULT NULL
    public static final String VENDOR_ID = "vendor_id"; // varchar(37) DEFAULT NULL
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段'
    public static final String DISTRICT_CODE = "district_code";//地区code
    public static final String OPERATION_CONTENTS = "operation_contents";//经营内容
    public static final String VERIFY_STATUS = "verify_status";//门店认证状态 0未知  1未认证  2认证成功  3认证失败
    public static final String POI_NAME = "poi_name"; // varchar(1024) DEFAULT NULL poi_name,地图地址名
    public static final String POI_SIMPLE_ADDRESS = "poi_simple_address"; // varchar(1024) DEFAULT NULL  poi_simple_address,地图详细地址

    public static final String TYPE = "type";  // int(11) 1固定门店  2移动门店
    public static final String OPEN_ACCOUNT_WAY = "open_account_way";  // int(11) 开户方式 1实地开户，2异地开户

}
