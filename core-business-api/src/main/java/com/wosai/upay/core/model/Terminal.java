package com.wosai.upay.core.model;


import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Terminal {

    public static final int STATUS_UNACTIVATED = 0; //未激活
    public static final int STATUS_ACTIVATED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用

    public static final int STATUS_CHANGE_TYPE_UNACTIVATE = 0; // 解绑
    public static final int STATUS_CHANGE_TYPE_ACTIVATE = 1; // 激活
    public static final int STATUS_CHANGE_TYPE_DISABLE = 2; // 禁用
    public static final int STATUS_CHANGE_TYPE_OTHER = 3; // 其他
    public static final int STATUS_CHANGE_TYPE_ENABLE = 4; // 启用


    public static final int TYPE_ANDROID = 10;
    public static final int TYPE_IOS = 11;
    public static final int TYPE_DESKTOP = 20;
    public static final int TYPE_DEDICATED_DEVICE = 30;
    public static final int TYPE_WAP = 40;
    public static final int TYPE_WAP_INVOICE = 41;
    public static final int TYPE_SERVICE = 50;

    public static final int CURRENT_CHECKIN_STATUS_CHECKIN = 1;		// 签到
    public static final int CURRENT_CHECKIN_STATUS_CHECKOUT = 2;	// 签退

    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '用户可见终端编号'
    public static final String DEVICE_FINGERPRINT = "device_fingerprint"; // varchar(128) DEFAULT NULL COMMENT '设备指纹'
    public static final String NAME = "name"; // varchar(128) DEFAULT NULL COMMENT '终端名'
    public static final String TYPE = "type"; // int unsigned NOT NULL COMMENT '类型 10: Android应用 11: iOS应用  20: Windows桌面应用 30: 专用设备 40: 门店码    50: 服务 '
    public static final String STATUS = "status"; // int DEFAULT NULL COMMENT '状态'
    public static final String LAST_SIGNON_TIME = "last_signon_time"; // bigint(20) DEFAULT NULL
    public static final String SDK_VERSION = "sdk_version"; // varchar(45) DEFAULT NULL
    public static final String OS_VERSION = "os_version"; // varchar(45) DEFAULT NULL
    public static final String CURRENT_SECRET = "current_secret"; // varchar(64) DEFAULT NULL
    public static final String LAST_SECRET = "last_secret"; // varchar(64) DEFAULT NULL
    public static final String NEXT_SECRET = "next_secret"; // varchar(64) DEFAULT NULL
    public static final String LONGITUDE = "longitude"; // varchar(16) DEFAULT NULL COMMENT '经度'
    public static final String LATITUDE = "latitude"; // varchar(16) DEFAULT NULL COMMENT '纬度'
    public static final String CLIENT_SN = "client_sn"; // varchar(50) DEFAULT NULL COMMENT '商户外部终端号 '
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段'
    public static final String TARGET = "target"; // varchar(255) DEFAULT NULL
    public static final String TARGET_TYPE = "target_type"; // bigint(20) DEFAULT NULL
    public static final String STORE_ID = "store_id"; // varchar(37) NOT NULL
    public static final String MERCHANT_ID = "merchant_id"; // varchar(37) NOT NULL
    public static final String SOLICITOR_ID = "solicitor_id"; // varchar(37) DEFAULT NULL
    public static final String VENDOR_ID = "vendor_id"; // varchar(37) NOT NULL
    public static final String VENDOR_APP_ID = "vendor_app_id"; // varchar(36) DEFAULT NULL
    public static final String VENDOR_APP_APPID = "vendor_app_appid"; // varchar(36) DEFAULT NULL
    public static final String CURRENT_CHECKIN_STATUS = "current_checkin_status"; //int unsigned 终端签到签退状态 1签到 2签退
    public static final String EXTRA_ENV_INFO = "term_env_info"; // extra.term_env_info  采集终端安装环境信息
    public static final String EXTRA_ENV_TERM_TYPE = "term_type";//扫码设备类型
    public static final String EXTRA_ENV_TERM_NO = "term_no";//扫码设备编号
    public static final String EXTRA_ENV_SOFT_NAME = "soft_name"; //收银软件名
    public static final String EXTRA_ENV_SOFT_MANU_NAME = "manufacturer";//收银软件厂商
    public static final String EXTRA_ENV_TERM_VER = "soft_version";//软件版本号
    public static final String EXTRA_ENV_UPLOAD_TIME = "upload_time";//上送日期
    public static final String CATEGORY = "category";//终端类目

    public static final int CATEGORY_POS = 101; //POS终端
    public static final int CATEGORY_SMW = 102; //扫码王
    public static final int CATEGORY_CASHIER_PLUGIN = 103; //收银插件
    public static final int CATEGORY_CAFETERIA = 104; //自助点餐码
    public static final int CATEGORY_JJZ = 105; //九九折
    public static final int CATEGORY_APP = 106; //收钱吧APP
    public static final int CATEGORY_FACE = 107; //刷脸设备
    public static final int CATEGORY_QRCODE = 108; //收款门店码
    public static final int CATEGORY_CAMPUS_TERMINAL = 109; //校园终端
    public static final int CATEGORY_CASH_REGISTER = 114; //收银机


    public static final Map<String, Integer> GET_TERMINAL_GATEGORY = new HashMap(){
        {
            put("isPOS", CATEGORY_POS);
            put("isSMW", CATEGORY_SMW);
            put("isCashierPlugin", CATEGORY_CASHIER_PLUGIN);
            put("isCafeteria", CATEGORY_CAFETERIA);
            put("isJJZ", CATEGORY_JJZ);
            put("isApp", CATEGORY_APP);
            put("isFace", CATEGORY_FACE);
            put("isQrcode", CATEGORY_QRCODE);
            put("isCampusTerminal", CATEGORY_CAMPUS_TERMINAL);
            put("isMoneyRegister", CATEGORY_CASH_REGISTER);

        }
    };

    public static final List<String>  EXTRA_ENV_LIST = Arrays.asList(EXTRA_ENV_TERM_NO, EXTRA_ENV_TERM_TYPE, EXTRA_ENV_TERM_VER, EXTRA_ENV_SOFT_NAME, EXTRA_ENV_SOFT_MANU_NAME, EXTRA_ENV_UPLOAD_TIME);

}
