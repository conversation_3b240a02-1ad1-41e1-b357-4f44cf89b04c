package com.wosai.upay.core.model;

public class TerminalActivationCode {

    public static final int STATUS_ACTIVE = 1;
    public static final int STATUS_EXPIRED = 2;
    public static final int STATUS_USED = 3;

    public static final String VENDOR_SN = "vendor_sn"; // varchar(32) NOT NULL COMMENT '服务商序列号'
    public static final String VENDOR_ID = "vendor_id"; // varchar(36) NOT NULL COMMENT '服务商UUID'
    public static final String CODE = "code"; // varchar(32) NOT NULL COMMENT '激活码不要求全局唯一，但在同一个服务商下面具有唯一性。'
    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) DEFAULT NULL COMMENT '商户ID'
    public static final String STORE_ID = "store_id"; // varchar(36) DEFAULT NULL COMMENT '门店ID'
    public static final String TERMINAL_ID = "terminal_id"; // varchar(36) DEFAULT NULL COMMENT '终端ID。如果不为空，激活已经存在的终端记录。否则根据merchant_id 和 store_id 创建终端记录并激活。'
    public static final String DEFAULT_TERMINAL_NAME = "default_terminal_name"; // VARCHAR(128) NULL COMMENT '用这个激活码激活的终端的默认名称。如果为NULL，系统会自动生成一个。'
    public static final String USAGE_LIMITS = "usage_limits"; // bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '激活码使用次数上限'
    public static final String REMAINING = "remaining"; // bigint(20) unsigned NOT NULL DEFAULT 1 COMMENT '剩余使用次数'
    public static final String EXPIRE_TIME = "expire_time"; // bigint(20) NOT NULL COMMENT '过期时间'
    public static final String STATUS = "status"; // int unsigned NOT NULL DEFAULT 1 COMMENT '状态 1: ACTIVE  2: EXPIRED 3: USED'


}
