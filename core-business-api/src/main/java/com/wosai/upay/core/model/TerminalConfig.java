package com.wosai.upay.core.model;

/**
 * <AUTHOR> Date: 2019-06-17 Time: 10:41
 */
public class TerminalConfig {

    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_OPENED = 1; //开通
    public static final int LADDER_STATUS_NOT_ALLOW = 2; //不允许开通

    public static final int PAYWAY_ALIPAY_V1 = 1;
    public static final int PAYWAY_ALIPAY_V2 = 2;
    public static final int PAYWAY_WEIXIN = 3;
    public static final int PAYWAY_BFB = 4;
    public static final int PAYWAY_JD = 5;
    public static final int PAYWAY_QQ = 6;


    public static final String TERMINAL_ID = "terminal_id";  //varchar(37) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '终端id'
    public static final String PAYWAY = "payway";  // int(11) DEFAULT NULL COMMENT '支付方式，1：支付宝1.0；2：支付宝2.0；3：微信；4：百付宝；5：京东钱包；6：QQ钱包；7：ApplePay；8：三星支付；9：小米支付；10：华为支付；11：翼支付；12：苏宁易钱包；13：银联云闪付（AndroidPay）；14银联钱包'
    public static final String B2C_FORMAL = "b2c_formal";  //tinyint(1) DEFAULT NULL COMMENT 'b扫c是否正式商户 0:否  1:是 '
    public static final String B2C_STATUS = "b2c_status";  //int(11) DEFAULT NULL COMMENT 'b扫c 是否开通关闭 0:关闭 1：开通'
    public static final String B2C_FEE_RATE = "b2c_fee_rate";  //varchar(45) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'b扫c 费率'
    public static final String B2C_AGENT_NAME = "b2c_agent_name";  //varchar(37) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '受理商'
    public static final String C2B_FORMAL = "c2b_formal"; // tinyint(1) DEFAULT NULL COMMENT 'c扫b是否正式商户 0:否  1:是 '
    public static final String C2B_FEE_RATE = "c2b_fee_rate"; // varchar(45) DEFAULT NULL COMMENT 'c扫b 费率'
    public static final String C2B_STATUS = "c2b_status"; // int DEFAULT NULL COMMENT 'c扫b是否开通关闭 0:关闭 1：开通'
    public static final String C2B_AGENT_NAME = "c2b_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String WAP_FORMAL = "wap_formal"; // tinyint(1) DEFAULT NULL COMMENT 'wap是否正式商户 0:否  1:是 '
    public static final String WAP_STATUS = "wap_status"; // int DEFAULT NULL COMMENT 'wap 是否开通关闭 0:关闭 1：开通'
    public static final String WAP_FEE_RATE = "wap_fee_rate"; // varchar(45) DEFAULT NULL COMMENT 'wap 费率'
    public static final String WAP_AGENT_NAME = "wap_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String MINI_FORMAL = "mini_formal"; // tinyint(1) DEFAULT NULL COMMENT '交易模式保留字段  是否正式商户 0:否  1:是 '
    public static final String MINI_STATUS = "mini_status"; // int DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通'
    public static final String MINI_FEE_RATE = "mini_fee_rate"; // varchar(45) DEFAULT NULL COMMENT '交易模式保留字段   费率'
    public static final String MINI_AGENT_NAME = "mini_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'public static final String B2C_AGENT_NAME  = "b2c_agent_name";  //varchar(37) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '受理商'
    public static final String APP_FORMAL = "app_formal"; // tinyint(1) DEFAULT NULL COMMENT '交易模式保留字段  是否正式商户 0:否  1:是 '
    public static final String APP_STATUS = "app_status"; // int DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通'
    public static final String APP_FEE_RATE = "app_fee_rate"; // int DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通'
    public static final String APP_AGENT_NAME = "app_agent_name"; // varchar(45) DEFAULT NULL COMMENT '交易模式保留字段   费率'
    public static final String H5_FORMAL = "h5_formal"; // tinyint(1) DEFAULT NULL COMMENT '交易模式保留字段  是否正式商户 0:否  1:是 '
    public static final String H5_STATUS = "h5_status"; // int DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通'
    public static final String H5_FEE_RATE = "h5_fee_rate"; // varchar(45) DEFAULT NULL COMMENT '交易模式保留字段   费率'
    public static final String H5_AGENT_NAME = "h5_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String EXTEND2_FORMAL = "extend2_formal"; // tinyint(1) DEFAULT NULL COMMENT '交易模式保留字段   是否正式商户 0:否  1:是 '
    public static final String EXTEND2_STATUS = "extend2_status"; // int DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通'
    public static final String EXTEND2_FEE_RATE = "extend2_fee_rate"; // varchar(45) DEFAULT NULL COMMENT '交易模式保留字段  费率'
    public static final String EXTEND2_AGENT_NAME = "extend2_agent_name"; // varchar(37)  NULL DEFAULT NULL COMMENT '受理商'
    public static final String PROVIDER = "provider"; // int(11) NULL DEFAULT NULL COMMENT '支付通道 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉'
    public static final String PARAMS = "params"; // blob DEFAULT NULL COMMENT '配置参数（JSON）'
    public static final String LADDER_STATUS = "ladder_status"; // 阶梯状态
}
