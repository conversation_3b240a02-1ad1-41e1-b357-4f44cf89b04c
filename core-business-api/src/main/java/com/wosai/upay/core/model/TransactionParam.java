package com.wosai.upay.core.model;


import com.wosai.pantheon.util.SetUtil;
import com.wosai.upay.core.meta.Payway;

import java.util.*;

public class TransactionParam {
    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_OPENED = 1; //开通
    public static final int STATUS_NOT_CONFIGURED = 2; //未配置

    public static final String PROVIDER_ENROLLED_INFO = "provider_enrolled_info";           // payway为null的param里面的key,记录的是入网到provider得到的比如商户号这些的交易参数

    public static final String SWITCHES = "switches";                                        // payway为null的param里面的key,表示各类功能开关
    public static final String PAY_STATUS = "pay_status";                                   // payway为null的param里面的switch状态开关里面的key,表示商家收款功能参数  0:关闭 1：开通
    public static final String STORE_PAY_STATUS = "store_pay_status";                       // payway为null的param里面的switch状态开关里面的key,表示门店收款功能参数  0:关闭 1：开通

    public static final String WEIXIN_CITY_GOODS_TAG = "weixin_city_goods_tag";             //微信活动城市参数
    public static final String WEXIN_CITY_SUB_MCH_ID = "weixin_city_sub_mch_id";            //微信支付按照城市地区指定子商户号

    public static final String FEE_RATE = "fee_rate";                                       // COMMON CONFIG
    public static final String FEE = "fee";                                                 // COMMON CONFIG 手续费常量，用于支付网关支付或者退款时，计算手续费
    public static final String FEE_RATE_TYPE = "fee_rate_type";                             // COMMON CONFIG 手续费类型 fixed：固定；ladder：阶梯；channel：资金渠道；channel_ladder：资金渠道阶梯
    public static final String IS_NEED_REFUND_FEE_FLAG = "is_need_refund_fee_flag";         //退款时是否需要退还手续费，默认true（退还）

    public static final String CHANNEL_FEE_RATE = "channel_fee_rate";                       // COMMON CONFIG 支付通道费率
    public static final String LIQUIDATION_NEXT_DAY = "liquidation_next_day";               // COMMON CONFIG
    public static final String LADDER_FEE_RATES = "ladder_fee_rates";
    public static final String LADDER_FEE_RATE_MIN = "min";
    public static final String LADDER_FEE_RATE_MAX = "max";
    public static final String FEE_RATE_ORIGINAL = "fee_rate_original";                     // 原始交易费率
    public static final String FEE_ORIGINAL = "fee_original";                               // 原始交易手续费
    @Deprecated
    public static final String LADDER_FEE_RATE_TAG = "ladder_fee_rate_tag";                 // 阶梯费率配置原因

    public static final String FEE_RATE_TAG = "fee_rate_tag";                               // 费率配置原因

    public static final String CURRENCY = "currency";                                       // 交易货币类型，符合ISO 4217标准的三位字母代码
    public static final String EXCHANGE_RATE = "exchange_rate";                             // 境外支付，支付通道支付接口返回的交易时人民币对交易货币的汇率
    public static final String TRANS_AMOUNT_CNY = "trans_amount_cny";                       // 境外支付，支付时支付通道返回的换算成人民币的支付金额
    public static final String REFUND_AMOUNT_CNY = "refund_amount_cny";                     // 境外支付，退款时支付通道返回的换算成人民币的支付金额
    public static final String PAYMENT_INST = "payment_inst";                               // 支付的钱包类型(ALIPAYCN ALPAYHK ...)
    
    public static final String UPAY_DEFAULT_CURRENCY_CNY = "CNY";                           // 网关默认的支付货币类型，人民币 CNY
    public static final String UPAY_CURRENCY_HKD = "HKD";                           		// 支付货币类型，香港港币 HKD
    public static final String UPAY_CURRENCY_VND = "VND";                           		// 支付货币类型，越南盾 HKD
    public static final String PASSWORD = "password";
    public static final String KEY = "key";

    public static final String SIGN_TYPE = "sign_type";
    public static final String SIGN_TYPE_RSA = "RSA";
    public static final String SIGN_TYPE_RSA2 = "RSA2";
    public static final String SIGN_TYPE_SM2 = "SM2";

    public static final String VENDOR_ID = "vendor_id";
    public static final String MERCHANT_ID = "merchant_id";
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String MERCHANT_NAME = "merchant_name";
    public static final String MERCHANT_COUNTRY = "merchant_country";
    public static final String MERCHANT_WECHAT_INDUSTRY = "merchant_wechat_industry";
    public static final String MERCHANT_ALIPAY_INDUSTRY = "merchant_alipay_industry";
    public static final String MERCHANT_DEFAULT_ALIPAY_INDUSTRY = "5311";                   //默认的商户行业类别对应支付宝境外行业类别目录ID

    public static final String MERCHANT_TRANS_UNLIMITED = "2147483647"; //限额 无限额 常量

    public static final String MERCHANT_DAILY_MAX_SUM_OF_TRANS = "merchant_daily_max_sum_of_trans";
    public static final String MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS = "merchant_daily_payway_max_sum_of_trans"; //商户支付方式当日限额

    public static final String MERCHANT_SINGLE_MAX_OF_TRAN = "merchant_single_max_of_tran";//单笔交易限额
    public static final String MERCHANT_DAILY_MAX_CREDIT_LIMIT_TRANS = "merchant_daily_max_credit_limit_trans"; //商户日信用限额
    public static final String MERCHANT_MONTHLY_MAX_CREDIT_LIMIT_TRANS = "merchant_monthly_max_credit_limit_trans"; //商户月信用限额

    public static final String PAYWAY_DAY_CREDIT_LIMITS = "payway_day_credit_limits";
    public static final String PAYWAY_MONTH_CREDIT_LIMITS = "payway_month_credit_limits";

    public static final String MERCHANT_BANKCARD_SINGLE_MAX_LIMIT = "merchant_bankcard_single_max_limit";

    public static final String MERCHANT_BANKCARD_DAY_MAX_LIMIT = "merchant_bankcard_day_max_limit";

    public static final String MERCHANT_BANKCARD_MONTH_MAX_LIMIT = "merchant_bankcard_month_max_limit";

    public static final String UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT = "union_over_seas_wallet_single_tran_limit"; //云闪付境外钱包单笔限额

    public static final String UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT = "union_over_seas_wallet_day_tran_limit"; //云闪付境外钱包单日限额


    public static final String PHONE_POS_SINGLE_TRAN_LIMIT = "phone_pos_single_tran_limit"; //手机外卡刷卡 单笔限额
    public static final String PHONE_POS_DAY_TRAN_LIMIT = "phone_pos_day_tran_limit"; //手机外卡刷卡 单日限额



    public static final String CATEGORY_MERCHANT_SINGLE_MAX_OF_TRAN = "category_merchant_single_max_of_tran";//类型终端单笔交易限额
    public static final String STORE_ID = "store_id";
    public static final String STORE_SN = "store_sn";
    public static final String STORE_NAME = "store_name";
    public static final String STORE_CITY = "store_city";
    public static final String STORE_CLIENT_SN = "store_client_sn";
    public static final String STORE_DAILY_MAX_SUM_OF_TRANS = "store_daily_max_sum_of_trans";
    public static final String TERMINAL_ID = "terminal_id";
    public static final String TERMINAL_SN = "terminal_sn";
    public static final String TERMINAL_NAME = "terminal_name";
    public static final String TERMINAL_DEVICE_FINGERPRINT = "terminal_device_fingerprint";//设备指纹
    public static final String TERMINAL_VENDOR_APP_APPID = "terminal_vendor_app_appid";
    public static final String TERMINAL_CATEGORY = "terminal_category";
    public static final String AREA_INFO = "area_info";                                // 交易发生地的地区信息

    public static final String DISTRICT_CODE = "district_code"; //地区编码
    public static final String TRADE_EXT_TERM_ID = "term_id"; //交易扩展参数中的终端id
    public static final String TRADE_EXT_TERM_INFO = "term_info";               //交易扩展参数中的终端id
    public static final String TRADE_EXT_TERM_INFO_TERM_ID = "term_id";         //交易扩展参数中的终端id 终端设备号
    public static final String TRADE_EXT_TERM_INFO_TERM_TYPE = "term_type";     //交易扩展参数中的设备类型
    public static final String TRADE_EXT_TERM_INFO_SERIAL_NUM = "serial_num";   //交易扩展参数中的终端序列号
    public static final String LONGITUDE = "longitude";
    public static final String LATITUDE = "latitude";
    public static final String CHANNEL_NAME = "channel_name";
    public static final Set<String> BYPASS_REMOVE_TRADE_EXT = SetUtil.hashSet(TRADE_EXT_TERM_INFO, TRADE_EXT_TERM_ID); // 备用通道移除参数-通道特有参数


    public static final String NOTIFY_URL = "notify_url";                                   // 商户或门店级别配置的回调URL
    public static final String IS_PROTECT_PAYER_PRIVACY = "is_protect_payer_privacy";       // 是否保护消费者的隐私  用于控制支付网关是否返回消费者信息
    public static final String MERCHANT_HISTORY_TRADE_REFUND_FLAG = "history_trade_refund_flag";     // 历史交易退款配置
    public static final int MERCHANT_HISTORY_TRADE_REFUND_CLOSE = 0;                       // 不允许退历史交易
    public static final int MERCHANT_HISTORY_TRADE_REFUND_OPEN = 1;                        // 允许退历史交易
    public static final String CLEARANCE_PROVIDER = "clearance_provider";                  // 清算通道信息
    public static final int CLEARANCE_PROVIDER_SWITCH = -1;                                // 切换中
    public static final int CLEARANCE_PROVIDER_LKL = 2;                                    // 拉卡拉清算
    public static final int CLEARANCE_PROVIDER_TL = 3;                                     // 通联清算
    public static final int CLEARANCE_PROVIDER_YS = 4;                                     // 银商清算
    public static final int CLEARANCE_PROVIDER_SYB = 5;                                    // 通联收银宝 间连直清模式
    public static final int CLEARANCE_PROVIDER_HAIKE = 7;                                  // 海科银联 间连提现模式
    public static final int CLEARANCE_PROVIDER_FUYOU = 8;                                  // 富友 间连提现模式
    public static final int CLEARANCE_PROVIDER_GUOTONG = 9;                                  // 国通 间连提现模式
    public static final String REFUND_NON_SQB_ORDER = "refund_non_sqb_order";              // 是否允许退非喔噻订单

    /* 收钱吧产品标识 */
    public static final String PRODUCT_CODE = "product_code";                        // 通用参数 产品标识
    public static final String TRADE_PARAMS_MERCHANT_NAME = "merchant_name";         // 通用参数 子商户号报备商户名称，配置在间连xxx_trade_params交易参数里面，为对应子商户号报备的商户名称

    public static final String SQB_PRODUCT_CODE_ALIPAY_ZM = "ALIPAY_ZM";             //芝麻先享 ALIPAY_ZM
    public static final String SQB_PRODUCT_CODE_PAY_SCORE = "PAY_SCORE";             //微信支付分
    public static final String SQB_PRODUCT_CODE_PALM_SERVICE = "PALM_SERVICE";       //支付宝刷掌
    public static final String SQB_PRODUCT_CODE_WEIXIN_B2B = "WEIXIN_B2B";           //微信B2b支付


    public static final String ALLOW_CREDIT_PAY = "credit_pay";                       //是否允许信用卡交易
    public static final String CREDIT_PAY_ENABLE = "1";                                          //允许信用卡交易
    public static final String CREDIT_PAY_DISABLE = "2";                                         //不允许信用卡交易

    public static final String MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER  = "duplicate_client_sn_of_order";
    public static final int MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER_ONE  = 1;  // 允许订单号重复，限制成功条数为1条
    public static final int MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER_INFINITE  = 2; // 允许订单号重复，不限制成功条数

    public static final String USE_CLIENT_STORE_SN  = "use_client_store_sn";
    public static final int USE_CLIENT_STORE_SN_YES  = 1;  // 上送自定义门店号
    public static final int USE_CLIENT_STORE_SN_NO  = 2; // 不上送自定义门店号

    /*预授权*/
    public static final String DEPOSIT = "deposit";
    public static final int DEPOSIT_CLOSE = 0;
    public static final int DEPOSIT_OPEN = 1;
    public static final String DEPOSIT_WX = "weixin";
    public static final String DEPOSIT_ALI = "alipay";
    public static final String DEPOSIT_SQB = "sqb";
    public static final String DEPOSIT_CARD_FLAG = "card_flag"; //预授权卡片展示标签

    //品牌挂靠
    public static final String IS_AFFILIATED = "is_affiliated"; //TRADE PARAMS CONFIG 是否是品牌挂靠
    public static final String ORIGINAL_PROVIDER_MCH_ID = "original_provider_mch_id"; //TRADE PARAMS CONFIG 品牌挂靠下 原渠道商户号

    //微信支付分 对接模式
    public static final String SERVICE_MODE = "mode";
    public static final int SERVICE_MODE_MERCHANT = 0;  //商户模式
    public static final int SERVICE_MODE_PARTNER = 1;   //服务商模式

    //微信支付分 确认模式
    public static final String NEED_CONFIRM = "need_confirm"; //需确认模式 - true , 免确认模式 - false, 默认false

    public static final String PROVIDER_MCH_ID = "provider_mch_id";                         // COMMON TRADE PARAMS CONFIG 渠道商户号
    public static final String LIMIT_PAYER = "limit_payer";                                 // COMMON TRADE PARAMS CONFIG 限制未成年人交易

    public static final String PROVIDER = "provider";                                       // 支付通道
    public static final String ACTIVE = "active";                                           // CONFIG 的公共字段，用于标识真正交易时用的交易参数
    public static final String WEIXIN_TRADE_PARAMS = "weixin_trade_params";                 // CONFIG
    public static final String WEIXIN_V3_OVERSEAS = "overseas";                             // 用于区别微信v3接口 是国内还是跨境交易， 跨境交易 - true , 国内交易 - false, 默认国内交易 - false
    public static final String WEIXIN_APP_ID = "weixin_appid";                              // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_SUB_APP_ID = "weixin_sub_appid";                      // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_MINI_SUB_APP_ID = "weixin_mini_sub_appid";                      // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_BRAND_MCH_ID = "weixin_brand_mch_id";                 //当服务商开通了“连锁品牌工具”后，使用品牌供应链分账时，此参数传入品牌主商户号。传入后，分账方的分账比例，校验品牌主配置的全局分账。使用普通分账，未开通“连锁品牌工具”的商户，可忽略此字段。

    public static final String WEIXIN_APP_KEY = "weixin_appkey";                            // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_APP_SECRET = "weixin_appsecret";                      // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_SUB_APP_SECRET = "weixin_sub_appsecret";              // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_MINI_SUB_APP_SECRET = "weixin_mini_sub_appsecret";    // WEIXIN_TRADE_PARAMS

    public static final String WEIXIN_MCH_ID = "weixin_mch_id";                             // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_SUB_MCH_ID = "weixin_sub_mch_id";                     // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_CERT_CONFIG_KEY = "weixin_cert_config_key";           // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_CERT_PASSWORD = "weixin_cert_password";               // WEIXIN_TRADE_PARAMS
    public static final String GOODS_TAG = "goods_tag";                                     // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_SERVICE_ID = "service_id";                            // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_SERIAL_NO = "serial_no";                              // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_APP_KEY_V3 = "weixin_appkey_v3";                      // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_PRIVATE_KEY_V3 = "private_key_v3";                    // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_PUBLIC_KEY_V3 = "public_key_v3";                      // WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_VERSION = "version";                                  // WEIXIN_TRADE_PARAMS 微信接口版本
    public static final String WEIXIN_WAP_TRADE_PARAMS = "weixin_wap_trade_params";         // CONFIG 内容同WEIXIN_TRADE_PARAMS
    public static final String WEIXIN_MINI_TRADE_PARAMS = "weixin_mini_trade_params";       // CONFIG 内容同WEIXIN_WAP_TRADE_PARAMS
    public static final String WEIXIN_H5_TRADE_PARAMS = "weixin_h5_trade_params";           // CONFIG 内容同WEIXIN_WAP_TRADE_PARAMS
    public static final String WEIXIN_APP_TRADE_PARAMS = "weixin_app_trade_params";         // CONFIG 内容同WEIXIN_WAP_TRADE_PARAMS
    public static final String WEIXIN_VERSION_V3 = "v3";                                    // WEIXIN_VERSION 配置，v3版本
    public static final String WEIXIN_PRODUCT_CODE = PRODUCT_CODE;                          // WEIXIN_TRADE_PARAMS

    public static final String ALIPAY_V1_TRADE_PARAMS = "alipay_v1_trade_params";           // CONFIG
    public static final String PARTNER = "partner";                                         // ALIPAY_V1_TRADE_PARAMS
    public static final String APP_KEY = "app_key";                                         // ALIPAY_V1_TRADE_PARAMS
    public static final String AGENT_ID = "agent_id";                                       // ALIPAY_V1_TRADE_PARAMS
    public static final String ALIPAY_V2_TRADE_PARAMS = "alipay_v2_trade_params";           // CONFIG
    public static final String ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";                     // 支付宝子商户号
    public static final String ALIPAY_PID = "pid";                                          // ALIPAY_V2_TRADE_PARAMS
    public static final String APP_ID = "app_id";                                           // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_OP_APP_ID = "op_app_id";                                     // ALIPAY_V2_TRADE_PARAMS 支付宝小程序 app_id
    public static final String PRIVATE_KEY = "private_key";                                 // ALIPAY_V2_TRADE_PARAMS
    public static final String PUBLIC_KEY = "public_key";                                   // ALIPAY_V2_TRADE_PARAMS
    public static final String PROVIDER_PUBLIC_KEY = "provider_public_key";                 // 通道侧公钥
    public static final String ALIPAY_AUTH_APP_ID = "auth_app_id";                          // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_MCH_ID = "mch_id";                                    // ALIPAY_V2_TRADE_PARAMS 值和ALIPAY_SELLER_ID值相同，建议使用ALIPAY_SELLER_ID
    public static final String ALIPAY_MCH_CATEGORY = "category";                            // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_SERVICE_ID = "service_id";                            // ALIPAY_V2_TRADE_PARAMS
    public static final String APP_AUTH_STORE_ID = "app_auth_store_id";                     // ALIPAY_V2_TRADE_PARAMS
    public static final String APP_AUTH_SHOP_ID = "app_auth_shop_id";                       // ALIPAY_V2_TRADE_PARAMS
    public static final String APP_AUTH_TOKEN = "app_auth_token";                           // ALIPAY_V2_TRADE_PARAMS
    public static final String APP_AUTH_USER_ID = "app_auth_user_id";                       // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_SELLER_ID = "seller_id";                              // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_EDU_SCHOOL_ID = "edu_school_id";                      // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_STORE_ID = "alipay_store_id";                         // ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_WAP_TRADE_PARAMS = "alipay_wap_trade_params";         //CONFIG
    public static final String ALIPAY_WAP_PID = ALIPAY_PID;                                 //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_APP_ID = APP_ID;                                  //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_APP_KEY = APP_KEY;                                //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_PARTNER = PARTNER;                                //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_PRIVATE_KEY = PRIVATE_KEY;                        //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_AUTH_APP_ID = ALIPAY_AUTH_APP_ID;                 //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_APP_AUTH_TOKEN = APP_AUTH_TOKEN;                  //ALIPAY_WAP_TRADE_PARAMS
    public static final String ALIPAY_WAP_V2_TRADE_PARAMS = "alipay_wap_v2_trade_params";   //CONFIG 内容同 ALIPAY_V2_TRADE_PARAMS
    public static final String ALIPAY_H5_V2_TRADE_PARAMS = "alipay_h5_v2_trade_params";
    public static final String ALIPAY_APP_V2_TRADE_PARAMS = "alipay_app_v2_trade_params";
    public static final String ALIPAY_MINI_V2_TRADE_PARAMS = "alipay_mini_v2_trade_params"; //支付宝小程序支付参数
    public static final String ALIPAY_ZM_SERVICE_ID = "zm_service_id";                     // 支付宝先享后付 - 芝麻信用服务ID
    public static final String ALIPAY_ZM_CATEGORY_ID = "category_id";                     // 支付宝先享后付 - 芝麻外部类目
    public static final String ALIPAY_PRODUCT_CODE = PRODUCT_CODE;                        //收钱吧侧使用的产品标识， 用来标识使用支付宝或微信的具体业务场景， 其中芝麻先享： ALIPAY_ZM

    public static final String TYPE_CUSTOM_ALIPAY_HUABEI = "ALIPAY_HUABEI"; //支付宝 花呗
    public static final String HB_FQ = "hb_fq";
    public static final String HB_FQ_NUM = "hb_fq_num"; //支付宝分期数量
    public static final String FQ_NUM = "fq_num"; //信用卡分期参数
    public static final String FQ_SELLER_PERCENT = "fq_seller_percent"; //信用卡分期商家手续费比例
    public static final String FQ_CHANNELS = "fq_channels"; //分期通道，信用卡分期
    public static final String HB_FQ_SELLER_PERCENT = "hb_fq_seller_percent"; //支付宝分期手续费比例
    public static final String SQB_HB_FQ_SELLER_SERVICE_CHARGE = "sqb_hb_fq_seller_service_charge"; //花呗商家分期手续费
    public static final String SQB_HB_FQ_BUYER_SERVICE_CHARGE = "sqb_hb_fq_buyer_service_charge"; //花呗商家分期手续费

    public static final String BAITIAO_FQ_NUM = "baitiao_fq_num";//白条分期数

    @Deprecated
    public static final String JD_TRADE_PARAMS = "jd_trade_params";                         //CONFIG
    @Deprecated
    public static final String JD_MERCHANT_NO = "merchant_no";                              //JD_TRADE_PARAMS
    @Deprecated
    public static final String JD_MERCHANT_KEY = "merchant_key";                            //JD_TRADE_PARAMS
    @Deprecated
    public static final String JD_WAP_TRADE_PARAMS = "jd_wap_trade_params";                 //CONFIG 内容同JD_TRADE_PARAMS
    @Deprecated
    public static final String JD_MERCHANT_PRIVATE_KEY = "merchant_private_key";            //JD_WAP_TRADE_PARAMS


    public static final String BAIFUBAO_TRADE_PARAMS = "baifubao_trade_params";             //CONFIG
    public static final String BAIFUBAO_SP_NO = "baifubao_sp_no";                           //BAIFUBAO_TRADE_PARAMS
    public static final String BAIFUBAO_SP_KEY = "baifubao_sp_key";                         //BAIFUBAO_TRADE_PARAMS

    public static final String QQ_TRADE_PARAMS = "qq_trade_params";                         // CONFIG
    public static final String QQ_CERT_CONFIG_KEY = "qq_cert_config_key";                   // QQ_TRADE_PARAMS
    public static final String QQ_CERT_PASSWORD = "qq_cert_password";                       // QQ_TRADE_PARAMS
    public static final String QQ_SP_ID = "qq_sp_id";                                       // QQ_TRADE_PARAMS
    public static final String QQ_SP_SECRET = "qq_sp_secret";                               // QQ_TRADE_PARAMS
    public static final String QQ_MERCHANT_ID = "qq_merchant_id";                           // QQ_TRADE_PARAMS

    public static final String NFC_TRADE_PARAMS = "nfc_trade_params";                       //CONFIG

    public static final String  SWIFTPASS_MCH_ID_SUFFIX = "mch_id";                          // 威富通交易参数商户号key的后缀
    public static final String  SWIFTPASS_GROUP_NO_SUFFIX = "group_no";                      // 威富通交易参数大商户号key的后缀
    public static final String  SWIFTPASS_AGENT_NO_SUFFIX = "agent_no";                      // 威富通交易参数渠道号key的后缀

    public static final String CIBBANK_TRADE_PARAMS = "cibbank_trade_params";               //CONFIG
    public static final String CIBBANK_SIGN_AGENT_NO = "cibbank_sign_agent_no";             // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_SIGN_AGENT_KEY = "cibbank_sign_agent_key";           // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_GROUP_NO = "cibbank_group_no";                       // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_GROUP_KEY = "cibbank_group_key";                     // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_MCH_ID = "cibbank_mch_id";                           // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_MCH_KEY = "cibbank_mch_key";                         // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_SYS_PROVIDER_ID = "cibbank_sys_provider_id";         // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;               // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;       // CIBBANK_TRADE_PARAMS
    public static final String CIBBANK_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;
    public static final String CIBBANK_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET;

    public static final String CITICBANK_TRADE_PARAMS = "citicbank_trade_params";           //CONFIG
    public static final String CITICBANK_SIGN_AGENT_NO = "citicbank_sign_agent_no";         // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_SIGN_AGENT_KEY = "citicbank_sign_agent_key";       // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_GROUP_NO = "citicbank_group_no";                   // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_GROUP_KEY = "citicbank_group_key";                 // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_MCH_ID = "citicbank_mch_id";                       // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_MCH_PAY_ID = "citicbank_mch_pay_id";               // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_MCH_KEY = "citicbank_mch_key";                     // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_SYS_PROVIDER_ID = "citicbank_sys_provider_id";     // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;             // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;     // CITICBANK_TRADE_PARAMS
    public static final String CITICBANK_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;
    public static final String CITICBANK_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET;

    /** 对接威富通的通用配置 **/
    public static final String SWIFTPASS_SIGN_AGENT_NO = "sp_sign_agent_no";                // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_SIGN_AGENT_KEY = "sp_sign_agent_key";              // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_GROUP_NO = "sp_group_no";                          // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_GROUP_KEY = "sp_group_key";                        // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_MCH_ID = "sp_mch_id";                              // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_MCH_KEY = "sp_mch_key";                            // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_ALIPAY_SYS_PROVIDER_ID = "alipay_sys_provider_id"; // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;             // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;     // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;             // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET;     // SWIFTPASS_TRADE_PARAMS COMMON CONFIG
    public static final String SWIFTPASS_PRIVATE_KEY = PRIVATE_KEY;                         // SWIFTPASS_TRADE_PARAMS COMMON CONFIG rsa商户私钥
    public static final String SWIFTPASS_PUBLIC_KEY = PUBLIC_KEY;                           // SWIFTPASS_TRADE_PARAMS COMMON CONFIG rsa威富通公钥

    public static final String LAKALA_TRADE_PARAMS = "lakala_trade_params";                 //CONFIG
    public static final String LAKALA_COMPORT_CODE = "lakala_comport_code";                 // LAKALA_TRADE_PARAMS 拉卡拉提供的机构代码。
    public static final String LAKALA_SECRET = "lakala_secret";                             // LAKALA_TRADE_PARAMS 拉卡拉分配的密钥
    public static final String LAKALA_WEXIN_SUB_APPID = "lakala_weixin_sub_appid";          // LAKALA_TRADE_PARAMS
    public static final String LAKALA_MERC_ID = "lakala_merc_id";                           // LAKALA_TRADE_PARAMS
    public static final String LAKALA_TERM_ID = "lakala_term_id";                           // LAKALA_TRADE_PARAMS
    public static final String LAKALA_APP_ID = "appid";                                     // LAKALA_TRADE_PARAMS
    public static final String LAKALA_PRIVATE_KEY = "private_key";                          // LAKALA_TRADE_PARAMS
    public static final String LAKALA_SERIAL_NO = "serial_no";                              // LAKALA_TRADE_PARAMS
    public static final String LAKALA_SYS_PROVIDER_ID = "sys_pid";                          // LAKALA_TRADE_PARAMS alipay sys_service_provider_id

    public static final String TL_TRADE_PARAMS = "tl_trade_params";                         //CONFIG
    public static final String TL_MCH_ID = "tl_mch_id";                                     //CONFIG 通联通道下商户号

    public static final String HK_TRADE_PARAMS = "hk_trade_params";                       //CONFIG
    public static final String HK_MCH_ID = "hk_mch_id";                                  //CONFIG 海科通道下商户号

    public static final String GUOTONG_TRADE_PARAMS = "guotong_trade_params";                       //CONFIG
    public static final String GUOTONG_MCH_ID = "guotong_mch_id";                                  //CONFIG 国通通道下商户号

    public static final String YS_TRADE_PARAMS = "ys_trade_params";                       //CONFIG
    public static final String YS_MCH_CODE = "mch_code";                                  //CONFIG 银商通道下商户号(bsc)
    public static final String YS_CSB_MCH_CODE = "csb_mch_code";                          //CONFIG 银商通道下商户号(csb)

    public static final String LAKALA_WANMA_TRADE_PARAMS = "lakala_wanma_trade_params";     //CONFIG
    public static final String LAKALA_WANMA_MCH_ID = "wanma_mch_id";     					//LAKALA_TRADE_PARAMS 万码商户号
    public static final String LAKALA_WNAMA_RECE_ORG_NO = "rece_org_no";                    // 万码受理商号
    public static final String LAKALA_WNAMA_SYS_PROVIDER_ID = "sys_provider_id";            // 支付宝系统商编号

    //对接和包支付通用配置
    public static final String CMCC_TRADE_PARAMS = "cmcc_trade_params";                         //CONFIG
    public static final String CMCC_MERCHANT_ID = "cmcc_merchant_id";                              //CMCC_TRADE_PARAMS
    public static final String CMCC_MERCHANT_KEY = "cmcc_merchant_key";                            //CMCC_TRADE_PARAMS

    //网联交易参数配置
    public static final String NUCC_TRADE_PARAMS = "nucc_trade_params";                     //config
    public static final String NUCC_IDC_FLAG = "idc_flag";                                  //NUCC_TRADE_PARAMS 网 联 IDC 标识
    public static final String NUCC_PRIVATE_KEY = "nucc_private_key";                       //NUCC_TRADE_PARAMS 企业证书私钥
    public static final String NUCC_CERT = "nucc_cert";                                     //NUCC_TRADE_PARAMS 服务器证书
    public static final String NUCC_CHANNEL_ID = "channel_id";                              //NUCC_TRADE_PARAMS weixin
    public static final String NUCC_WEIXIN_APP_ID = WEIXIN_APP_ID;                          //NUCC_TRADE_PARAMS weixin
    public static final String NUCC_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                  //NUCC_TRADE_PARAMS weixin
    public static final String NUCC_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;          //NUCC_TRADE_PARAMS weixin
    public static final String NUCC_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                          //NUCC_TRADE_PARAMS weixin
    public static final String NUCC_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                  //NUCC_TRADE_PARAMS weixin
    public static final String NUCC_ALIPAY_PID = ALIPAY_PID;                                //NUCC_TRADE_PARAMS alipay
    public static final String NUCC_SYS_PROVIDER_ID = "sys_pid";                            //NUCC_TRADE_PARAMS alipay sys_service_provider_id
    public static final String NUCC_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";                //NUCC_TRADE_PARAMS alipay
    public static final String NUCC_SP_MCH_ID = "sp_mch_id";                          //NUCC_TRADE_PARAMS 小支付源
    public static final String NUCC_PAY_BESTPAY_SIGN_SN = "bestpay_sign_sn";                //NUCC_TRADE_PARAMS 小支付源
    public static final String NUCC_PARENT_MERCHANT_ID = "parent_merchant_id";              //NUCC_TRADE_PARAMS 小支付源


    //银联交易参数配置
    public static final String UNION_PAY_TRADE_PARAMS = "up_trade_params";                     //config
    public static final String UNION_PAY_PRIVATE_KEY = "up_private_key";                       //UNION_PAY_TRADE_PARAMS 私钥
    public static final String UNION_PAY_CHANNEL_ID = "channel_id";                            //UNION_PAY_TRADE_PARAMS weixin
    public static final String UNION_PAY_WEIXIN_APP_ID = WEIXIN_APP_ID;                        //UNION_PAY_TRADE_PARAMS weixin
    public static final String UNION_PAY_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                //UNION_PAY_TRADE_PARAMS weixin
    public static final String UNION_PAY_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;        //UNION_PAY_TRADE_PARAMS weixin
    public static final String UNION_PAY_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                        //UNION_PAY_TRADE_PARAMS weixin
    public static final String UNION_PAY_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                //UNION_PAY_TRADE_PARAMS weixin
    public static final String UNION_PAY_ALIPAY_APP_ID = APP_ID;                               //UNION_PAY_TRADE_PARAMS alipay
    public static final String UNION_PAY_SYS_PROVIDER_ID = "sys_pid";                          //UNION_PAY_TRADE_PARAMS alipay sys_service_provider_id
    public static final String UNION_PAY_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";              //UNION_PAY_TRADE_PARAMS alipay

    //拉卡拉银联交易参数配置
    public static final String LAKALA_UNION_PAY_TRADE_PARAMS = "lkl_up_trade_params";                 //config
    public static final String LAKALA_UNION_PAY_PRIVATE_KEY = "up_private_key";                       //LAKALA_UNION_PAY_TRADE_PARAMS 私钥
    public static final String LAKALA_UNION_PAY_CERT_ID = "cert_id";                                  //LAKALA_UNION_PAY_TRADE_PARAMS 证书编号
    public static final String LAKALA_UNION_PAY_CHANNEL_ID = "channel_id";                            //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                  //LAKALA_UNION_PAY_TRADE_PARAMS 渠道商户号
    public static final String LAKALA_UNION_PAY_WEIXIN_APP_ID = WEIXIN_APP_ID;                        //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_WEIXIN_APP_KEY = WEIXIN_APP_KEY;                      //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;        //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                        //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                //LAKALA_UNION_PAY_TRADE_PARAMS weixin
    public static final String LAKALA_UNION_PAY_ALIPAY_APP_ID = APP_ID;                               //LAKALA_UNION_PAY_TRADE_PARAMS alipay
    public static final String LAKALA_UNION_PAY_SYS_PROVIDER_ID = "sys_pid";                          //LAKALA_UNION_PAY_TRADE_PARAMS alipay sys_service_provider_id
    public static final String LAKALA_UNION_PAY_ALIPAY_MCH_CATEGORY = ALIPAY_MCH_CATEGORY;            //LAKALA_UNION_PAY_TRADE_PARAMS alipay
    public static final String LAKALA_UNION_PAY_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";              //LAKALA_UNION_PAY_TRADE_PARAMS alipay
    public static final String LAKALA_UNION_PAY_ALIPAY_STORE_ID = ALIPAY_STORE_ID;                    //LAKALA_UNION_PAY_TRADE_PARAMS alipay

    // 翼支付直连参数
    public static final String BESTPAY_TRADE_PARAMS = "bestpay_trade_params";               //CONFIG
    public static final String BESTPAY_MERCHANT_ID = "merchant_id";                         //BESTPAY_TRADE_PARAMS 翼支付商户号
    public static final String BESTPAY_MERCHANT_KEY = "merchant_key";                       //BESTPAY_TRADE_PARAMS 翼支付商户交易密钥
    public static final String BESTPAY_MERCHANT_PWD = "merchant_pwd";                       //BESTPAY_TRADE_PARAMS 翼支付商户交易密码
    public static final String BESTPAY_SUB_MERCHANT_ID = "sub_merchant_id";                 //BESTPAY_TRADE_PARAMS 翼支付子商户号
    public static final String BESTPAY_STORE_ID = "store_id";                               //BESTPAY_TRADE_PARAMS 翼支付门店id
    public static final String BESTPAY_LEDGERDETAIL = "ledger_detail";                       //BESTPAY_TRADE_PARAMS 翼支付，是否支持分账
    
    //银联交易参数配置
    public static final String UNION_PAY_DIRECT_TRADE_PARAMS = "up_direct_trade_params";              //config
    public static final String UNION_PAY_DIRECT_PRIVATE_KEY = "up_private_key";                       //UNION_PAY_DIRECT_TRADE_PARAMS 私钥
    public static final String UNION_PAY_DIRECT_CHANNEL_ID = "channel_id";                            //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_WEIXIN_APP_ID = WEIXIN_APP_ID;                        //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;        //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                        //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_ALIPAY_APP_ID = APP_ID;                               //UNION_PAY_DIRECT_TRADE_PARAMS alipay
    public static final String UNION_PAY_DIRECT_SYS_PROVIDER_ID = "sys_pid";                          //UNION_PAY_DIRECT_TRADE_PARAMS alipay sys_service_provider_id
    public static final String UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";              //UNION_PAY_DIRECT_TRADE_PARAMS alipay
    public static final String UNION_PAY_DIRECT_ALIPAY_STORE_ID = ALIPAY_STORE_ID;                    //UNION_PAY_DIRECT_TRADE_PARAMS alipay
    public static final String UNION_PAY_DIRECT_PROVIDER_MCH_ID = "provider_mch_id";                  //UNION_PAY_DIRECT_TRADE_PARAMS 渠道商户号
    public static final String UNION_PAY_DIRECT_WEIXIN_APP_KEY = WEIXIN_APP_KEY;                      //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String UNION_PAY_DIRECT_CERT_ID = "cert_id";                                  //UNION_PAY_DIRECT_TRADE_PARAMS 签名证书对应的序号

    // 支付宝国际版参数
    public static final String ALIPAY_INTL_TRADE_PARAMS = "alipay_intl_trade_params";           //CONFIG
    public static final String ALIPAY_INTL_CLIENT_ID = "client_id";                             // ALIPAY_INTL_TRADE_PARAMS
    public static final String ALIPAY_INTL_PRIVATE_KEY = "private_key";                           // ALIPAY_INTL_TRADE_PARAMS
    public static final String ALIPAY_INTL_MERCHANT_ID = "merchant_id";                           // ALIPAY_INTL_TRADE_PARAMS
    public static final String ALIPAY_INTL_MERCHANT_ALIPAY_INDUSTRY = "merchant_alipay_industry"; // ALIPAY_INTL_TRADE_PARAMS

    //礼品卡支付参数
    public static final String GIFT_CARD_TRADE_PARAMS = "gift_card_trade_params";
    public static final String GIFT_CARD_APP_ID = APP_ID;
    public static final String GIFT_CARD_SIGN_TYPE = SIGN_TYPE;

    //花呗参数
    public static final String ALIPAY_HUABEI_STATUS = "alipay_huabei_status";//花呗状态
    public static final String ALIPAY_HUABEI_LIMIT = "alipay_huabei_limit";//花呗参数
    public static final String ALIPAY_HUABEI_PARAMS = "alipay_huabei_params";//花呗参数
    public static final String ALIPAY_CREDIT_PARAMS = "alipay_credit_params";//信用卡参数(花呗分期支持信用卡分期)

    public static final String JD_BAITIAO_STATUS = "jd_baitiao_status"; //京东白条分期状态
    public static final String JD_BAITIAO_LIMIT = "jd_baitiao_limit"; //京东白条最小分期金额
    public static final String JD_BAITIAO_PARAMS = "jd_baitiao_params"; //京东白条参数

    public static final String WEIXIN_INSTALLMENT_STATUS = "weixin_installment_status";//微信分期
    
    // 银联开放平台
    public static final String UNION_PAY_OPEN_TRADE_PARAMS = "upo_trade_params";                       //CONFIG
    public static final String UNION_PAY_OPEN_SERVICE_PROVIDER_ID = "upo_pid";                         //UNION_OPEN_TRADE_PARAMS 云闪付服务商标识(serProvId)
    public static final String UNION_PAY_OPEN_MCH_ID = "upo_mch_id";                                   //UNION_OPEN_TRADE_PARAMS 云闪付商户号(merId)
    public static final String UNION_PAY_OPEN_TERM_ID = LAKALA_TERM_ID;                                //UNION_OPEN_TRADE_PARAMS 拉卡拉终端号(termId)
    public static final String UNION_PAY_OPEN_PRIVATE_KEY = PRIVATE_KEY;                               //UNION_OPEN_TRADE_PARAMS 私钥
    public static final String UNION_PAY_OPEN_PUBLIC_KEY = PUBLIC_KEY;                                 //UNION_OPEN_TRADE_PARAMS 验签公钥
    public static final String UNION_PAY_OPEN_PNR_INS_ID_CD = "pnr_ins_id_cd";                           //UNION_PAY_OPEN_PNR_INS_ID_CD服务商机构标识

    //是否向支付源上送store_id，true:上送， false:不上送
    public static final String IS_SENT_STORE_ID = "is_sent_store_id";

    //银联商务通道
    public static final String CHINAUMS_TRADE_PARAMS = "chinaums_trade_params";                         //CONFIG
    public static final String CHINAUMS_MCH_CODE = "mch_code";                                          //CHINAUMS_TRADE_PARAMS 商户号(mchCode)
    public static final String CHINAUMS_TERM_CODE = "term_code";                                        //CHINAUMS_TRADE_PARAMS 终端号(termCode)
    public static final String CHINAUMS_CSB_MCH_CODE = "csb_mch_code";                                  //CHINAUMS_TRADE_PARAMS 商户号(csb)
    public static final String CHINAUMS_CSB_TERM_CODE = "csb_term_code";                                //CHINAUMS_TRADE_PARAMS 终端号(csb)
    public static final String CHINAUMS_APP_ID = "app_id";                                              //CHINAUMS_TRADE_PARAMS 应用id(appId)
    public static final String CHINAUMS_APP_KEY = "app_key";                                            //CHINAUMS_TRADE_PARAMS 应用密钥(appKey)
    public static final String CHINAUMS_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                          //CHINAUMS_WEIXIN_SUB_APP_ID weixin
    public static final String CHINAUMS_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;                  //CHINAUMS_WEIXIN_SUB_APP_SECRET weixin
    public static final String CHINAUMS_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;                //CHINAUMS_WEIXIN_MINI_SUB_APP_ID weixin
    public static final String CHINAUMS_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET;        //CHINAUMS_WEIXIN_MINI_SUB_APP_SECRET weixin
    public static final String CHINAUMS_SOURCE = "source";                                              //CHINAUMS_SOURCE 来源编号，需要作为订单号前缀上送
    public static final String CHINAUMS_SQB_VERSION = "sqb_version";                                    //CHINAUMS_TRADE_PARAMS 内部通道版本号 用于交易选择走哪个银商的通道（老的银商通道，银行联合收单通道，云闪付免密支付通道等）
    //银联网银支付
    public static final String UNION_PAY_ONLINE_TRADE_PARAMS = "uonline_trade_params";                  //CONFIG
    public static final String UNION_PAY_ONLINE_PRIVATE_KEY = PRIVATE_KEY;                              //UNION_PAY_ONLINE_TRADE_PARAMS 签名证书对应的私钥
    public static final String UNION_PAY_ONLINE_CERT_ID = "cert_id";                                    //UNION_PAY_ONLINE_TRADE_PARAMS 签名证书对应的序号
    public static final String UNION_PAY_ONLINE_MCH_ID = "mch_id";                                      //UNION_PAY_ONLINE_TRADE_PARAMS 商户号
    public static final String UNION_PAY_ONLINE_BIZ_TYPE = "biz_type";                                  //UNION_PAY_ONLINE_TRADE_PARAMS 产品类型

    //索迪斯
    public static final String SODEXO_TRADE_PARAMS = "sodexo_trade_params";                             //CONFIG
    public static final String SODEXO_CLIENT_ID = "client_id";                                                 //SODEXO_TRADE_PARAMS 客户端id
    public static final String SODEXO_CLIENT_SECRET = "client_secret";                                         //SODEXO_TRADE_PARAMS 客户端密钥
    public static final String SODEXO_MID = "mid";                                                             //SODEXO_TRADE_PARAMS 商户mid
    public static final String SODEXO_TID = "tid";                                                             //SODEXO_TRADE_PARAMS 客户端tid

    //通联银联交易参数配置
    public static final String UNION_PAY_TL_TRADE_PARAMS = "up_tl_trade_params";                   //config
    public static final String UNION_PAY_TL_ORGID = "org_id";                                      //UNION_PAY_TL_ORGID 通联分配的第四方机构号
    public static final String UNION_PAY_TL_PRIVATE_KEY = "up_private_key";                       //UNION_PAY_TL_PRIVATE_KEY 私钥
    public static final String UNION_PAY_TL_CHANNEL_ID = "channel_id";                            //UNION_PAY_TL_TRADE_PARAMS weixin
    public static final String UNION_PAY_TL_WEIXIN_APP_ID = WEIXIN_APP_ID;                        //UNION_PAY_TL_TRADE_PARAMS weixin
    public static final String UNION_PAY_TL_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                //UNION_PAY_TL_TRADE_PARAMS weixin
    public static final String UNION_PAY_TL_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;        //UNION_PAY_TL_TRADE_PARAMS weixin
    public static final String UNION_PAY_TL_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                        //UNION_PAY_TL_TRADE_PARAMS weixin
    public static final String UNION_PAY_TL_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                //UNION_PAY_TL_TRADE_PARAMS weixin
    public static final String UNION_PAY_TL_ALIPAY_APP_ID = APP_ID;                               //UNION_PAY_TL_TRADE_PARAMS alipay
    public static final String UNION_PAY_TL_SYS_PROVIDER_ID = "sys_pid";                          //UNION_PAY_TL_TRADE_PARAMS alipay sys_service_provider_id
    public static final String UNION_PAY_TL_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";              //UNION_PAY_TL_TRADE_PARAMS alipay
    public static final String UNION_PAY_TL_ALIPAY_STORE_ID = ALIPAY_STORE_ID;                    //UNION_PAY_TL_TRADE_PARAMS alipay
    public static final String UNION_PAY_TL_PROVIDER_MCH_ID = "provider_mch_id";                  //UNION_PAY_TL_TRADE_PARAMS 渠道商户号
    public static final String UNION_PAY_TL_UNION_CERT_ID = "cert_id";                                    //UNION_PAY_TL_UNION_CERT_ID 签名证书对应的序号
    public static final String UNION_PAY_TL_UNION_MCH_ID = "mch_id";                                      //UNION_PAY_TL_UNION_MCH_ID 商户号
    public static final String UNION_PAY_TL_UNION_MCH_CAT_CODE = "mer_cat_code";                            //UNION_PAY_TL_UNION_MCH_CAT_CODE商户类别
    public static final String UNION_PAY_TL_UNION_MCH_NAME = "mer_name";                                   //UNION_PAY_TL_UNION_MCH_NAME商户名称
    public static final String UNION_PAY_TL_UNION_TERM_ID = "term_id";                                    //UNION_PAY_TL_UNION_TERM_ID商户终端号
    public static final String UNION_PAY_TL_UNION_PNR_INS_ID_CD = "pnr_ins_id_cd";                        //UNION_PAY_TL_UNION_PNR_INS_ID_CD服务商机构标识

    //澳门极易付通道交易参数
    public static final String UEPAY_TRADE_PARAMS = "uepay_trade_params";
    public static final String UEPAY_MERCHANT_NO = "merchant_no";  //极易付商户号
    public static final String UEPAY_STORE_CODE = "store_code";  //极易付门店号
    public static final String UEPAY_TERMINAL_CODE = "terminal_code";  //极易付终端号
    public static final String UEPAY_SECRET_KEY = "secret_key";  //极易付签名密钥

    //招行通道交易参数
    public static final String CMB_TRADE_PARAMS = "cmb_trade_params";
    public static final String CMB_APP_ID = "app_id";
    public static final String CMB_SECRET = "secret";
    public static final String SQB_PRIVATE_KEY = "sqb_private_key";
    public static final String CMB_PUBLIC_KEY = "cmb_public_key";
    public static final String MER_ID = "mer_id";
    public static final String USER_ID = "user_id";
    public static final String CMB_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;
    public static final String CMB_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;


    //招行生活APP通道交易参数
    public static final String CMB_APP_TRADE_PARAMS = "cmb_app_trade_params";
    public static final String CMB_APP_MER_PRIVATE_KEY = "mer_private_key_id";     //商户私钥字段
    public static final String CMB_APP_CMB_PUBLIC_KEY = "cmb_public_key_id";       //招行公钥字段
    public static final String CMB_APP_MER_NO = "mer_no";                       //商户号
    public static final String CMB_APP_STR_NO = "str_no";                       //门店号
    public static final String CMB_APP_CERT = "cert_id";                           //证书
    public static final String CMB_APP_MID = "mid";                             //商户id
    public static final String CMB_APP_AID = "aid";                             //应用id

    //邮储通道交易参数
    public static final String PSBCBANK_TRADE_PARAMS = "psbcbank_trade_params";
    public static final String PSBCBANK_REQ_SYS_ID = "req_sys_id";  //请求方系统代码，由统一收单系统分配
    public static final String PSBCBANK_PLATFORM_ID = "platform_id";  //外包机构号，由统一收单系统分配
    public static final String PSBCBANK_CHANNEL_ID = "channel_id";  //渠道ID
    public static final String PSBCBANK_PROVIDER_CHANNEL_ID = "provider_channel_id"; //邮储渠道ID
    public static final String PSBCBANK_WEIXIN_APP_ID = WEIXIN_APP_ID;                        //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String PSBCBANK_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String PSBCBANK_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                        //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String PSBCBANK_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                //UNION_PAY_DIRECT_TRADE_PARAMS weixin
    public static final String PSBCBANK_ALIPAY_APP_ID = APP_ID;                               //UNION_PAY_DIRECT_TRADE_PARAMS alipay
    public static final String PSBCBANK_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";              //UNION_PAY_DIRECT_TRADE_PARAMS alipay
    public static final String PSBCBANK_PROVIDER_MCH_ID = "provider_mch_id";                  //UNION_PAY_DIRECT_TRADE_PARAMS 渠道商户号
    public static final String PSBCBANK_CERT_NUM = "cert_num";  //CFCA证书序列号（厂商公钥证书的序列号）
    public static final String PSBCBANK_SECRET_KEY = "secret_key";  //SM2签名密钥
    public static final String PSBCBANK_SM2_PASS = "sm2_pass";  //SM2密钥密码
    public static final String PSBC_ALIPAY_ORG_PROVIDER_ID = "org_pid";//收单机构(例如银行）在支付宝的标识
    public static final String PSBC_UNION_PAY_PNR_INS_ID_CD = "pnr_ins_id_cd";   // 云闪付服务商机构标识

    public static final String PREPAID_CARD_TRADE_PARAMS = "prepaid_card_trade_params";//储值卡交易参数

    //广发通道交易参数
    public static final String CGBBANK_TRADE_PARAMS = "cgbbank_trade_params";
    public static final String CGBBANK_INST_ID = "inst_id";                 //广发金融开放平台给合作伙伴,用于识别合作伙伴,给每个合作伙伴主体只分配一个商户号
    public static final String CGBBANK_APP_ID = "app_id";                   //用于识别合作伙伴对接的应用系统或程序,一个合作伙伴主体下可以有多个应用ID
    public static final String CGBBANK_PRIVATE_KEY = "private_key";         //签名密钥
    public static final String CGBBANK_PUBLIC_KEY = "public_key";           //签名公钥
    public static final String CGBBANK_CGB_PUBLIC_KEY = "cgb_public_key";   //广发加密公钥
    public static final String CGBBANK_SUB_CHANNEL = "sub_channel";         //二级渠道
    public static final String CGBBANK_PROVIDER_MCH_ID = "provider_mch_id"; //渠道商户号
    public static final String CGBBANK_PRODUCT_CODE = "product_code";       //渠道商户号
    public static final String CGBBANK_CHANNEL_ID = "channel_id";
    public static final String CGBBANK_WEIXIN_APP_ID = WEIXIN_APP_ID;                        //CGBBANK_TRADE_PARAMS weixin
    public static final String CGBBANK_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                //CGBBANK_TRADE_PARAMS weixin
    public static final String CGBBANK_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;        //CGBBANK_TRADE_PARAMS weixin
    public static final String CGBBANK_ALIPAY_APP_ID = APP_ID;                               //CGBBANK_TRADE_PARAMS alipay
    public static final String CGBBANK_SYS_PROVIDER_ID = "sys_pid";                          //CGBBANK_TRADE_PARAMS alipay sys_service_provider_id
    public static final String CGBBANK_TERMINAL_ID = "terminal_id";             //被扫终端id
    public static final String CGBBANK_PRE_TERMINAL_ID = "pre_terminal_id";     //主扫终端id
    public static final String CGBBANK_LONGITUDE = "longitude";     //商户经纬度
    public static final String CGBBANK_LATITUDE = "latitude";
    public static final String CGBBANK_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;
    public static final String CGBBANK_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;

    //富士康交易参数
    public static final String FOXCONN_TRADE_PARAMS = "foxconn_trade_params";
    public static final String FOXCONN_ACCESS_ID = "access_id";  // 平台分配给接入方编号(机构/商户/appCode)
    public static final String FOXCONN_EQUIPMENT_SN = "equipment_sn";  //机具序列号
    public static final String FOXCONN_USER_ID = "user_id";  // 支付平台商户/用户唯一编号
    public static final String FOXCONN_PRIVATE_KEY = PRIVATE_KEY;
    public static final String FOXCONN_PUBLIC_KEY = PUBLIC_KEY;

    //华夏银行交易参数
    public static final String HXBANK_TRADE_PARAMS = "hxbank_trade_params";
    public static final String HXBANK_DEVELOP_APP_ID = "develop_app_id";  // 开发者ID
    public static final String HXBANK_PROVIDER_SERVICE_ID = "provider_service_id";  // 服务商商户编号
    public static final String HXBANK_PROVIDER_MCH_ID = "provider_mch_id";  // 华夏提供给合作方的商户唯一标识
    public static final String HXBANK_PROVIDER_TERM_ID = "provider_term_id";  // 华夏端商户的虚拟终端号
    public static final String HXBANK_WEIXIN_APP_ID = WEIXIN_APP_ID;
    public static final String HXBANK_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;
    public static final String HXBANK_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;
    public static final String HXBANK_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;
    public static final String HXBANK_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET;
    public static final String HXBANK_WEIXIN_MCH_ID = WEIXIN_MCH_ID;
    public static final String HXBANK_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;
    public static final String SQB_HXBANK_PRIVATE_KEY = "sqb_private_key";
    public static final String HXBANK_PUBLIC_KEY = "hxbank_public_key";
    public static final String HXBANK_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;
    public static final String HXBANK_DCEP_BUS_CTGTY = "bus_ctgty";  //业务种类编码
    public static final String HXBANK_VERSION = "version";  //华夏接口版本号

    //建行通道交易参数
    public static final String CCB_TRADE_PARAMS = "ccb_trade_params";
    public static final String CCB_INST_NO = "inst_no"; //机构号
    public static final String CCB_MERCHANT_ID = "merchant_id"; //建行商户代码
    public static final String CCB_TERMINAL_ID = "terminal_id"; //建行终端id
    public static final String CCB_TERMINAL_NO = "terminal_no"; //建行终端号
    public static final String CCB_POS_ID = "pos_id"; //商户柜台代码
    public static final String CCB_BRANCH_ID = "branch_id"; //分行代码
    public static final String CCB_PUBLIC_KEY = "public_key"; //建行公钥
    public static final String CCB_SECRET_KEY = "secret_key"; //建行密钥
    public static final String CCB_WX_SUB_APPID = "weixin_sub_appid"; //微信sub_appid
    public static final String CCB_WX_MINI_SUB_APPID = "weixin_mini_sub_appid"; //微信sub_appid
    public static final String CCB_WX_MINI_SUB_APPSECRET = "weixin_sub_appsecret"; //微信sub_appsecret
    public static final String CCB_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;
    public static final String CCB_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;

    //grabpay通道交易参数
    public static final String GRABPAY_TRADE_PARAMS = "grabpay_trade_params";
    public static final String GRABPAY_PARTNER_ID = "partner_id"; //grabpay 提供给合作方的唯一标识
    public static final String GRABPAY_PRIVATE_KEY = PRIVATE_KEY; //建行公钥
    public static final String GRABPAY_MERCHANT_ID = "merchant_id"; //grabpay 提供给商户的唯一标识 即grabID
    public static final String GRABPAY_TERMINAL_ID = "terminal_id"; //grabpay 提供给终端的唯一标识

    //工行通道交易参数
    public static final String ICBC_TRADE_PARAMS = "icbc_trade_params";
    public static final String ICBC_APP_ID = "app_id"; //APP的编号,应用在API开放平台注册时生成
    public static final String ICBC_MER_ID = "mer_id"; //商户、部门编号
    public static final String ICBC_MER_PRTCL_NO = "mer_prtcl_no"; //收单产品协议编号
    public static final String ICBC_PRIVATE_KEY = PRIVATE_KEY; //工行密钥
    public static final String ICBC_PUBLIC_KEY = PUBLIC_KEY; //工行公钥
    public static final String ICBC_WX_SUB_APPID = WEIXIN_SUB_APP_ID; //微信sub_appid
    public static final String ICBC_WX_MINI_SUB_APPID = WEIXIN_MINI_SUB_APP_ID; //微信sub_appid
    public static final String ICBC_WX_MINI_SUB_APPSECRET = WEIXIN_SUB_APP_SECRET; //微信sub_appsecret
    public static final String ICBC_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;
    public static final String ICBC_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;

    //农行交易参数
    public static final String ABC_UP_TRADE_PARAMS = "abc_up_trade_params";

    public static final String ABC_PUBLIC_KEY = "rsa_key"; //公钥
    public static final String ABC_COUNTNO = "countno"; //款台号

    public static final String ABC_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                         //ABC_TRADE_PARAMS 微信支付分配的商户号
    public static final String ABC_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;

    public static final String ABC_AILIPAY_SUB_MCH_ID = "alipay_sub_mch_id";               //ABC_TRADE_PARAMS alipay



    //拉卡拉开发平台v3
    public static final String LAKALA_UNION_PAY_OPEN_TRADE_PARAMS = "lakala_open_trade_params";
    public static final String LAKALA_UNION_PAY_OPEN_MERC_ID = "merc_id";
    public static final String LAKALA_UNION_PAY_OPEN_TERM_ID = "term_id";
    public static final String LAKALA_UNION_PAY_OPEN_TERM_NO = "term_no";
    public static final String LAKALA_UNION_PAY_OPEN_APP_ID = "app_id";

    public static final String PARAMS_BANKCARD_FEE = "bankcard_fee";
    public static final String PARAMS_BANKCARD_FEE_CREDIT = "credit";
    public static final String PARAMS_BANKCARD_FEE_DEBIT = "debit";
    public static final String PARAMS_BANKCARD_FEE_DCC = "dcc";
    public static final String PARAMS_BANKCARD_FEE_EDC = "edc";
    public static final String PARAMS_BANKCARD_FEE_OTHERS = "others";
    public static final String PARAMS_BANKCARD_FEE_MAX = "max";

    public static final String PARAMS_BAITIAO_PRE_PRODUCT = "preproduct";
    public static final String PARAMS_BAITIAO_ITEM_NO = "item_no";
    public static final String PARAMS_MERC_TYPE = "merc_type";


    //通联收银宝
    public static final String TL_SYB_TRADE_PARAMS = "tl_syb_trade_params";
    public static final String TL_SYB_APP_Id = "app_id";
    public static final String TL_SYB_ORG_ID = "org_id";
    public static final String TL_SYB_TERM_ID = "term_id";
    public static final String TL_SYB_CUS_ID = "cus_id"; //收银宝商户号
    public static final String TL_SYB_PRIVATE_KEY = PRIVATE_KEY; //收银宝请求私钥
    public static final String TL_SYB_PUBLIC_KEY = PUBLIC_KEY; //收银宝通知公钥
    public static final String TL_SYB_SUB_APPID = WEIXIN_SUB_APP_ID; //微信sub_appid
    public static final String TL_SYB_SUB_APPSECRET = WEIXIN_SUB_APP_SECRET; //微信sub_appsecret
    public static final String TL_SYB_SUB_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;
    public static final String TL_SYB_MINI_SUB_APPID = WEIXIN_MINI_SUB_APP_ID; //微信sub_appid
    public static final String TL_SYB_MINI_SUB_APPSECRET = WEIXIN_MINI_SUB_APP_SECRET; //微信sub_appsecret
    public static final String TL_SYB_UNION_MCH_ID = "union_mch_id"; //通联收银宝 云闪付商户号
    public static final String TL_SYB_CERT_ID = "cert_id";

    public static final String TL_SYB_ALIPAY_SYS_PID = "sys_pid";                          //sys_service_provider_id
    public static final String TL_SYB_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";

    //建行福利卡（智慧食堂
    public static final String CCB_GIFT_CARD_TRADE_PARAMS = "ccb_gift_card_trade_params";
    public static final String CCB_GIFT_CARD_PARAMS_CAMPUS_ID = "campus_id";
    public static final String CCB_GIFT_CARD_PARAMS_CORP_ID = "corp_id";
    public static final String CCB_GIFT_CARD_PARAMS_BUSINESS_ID = "business_id";
    public static final String CCB_GIFT_CARD_PARAMS_VPOS_ID = "vpos_id";

    //海科银联交易参数配置
    public static final String HAIKE_UNION_PAY_TRADE_PARAMS = "haike_up_trade_params";                  //config
    public static final String HAIKE_UNION_PAY_CERT_ID = "cert_id";                                     //HAIKE_UNION_PAY_TRADE_PARAMS 签名证书序列号
    public static final String HAIKE_UNION_PAY_ACCESS_KEY = "access_key";                               //HAIKE_UNION_PAY_TRADE_PARAMS 海科加签密钥
    public static final String HAIKE_UNION_PAY_AGENT_NO = "agent_no";                                   //HAIKE_UNION_PAY_TRADE_PARAMS 海科服务商编号
    public static final String HAIKE_UNION_PAY_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                       //HAIKE_UNION_PAY_TRADE_PARAMS 渠道商户号
    //HAIKE_UNION_PAY_TRADE_PARAMS weixin
    public static final String HAIKE_UNION_PAY_CHANNEL_ID = "channel_id";                               //HAIKE_UNION_PAY_TRADE_PARAMS 微信支付分配给收单服务商的ID
    public static final String HAIKE_UNION_PAY_WEIXIN_APP_ID = WEIXIN_APP_ID;                           //HAIKE_UNION_PAY_TRADE_PARAMS 微信分配的公众账号ID
    public static final String HAIKE_UNION_PAY_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                   //HAIKE_UNION_PAY_TRADE_PARAMS 微信分配的子商户公众账号ID
    public static final String HAIKE_UNION_PAY_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;         //HAIKE_UNION_PAY_TRADE_PARAMS weixin
    public static final String HAIKE_UNION_PAY_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;           //HAIKE_UNION_PAY_TRADE_PARAMS weixin
    public static final String HAIKE_UNION_PAY_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET; //HAIKE_UNION_PAY_TRADE_PARAMS weixin
    public static final String HAIKE_UNION_PAY_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                           //HAIKE_UNION_PAY_TRADE_PARAMS 微信支付分配的商户号
    public static final String HAIKE_UNION_PAY_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                   //HAIKE_UNION_PAY_TRADE_PARAMS 微信支付分配的子商户号
    public static final String HAIKE_UNION_PAY_WEIXIN_APP_KEY = WEIXIN_APP_KEY;                         //HAIKE_UNION_PAY_TRADE_PARAMS weixin
    //HAIKE_UNION_PAY_TRADE_PARAMS alipay
    public static final String HAIKE_UNION_PAY_ALIPAY_APP_ID = APP_ID;                                  //HAIKE_UNION_PAY_TRADE_PARAMS alipay 支付宝分配给开发者的应用ID
    public static final String HAIKE_UNION_PAY_SYS_PROVIDER_ID = "sys_pid";                             //HAIKE_UNION_PAY_TRADE_PARAMS alipay sys_service_provider_id
    public static final String HAIKE_UNION_PAY_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";                 //HAIKE_UNION_PAY_TRADE_PARAMS alipay
    //HAIKE_UNION_PAY_TRADE_PARAMS unionpay
    public static final String HAIKE_UNION_PAY_UNION_MER_ID = "mer_id";                                 //HAIKE_UNION_PAY_UNION_MER_ID 商户号
    public static final String HAIKE_UNION_PAY_UNION_MER_NAME = "mer_name";                             //HAIKE_UNION_PAY_UNION_MER_NAME 商户名称
    public static final String HAIKE_UNION_PAY_UNION_CERT_ID = "union_cert_id";                         //HAIKE_UNION_PAY_UNION_CERT_ID 云闪付加密证书编号， 用于加密的公钥证书的序列号
    public static final String HAIKE_UNION_PAY_UNION_ACQ_CODE = "acq_code";                             //HAIKE_UNION_PAY_UNION_TERM_ID 受理机构代码
    public static final String HAIKE_UNION_PAY_UNION_PNR_INS_ID_CD = "pnr_ins_id_cd";                   //HAIKE_UNION_PAY_UNION_PNR_INS_ID_CD服务商机构标识
    public static final String HAIKE_UNION_PAY_UNION_MCH_CAT_CODE = "mer_cat_code";                     //HAIKE_UNION_PAY_UNION_MCH_CAT_CODE商户类别

    //富友交易参数配置
    public static final String FUYOU_TRADE_PARAMS = "fuyou_trade_params";                   //config
    public static final String FUYOU_AGENT_NO = "agent_no";                                 //FUYOU_TRADE_PARAMS 机构号,接入机构在富友的唯一代码
    public static final String FUYOU_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                     //FUYOU_TRADE_PARAMS 商户号,富友分配给二级商户的商户号
    public static final String FUYOU_PRIVATE_KEY = PRIVATE_KEY;                             //FUYOU_TRADE_PARAMS 私钥
    public static final String FUYOU_PUBLIC_KEY = PUBLIC_KEY;                               //FUYOU_TRADE_PARAMS 公钥
    //FUYOU_TRADE_PARAMS weixin
    public static final String FUYOU_CHANNEL_ID = "channel_id";                             //FUYOU_TRADE_PARAMS 微信支付分配给收单服务商的ID
    public static final String FUYOU_WEIXIN_APP_ID = WEIXIN_APP_ID;                         //FUYOU_TRADE_PARAMS 微信分配的公众账号ID
    public static final String FUYOU_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                 //FUYOU_TRADE_PARAMS 微信分配的子商户公众账号ID
    public static final String FUYOU_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;       //FUYOU_TRADE_PARAMS 微信分配的子商户公众账号ID
    public static final String FUYOU_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                         //FUYOU_TRADE_PARAMS 微信支付分配的商户号
    public static final String FUYOU_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                 //FUYOU_TRADE_PARAMS 微信支付分配的子商户号
    //FUYOU_TRADE_PARAMS alipay
    public static final String FUYOU_ALIPAY_APP_ID = APP_ID;                                //FUYOU_TRADE_PARAMS alipay 支付宝分配给开发者的应用ID
    public static final String FUYOU_SYS_PROVIDER_ID = "sys_pid";                           //FUYOU_TRADE_PARAMS alipay sys_service_provider_id
    public static final String FUYOU_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";               //FUYOU_TRADE_PARAMS alipay


    //FUYOU_TRADE_PARAMS bankcard
    public static final String FUYOU_BANK_QUERY_PRIVATE_KEY = "query_private_key"; //查单接口秘钥
    public static final String FUYOU_BANK_TERM_ID = "term_id"; //富友终端号
    public static final String FUYOU_UNION_PAY_OPEN_MCH_ID = UNION_PAY_OPEN_MCH_ID;
    //平安银行交易参数
    public static final String PAB_TRADE_PARAMS = "pab_up_trade_params";


    public static final String PAB_SM2_USER_ID = "sm2_user_id";                           // 微信分配的公众账号ID
    public static final String S_SIGNCERT_ID = "s_signcert_id";                           // 微信分配的公众账号ID


    //微信
    public static final String PAB_PAY_WEIXIN_APP_ID = WEIXIN_APP_ID;                           //PAD_TRADE_PARAMS 微信分配的公众账号ID
    public static final String PAB_PAY_WEIXIN_SUB_APP_ID = WEIXIN_SUB_APP_ID;                   //PAD_TRADE_PARAMS 微信分配的子商户公众账号ID
    public static final String PAB_PAY_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;         //PAD_TRADE_PARAMS weixin
    public static final String PAB_PAY_WEIXIN_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;           //PAD_TRADE_PARAMS weixin
    public static final String PAB_PAY_WEIXIN_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET; //PAD_TRADE_PARAMS weixin
    public static final String PAB_PAY_PAY_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                           //PAD_TRADE_PARAMS 微信支付分配的商户号
    public static final String PAB_PAY_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;

    public static final String PAB_PAY_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                  //LAKALA_UNION_PAY_TRADE_PARAMS 渠道商户号

    //alipay

    public static final String PAB_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";                  //LAKALA_UNION_PAY_TRADE_PARAMS 渠道商户号

    public static final String PAB_PAY_B2C_TERMINAL_ID = "b2c_terminal_id"; //b2c 终端id
    public static final String PAB_PAY_OTHER_B2C_TERMINAL_ID = "other_terminal_id"; //非b2c建行终端id


    //交行交易参数配置
    public static final String BOCOM_TRADE_PARAMS = "bocom_trade_params";                   //config
    public static final String BOCOM_APP_ID = "app_id";                                     //BOCOM_TRADE_PARAMS app_id
    public static final String BOCOM_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                     //BOCOM_TRADE_PARAMS 商户编号
    public static final String BOCOM_PRIVATE_KEY = PRIVATE_KEY;                             //BOCOM_TRADE_PARAMS 私钥
    public static final String BOCOM_PUBLIC_KEY = PUBLIC_KEY;                               //BOCOM_TRADE_PARAMS 公钥
    //BOCOM_TRADE_PARAMS weixin
    public static final String BOCOM_WEIXIN_MCH_ID = WEIXIN_MCH_ID;                         //BOCOM_TRADE_PARAMS 微信支付分配的商户号
    public static final String BOCOM_WEIXIN_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                 //BOCOM_TRADE_PARAMS 微信支付分配的子商户号
    //BOCOM_TRADE_PARAMS alipay
    public static final String BOCOM_SYS_PROVIDER_ID = "sys_pid";                           //BOCOM_TRADE_PARAMS alipay sys_service_provider_id
    public static final String BOCOM_ALIPAY_SUB_MCH_ID = "alipay_sub_mch_id";               //BOCOM_TRADE_PARAMS alipay

    //微企付通道交易参数
    public static final String ENTPAY_TRADE_PARAMS = "entpay_trade_params";
    public static final String ENTPAY_PLATFORM_ID = "platform_id";              //ENTPAY_TRADE_PARAMS 微企付平台的平台帐号
    public static final String ENTPAY_ENT_ID = "ent_id";                        //ENTPAY_TRADE_PARAMS 商户企业ID
    public static final String ENTPAY_ENT_NAME = "ent_name";                    //ENTPAY_TRADE_PARAMS 商户名称
    public static final String ENTPAY_PRIVATE_SERIAL_NO = "private_serial_no";  //ENTPAY_TRADE_PARAMS 平台的API私钥证书序列号
    public static final String ENTPAY_PUBLIC_SERIAL_NO = "public_serial_no";    //ENTPAY_TRADE_PARAMS 商企付公钥序列号
    public static final String ENTPAY_PRIVATE_KEY = PRIVATE_KEY;                //ENTPAY_TRADE_PARAMS 平台私钥
    public static final String ENTPAY_PUBLIC_KEY = PUBLIC_KEY;                  //ENTPAY_TRADE_PARAMS 商企付公钥

    public static final String FITNESS_PARAMS = "fitness_params";                                       //先享后付参数
    public static final String FITNESS_PARAMS_APP_ID = APP_ID;
    public static final String FITNESS_PARAMS_ISV_APP_ID = "isv_app_id";                                //行业云上的isv_app_id
    public static final String FITNESS_PARAMS_PRIVATE_KEY = PRIVATE_KEY;                                //isv的私钥
    public static final String FITNESS_PARAMS_ALIPAY_PID = ALIPAY_PID;
    public static final String FITNESS_PARAMS_ALIPAY_MERCHANT_PID = "merchant_pid";
    public static final String FITNESS_PARAMS_ALIPAY_SHOP_ID = "shop_id";
    public static final String FITNESS_STATUS = "fitness_status";                                       //先享后付开关
    public static final String FITNESS_PARAMS_SCENS = "scens";
    public static final String FITNESS_PARAMS_REMARK_FLAG = "remark_flag";
    public static final String FITNESS_PARAMS_PRODUCTS = "products";
    public static final String FITNESS_PARAMS_EXCLUSIVE = "exclusive";                                  //是否专属门店码
    public static final String FITNESS_PARAMS_PRODUCTS_ID = "id";
    public static final String FITNESS_PARAMS_PRODUCTS_NAME = "name";
    public static final String FITNESS_PARAMS_PRODUCTS_DESC = "desc";
    public static final String FITNESS_PARAMS_PRODUCTS_CYCLE = "cycle";
    public static final String FITNESS_PARAMS_SETTLE_FEE_RATE = "settle_fee_rate";                      //技术服务费 分账费率
    public static final int FITNESS_STATUS_CLOSE = 0;
    public static final int FITNESS_STATUS_OPEN = 1;
    public static final String FITNESS_TRADE_PARAMS = "fitness_trade_params";


    public static final String ZJTLCB_UP_TRADE_PARAMS = "zjtlcb_up_trade_params";
    public static final String ZJTLCB_APP_SECRET_KEY = "app_secret_key";
    public static final String ZJTLCB_SM2_PRIVATE_KEY = "sm2_private_key";
    public static final String ZJTLCB_PUBLIC_KEY = "tl_public_key";
    public static final String ZJTLCB_CHANNEL_CODE = "channel_code";

    //福建农信交易参数
    public static final String FJNX_TRADE_PARAMS = "fjnx_trade_params";
    public static final String FJNX_PAY_WEIXIN_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID;         //FJNX_TRADE_PARAMS weixin
    public static final String FJNX_PUBLIC_KEY = "nx_public_key"; //商户私钥  非唯一
    public static final String FJNX_PRIVATE_KEY = "nx_private_key"; //农信公钥 唯一
    public static final String FJNX_PASSWORD = "password"; //密码
    public static final String FJNX_TERMINAL_ID = "fjnx_terminal_id"; //农信终端id
    public static final String FJNX_APPLICATION = "application"; //渠道号
    public static final String FJNX_MER_INSTID = "merInstId"; //商户标识
    public static final String FJNX_SYS_INSTID = "sysInstId"; //机构标识
    public static final String FJNX_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                     //FJNX_TRADE_PARAMS 商户编号

    //锦医一卡通交易参数
    public static final String JYCARD_TRADE_PARAMS = "jycard_trade_params";
    public static final String JYCARD_PROVIDER_MCH_ID = PROVIDER_MCH_ID; //JYCARD_TRADE_PARAMS 商户编号
    public static final String JYCARD_ACC_TYPE = "acc_type";             //JYCARD_TRADE_PARAMS 账户类型. 000表示电子账户
    public static final String JYCARD_APP_KEY = APP_KEY;                 //JYCARD_TRADE_PARAMS 锦医一卡通分配给收钱吧应用的AppKey
    public static final String JYCARD_APP_SECRET = "app_secret";         //JYCARD_TRADE_PARAMS 秘钥
    public static final String JYCARD_PRIVATE_KEY = PRIVATE_KEY;         //JYCARD_TRADE_PARAMS 私钥
    public static final String JYCARD_PUBLIC_KEY = PUBLIC_KEY;           //JYCARD_TRADE_PARAMS 公钥

    public static final String SPDB_UP_TRADE_PARAMS = "spdb_up_trade_params";  //浦发银行对接参数
    public static final String SPDB_APP_SECRET_KEY = "app_secret_key";
    public static final String SQB_SM2_PRIVATE_KEY = "sqb_sm2_private_key"; //收钱吧SM2 私钥
    public static final String SQB_SM2_PUBLIC_KEY = "sqb_sm2_public_key"; // 收钱吧SM2公钥
    public static final String SPDB_SM2_PUBLIC_KEY = "spdb_sm2_public_key"; // 浦发sm2公钥 验签
    public static final String SPDB_CLIENT_ID = "client_id";
    public static final String SPDB_PROVIDER_MCH_ID = "provider_mch_id";  // 浦发银行提供的商户编号
    public static final String SPDB_PROVIDER_TERM_ID = "spdb_terminal_id";  // 浦发银行提供的终端号
    public static final String SPDB_WD_PRIVATE_KEY = "spdb_wd_private_key"; //浦发外调私钥
    public static final String SPDB_WD_PUBLIC_KEY = "spdb_wd_public_key"; //浦发外调公钥
    public static final String SQB_WD_PRIVATE_KEY = "sqb_wd_private_key"; //收钱吧外调私钥
    public static final String SQB_WD_PUBLIC_KEY = "sqb__wd_public_key"; //收钱吧外调公钥

    public static final String CMBC_TRADE_PARAMS = "cmbc_trade_params";                 //CONFIG
    public static final String CMBC_PLATFORM_ID = "platform_id";                        // CMBC_TRADE_PARAMS 民生平台ID
    public static final String CMBC_MCH_ID = "mch_id";                                  // CMBC_TRADE_PARAMS 民生商户号
    public static final String CMBC_PRIVATE_KEY = "private_key";                        // CMBC_TRADE_PARAMS 民生私钥
    public static final String CMBC_PRIVATE_KEY_PASSWORD = "private_key_password";      // CMBC_TRADE_PARAMS 民生私钥密码
    public static final String CMBC_PUBLIC_KEY = "public_key";                          // CMBC_TRADE_PARAMS 民生商户公钥
    public static final String CMBC_PLATFORM_PUBLIC_KEY = "platform_public_key";        // CMBC_TRADE_PARAMS 民生平台公钥

    //江苏银行-交易参数
    public static final String JSB_TRADE_PARAMS = "jsb_trade_params";
    public static final String JSB_PROVIDER_MCH_ID = PROVIDER_MCH_ID;               //JSB_TRADE_PARAMS 商户编号
    public static final String JSB_PROVIDER_MCH_UUID = "provider_mch_uuid";         //JSB_TRADE_PARAMS 商户在江苏银行的唯一标识(合作商id)
    public static final String JSB_CHANNEL_PARTNER_ID = "jsb_channel_partner_id";   //JSB_TRADE_PARAMS 渠道商编号
    public static final String JSB_SQB_PRIVATE_KEY = PRIVATE_KEY;                   //JSB_TRADE_PARAMS 收钱吧私钥
    public static final String JSB_SQB_PUBLIC_KEY = PUBLIC_KEY;                     //JSB_TRADE_PARAMS 收钱吧公钥
    public static final String JSB_PROVIDER_PUBLIC_KEY = PROVIDER_PUBLIC_KEY;       //JSB_TRADE_PARAMS 江苏银行公钥
    public static final String JSB_ALIPAY_APP_ID = APP_ID;                          //JSB_TRADE_PARAMS 支付宝appId
    public static final String JSB_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;           //JSB_TRADE_PARAMS 支付宝子商户号
    public static final String JSB_WECHAT_SUB_APP_ID = WEIXIN_SUB_APP_ID;           //JSB_TRADE_PARAMS 微信子appId
    public static final String JSB_WECHAT_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID; //JSB_TRADE_PARAMS 微信小程序子appId
    public static final String JSB_WECHAT_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;           //JSB_TRADE_PARAMS 微信子商户号
    public static final String JSB_UNION_PAY_SUB_MCH_ID = "union_pay_sub_mch_id";   //JSB_TRADE_PARAMS 银联子商户号

    // 通联S2P 交易参数
    public static final String TL_S2P_TRADE_PARAMS = "tl_s2p_trade_params";         // TL_S2P_TRADE_PARAMS
    public static final String TL_S2P_INST_NO = "inst_no";                         //  TL_S2P_TRADE_PARAMS 接入号
    public static final String TL_S2P_PROVIDER_MCH_ID = PROVIDER_MCH_ID;           //  TL_S2P_TRADE_PARAMS 商户号
    public static final String TL_S2P_SQB_PRIVATE_KEY = PRIVATE_KEY;               //   TL_S2P_TRADE_PARAMS 收钱吧私钥
    public static final String TL_S2P_TL_PUBLIC_KEY = PUBLIC_KEY;                  //   TL_S2P_TRADE_PARAMS 通联公钥


    // 易宝支付
    public static final String YOP_TRADE_PARAMS = "yop_trade_params";                       // YOP_TRADE_PARAMS
    public static final String YOP_APP_KEY = "app_key";                                     // YOP_TRADE_PARAMS 易宝应用
    public static final String YOP_ISV_MCH_ID = "isv_mch_id";                               // YOP_TRADE_PARAMS 易宝服务商商户号
    public static final String YOP_PROVIDER_MCH_ID = PROVIDER_MCH_ID;                       // YOP_TRADE_PARAMS 易宝商户号
    public static final String YOP_PRIVATE_KEY = PRIVATE_KEY;                               // YOP_TRADE_PARAMS 收钱吧私钥
    public static final String YOP_PUBLIC_KEY = PUBLIC_KEY;                                 // YOP_TRADE_PARAMS 易宝公钥
    public static final String YOP_SUB_APPID = WEIXIN_SUB_APP_ID;                           // YOP_TRADE_PARAMS 微信sub_appid
    public static final String YOP_SUB_APP_SECRET = WEIXIN_SUB_APP_SECRET;                  // YOP_TRADE_PARAMS 微信
    public static final String YOP_SUB_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;                      // YOP_TRADE_PARAMS 支付源微信子商户号
    public static final String YOP_MINI_SUB_APPID = WEIXIN_MINI_SUB_APP_ID;                 // YOP_TRADE_PARAMS 微信sub_appid
    public static final String YOP_MINI_SUB_APP_SECRET = WEIXIN_MINI_SUB_APP_SECRET;        // YOP_TRADE_PARAMS 微信
    public static final String YOP_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;                   // YOP_TRADE_PARAMS 支付源支付宝子商户号

    // 大兴机场
    public static final String PKX_AIRPORT_TRADE_PARAMS = "pkx_airport_trade_params";       // PKX_AIRPORT_TRADE_PARAMS
    




    // 泸州银行交易参数
    public static final String LZCCB_UP_TRADE_PARAMS = "lzccb_up_trade_params";  //泸州银行对接参数

    public static final String LZCCB_PROVIDER_MCH_ID = "provider_mch_id";  // 泸州银行提供的商户编号

    public static final String LZCCB_PROVIDER_STORE_SN = "provider_store_sn";  // 泸州银行提供的门店编号

    public static final String LZCCB_SM2_PKEY = "lzccb_sm2_pkey";  // 泸州银行sm2私钥

    public static final String LZCCB_SM3_KEY = "lzccb_sm3_key";  // 泸州银行sm3密钥


    public static final String LZCCB_APP_ID = APP_ID;                          //LZCCB_UP_TRADE_PARAMS 支付宝appId
    public static final String LZCCB_ALIPAY_SUB_MCH_ID = ALIPAY_SUB_MCH_ID;           //JSB_TRADE_PARAMS 支付宝子商户号
    public static final String LZCCB_WECHAT_SUB_APP_ID = WEIXIN_SUB_APP_ID;           //JSB_TRADE_PARAMS 微信子appId
    public static final String LZCCB_WECHAT_MINI_SUB_APP_ID = WEIXIN_MINI_SUB_APP_ID; //JSB_TRADE_PARAMS 微信小程序子appId
    public static final String LZCCB_WECHAT_SUB_MCH_ID = WEIXIN_SUB_MCH_ID;           //JSB_TRADE_PARAMS 微信子商户号

    // 中投科信交易参数
    public static final String ZTKX_TRADE_PARAMS               = "ztkx_trade_params";
    public static final String ZTKX_PROVIDER_MCH_ID            = PROVIDER_MCH_ID;                // ZTKX_TRADE_PARAMS 商户编号
    public static final String PLATFORM_MCH_ID                 = "platform_mch_id";              // ZTKX_TRADE_PARAMS 平台商户编号
    public static final String ZTKX_CHANNEL_PARTNER_ID         = "ztkx_channel_partner_id";      // ZTKX_TRADE_PARAMS 渠道商编号
    public static final String ZTKX_SQB_ENC_PRIVATE_KEY        = "enc_private_key";              // ZTKX_TRADE_PARAMS 收钱吧加密私钥
    public static final String ZTKX_SQB_ENC_PRIVATE_KEY_PWD    = "enc_private_key_pwd";          // ZTKX_TRADE_PARAMS 收钱吧加密私钥密码
    public static final String ZTKX_SQB_SIGN_PRIVATE_KEY       = "sign_private_key";             // ZTKX_TRADE_PARAMS 收钱吧签名私钥
    public static final String ZTKX_SQB_SIGN_PRIVATE_KEY_PWD   = "sign_private_key_pwd";         // ZTKX_TRADE_PARAMS 收钱吧签名私钥密码
    public static final String ZTKX_PROVIDER_ENC_PUBLIC_KEY    = "provider_enc_public_key";      // ZTKX_TRADE_PARAMS 中投科信加密公钥
    public static final String ZTKX_PROVIDER_SIGN_PUBLIC_KEY   = "provider_sign_public_key";     // ZTKX_TRADE_PARAMS 中投科信签名公钥
    public static final String ZTKX_ALIPAY_APP_ID              = APP_ID;                         // ZTKX_TRADE_PARAMS 支付宝appId
    public static final String ZTKX_ALIPAY_SUB_MCH_ID          = ALIPAY_SUB_MCH_ID;              // ZTKX_TRADE_PARAMS 支付宝子商户号
    public static final String ZTKX_WECHAT_SUB_APP_ID          = WEIXIN_SUB_APP_ID;              // ZTKX_TRADE_PARAMS 微信子appId
    public static final String ZTKX_WECHAT_MINI_SUB_APP_ID     = WEIXIN_MINI_SUB_APP_ID;         // ZTKX_TRADE_PARAMS 微信小程序子appId
    public static final String ZTKX_WECHAT_SUB_MCH_ID          = WEIXIN_SUB_MCH_ID;              // ZTKX_TRADE_PARAMS 微信子商户号


    //跨门店退款开关
    public static final String ACROSS_STORE_REFUND_SWITCH = "across_store_refund_switch";
    //商户调用网关订单号生成接口开关
    public static final String GEN_ORDER_SN_SWITCH = "gen_order_sn_switch";
    //商户分账开关
    public static final String SHARING_SWITCH = "sharing_switch";

    public static final String LANGUAGE_CASE = "language_case";
    public static final String COMMON_SWITCH = "common_switch";

    public static final String PARAMS_SWITCH_MCH_TIME = "switch_mch_time";//小微升级成功替换商户号的时间点 历史该通道下的交易都不允许退款

    public static final int TYPE_COMMON_SWITCH_TL_UNIONPAY = 0;                 // 通联云闪付大额支付开关
    public static final int TYPE_COMMON_SWITCH_MERCHANT_ACTIIVTY = 1;           // 调用活动服务开关
    public static final int TYPE_COMMON_SWITCH_RETURN_PROVIDER_RESPONSE = 2;    // 返回渠道信息
    public static final int TYPE_COMMON_SWITCH_HBFQ_NUM_24 = 3;                 // 花呗分期24期开关
    public static final int TYPE_COMMON_SWITCH_LKL_UNIONPAY = 4;                // 拉卡拉云闪付大额支付开关
    public static final int TYPE_COMMON_SWITCH_SEND_FUND_BILL_LIST = 5;         // 支付宝接口上送查询渠道资金详情开关
    public static final int TYPE_COMMON_SWITCH_QUOTA_APPLY_ACTIVITY = 6;              // 商户额度包优惠费率活动开关　原配置名为：TYPE_COMMON_SWITCH_APPLY_ACTIVITY
    public static final int TYPE_COMMON_SWITCH_APPLY_ACTIVITY_RULE = 7;         // 商户活动申请规则交易额度交易笔数
    public static final int TYPE_COMMON_SWITCH_CASH_DESK = 8;                   // 商户收银台开关
    public static final int TYPE_COMMON_SWITCH_PREPAID_CPAY = 9;                // 储值组合支付开关
    public static final int TYPE_COMMON_SWITCH_SODEXO_CPAY = 10;                // 索迪斯组合支付开关

    public static final int TYPE_COMMON_SWITCH_SUPPORT_TRADE_1000 = 11;                // 商户层级，支持1000以上交易开关
    public static final int TYPE_COMMON_SWITCH_PRE_CANCEL = 12;                 // 商户层级, 交易预撤单开关
    public static final int TYPE_COMMON_SWITCH_ENTPAY_SUPPORT_PROFIT = 13;      // 商户层级, 微企付支持分账
    public static final int TYPE_COMMON_SWITCH_QUERY_TRADE_MEMO = 14;           // 商户层级, 流水备注筛选开关
    public static final int TYPE_COMMON_SWITCH_QUOTA_ACTIVITY_N_POS = 15;       // 针对TYPE_COMMON_SWITCH_QUOTA_APPLY_ACTIVITY二级分类，n-pos额度包
    public static final int TYPE_COMMON_SWITCH_LIQUIDATION_NEXT_DAY_INDIRECT = 16;       // 商户变更为间连直清，在同时存在间连直清和间连提现时才会生效
    public static final int TYPE_COMMON_SWITCH_AUTO_VERIFICATION = 17;           // 自动核销开关。商户开启后，用户在使用微信或支付宝支付时，如果用户开通了储值会员且存在余额，则使用用户的储值账户进行核销；否则走普通的微信或支付宝进行支付
    public static final int TYPE_COMMON_SWITCH_AGGREGATION_CONFIG = 18;        //归集开关 退款、撤销交易时需要校验昨日归集是否完成
    public static final int TYPE_COMMON_SWITCH_REFUND_CONFIG = 19;        //退款审批开关
    public static final int TYPE_COMMON_SWITCH_WEIXIN_B2b_TRADE = 20;           //微信B2b支付开关
    public static final int TYPE_COMMON_SWITCH_BRAND_AFFILIATION = 21;          //商户品牌挂靠开关

    public static final int LANGUAGE_CASE_CHINESE = 0;
    public static final int LANGUAGE_CASE_ENGLISH = 1;

    // 业务方类型
    public static final String TRADE_APP = "trade_app";
    public static final String TRADE_APP_BASIC_PAY = "1";               // 基础支付业务

    public static final List<String> switchList = Arrays.asList(PAY_STATUS, ACROSS_STORE_REFUND_SWITCH, GEN_ORDER_SN_SWITCH, SHARING_SWITCH, COMMON_SWITCH, LANGUAGE_CASE);
    public static final List<String> storeSwitchList = Arrays.asList(STORE_PAY_STATUS);

    public static final List<Integer> statusList = Arrays.asList(STATUS_CLOSED,STATUS_OPENED
            , LANGUAGE_CASE_CHINESE, LANGUAGE_CASE_ENGLISH);
    
    public static final String ALIPAY_HUABEI_SELLER_DISCOUNT = "alipay_huabei_seller_discount"; //支付宝商户贴息配置
    public static final String ALIPAY_HUABEI_SELLER_DISCOUNT_MIN = "min"; // 贴息最小金额
    public static final String ALIPAY_HUABEI_SELLER_DISCOUNT_MAX = "max"; // 贴息最大金额
    public static final String ALIPAY_HUABEI_SELLER_DISCOUNT_NUMS = "nums"; // 贴息分期数

    public static final String HIT_PAYWAY = "hit_payway";     //配置命中支付源(payway)

    public static final String DISCOUNT_QUOTA_FEE_RATE = "quota_fee_rate";         // 优惠费率
    public static final String DISCOUNT_QUOTA_FEE_RATE_TAG = "quota_fee_rate_tag"; // 优惠费率共享额度标识
    public static final String BYPASS_BATCH_NO = "bypass_batch_no";                // 备用通道批次号
    public static final String SOURCE_ORGANIZATION = "source_organization";  // 原结算组织（备用通道切换前的结算通道或支付通道）

    public static final String CHANNEL_STATUS = "channel_status";   //资金渠道状态

    @Deprecated
    public static final String CHANNEL_FEE_RATE_TAG = "channel_fee_rate_tag";   // 资金渠道费率标签
    public static final String CHANNEL_CURRENCY_FEE_RATES = "channel_currency_fee_rates";   // 资金渠道币种费率

    public static final String CHANNEL_LADDER_FEE_RATES = "channel_ladder_fee_rates";   // 资金渠道阶梯费率
    public static final String CHANNEL_LADDER_FEE_RATES_CREDIT = PARAMS_BANKCARD_FEE_CREDIT;
    public static final String CHANNEL_LADDER_FEE_RATES_DEBIT = PARAMS_BANKCARD_FEE_DEBIT;
    public static final String CHANNEL_LADDER_FEE_RATES_OTHERS = PARAMS_BANKCARD_FEE_OTHERS;
    public static final String CHANNEL_LADDER_FEE_RATES_LADDER_FEE_TATES = LADDER_FEE_RATES;

    public static final String SFT_BRAND_ID = "sft_brand_id";   // 收付通品牌id

    public static final Map<Integer, List<String>> FQ_PARAMS_ALLOW_FILEDS = com.wosai.pantheon.util.MapUtil.hashMap(
            Payway.JDPAY.getCode(), Arrays.asList(TransactionParam.JD_BAITIAO_LIMIT, TransactionParam.JD_BAITIAO_STATUS),
            Payway.WEIXIN.getCode(), Arrays.asList(TransactionParam.WEIXIN_INSTALLMENT_STATUS)
    );

    public static final Map<Integer, String> FQ_STATUS_MAPPING = com.wosai.pantheon.util.MapUtil.hashMap(
            Payway.JDPAY.getCode(), TransactionParam.JD_BAITIAO_STATUS
    );

}
