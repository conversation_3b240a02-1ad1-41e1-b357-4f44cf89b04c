package com.wosai.upay.core.model;

public class Vendor {

    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用

    public static final String EXTRA_REMARK = "remark";//备注     extra

    public static final String NAME = "name"; // varchar(128) NOT NULL COMMENT '名称'
    public static final String SN = "sn"; // varchar(32) NOT NULL COMMENT '服务商可见的编号'
    public static final String CELLPHONE = "cellphone"; // varchar(32) DEFAULT NULL COMMENT '绑定的手机号'
    public static final String CONTACT_NAME = "contact_name"; // varchar(32) DEFAULT NULL COMMENT '联系人姓名'
    public static final String CONTACT_PHONE = "contact_phone"; // varchar(32) DEFAULT NULL COMMENT '联系固定电话号码'
    public static final String CONTACT_CELLPHONE = "contact_cellphone"; // varchar(32) DEFAULT NULL COMMENT '联系移动电话号码'
    public static final String CONTACT_EMAIL = "contact_email"; // varchar(64) DEFAULT NULL COMMENT '联系邮箱'
    public static final String CONTACT_ADDRESS = "contact_address"; // varchar(255) DEFAULT NULL COMMENT '联系地址'
    public static final String RANK = "rank"; // int DEFAULT NULL COMMENT '信用等级'
    public static final String STATUS = "status"; // int DEFAULT NULL
    public static final String PROVINCE = "province"; // varchar(32) DEFAULT NULL COMMENT '省'
    public static final String CITY = "city"; // varchar(32) DEFAULT NULL COMMENT '市'
    public static final String DISTRICT = "district"; // varchar(32) DEFAULT NULL COMMENT '区'
    public static final String STREET_ADDRESS = "street_address"; // varchar(255) DEFAULT NULL COMMENT '街道门牌号'
    public static final String EXTRA = "extra"; // blob DEFAULT NULL COMMENT '扩展字段 remark '

}
