package com.wosai.upay.core.model;

public class VendorApp {
    public static final int TYPE_ANDROID = 10;
    public static final int TYPE_IOS = 11;
    public static final int TYPE_DESKTOP = 20;
    public static final int TYPE_DEDICATED_DEVICE = 30;
    public static final int TYPE_WAP = 40;
    public static final int TYPE_SERVICE = 50;
    public static final int TYPE_INVOICE = 60;
    public static final int TYPE_CARD = 70;

    public static final String VENDOR_ID = "vendor_id"; // varchar(36) NOT NULL COMMENT '服务商id'
    public static final String APPID = "appid"; // varchar(36) NOT NULL COMMENT '应用编号'
    public static final String APPKEY = "appkey"; // varchar(36) NOT NULL COMMENT '应用密钥'
    public static final String NAME = "name"; // varchar(128) NOT NULL COMMENT '应用名称'
    public static final String TYPE = "type"; //int unsigned NOT NULL COMMENT '类型 10: Android应用 11: iOS应用  20: Windows桌面应用 30: 专用设备 40: 门店码    50: 服务 '
    public static final String CATEGORY = "category"; //int  终端类目

}
