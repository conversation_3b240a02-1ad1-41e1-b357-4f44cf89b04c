package com.wosai.upay.core.model.application;

public class ApplicationAlipay {

    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) DEFAULT NULL
    public static final String MERCHANT_SN = "merchant_sn"; // varchar(32) DEFAULT NULL
    public static final String STATUS = "status"; // int NOT NULL DEFAULT 1 COMMENT '当前审核状态，0:待提交；1:等待审核；11：审核驳回; 2：初审通过，在线签约审核中；21：已确认签约； 22：签约驳回; 3：签约已完成等待资料回填；4：资料已回填； 5：审核通过，开通成功 '
    public static final String STATUS_DESCRIPTION = "status_description"; // varchar(512) DEFAULT NULL COMMENT '当前审核描述'
    public static final String COMPANY_NAME = "company_name"; // varchar(255) DEFAULT NULL COMMENT '公司名称'
    public static final String ACCOUNT_TYPE = "account_type"; // int DEFAULT NULL COMMENT '账号类型，1：个人；2：企业'
    public static final String ADDRESS = "address"; // varchar(255) DEFAULT NULL COMMENT '详细地址'
    public static final String LEGAL_PERSON = "legal_person"; // varchar(100) DEFAULT NULL COMMENT '法人姓名'
    public static final String LEGAL_PERSON_ID_NUMBER = "legal_person_id_number"; // varchar(100) DEFAULT NULL COMMENT '法人身份证号码'
    public static final String LEGAL_PERSON_ID_CARD_FRONT_PHOTO = "legal_person_id_card_front_photo"; // varchar(255) DEFAULT NULL COMMENT '法人身份证正面照'
    public static final String LEGAL_PERSON_ID_CARD_BACK_PHOTO = "legal_person_id_card_back_photo"; // varchar(255) DEFAULT NULL COMMENT '法人身份证反面照'
    public static final String BUSINESS_LICENSE_PHOTO = "business_license_photo"; // varchar(255) DEFAULT NULL COMMENT '营业执照'
    public static final String FRONT_INTERIOR_PHOTO = "front_interior_photo"; // varchar(255) DEFAULT NULL COMMENT '店内正面照片'
    public static final String INTERIOR_PHOTOS = "interior_photos"; // blob DEFAULT NULL COMMENT '店内内景照片,json'
    public static final String STREET_NUMBER_PHOTO = "street_number_photo"; // varchar(255) DEFAULT NULL COMMENT '门牌号照片'
    public static final String CASHIER_REGISTRY_PHOTO = "cashier_registry_photo"; // varchar(255) DEFAULT NULL COMMENT '收银台照片'
    public static final String NEON_SIGN_PHOTO = "neon_sign_photo"; // varchar(255) DEFAULT NULL COMMENT '门店招牌照片'
    public static final String BANK_CARD_FRONT_PHOTO = "bank_card_front_photo"; // varchar(255) DEFAULT NULL COMMENT '银行卡正面照'
    public static final String BANK_CARD_BACK_PHOTO = "bank_card_back_photo"; // varchar(255) DEFAULT NULL COMMENT '银行卡反面照'
    public static final String EXTRA_LICENSES = "extra_licenses"; // varchar(255) DEFAULT NULL COMMENT '额外的行业证书'
    public static final String PERSONAL_PROPRIETARY_LICENCSE_PHOTO = "personal_proprietary_licencse_photo"; // varchar(255) DEFAULT NULL COMMENT '个体工商户执照'
    public static final String REGISTERED_CAPITAL = "registered_capital"; // int NOT NULL DEFAULT 0 COMMENT '注册资本'
    public static final String STAFF_COUNT = "staff_count"; // int DEFAULT NULL COMMENT '员工人数'
    public static final String OPERATING_SQUARE_METERAGE = "operating_square_meterage"; // varchar(50) DEFAULT NULL COMMENT '经营场所面积'
    public static final String PREDICTED_ANNUAL_REVENUE = "predicted_annual_revenue"; // int DEFAULT NULL COMMENT '预计年收入'
    public static final String APLIAY_ACCOUNT_UID = "apliay_account_uid"; // varchar(255) DEFAULT NULL COMMENT '支付宝账号'
    public static final String PID_APPKEY_PHOTO = "pid_appkey_photo"; // varchar(255) DEFAULT NULL COMMENT '支付宝申请成功以后的pid和appkey照片'
    public static final String TOBACCONISTS = "tobacconists"; // tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否售卖烟草'
    public static final String PAYLOAD = "payload"; // blob DEFAULT NULL COMMENT '本次修改内容（JSON）'

}
