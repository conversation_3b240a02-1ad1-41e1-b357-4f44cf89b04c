package com.wosai.upay.core.model.application;

public class ApplicationWeixin {

    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) DEFAULT NULL
    public static final String MERCHANT_SN = "merchant_sn"; // varchar(32) DEFAULT NULL
    public static final String STATUS = "status"; // int NOT NULL DEFAULT 1 COMMENT '当前审核状态，0:待提交；1:等待审核；11：审核驳回; 2：初审通过，在线签约审核中；21：已确认签约； 22：签约驳回; 3：签约已完成等待资料回填；4：资料已回填； 5：审核通过，开通成功 '
    public static final String STATUS_DESCRIPTION = "status_description"; // varchar(512) DEFAULT NULL COMMENT '当前审核描述'
    public static final String COMPANY_NAME = "company_name"; // varchar(255) DEFAULT NULL COMMENT '公司名称'
    public static final String ACCOUNT_TYPE = "account_type"; // int DEFAULT NULL COMMENT '账号类型，1：个人；2：企业'
    public static final String ADDRESS = "address"; // varchar(255) DEFAULT NULL COMMENT '详细地址'
    public static final String LEGAL_PERSON = "legal_person"; // varchar(100) DEFAULT NULL COMMENT '法人姓名'
    public static final String LEGAL_PERSON_ID_NUMBER = "legal_person_id_number"; // varchar(100) DEFAULT NULL COMMENT '法人身份证号码'
    public static final String LEGAL_PERSON_ID_CARD_FRONT_PHOTO = "legal_person_id_card_front_photo"; // varchar(255) DEFAULT NULL COMMENT '法人身份证正面照'
    public static final String LEGAL_PERSON_ID_CARD_BACK_PHOTO = "legal_person_id_card_back_photo"; // varchar(255) DEFAULT NULL COMMENT '法人身份证反面照'
    public static final String BUSINESS_LICENSE_PHOTO = "business_license_photo"; // varchar(255) DEFAULT NULL COMMENT '营业执照'
    public static final String FRONT_INTERIOR_PHOTO = "front_interior_photo"; // varchar(255) DEFAULT NULL COMMENT '店内正面照片'
    public static final String INTERIOR_PHOTOS = "interior_photos"; // blob DEFAULT NULL COMMENT '店内内景照片,json'
    public static final String STREET_NUMBER_PHOTO = "street_number_photo"; // varchar(255) DEFAULT NULL COMMENT '门牌号照片'
    public static final String CASHIER_REGISTRY_PHOTO = "cashier_registry_photo"; // varchar(255) DEFAULT NULL COMMENT '收银台照片'
    public static final String NEON_SIGN_PHOTO = "neon_sign_photo"; // varchar(255) DEFAULT NULL COMMENT '门店招牌照片'
    public static final String BANK_CARD_FRONT_PHOTO = "bank_card_front_photo"; // varchar(255) DEFAULT NULL COMMENT '银行卡正面照'
    public static final String BANK_CARD_BACK_PHOTO = "bank_card_back_photo"; // varchar(255) DEFAULT NULL COMMENT '银行卡反面照'
    public static final String EXTRA_LICENSES = "extra_licenses"; // varchar(255) DEFAULT NULL COMMENT '额外的行业证书'
    public static final String ORGANIZATION_CODE_CERTIFICATE = "organization_code_certificate"; // varchar(255) DEFAULT NULL COMMENT '组织机构代码证'
    public static final String BANK_NAME = "bank_name"; // varchar(512) DEFAULT NULL COMMENT '开户行全名'
    public static final String BANK_ACCOUNT_NUMBER = "bank_account_number"; // varchar(512) DEFAULT NULL COMMENT '银行账户'
    public static final String BANK_ACCOUNT_HOLDER_NAME = "bank_account_holder_name"; // varchar(512) DEFAULT NULL COMMENT '账号所有人名称'
    public static final String BANK_BRANCH_PROVINCE = "bank_branch_province"; // varchar(100) DEFAULT NULL COMMENT '分支行所在省份'
    public static final String CITY = "city"; // varchar(36) DEFAULT NULL COMMENT '城市'
    public static final String PAYLOAD = "payload"; // blob DEFAULT NULL COMMENT '本次修改内容（JSON）'

}
