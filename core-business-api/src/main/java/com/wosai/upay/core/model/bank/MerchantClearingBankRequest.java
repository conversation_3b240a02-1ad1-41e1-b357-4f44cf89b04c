package com.wosai.upay.core.model.bank;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 商户清算行信息
 * @date 2025-01-20
 */
@Data
public class MerchantClearingBankRequest implements Serializable {
    private static final long serialVersionUID = 9218245950190852169L;
    /**
     * 清算行号
     */
    private String clearingNumber;
    /**
     * 查询起始时间, utc时间戳
     */
    private Long beginTime;
    /**
     * 每次查询的条数限制，默认1000条
     */
    private Integer limit = 1000;
}
