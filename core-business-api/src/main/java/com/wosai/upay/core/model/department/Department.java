package com.wosai.upay.core.model.department;

/**
 * Created by l<PERSON><PERSON><PERSON> on 07/03/2018.
 */
public class Department {

    public static final int STATUS_CLOSED = 0; //关闭
    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 2; //禁用

    public static final String SN = "sn"; //VARCHAR(36) NOT NULL COMMENT '部门号'
    public static final String NAME = "name"; // VARCHAR(128) NOT NULL COMMENT '部门名称'
    public static final String STATUS = "status"; // int DEFAULT NULL
    public static final String MERCHANT_ID = "merchant_id"; // VARCHAR(36) NOT NULL COMMENT '商户id'
    public static final String MERCHANT_SN = "merchant_sn"; // VARCHAR(36) NOT NULL COMMENT '商户sn'
    public static final String STORE_ID="store_id";

    public static final String DEPARTMENT_ID = "department_id";  //VARCHAR(36) NOT NULL COMMENT '部门id'
    public static final String DEPARTMENT_SN = "department_sn";  //VARCHAR(36) NOT NULL COMMENT '部门号'
    public static final String PARENT_DEPARTMENT_SN = "parent_department_sn";  //VARCHAR(36) NULL COMMENT '上级部门号'
    public static final String PATH = "path"; //VARCHAR(128) NOT NULL COMMENT '部门树上游节点，格式:department_sn,department_sn...'
    public static final String TYPE = "type";  //INT(11) NULL DEFAULT NULL COMMENT '状态：0：无门店；1:有门店'
}
