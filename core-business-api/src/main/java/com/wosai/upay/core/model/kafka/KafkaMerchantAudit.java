/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class KafkaMerchantAudit extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -3982841692158078156L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"KafkaMerchantAudit\",\"namespace\":\"com.wosai.upay.core.model.kafka\",\"fields\":[{\"name\":\"merchant_id\",\"type\":\"string\"},{\"name\":\"status\",\"type\":\"int\"},{\"name\":\"submit_platform\",\"type\":\"string\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<KafkaMerchantAudit> ENCODER =
      new BinaryMessageEncoder<KafkaMerchantAudit>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<KafkaMerchantAudit> DECODER =
      new BinaryMessageDecoder<KafkaMerchantAudit>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<KafkaMerchantAudit> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<KafkaMerchantAudit> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<KafkaMerchantAudit>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this KafkaMerchantAudit to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a KafkaMerchantAudit from a ByteBuffer. */
  public static KafkaMerchantAudit fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public CharSequence merchant_id;
  @Deprecated public int status;
  @Deprecated public CharSequence submit_platform;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public KafkaMerchantAudit() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param status The new value for status
   * @param submit_platform The new value for submit_platform
   */
  public KafkaMerchantAudit(CharSequence merchant_id, Integer status, CharSequence submit_platform) {
    this.merchant_id = merchant_id;
    this.status = status;
    this.submit_platform = submit_platform;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return status;
    case 2: return submit_platform;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: merchant_id = (CharSequence)value$; break;
    case 1: status = (Integer)value$; break;
    case 2: submit_platform = (CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'submit_platform' field.
   * @return The value of the 'submit_platform' field.
   */
  public CharSequence getSubmitPlatform() {
    return submit_platform;
  }

  /**
   * Sets the value of the 'submit_platform' field.
   * @param value the value to set.
   */
  public void setSubmitPlatform(CharSequence value) {
    this.submit_platform = value;
  }

  /**
   * Creates a new KafkaMerchantAudit RecordBuilder.
   * @return A new KafkaMerchantAudit RecordBuilder
   */
  public static Builder newBuilder() {
    return new Builder();
  }

  /**
   * Creates a new KafkaMerchantAudit RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new KafkaMerchantAudit RecordBuilder
   */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }

  /**
   * Creates a new KafkaMerchantAudit RecordBuilder by copying an existing KafkaMerchantAudit instance.
   * @param other The existing instance to copy.
   * @return A new KafkaMerchantAudit RecordBuilder
   */
  public static Builder newBuilder(KafkaMerchantAudit other) {
    return new Builder(other);
  }

  /**
   * RecordBuilder for KafkaMerchantAudit instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<KafkaMerchantAudit>
    implements org.apache.avro.data.RecordBuilder<KafkaMerchantAudit> {

    private CharSequence merchant_id;
    private int status;
    private CharSequence submit_platform;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.status)) {
        this.status = data().deepCopy(fields()[1].schema(), other.status);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.submit_platform)) {
        this.submit_platform = data().deepCopy(fields()[2].schema(), other.submit_platform);
        fieldSetFlags()[2] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing KafkaMerchantAudit instance
     * @param other The existing instance to copy.
     */
    private Builder(KafkaMerchantAudit other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.status)) {
        this.status = data().deepCopy(fields()[1].schema(), other.status);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.submit_platform)) {
        this.submit_platform = data().deepCopy(fields()[2].schema(), other.submit_platform);
        fieldSetFlags()[2] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public Builder setMerchantId(CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public Builder setStatus(int value) {
      validate(fields()[1], value);
      this.status = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public Builder clearStatus() {
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'submit_platform' field.
      * @return The value.
      */
    public CharSequence getSubmitPlatform() {
      return submit_platform;
    }

    /**
      * Sets the value of the 'submit_platform' field.
      * @param value The value of 'submit_platform'.
      * @return This builder.
      */
    public Builder setSubmitPlatform(CharSequence value) {
      validate(fields()[2], value);
      this.submit_platform = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'submit_platform' field has been set.
      * @return True if the 'submit_platform' field has been set, false otherwise.
      */
    public boolean hasSubmitPlatform() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'submit_platform' field.
      * @return This builder.
      */
    public Builder clearSubmitPlatform() {
      submit_platform = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public KafkaMerchantAudit build() {
      try {
        KafkaMerchantAudit record = new KafkaMerchantAudit();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (CharSequence) defaultValue(fields()[0]);
        record.status = fieldSetFlags()[1] ? this.status : (Integer) defaultValue(fields()[1]);
        record.submit_platform = fieldSetFlags()[2] ? this.submit_platform : (CharSequence) defaultValue(fields()[2]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<KafkaMerchantAudit>
    WRITER$ = (org.apache.avro.io.DatumWriter<KafkaMerchantAudit>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<KafkaMerchantAudit>
    READER$ = (org.apache.avro.io.DatumReader<KafkaMerchantAudit>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
