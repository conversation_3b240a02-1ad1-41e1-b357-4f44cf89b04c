/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class KafkaStore extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 81665304204001893L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"KafkaStore\",\"namespace\":\"com.wosai.upay.core.model.kafka\",\"fields\":[{\"name\":\"id\",\"type\":\"string\"},{\"name\":\"sn\",\"type\":\"string\"},{\"name\":\"name\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_id\",\"type\":\"string\"},{\"name\":\"ctime\",\"type\":\"long\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<KafkaStore> ENCODER =
      new BinaryMessageEncoder<KafkaStore>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<KafkaStore> DECODER =
      new BinaryMessageDecoder<KafkaStore>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<KafkaStore> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<KafkaStore> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<KafkaStore>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this KafkaStore to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a KafkaStore from a ByteBuffer. */
  public static KafkaStore fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public CharSequence id;
  @Deprecated public CharSequence sn;
  @Deprecated public CharSequence name;
  @Deprecated public CharSequence merchant_id;
  @Deprecated public long ctime;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public KafkaStore() {}

  /**
   * All-args constructor.
   * @param id The new value for id
   * @param sn The new value for sn
   * @param name The new value for name
   * @param merchant_id The new value for merchant_id
   * @param ctime The new value for ctime
   */
  public KafkaStore(CharSequence id, CharSequence sn, CharSequence name, CharSequence merchant_id, Long ctime) {
    this.id = id;
    this.sn = sn;
    this.name = name;
    this.merchant_id = merchant_id;
    this.ctime = ctime;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return sn;
    case 2: return name;
    case 3: return merchant_id;
    case 4: return ctime;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: id = (CharSequence)value$; break;
    case 1: sn = (CharSequence)value$; break;
    case 2: name = (CharSequence)value$; break;
    case 3: merchant_id = (CharSequence)value$; break;
    case 4: ctime = (Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return The value of the 'id' field.
   */
  public CharSequence getId() {
    return id;
  }

  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(CharSequence value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'sn' field.
   * @return The value of the 'sn' field.
   */
  public CharSequence getSn() {
    return sn;
  }

  /**
   * Sets the value of the 'sn' field.
   * @param value the value to set.
   */
  public void setSn(CharSequence value) {
    this.sn = value;
  }

  /**
   * Gets the value of the 'name' field.
   * @return The value of the 'name' field.
   */
  public CharSequence getName() {
    return name;
  }

  /**
   * Sets the value of the 'name' field.
   * @param value the value to set.
   */
  public void setName(CharSequence value) {
    this.name = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public Long getCtime() {
    return ctime;
  }

  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(Long value) {
    this.ctime = value;
  }

  /**
   * Creates a new KafkaStore RecordBuilder.
   * @return A new KafkaStore RecordBuilder
   */
  public static Builder newBuilder() {
    return new Builder();
  }

  /**
   * Creates a new KafkaStore RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new KafkaStore RecordBuilder
   */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }

  /**
   * Creates a new KafkaStore RecordBuilder by copying an existing KafkaStore instance.
   * @param other The existing instance to copy.
   * @return A new KafkaStore RecordBuilder
   */
  public static Builder newBuilder(KafkaStore other) {
    return new Builder(other);
  }

  /**
   * RecordBuilder for KafkaStore instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<KafkaStore>
    implements org.apache.avro.data.RecordBuilder<KafkaStore> {

    private CharSequence id;
    private CharSequence sn;
    private CharSequence name;
    private CharSequence merchant_id;
    private long ctime;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.sn)) {
        this.sn = data().deepCopy(fields()[1].schema(), other.sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.name)) {
        this.name = data().deepCopy(fields()[2].schema(), other.name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[3].schema(), other.merchant_id);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.ctime)) {
        this.ctime = data().deepCopy(fields()[4].schema(), other.ctime);
        fieldSetFlags()[4] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing KafkaStore instance
     * @param other The existing instance to copy.
     */
    private Builder(KafkaStore other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.sn)) {
        this.sn = data().deepCopy(fields()[1].schema(), other.sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.name)) {
        this.name = data().deepCopy(fields()[2].schema(), other.name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[3].schema(), other.merchant_id);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.ctime)) {
        this.ctime = data().deepCopy(fields()[4].schema(), other.ctime);
        fieldSetFlags()[4] = true;
      }
    }

    /**
      * Gets the value of the 'id' field.
      * @return The value.
      */
    public CharSequence getId() {
      return id;
    }

    /**
      * Sets the value of the 'id' field.
      * @param value The value of 'id'.
      * @return This builder.
      */
    public Builder setId(CharSequence value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * @return This builder.
      */
    public Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'sn' field.
      * @return The value.
      */
    public CharSequence getSn() {
      return sn;
    }

    /**
      * Sets the value of the 'sn' field.
      * @param value The value of 'sn'.
      * @return This builder.
      */
    public Builder setSn(CharSequence value) {
      validate(fields()[1], value);
      this.sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'sn' field has been set.
      * @return True if the 'sn' field has been set, false otherwise.
      */
    public boolean hasSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'sn' field.
      * @return This builder.
      */
    public Builder clearSn() {
      sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'name' field.
      * @return The value.
      */
    public CharSequence getName() {
      return name;
    }

    /**
      * Sets the value of the 'name' field.
      * @param value The value of 'name'.
      * @return This builder.
      */
    public Builder setName(CharSequence value) {
      validate(fields()[2], value);
      this.name = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'name' field has been set.
      * @return True if the 'name' field has been set, false otherwise.
      */
    public boolean hasName() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'name' field.
      * @return This builder.
      */
    public Builder clearName() {
      name = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public Builder setMerchantId(CharSequence value) {
      validate(fields()[3], value);
      this.merchant_id = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public Long getCtime() {
      return ctime;
    }

    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public Builder setCtime(long value) {
      validate(fields()[4], value);
      this.ctime = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public Builder clearCtime() {
      fieldSetFlags()[4] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public KafkaStore build() {
      try {
        KafkaStore record = new KafkaStore();
        record.id = fieldSetFlags()[0] ? this.id : (CharSequence) defaultValue(fields()[0]);
        record.sn = fieldSetFlags()[1] ? this.sn : (CharSequence) defaultValue(fields()[1]);
        record.name = fieldSetFlags()[2] ? this.name : (CharSequence) defaultValue(fields()[2]);
        record.merchant_id = fieldSetFlags()[3] ? this.merchant_id : (CharSequence) defaultValue(fields()[3]);
        record.ctime = fieldSetFlags()[4] ? this.ctime : (Long) defaultValue(fields()[4]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<KafkaStore>
    WRITER$ = (org.apache.avro.io.DatumWriter<KafkaStore>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<KafkaStore>
    READER$ = (org.apache.avro.io.DatumReader<KafkaStore>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
