/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class KafkaTerminal extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 8684148888752378512L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"KafkaTerminal\",\"namespace\":\"com.wosai.upay.core.model.kafka\",\"fields\":[{\"name\":\"id\",\"type\":\"string\"},{\"name\":\"sn\",\"type\":\"string\"},{\"name\":\"device_fingerprint\",\"type\":[\"string\",\"null\"]},{\"name\":\"type\",\"type\":\"int\"},{\"name\":\"status\",\"type\":\"int\"},{\"name\":\"code\",\"type\":[\"string\",\"null\"]},{\"name\":\"store_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"vendor_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"vendor_app_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"vendor_app_appid\",\"type\":[\"string\",\"null\"]},{\"name\":\"ctime\",\"type\":\"long\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<KafkaTerminal> ENCODER =
      new BinaryMessageEncoder<KafkaTerminal>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<KafkaTerminal> DECODER =
      new BinaryMessageDecoder<KafkaTerminal>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<KafkaTerminal> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<KafkaTerminal> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<KafkaTerminal>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this KafkaTerminal to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a KafkaTerminal from a ByteBuffer. */
  public static KafkaTerminal fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public CharSequence id;
  @Deprecated public CharSequence sn;
  @Deprecated public CharSequence device_fingerprint;
  @Deprecated public int type;
  @Deprecated public int status;
  @Deprecated public CharSequence code;
  @Deprecated public CharSequence store_id;
  @Deprecated public CharSequence merchant_id;
  @Deprecated public CharSequence vendor_id;
  @Deprecated public CharSequence vendor_app_id;
  @Deprecated public CharSequence vendor_app_appid;
  @Deprecated public long ctime;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public KafkaTerminal() {}

  /**
   * All-args constructor.
   * @param id The new value for id
   * @param sn The new value for sn
   * @param device_fingerprint The new value for device_fingerprint
   * @param type The new value for type
   * @param status The new value for status
   * @param code The new value for code
   * @param store_id The new value for store_id
   * @param merchant_id The new value for merchant_id
   * @param vendor_id The new value for vendor_id
   * @param vendor_app_id The new value for vendor_app_id
   * @param vendor_app_appid The new value for vendor_app_appid
   * @param ctime The new value for ctime
   */
  public KafkaTerminal(CharSequence id, CharSequence sn, CharSequence device_fingerprint, Integer type, Integer status, CharSequence code, CharSequence store_id, CharSequence merchant_id, CharSequence vendor_id, CharSequence vendor_app_id, CharSequence vendor_app_appid, Long ctime) {
    this.id = id;
    this.sn = sn;
    this.device_fingerprint = device_fingerprint;
    this.type = type;
    this.status = status;
    this.code = code;
    this.store_id = store_id;
    this.merchant_id = merchant_id;
    this.vendor_id = vendor_id;
    this.vendor_app_id = vendor_app_id;
    this.vendor_app_appid = vendor_app_appid;
    this.ctime = ctime;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return sn;
    case 2: return device_fingerprint;
    case 3: return type;
    case 4: return status;
    case 5: return code;
    case 6: return store_id;
    case 7: return merchant_id;
    case 8: return vendor_id;
    case 9: return vendor_app_id;
    case 10: return vendor_app_appid;
    case 11: return ctime;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: id = (CharSequence)value$; break;
    case 1: sn = (CharSequence)value$; break;
    case 2: device_fingerprint = (CharSequence)value$; break;
    case 3: type = (Integer)value$; break;
    case 4: status = (Integer)value$; break;
    case 5: code = (CharSequence)value$; break;
    case 6: store_id = (CharSequence)value$; break;
    case 7: merchant_id = (CharSequence)value$; break;
    case 8: vendor_id = (CharSequence)value$; break;
    case 9: vendor_app_id = (CharSequence)value$; break;
    case 10: vendor_app_appid = (CharSequence)value$; break;
    case 11: ctime = (Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return The value of the 'id' field.
   */
  public CharSequence getId() {
    return id;
  }

  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(CharSequence value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'sn' field.
   * @return The value of the 'sn' field.
   */
  public CharSequence getSn() {
    return sn;
  }

  /**
   * Sets the value of the 'sn' field.
   * @param value the value to set.
   */
  public void setSn(CharSequence value) {
    this.sn = value;
  }

  /**
   * Gets the value of the 'device_fingerprint' field.
   * @return The value of the 'device_fingerprint' field.
   */
  public CharSequence getDeviceFingerprint() {
    return device_fingerprint;
  }

  /**
   * Sets the value of the 'device_fingerprint' field.
   * @param value the value to set.
   */
  public void setDeviceFingerprint(CharSequence value) {
    this.device_fingerprint = value;
  }

  /**
   * Gets the value of the 'type' field.
   * @return The value of the 'type' field.
   */
  public Integer getType() {
    return type;
  }

  /**
   * Sets the value of the 'type' field.
   * @param value the value to set.
   */
  public void setType(Integer value) {
    this.type = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'code' field.
   * @return The value of the 'code' field.
   */
  public CharSequence getCode() {
    return code;
  }

  /**
   * Sets the value of the 'code' field.
   * @param value the value to set.
   */
  public void setCode(CharSequence value) {
    this.code = value;
  }

  /**
   * Gets the value of the 'store_id' field.
   * @return The value of the 'store_id' field.
   */
  public CharSequence getStoreId() {
    return store_id;
  }

  /**
   * Sets the value of the 'store_id' field.
   * @param value the value to set.
   */
  public void setStoreId(CharSequence value) {
    this.store_id = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'vendor_id' field.
   * @return The value of the 'vendor_id' field.
   */
  public CharSequence getVendorId() {
    return vendor_id;
  }

  /**
   * Sets the value of the 'vendor_id' field.
   * @param value the value to set.
   */
  public void setVendorId(CharSequence value) {
    this.vendor_id = value;
  }

  /**
   * Gets the value of the 'vendor_app_id' field.
   * @return The value of the 'vendor_app_id' field.
   */
  public CharSequence getVendorAppId() {
    return vendor_app_id;
  }

  /**
   * Sets the value of the 'vendor_app_id' field.
   * @param value the value to set.
   */
  public void setVendorAppId(CharSequence value) {
    this.vendor_app_id = value;
  }

  /**
   * Gets the value of the 'vendor_app_appid' field.
   * @return The value of the 'vendor_app_appid' field.
   */
  public CharSequence getVendorAppAppid() {
    return vendor_app_appid;
  }

  /**
   * Sets the value of the 'vendor_app_appid' field.
   * @param value the value to set.
   */
  public void setVendorAppAppid(CharSequence value) {
    this.vendor_app_appid = value;
  }

  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public Long getCtime() {
    return ctime;
  }

  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(Long value) {
    this.ctime = value;
  }

  /**
   * Creates a new KafkaTerminal RecordBuilder.
   * @return A new KafkaTerminal RecordBuilder
   */
  public static Builder newBuilder() {
    return new Builder();
  }

  /**
   * Creates a new KafkaTerminal RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new KafkaTerminal RecordBuilder
   */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }

  /**
   * Creates a new KafkaTerminal RecordBuilder by copying an existing KafkaTerminal instance.
   * @param other The existing instance to copy.
   * @return A new KafkaTerminal RecordBuilder
   */
  public static Builder newBuilder(KafkaTerminal other) {
    return new Builder(other);
  }

  /**
   * RecordBuilder for KafkaTerminal instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<KafkaTerminal>
    implements org.apache.avro.data.RecordBuilder<KafkaTerminal> {

    private CharSequence id;
    private CharSequence sn;
    private CharSequence device_fingerprint;
    private int type;
    private int status;
    private CharSequence code;
    private CharSequence store_id;
    private CharSequence merchant_id;
    private CharSequence vendor_id;
    private CharSequence vendor_app_id;
    private CharSequence vendor_app_appid;
    private long ctime;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.sn)) {
        this.sn = data().deepCopy(fields()[1].schema(), other.sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.device_fingerprint)) {
        this.device_fingerprint = data().deepCopy(fields()[2].schema(), other.device_fingerprint);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.type)) {
        this.type = data().deepCopy(fields()[3].schema(), other.type);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.status)) {
        this.status = data().deepCopy(fields()[4].schema(), other.status);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.code)) {
        this.code = data().deepCopy(fields()[5].schema(), other.code);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.store_id)) {
        this.store_id = data().deepCopy(fields()[6].schema(), other.store_id);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[7].schema(), other.merchant_id);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.vendor_id)) {
        this.vendor_id = data().deepCopy(fields()[8].schema(), other.vendor_id);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.vendor_app_id)) {
        this.vendor_app_id = data().deepCopy(fields()[9].schema(), other.vendor_app_id);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.vendor_app_appid)) {
        this.vendor_app_appid = data().deepCopy(fields()[10].schema(), other.vendor_app_appid);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.ctime)) {
        this.ctime = data().deepCopy(fields()[11].schema(), other.ctime);
        fieldSetFlags()[11] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing KafkaTerminal instance
     * @param other The existing instance to copy.
     */
    private Builder(KafkaTerminal other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.sn)) {
        this.sn = data().deepCopy(fields()[1].schema(), other.sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.device_fingerprint)) {
        this.device_fingerprint = data().deepCopy(fields()[2].schema(), other.device_fingerprint);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.type)) {
        this.type = data().deepCopy(fields()[3].schema(), other.type);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.status)) {
        this.status = data().deepCopy(fields()[4].schema(), other.status);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.code)) {
        this.code = data().deepCopy(fields()[5].schema(), other.code);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.store_id)) {
        this.store_id = data().deepCopy(fields()[6].schema(), other.store_id);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[7].schema(), other.merchant_id);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.vendor_id)) {
        this.vendor_id = data().deepCopy(fields()[8].schema(), other.vendor_id);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.vendor_app_id)) {
        this.vendor_app_id = data().deepCopy(fields()[9].schema(), other.vendor_app_id);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.vendor_app_appid)) {
        this.vendor_app_appid = data().deepCopy(fields()[10].schema(), other.vendor_app_appid);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.ctime)) {
        this.ctime = data().deepCopy(fields()[11].schema(), other.ctime);
        fieldSetFlags()[11] = true;
      }
    }

    /**
      * Gets the value of the 'id' field.
      * @return The value.
      */
    public CharSequence getId() {
      return id;
    }

    /**
      * Sets the value of the 'id' field.
      * @param value The value of 'id'.
      * @return This builder.
      */
    public Builder setId(CharSequence value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * @return This builder.
      */
    public Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'sn' field.
      * @return The value.
      */
    public CharSequence getSn() {
      return sn;
    }

    /**
      * Sets the value of the 'sn' field.
      * @param value The value of 'sn'.
      * @return This builder.
      */
    public Builder setSn(CharSequence value) {
      validate(fields()[1], value);
      this.sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'sn' field has been set.
      * @return True if the 'sn' field has been set, false otherwise.
      */
    public boolean hasSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'sn' field.
      * @return This builder.
      */
    public Builder clearSn() {
      sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'device_fingerprint' field.
      * @return The value.
      */
    public CharSequence getDeviceFingerprint() {
      return device_fingerprint;
    }

    /**
      * Sets the value of the 'device_fingerprint' field.
      * @param value The value of 'device_fingerprint'.
      * @return This builder.
      */
    public Builder setDeviceFingerprint(CharSequence value) {
      validate(fields()[2], value);
      this.device_fingerprint = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'device_fingerprint' field has been set.
      * @return True if the 'device_fingerprint' field has been set, false otherwise.
      */
    public boolean hasDeviceFingerprint() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'device_fingerprint' field.
      * @return This builder.
      */
    public Builder clearDeviceFingerprint() {
      device_fingerprint = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'type' field.
      * @return The value.
      */
    public Integer getType() {
      return type;
    }

    /**
      * Sets the value of the 'type' field.
      * @param value The value of 'type'.
      * @return This builder.
      */
    public Builder setType(int value) {
      validate(fields()[3], value);
      this.type = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'type' field has been set.
      * @return True if the 'type' field has been set, false otherwise.
      */
    public boolean hasType() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'type' field.
      * @return This builder.
      */
    public Builder clearType() {
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public Builder setStatus(int value) {
      validate(fields()[4], value);
      this.status = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public Builder clearStatus() {
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'code' field.
      * @return The value.
      */
    public CharSequence getCode() {
      return code;
    }

    /**
      * Sets the value of the 'code' field.
      * @param value The value of 'code'.
      * @return This builder.
      */
    public Builder setCode(CharSequence value) {
      validate(fields()[5], value);
      this.code = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'code' field has been set.
      * @return True if the 'code' field has been set, false otherwise.
      */
    public boolean hasCode() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'code' field.
      * @return This builder.
      */
    public Builder clearCode() {
      code = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'store_id' field.
      * @return The value.
      */
    public CharSequence getStoreId() {
      return store_id;
    }

    /**
      * Sets the value of the 'store_id' field.
      * @param value The value of 'store_id'.
      * @return This builder.
      */
    public Builder setStoreId(CharSequence value) {
      validate(fields()[6], value);
      this.store_id = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'store_id' field has been set.
      * @return True if the 'store_id' field has been set, false otherwise.
      */
    public boolean hasStoreId() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'store_id' field.
      * @return This builder.
      */
    public Builder clearStoreId() {
      store_id = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public Builder setMerchantId(CharSequence value) {
      validate(fields()[7], value);
      this.merchant_id = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'vendor_id' field.
      * @return The value.
      */
    public CharSequence getVendorId() {
      return vendor_id;
    }

    /**
      * Sets the value of the 'vendor_id' field.
      * @param value The value of 'vendor_id'.
      * @return This builder.
      */
    public Builder setVendorId(CharSequence value) {
      validate(fields()[8], value);
      this.vendor_id = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'vendor_id' field has been set.
      * @return True if the 'vendor_id' field has been set, false otherwise.
      */
    public boolean hasVendorId() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'vendor_id' field.
      * @return This builder.
      */
    public Builder clearVendorId() {
      vendor_id = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'vendor_app_id' field.
      * @return The value.
      */
    public CharSequence getVendorAppId() {
      return vendor_app_id;
    }

    /**
      * Sets the value of the 'vendor_app_id' field.
      * @param value The value of 'vendor_app_id'.
      * @return This builder.
      */
    public Builder setVendorAppId(CharSequence value) {
      validate(fields()[9], value);
      this.vendor_app_id = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'vendor_app_id' field has been set.
      * @return True if the 'vendor_app_id' field has been set, false otherwise.
      */
    public boolean hasVendorAppId() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'vendor_app_id' field.
      * @return This builder.
      */
    public Builder clearVendorAppId() {
      vendor_app_id = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'vendor_app_appid' field.
      * @return The value.
      */
    public CharSequence getVendorAppAppid() {
      return vendor_app_appid;
    }

    /**
      * Sets the value of the 'vendor_app_appid' field.
      * @param value The value of 'vendor_app_appid'.
      * @return This builder.
      */
    public Builder setVendorAppAppid(CharSequence value) {
      validate(fields()[10], value);
      this.vendor_app_appid = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'vendor_app_appid' field has been set.
      * @return True if the 'vendor_app_appid' field has been set, false otherwise.
      */
    public boolean hasVendorAppAppid() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'vendor_app_appid' field.
      * @return This builder.
      */
    public Builder clearVendorAppAppid() {
      vendor_app_appid = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public Long getCtime() {
      return ctime;
    }

    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public Builder setCtime(long value) {
      validate(fields()[11], value);
      this.ctime = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public Builder clearCtime() {
      fieldSetFlags()[11] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public KafkaTerminal build() {
      try {
        KafkaTerminal record = new KafkaTerminal();
        record.id = fieldSetFlags()[0] ? this.id : (CharSequence) defaultValue(fields()[0]);
        record.sn = fieldSetFlags()[1] ? this.sn : (CharSequence) defaultValue(fields()[1]);
        record.device_fingerprint = fieldSetFlags()[2] ? this.device_fingerprint : (CharSequence) defaultValue(fields()[2]);
        record.type = fieldSetFlags()[3] ? this.type : (Integer) defaultValue(fields()[3]);
        record.status = fieldSetFlags()[4] ? this.status : (Integer) defaultValue(fields()[4]);
        record.code = fieldSetFlags()[5] ? this.code : (CharSequence) defaultValue(fields()[5]);
        record.store_id = fieldSetFlags()[6] ? this.store_id : (CharSequence) defaultValue(fields()[6]);
        record.merchant_id = fieldSetFlags()[7] ? this.merchant_id : (CharSequence) defaultValue(fields()[7]);
        record.vendor_id = fieldSetFlags()[8] ? this.vendor_id : (CharSequence) defaultValue(fields()[8]);
        record.vendor_app_id = fieldSetFlags()[9] ? this.vendor_app_id : (CharSequence) defaultValue(fields()[9]);
        record.vendor_app_appid = fieldSetFlags()[10] ? this.vendor_app_appid : (CharSequence) defaultValue(fields()[10]);
        record.ctime = fieldSetFlags()[11] ? this.ctime : (Long) defaultValue(fields()[11]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<KafkaTerminal>
    WRITER$ = (org.apache.avro.io.DatumWriter<KafkaTerminal>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<KafkaTerminal>
    READER$ = (org.apache.avro.io.DatumReader<KafkaTerminal>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
