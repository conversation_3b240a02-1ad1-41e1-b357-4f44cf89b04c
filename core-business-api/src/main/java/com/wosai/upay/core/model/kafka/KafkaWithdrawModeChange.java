/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class KafkaWithdrawModeChange extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 1785770412587865031L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"KafkaWithdrawModeChange\",\"namespace\":\"com.wosai.upay.core.model.kafka\",\"fields\":[{\"name\":\"merchant_id\",\"type\":\"string\"},{\"name\":\"merchant_sn\",\"type\":\"string\"},{\"name\":\"merchant_name\",\"type\":[\"null\",\"string\"]},{\"name\":\"alias\",\"type\":[\"null\",\"string\"]},{\"name\":\"industry\",\"type\":[\"null\",\"string\"]},{\"name\":\"status\",\"type\":[\"int\"]},{\"name\":\"withdraw_mode\",\"type\":[\"int\"]},{\"name\":\"longitude\",\"type\":[\"null\",\"string\"]},{\"name\":\"latitude\",\"type\":[\"null\",\"string\"]},{\"name\":\"country\",\"type\":[\"null\",\"string\"]},{\"name\":\"province\",\"type\":[\"null\",\"string\"]},{\"name\":\"city\",\"type\":[\"null\",\"string\"]},{\"name\":\"district\",\"type\":[\"null\",\"string\"]},{\"name\":\"platform\",\"type\":[\"string\"]},{\"name\":\"operator\",\"type\":[\"string\"]},{\"name\":\"operatorId\",\"type\":[\"null\",\"string\"]},{\"name\":\"ctime\",\"type\":\"long\"},{\"name\":\"mtime\",\"type\":[\"null\",\"long\"]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<KafkaWithdrawModeChange> ENCODER =
      new BinaryMessageEncoder<KafkaWithdrawModeChange>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<KafkaWithdrawModeChange> DECODER =
      new BinaryMessageDecoder<KafkaWithdrawModeChange>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<KafkaWithdrawModeChange> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<KafkaWithdrawModeChange> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<KafkaWithdrawModeChange>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this KafkaWithdrawModeChange to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a KafkaWithdrawModeChange from a ByteBuffer. */
  public static KafkaWithdrawModeChange fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_name;
  @Deprecated public java.lang.CharSequence alias;
  @Deprecated public java.lang.CharSequence industry;
  @Deprecated public java.lang.Object status;
  @Deprecated public java.lang.Object withdraw_mode;
  @Deprecated public java.lang.CharSequence longitude;
  @Deprecated public java.lang.CharSequence latitude;
  @Deprecated public java.lang.CharSequence country;
  @Deprecated public java.lang.CharSequence province;
  @Deprecated public java.lang.CharSequence city;
  @Deprecated public java.lang.CharSequence district;
  @Deprecated public java.lang.Object platform;
  @Deprecated public java.lang.Object operator;
  @Deprecated public java.lang.CharSequence operatorId;
  @Deprecated public long ctime;
  @Deprecated public java.lang.Long mtime;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public KafkaWithdrawModeChange() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_name The new value for merchant_name
   * @param alias The new value for alias
   * @param industry The new value for industry
   * @param status The new value for status
   * @param withdraw_mode The new value for withdraw_mode
   * @param longitude The new value for longitude
   * @param latitude The new value for latitude
   * @param country The new value for country
   * @param province The new value for province
   * @param city The new value for city
   * @param district The new value for district
   * @param platform The new value for platform
   * @param operator The new value for operator
   * @param operatorId The new value for operatorId
   * @param ctime The new value for ctime
   * @param mtime The new value for mtime
   */
  public KafkaWithdrawModeChange(java.lang.CharSequence merchant_id, java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_name, java.lang.CharSequence alias, java.lang.CharSequence industry, java.lang.Object status, java.lang.Object withdraw_mode, java.lang.CharSequence longitude, java.lang.CharSequence latitude, java.lang.CharSequence country, java.lang.CharSequence province, java.lang.CharSequence city, java.lang.CharSequence district, java.lang.Object platform, java.lang.Object operator, java.lang.CharSequence operatorId, java.lang.Long ctime, java.lang.Long mtime) {
    this.merchant_id = merchant_id;
    this.merchant_sn = merchant_sn;
    this.merchant_name = merchant_name;
    this.alias = alias;
    this.industry = industry;
    this.status = status;
    this.withdraw_mode = withdraw_mode;
    this.longitude = longitude;
    this.latitude = latitude;
    this.country = country;
    this.province = province;
    this.city = city;
    this.district = district;
    this.platform = platform;
    this.operator = operator;
    this.operatorId = operatorId;
    this.ctime = ctime;
    this.mtime = mtime;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return merchant_sn;
    case 2: return merchant_name;
    case 3: return alias;
    case 4: return industry;
    case 5: return status;
    case 6: return withdraw_mode;
    case 7: return longitude;
    case 8: return latitude;
    case 9: return country;
    case 10: return province;
    case 11: return city;
    case 12: return district;
    case 13: return platform;
    case 14: return operator;
    case 15: return operatorId;
    case 16: return ctime;
    case 17: return mtime;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_id = (java.lang.CharSequence)value$; break;
    case 1: merchant_sn = (java.lang.CharSequence)value$; break;
    case 2: merchant_name = (java.lang.CharSequence)value$; break;
    case 3: alias = (java.lang.CharSequence)value$; break;
    case 4: industry = (java.lang.CharSequence)value$; break;
    case 5: status = (java.lang.Object)value$; break;
    case 6: withdraw_mode = (java.lang.Object)value$; break;
    case 7: longitude = (java.lang.CharSequence)value$; break;
    case 8: latitude = (java.lang.CharSequence)value$; break;
    case 9: country = (java.lang.CharSequence)value$; break;
    case 10: province = (java.lang.CharSequence)value$; break;
    case 11: city = (java.lang.CharSequence)value$; break;
    case 12: district = (java.lang.CharSequence)value$; break;
    case 13: platform = (java.lang.Object)value$; break;
    case 14: operator = (java.lang.Object)value$; break;
    case 15: operatorId = (java.lang.CharSequence)value$; break;
    case 16: ctime = (java.lang.Long)value$; break;
    case 17: mtime = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_name' field.
   * @return The value of the 'merchant_name' field.
   */
  public java.lang.CharSequence getMerchantName() {
    return merchant_name;
  }

  /**
   * Sets the value of the 'merchant_name' field.
   * @param value the value to set.
   */
  public void setMerchantName(java.lang.CharSequence value) {
    this.merchant_name = value;
  }

  /**
   * Gets the value of the 'alias' field.
   * @return The value of the 'alias' field.
   */
  public java.lang.CharSequence getAlias() {
    return alias;
  }

  /**
   * Sets the value of the 'alias' field.
   * @param value the value to set.
   */
  public void setAlias(java.lang.CharSequence value) {
    this.alias = value;
  }

  /**
   * Gets the value of the 'industry' field.
   * @return The value of the 'industry' field.
   */
  public java.lang.CharSequence getIndustry() {
    return industry;
  }

  /**
   * Sets the value of the 'industry' field.
   * @param value the value to set.
   */
  public void setIndustry(java.lang.CharSequence value) {
    this.industry = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public java.lang.Object getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(java.lang.Object value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'withdraw_mode' field.
   * @return The value of the 'withdraw_mode' field.
   */
  public java.lang.Object getWithdrawMode() {
    return withdraw_mode;
  }

  /**
   * Sets the value of the 'withdraw_mode' field.
   * @param value the value to set.
   */
  public void setWithdrawMode(java.lang.Object value) {
    this.withdraw_mode = value;
  }

  /**
   * Gets the value of the 'longitude' field.
   * @return The value of the 'longitude' field.
   */
  public java.lang.CharSequence getLongitude() {
    return longitude;
  }

  /**
   * Sets the value of the 'longitude' field.
   * @param value the value to set.
   */
  public void setLongitude(java.lang.CharSequence value) {
    this.longitude = value;
  }

  /**
   * Gets the value of the 'latitude' field.
   * @return The value of the 'latitude' field.
   */
  public java.lang.CharSequence getLatitude() {
    return latitude;
  }

  /**
   * Sets the value of the 'latitude' field.
   * @param value the value to set.
   */
  public void setLatitude(java.lang.CharSequence value) {
    this.latitude = value;
  }

  /**
   * Gets the value of the 'country' field.
   * @return The value of the 'country' field.
   */
  public java.lang.CharSequence getCountry() {
    return country;
  }

  /**
   * Sets the value of the 'country' field.
   * @param value the value to set.
   */
  public void setCountry(java.lang.CharSequence value) {
    this.country = value;
  }

  /**
   * Gets the value of the 'province' field.
   * @return The value of the 'province' field.
   */
  public java.lang.CharSequence getProvince() {
    return province;
  }

  /**
   * Sets the value of the 'province' field.
   * @param value the value to set.
   */
  public void setProvince(java.lang.CharSequence value) {
    this.province = value;
  }

  /**
   * Gets the value of the 'city' field.
   * @return The value of the 'city' field.
   */
  public java.lang.CharSequence getCity() {
    return city;
  }

  /**
   * Sets the value of the 'city' field.
   * @param value the value to set.
   */
  public void setCity(java.lang.CharSequence value) {
    this.city = value;
  }

  /**
   * Gets the value of the 'district' field.
   * @return The value of the 'district' field.
   */
  public java.lang.CharSequence getDistrict() {
    return district;
  }

  /**
   * Sets the value of the 'district' field.
   * @param value the value to set.
   */
  public void setDistrict(java.lang.CharSequence value) {
    this.district = value;
  }

  /**
   * Gets the value of the 'platform' field.
   * @return The value of the 'platform' field.
   */
  public java.lang.Object getPlatform() {
    return platform;
  }

  /**
   * Sets the value of the 'platform' field.
   * @param value the value to set.
   */
  public void setPlatform(java.lang.Object value) {
    this.platform = value;
  }

  /**
   * Gets the value of the 'operator' field.
   * @return The value of the 'operator' field.
   */
  public java.lang.Object getOperator() {
    return operator;
  }

  /**
   * Sets the value of the 'operator' field.
   * @param value the value to set.
   */
  public void setOperator(java.lang.Object value) {
    this.operator = value;
  }

  /**
   * Gets the value of the 'operatorId' field.
   * @return The value of the 'operatorId' field.
   */
  public java.lang.CharSequence getOperatorId() {
    return operatorId;
  }

  /**
   * Sets the value of the 'operatorId' field.
   * @param value the value to set.
   */
  public void setOperatorId(java.lang.CharSequence value) {
    this.operatorId = value;
  }

  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public java.lang.Long getCtime() {
    return ctime;
  }

  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(java.lang.Long value) {
    this.ctime = value;
  }

  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public java.lang.Long getMtime() {
    return mtime;
  }

  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(java.lang.Long value) {
    this.mtime = value;
  }

  /**
   * Creates a new KafkaWithdrawModeChange RecordBuilder.
   * @return A new KafkaWithdrawModeChange RecordBuilder
   */
  public static com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder newBuilder() {
    return new com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder();
  }

  /**
   * Creates a new KafkaWithdrawModeChange RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new KafkaWithdrawModeChange RecordBuilder
   */
  public static com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder newBuilder(com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder other) {
    return new com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder(other);
  }

  /**
   * Creates a new KafkaWithdrawModeChange RecordBuilder by copying an existing KafkaWithdrawModeChange instance.
   * @param other The existing instance to copy.
   * @return A new KafkaWithdrawModeChange RecordBuilder
   */
  public static com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder newBuilder(com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange other) {
    return new com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder(other);
  }

  /**
   * RecordBuilder for KafkaWithdrawModeChange instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<KafkaWithdrawModeChange>
    implements org.apache.avro.data.RecordBuilder<KafkaWithdrawModeChange> {

    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_name;
    private java.lang.CharSequence alias;
    private java.lang.CharSequence industry;
    private java.lang.Object status;
    private java.lang.Object withdraw_mode;
    private java.lang.CharSequence longitude;
    private java.lang.CharSequence latitude;
    private java.lang.CharSequence country;
    private java.lang.CharSequence province;
    private java.lang.CharSequence city;
    private java.lang.CharSequence district;
    private java.lang.Object platform;
    private java.lang.Object operator;
    private java.lang.CharSequence operatorId;
    private long ctime;
    private java.lang.Long mtime;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_name)) {
        this.merchant_name = data().deepCopy(fields()[2].schema(), other.merchant_name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.alias)) {
        this.alias = data().deepCopy(fields()[3].schema(), other.alias);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.industry)) {
        this.industry = data().deepCopy(fields()[4].schema(), other.industry);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.status)) {
        this.status = data().deepCopy(fields()[5].schema(), other.status);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.withdraw_mode)) {
        this.withdraw_mode = data().deepCopy(fields()[6].schema(), other.withdraw_mode);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.longitude)) {
        this.longitude = data().deepCopy(fields()[7].schema(), other.longitude);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.latitude)) {
        this.latitude = data().deepCopy(fields()[8].schema(), other.latitude);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.country)) {
        this.country = data().deepCopy(fields()[9].schema(), other.country);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.province)) {
        this.province = data().deepCopy(fields()[10].schema(), other.province);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.city)) {
        this.city = data().deepCopy(fields()[11].schema(), other.city);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.district)) {
        this.district = data().deepCopy(fields()[12].schema(), other.district);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.platform)) {
        this.platform = data().deepCopy(fields()[13].schema(), other.platform);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.operator)) {
        this.operator = data().deepCopy(fields()[14].schema(), other.operator);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.operatorId)) {
        this.operatorId = data().deepCopy(fields()[15].schema(), other.operatorId);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.ctime)) {
        this.ctime = data().deepCopy(fields()[16].schema(), other.ctime);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.mtime)) {
        this.mtime = data().deepCopy(fields()[17].schema(), other.mtime);
        fieldSetFlags()[17] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing KafkaWithdrawModeChange instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_name)) {
        this.merchant_name = data().deepCopy(fields()[2].schema(), other.merchant_name);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.alias)) {
        this.alias = data().deepCopy(fields()[3].schema(), other.alias);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.industry)) {
        this.industry = data().deepCopy(fields()[4].schema(), other.industry);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.status)) {
        this.status = data().deepCopy(fields()[5].schema(), other.status);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.withdraw_mode)) {
        this.withdraw_mode = data().deepCopy(fields()[6].schema(), other.withdraw_mode);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.longitude)) {
        this.longitude = data().deepCopy(fields()[7].schema(), other.longitude);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.latitude)) {
        this.latitude = data().deepCopy(fields()[8].schema(), other.latitude);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.country)) {
        this.country = data().deepCopy(fields()[9].schema(), other.country);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.province)) {
        this.province = data().deepCopy(fields()[10].schema(), other.province);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.city)) {
        this.city = data().deepCopy(fields()[11].schema(), other.city);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.district)) {
        this.district = data().deepCopy(fields()[12].schema(), other.district);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.platform)) {
        this.platform = data().deepCopy(fields()[13].schema(), other.platform);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.operator)) {
        this.operator = data().deepCopy(fields()[14].schema(), other.operator);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.operatorId)) {
        this.operatorId = data().deepCopy(fields()[15].schema(), other.operatorId);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.ctime)) {
        this.ctime = data().deepCopy(fields()[16].schema(), other.ctime);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.mtime)) {
        this.mtime = data().deepCopy(fields()[17].schema(), other.mtime);
        fieldSetFlags()[17] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_name' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantName() {
      return merchant_name;
    }

    /**
      * Sets the value of the 'merchant_name' field.
      * @param value The value of 'merchant_name'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setMerchantName(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.merchant_name = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_name' field has been set.
      * @return True if the 'merchant_name' field has been set, false otherwise.
      */
    public boolean hasMerchantName() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'merchant_name' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearMerchantName() {
      merchant_name = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'alias' field.
      * @return The value.
      */
    public java.lang.CharSequence getAlias() {
      return alias;
    }

    /**
      * Sets the value of the 'alias' field.
      * @param value The value of 'alias'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setAlias(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.alias = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'alias' field has been set.
      * @return True if the 'alias' field has been set, false otherwise.
      */
    public boolean hasAlias() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'alias' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearAlias() {
      alias = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'industry' field.
      * @return The value.
      */
    public java.lang.CharSequence getIndustry() {
      return industry;
    }

    /**
      * Sets the value of the 'industry' field.
      * @param value The value of 'industry'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setIndustry(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.industry = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'industry' field has been set.
      * @return True if the 'industry' field has been set, false otherwise.
      */
    public boolean hasIndustry() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'industry' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearIndustry() {
      industry = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public java.lang.Object getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setStatus(java.lang.Object value) {
      validate(fields()[5], value);
      this.status = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearStatus() {
      status = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'withdraw_mode' field.
      * @return The value.
      */
    public java.lang.Object getWithdrawMode() {
      return withdraw_mode;
    }

    /**
      * Sets the value of the 'withdraw_mode' field.
      * @param value The value of 'withdraw_mode'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setWithdrawMode(java.lang.Object value) {
      validate(fields()[6], value);
      this.withdraw_mode = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'withdraw_mode' field has been set.
      * @return True if the 'withdraw_mode' field has been set, false otherwise.
      */
    public boolean hasWithdrawMode() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'withdraw_mode' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearWithdrawMode() {
      withdraw_mode = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'longitude' field.
      * @return The value.
      */
    public java.lang.CharSequence getLongitude() {
      return longitude;
    }

    /**
      * Sets the value of the 'longitude' field.
      * @param value The value of 'longitude'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setLongitude(java.lang.CharSequence value) {
      validate(fields()[7], value);
      this.longitude = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'longitude' field has been set.
      * @return True if the 'longitude' field has been set, false otherwise.
      */
    public boolean hasLongitude() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'longitude' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearLongitude() {
      longitude = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'latitude' field.
      * @return The value.
      */
    public java.lang.CharSequence getLatitude() {
      return latitude;
    }

    /**
      * Sets the value of the 'latitude' field.
      * @param value The value of 'latitude'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setLatitude(java.lang.CharSequence value) {
      validate(fields()[8], value);
      this.latitude = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'latitude' field has been set.
      * @return True if the 'latitude' field has been set, false otherwise.
      */
    public boolean hasLatitude() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'latitude' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearLatitude() {
      latitude = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'country' field.
      * @return The value.
      */
    public java.lang.CharSequence getCountry() {
      return country;
    }

    /**
      * Sets the value of the 'country' field.
      * @param value The value of 'country'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setCountry(java.lang.CharSequence value) {
      validate(fields()[9], value);
      this.country = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'country' field has been set.
      * @return True if the 'country' field has been set, false otherwise.
      */
    public boolean hasCountry() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'country' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearCountry() {
      country = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'province' field.
      * @return The value.
      */
    public java.lang.CharSequence getProvince() {
      return province;
    }

    /**
      * Sets the value of the 'province' field.
      * @param value The value of 'province'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setProvince(java.lang.CharSequence value) {
      validate(fields()[10], value);
      this.province = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'province' field has been set.
      * @return True if the 'province' field has been set, false otherwise.
      */
    public boolean hasProvince() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'province' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearProvince() {
      province = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'city' field.
      * @return The value.
      */
    public java.lang.CharSequence getCity() {
      return city;
    }

    /**
      * Sets the value of the 'city' field.
      * @param value The value of 'city'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setCity(java.lang.CharSequence value) {
      validate(fields()[11], value);
      this.city = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'city' field has been set.
      * @return True if the 'city' field has been set, false otherwise.
      */
    public boolean hasCity() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'city' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearCity() {
      city = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'district' field.
      * @return The value.
      */
    public java.lang.CharSequence getDistrict() {
      return district;
    }

    /**
      * Sets the value of the 'district' field.
      * @param value The value of 'district'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setDistrict(java.lang.CharSequence value) {
      validate(fields()[12], value);
      this.district = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'district' field has been set.
      * @return True if the 'district' field has been set, false otherwise.
      */
    public boolean hasDistrict() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'district' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearDistrict() {
      district = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'platform' field.
      * @return The value.
      */
    public java.lang.Object getPlatform() {
      return platform;
    }

    /**
      * Sets the value of the 'platform' field.
      * @param value The value of 'platform'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setPlatform(java.lang.Object value) {
      validate(fields()[13], value);
      this.platform = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'platform' field has been set.
      * @return True if the 'platform' field has been set, false otherwise.
      */
    public boolean hasPlatform() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'platform' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearPlatform() {
      platform = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'operator' field.
      * @return The value.
      */
    public java.lang.Object getOperator() {
      return operator;
    }

    /**
      * Sets the value of the 'operator' field.
      * @param value The value of 'operator'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setOperator(java.lang.Object value) {
      validate(fields()[14], value);
      this.operator = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'operator' field has been set.
      * @return True if the 'operator' field has been set, false otherwise.
      */
    public boolean hasOperator() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'operator' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearOperator() {
      operator = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'operatorId' field.
      * @return The value.
      */
    public java.lang.CharSequence getOperatorId() {
      return operatorId;
    }

    /**
      * Sets the value of the 'operatorId' field.
      * @param value The value of 'operatorId'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setOperatorId(java.lang.CharSequence value) {
      validate(fields()[15], value);
      this.operatorId = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'operatorId' field has been set.
      * @return True if the 'operatorId' field has been set, false otherwise.
      */
    public boolean hasOperatorId() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'operatorId' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearOperatorId() {
      operatorId = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public java.lang.Long getCtime() {
      return ctime;
    }

    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setCtime(long value) {
      validate(fields()[16], value);
      this.ctime = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearCtime() {
      fieldSetFlags()[16] = false;
      return this;
    }

    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public java.lang.Long getMtime() {
      return mtime;
    }

    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder setMtime(java.lang.Long value) {
      validate(fields()[17], value);
      this.mtime = value;
      fieldSetFlags()[17] = true;
      return this;
    }

    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[17];
    }


    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.KafkaWithdrawModeChange.Builder clearMtime() {
      mtime = null;
      fieldSetFlags()[17] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public KafkaWithdrawModeChange build() {
      try {
        KafkaWithdrawModeChange record = new KafkaWithdrawModeChange();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_sn = fieldSetFlags()[1] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.merchant_name = fieldSetFlags()[2] ? this.merchant_name : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.alias = fieldSetFlags()[3] ? this.alias : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.industry = fieldSetFlags()[4] ? this.industry : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.status = fieldSetFlags()[5] ? this.status : (java.lang.Object) defaultValue(fields()[5]);
        record.withdraw_mode = fieldSetFlags()[6] ? this.withdraw_mode : (java.lang.Object) defaultValue(fields()[6]);
        record.longitude = fieldSetFlags()[7] ? this.longitude : (java.lang.CharSequence) defaultValue(fields()[7]);
        record.latitude = fieldSetFlags()[8] ? this.latitude : (java.lang.CharSequence) defaultValue(fields()[8]);
        record.country = fieldSetFlags()[9] ? this.country : (java.lang.CharSequence) defaultValue(fields()[9]);
        record.province = fieldSetFlags()[10] ? this.province : (java.lang.CharSequence) defaultValue(fields()[10]);
        record.city = fieldSetFlags()[11] ? this.city : (java.lang.CharSequence) defaultValue(fields()[11]);
        record.district = fieldSetFlags()[12] ? this.district : (java.lang.CharSequence) defaultValue(fields()[12]);
        record.platform = fieldSetFlags()[13] ? this.platform : (java.lang.Object) defaultValue(fields()[13]);
        record.operator = fieldSetFlags()[14] ? this.operator : (java.lang.Object) defaultValue(fields()[14]);
        record.operatorId = fieldSetFlags()[15] ? this.operatorId : (java.lang.CharSequence) defaultValue(fields()[15]);
        record.ctime = fieldSetFlags()[16] ? this.ctime : (java.lang.Long) defaultValue(fields()[16]);
        record.mtime = fieldSetFlags()[17] ? this.mtime : (java.lang.Long) defaultValue(fields()[17]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<KafkaWithdrawModeChange>
    WRITER$ = (org.apache.avro.io.DatumWriter<KafkaWithdrawModeChange>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<KafkaWithdrawModeChange>
    READER$ = (org.apache.avro.io.DatumReader<KafkaWithdrawModeChange>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
