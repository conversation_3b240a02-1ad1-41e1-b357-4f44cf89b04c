/**
 * Autogenerated by Avro
 * 
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka;
@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MerchantUpdateBusinessLicense extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MerchantUpdateBusinessLicense\",\"namespace\":\"com.wosai.upay.core.model.kafka\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"merchant_type\",\"type\":\"string\",\"meta\":\"商户类型\"},{\"name\":\"merchant_business_license_type\",\"type\":\"string\",\"meta\":\"商户营业执照类型\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }
  @Deprecated public CharSequence merchant_sn;
  @Deprecated public CharSequence merchant_id;
  @Deprecated public CharSequence merchant_type;
  @Deprecated public CharSequence merchant_business_license_type;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>. 
   */
  public MerchantUpdateBusinessLicense() {}

  /**
   * All-args constructor.
   */
  public MerchantUpdateBusinessLicense(CharSequence merchant_sn, CharSequence merchant_id, CharSequence merchant_type, CharSequence merchant_business_license_type) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.merchant_type = merchant_type;
    this.merchant_business_license_type = merchant_business_license_type;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call. 
  public Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return merchant_type;
    case 3: return merchant_business_license_type;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }
  // Used by DatumReader.  Applications should not call. 
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: merchant_sn = (CharSequence)value$; break;
    case 1: merchant_id = (CharSequence)value$; break;
    case 2: merchant_type = (CharSequence)value$; break;
    case 3: merchant_business_license_type = (CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   */
  public CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   */
  public CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_type' field.
   */
  public CharSequence getMerchantType() {
    return merchant_type;
  }

  /**
   * Sets the value of the 'merchant_type' field.
   * @param value the value to set.
   */
  public void setMerchantType(CharSequence value) {
    this.merchant_type = value;
  }

  /**
   * Gets the value of the 'merchant_business_license_type' field.
   */
  public CharSequence getMerchantBusinessLicenseType() {
    return merchant_business_license_type;
  }

  /**
   * Sets the value of the 'merchant_business_license_type' field.
   * @param value the value to set.
   */
  public void setMerchantBusinessLicenseType(CharSequence value) {
    this.merchant_business_license_type = value;
  }

  /** Creates a new MerchantUpdateBusinessLicense RecordBuilder */
  public static Builder newBuilder() {
    return new Builder();
  }
  
  /** Creates a new MerchantUpdateBusinessLicense RecordBuilder by copying an existing Builder */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }
  
  /** Creates a new MerchantUpdateBusinessLicense RecordBuilder by copying an existing MerchantUpdateBusinessLicense instance */
  public static Builder newBuilder(MerchantUpdateBusinessLicense other) {
    return new Builder(other);
  }
  
  /**
   * RecordBuilder for MerchantUpdateBusinessLicense instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MerchantUpdateBusinessLicense>
    implements org.apache.avro.data.RecordBuilder<MerchantUpdateBusinessLicense> {

    private CharSequence merchant_sn;
    private CharSequence merchant_id;
    private CharSequence merchant_type;
    private CharSequence merchant_business_license_type;

    /** Creates a new Builder */
    private Builder() {
      super(MerchantUpdateBusinessLicense.SCHEMA$);
    }
    
    /** Creates a Builder by copying an existing Builder */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_type)) {
        this.merchant_type = data().deepCopy(fields()[2].schema(), other.merchant_type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.merchant_business_license_type)) {
        this.merchant_business_license_type = data().deepCopy(fields()[3].schema(), other.merchant_business_license_type);
        fieldSetFlags()[3] = true;
      }
    }
    
    /** Creates a Builder by copying an existing MerchantUpdateBusinessLicense instance */
    private Builder(MerchantUpdateBusinessLicense other) {
            super(MerchantUpdateBusinessLicense.SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_type)) {
        this.merchant_type = data().deepCopy(fields()[2].schema(), other.merchant_type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.merchant_business_license_type)) {
        this.merchant_business_license_type = data().deepCopy(fields()[3].schema(), other.merchant_business_license_type);
        fieldSetFlags()[3] = true;
      }
    }

    /** Gets the value of the 'merchant_sn' field */
    public CharSequence getMerchantSn() {
      return merchant_sn;
    }
    
    /** Sets the value of the 'merchant_sn' field */
    public Builder setMerchantSn(CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this; 
    }
    
    /** Checks whether the 'merchant_sn' field has been set */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }
    
    /** Clears the value of the 'merchant_sn' field */
    public Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /** Gets the value of the 'merchant_id' field */
    public CharSequence getMerchantId() {
      return merchant_id;
    }
    
    /** Sets the value of the 'merchant_id' field */
    public Builder setMerchantId(CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this; 
    }
    
    /** Checks whether the 'merchant_id' field has been set */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }
    
    /** Clears the value of the 'merchant_id' field */
    public Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /** Gets the value of the 'merchant_type' field */
    public CharSequence getMerchantType() {
      return merchant_type;
    }
    
    /** Sets the value of the 'merchant_type' field */
    public Builder setMerchantType(CharSequence value) {
      validate(fields()[2], value);
      this.merchant_type = value;
      fieldSetFlags()[2] = true;
      return this; 
    }
    
    /** Checks whether the 'merchant_type' field has been set */
    public boolean hasMerchantType() {
      return fieldSetFlags()[2];
    }
    
    /** Clears the value of the 'merchant_type' field */
    public Builder clearMerchantType() {
      merchant_type = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /** Gets the value of the 'merchant_business_license_type' field */
    public CharSequence getMerchantBusinessLicenseType() {
      return merchant_business_license_type;
    }
    
    /** Sets the value of the 'merchant_business_license_type' field */
    public Builder setMerchantBusinessLicenseType(CharSequence value) {
      validate(fields()[3], value);
      this.merchant_business_license_type = value;
      fieldSetFlags()[3] = true;
      return this; 
    }
    
    /** Checks whether the 'merchant_business_license_type' field has been set */
    public boolean hasMerchantBusinessLicenseType() {
      return fieldSetFlags()[3];
    }
    
    /** Clears the value of the 'merchant_business_license_type' field */
    public Builder clearMerchantBusinessLicenseType() {
      merchant_business_license_type = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    public MerchantUpdateBusinessLicense build() {
      try {
        MerchantUpdateBusinessLicense record = new MerchantUpdateBusinessLicense();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (CharSequence) defaultValue(fields()[1]);
        record.merchant_type = fieldSetFlags()[2] ? this.merchant_type : (CharSequence) defaultValue(fields()[2]);
        record.merchant_business_license_type = fieldSetFlags()[3] ? this.merchant_business_license_type : (CharSequence) defaultValue(fields()[3]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }
}
