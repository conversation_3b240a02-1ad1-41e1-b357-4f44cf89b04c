/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka.storeExt;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class Digital extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = -6338341575917996321L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"Digital\",\"namespace\":\"com.wosai.upay.core.model.kafka.storeExt\",\"fields\":[{\"name\":\"video\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"business_hour\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"store_area\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"room_count\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"table_count\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"average_consumption_time\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"around_type\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"extra\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"brand_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"PhotoAvro\",\"fields\":[{\"name\":\"id\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"url\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"brand_only_scene_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"indoor_material_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"indoor_only_scene_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"outdoor_material_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"outdoor_only_scene_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"other_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"order_price_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"product_price\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"audit_picture\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<Digital> ENCODER =
      new BinaryMessageEncoder<Digital>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<Digital> DECODER =
      new BinaryMessageDecoder<Digital>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<Digital> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<Digital> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<Digital>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this Digital to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a Digital from a ByteBuffer. */
  public static Digital fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public CharSequence video;
  @Deprecated public CharSequence business_hour;
  @Deprecated public CharSequence store_area;
  @Deprecated public Integer room_count;
  @Deprecated public Integer table_count;
  @Deprecated public CharSequence average_consumption_time;
  @Deprecated public CharSequence around_type;
  @Deprecated public CharSequence extra;
  @Deprecated public java.util.List<PhotoAvro> brand_photo;
  @Deprecated public java.util.List<PhotoAvro> brand_only_scene_photo;
  @Deprecated public java.util.List<PhotoAvro> indoor_material_photo;
  @Deprecated public java.util.List<PhotoAvro> indoor_only_scene_photo;
  @Deprecated public java.util.List<PhotoAvro> outdoor_material_photo;
  @Deprecated public java.util.List<PhotoAvro> outdoor_only_scene_photo;
  @Deprecated public java.util.List<PhotoAvro> other_photo;
  @Deprecated public java.util.List<PhotoAvro> order_price_photo;
  @Deprecated public java.util.List<PhotoAvro> product_price;
  @Deprecated public java.util.List<PhotoAvro> audit_picture;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public Digital() {}

  /**
   * All-args constructor.
   * @param video The new value for video
   * @param business_hour The new value for business_hour
   * @param store_area The new value for store_area
   * @param room_count The new value for room_count
   * @param table_count The new value for table_count
   * @param average_consumption_time The new value for average_consumption_time
   * @param around_type The new value for around_type
   * @param extra The new value for extra
   * @param brand_photo The new value for brand_photo
   * @param brand_only_scene_photo The new value for brand_only_scene_photo
   * @param indoor_material_photo The new value for indoor_material_photo
   * @param indoor_only_scene_photo The new value for indoor_only_scene_photo
   * @param outdoor_material_photo The new value for outdoor_material_photo
   * @param outdoor_only_scene_photo The new value for outdoor_only_scene_photo
   * @param other_photo The new value for other_photo
   * @param order_price_photo The new value for order_price_photo
   * @param product_price The new value for product_price
   * @param audit_picture The new value for audit_picture
   */
  public Digital(CharSequence video, CharSequence business_hour, CharSequence store_area, Integer room_count, Integer table_count, CharSequence average_consumption_time, CharSequence around_type, CharSequence extra, java.util.List<PhotoAvro> brand_photo, java.util.List<PhotoAvro> brand_only_scene_photo, java.util.List<PhotoAvro> indoor_material_photo, java.util.List<PhotoAvro> indoor_only_scene_photo, java.util.List<PhotoAvro> outdoor_material_photo, java.util.List<PhotoAvro> outdoor_only_scene_photo, java.util.List<PhotoAvro> other_photo, java.util.List<PhotoAvro> order_price_photo, java.util.List<PhotoAvro> product_price, java.util.List<PhotoAvro> audit_picture) {
    this.video = video;
    this.business_hour = business_hour;
    this.store_area = store_area;
    this.room_count = room_count;
    this.table_count = table_count;
    this.average_consumption_time = average_consumption_time;
    this.around_type = around_type;
    this.extra = extra;
    this.brand_photo = brand_photo;
    this.brand_only_scene_photo = brand_only_scene_photo;
    this.indoor_material_photo = indoor_material_photo;
    this.indoor_only_scene_photo = indoor_only_scene_photo;
    this.outdoor_material_photo = outdoor_material_photo;
    this.outdoor_only_scene_photo = outdoor_only_scene_photo;
    this.other_photo = other_photo;
    this.order_price_photo = order_price_photo;
    this.product_price = product_price;
    this.audit_picture = audit_picture;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public Object get(int field$) {
    switch (field$) {
    case 0: return video;
    case 1: return business_hour;
    case 2: return store_area;
    case 3: return room_count;
    case 4: return table_count;
    case 5: return average_consumption_time;
    case 6: return around_type;
    case 7: return extra;
    case 8: return brand_photo;
    case 9: return brand_only_scene_photo;
    case 10: return indoor_material_photo;
    case 11: return indoor_only_scene_photo;
    case 12: return outdoor_material_photo;
    case 13: return outdoor_only_scene_photo;
    case 14: return other_photo;
    case 15: return order_price_photo;
    case 16: return product_price;
    case 17: return audit_picture;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: video = (CharSequence)value$; break;
    case 1: business_hour = (CharSequence)value$; break;
    case 2: store_area = (CharSequence)value$; break;
    case 3: room_count = (Integer)value$; break;
    case 4: table_count = (Integer)value$; break;
    case 5: average_consumption_time = (CharSequence)value$; break;
    case 6: around_type = (CharSequence)value$; break;
    case 7: extra = (CharSequence)value$; break;
    case 8: brand_photo = (java.util.List<PhotoAvro>)value$; break;
    case 9: brand_only_scene_photo = (java.util.List<PhotoAvro>)value$; break;
    case 10: indoor_material_photo = (java.util.List<PhotoAvro>)value$; break;
    case 11: indoor_only_scene_photo = (java.util.List<PhotoAvro>)value$; break;
    case 12: outdoor_material_photo = (java.util.List<PhotoAvro>)value$; break;
    case 13: outdoor_only_scene_photo = (java.util.List<PhotoAvro>)value$; break;
    case 14: other_photo = (java.util.List<PhotoAvro>)value$; break;
    case 15: order_price_photo = (java.util.List<PhotoAvro>)value$; break;
    case 16: product_price = (java.util.List<PhotoAvro>)value$; break;
    case 17: audit_picture = (java.util.List<PhotoAvro>)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'video' field.
   * @return The value of the 'video' field.
   */
  public CharSequence getVideo() {
    return video;
  }

  /**
   * Sets the value of the 'video' field.
   * @param value the value to set.
   */
  public void setVideo(CharSequence value) {
    this.video = value;
  }

  /**
   * Gets the value of the 'business_hour' field.
   * @return The value of the 'business_hour' field.
   */
  public CharSequence getBusinessHour() {
    return business_hour;
  }

  /**
   * Sets the value of the 'business_hour' field.
   * @param value the value to set.
   */
  public void setBusinessHour(CharSequence value) {
    this.business_hour = value;
  }

  /**
   * Gets the value of the 'store_area' field.
   * @return The value of the 'store_area' field.
   */
  public CharSequence getStoreArea() {
    return store_area;
  }

  /**
   * Sets the value of the 'store_area' field.
   * @param value the value to set.
   */
  public void setStoreArea(CharSequence value) {
    this.store_area = value;
  }

  /**
   * Gets the value of the 'room_count' field.
   * @return The value of the 'room_count' field.
   */
  public Integer getRoomCount() {
    return room_count;
  }

  /**
   * Sets the value of the 'room_count' field.
   * @param value the value to set.
   */
  public void setRoomCount(Integer value) {
    this.room_count = value;
  }

  /**
   * Gets the value of the 'table_count' field.
   * @return The value of the 'table_count' field.
   */
  public Integer getTableCount() {
    return table_count;
  }

  /**
   * Sets the value of the 'table_count' field.
   * @param value the value to set.
   */
  public void setTableCount(Integer value) {
    this.table_count = value;
  }

  /**
   * Gets the value of the 'average_consumption_time' field.
   * @return The value of the 'average_consumption_time' field.
   */
  public CharSequence getAverageConsumptionTime() {
    return average_consumption_time;
  }

  /**
   * Sets the value of the 'average_consumption_time' field.
   * @param value the value to set.
   */
  public void setAverageConsumptionTime(CharSequence value) {
    this.average_consumption_time = value;
  }

  /**
   * Gets the value of the 'around_type' field.
   * @return The value of the 'around_type' field.
   */
  public CharSequence getAroundType() {
    return around_type;
  }

  /**
   * Sets the value of the 'around_type' field.
   * @param value the value to set.
   */
  public void setAroundType(CharSequence value) {
    this.around_type = value;
  }

  /**
   * Gets the value of the 'extra' field.
   * @return The value of the 'extra' field.
   */
  public CharSequence getExtra() {
    return extra;
  }

  /**
   * Sets the value of the 'extra' field.
   * @param value the value to set.
   */
  public void setExtra(CharSequence value) {
    this.extra = value;
  }

  /**
   * Gets the value of the 'brand_photo' field.
   * @return The value of the 'brand_photo' field.
   */
  public java.util.List<PhotoAvro> getBrandPhoto() {
    return brand_photo;
  }

  /**
   * Sets the value of the 'brand_photo' field.
   * @param value the value to set.
   */
  public void setBrandPhoto(java.util.List<PhotoAvro> value) {
    this.brand_photo = value;
  }

  /**
   * Gets the value of the 'brand_only_scene_photo' field.
   * @return The value of the 'brand_only_scene_photo' field.
   */
  public java.util.List<PhotoAvro> getBrandOnlyScenePhoto() {
    return brand_only_scene_photo;
  }

  /**
   * Sets the value of the 'brand_only_scene_photo' field.
   * @param value the value to set.
   */
  public void setBrandOnlyScenePhoto(java.util.List<PhotoAvro> value) {
    this.brand_only_scene_photo = value;
  }

  /**
   * Gets the value of the 'indoor_material_photo' field.
   * @return The value of the 'indoor_material_photo' field.
   */
  public java.util.List<PhotoAvro> getIndoorMaterialPhoto() {
    return indoor_material_photo;
  }

  /**
   * Sets the value of the 'indoor_material_photo' field.
   * @param value the value to set.
   */
  public void setIndoorMaterialPhoto(java.util.List<PhotoAvro> value) {
    this.indoor_material_photo = value;
  }

  /**
   * Gets the value of the 'indoor_only_scene_photo' field.
   * @return The value of the 'indoor_only_scene_photo' field.
   */
  public java.util.List<PhotoAvro> getIndoorOnlyScenePhoto() {
    return indoor_only_scene_photo;
  }

  /**
   * Sets the value of the 'indoor_only_scene_photo' field.
   * @param value the value to set.
   */
  public void setIndoorOnlyScenePhoto(java.util.List<PhotoAvro> value) {
    this.indoor_only_scene_photo = value;
  }

  /**
   * Gets the value of the 'outdoor_material_photo' field.
   * @return The value of the 'outdoor_material_photo' field.
   */
  public java.util.List<PhotoAvro> getOutdoorMaterialPhoto() {
    return outdoor_material_photo;
  }

  /**
   * Sets the value of the 'outdoor_material_photo' field.
   * @param value the value to set.
   */
  public void setOutdoorMaterialPhoto(java.util.List<PhotoAvro> value) {
    this.outdoor_material_photo = value;
  }

  /**
   * Gets the value of the 'outdoor_only_scene_photo' field.
   * @return The value of the 'outdoor_only_scene_photo' field.
   */
  public java.util.List<PhotoAvro> getOutdoorOnlyScenePhoto() {
    return outdoor_only_scene_photo;
  }

  /**
   * Sets the value of the 'outdoor_only_scene_photo' field.
   * @param value the value to set.
   */
  public void setOutdoorOnlyScenePhoto(java.util.List<PhotoAvro> value) {
    this.outdoor_only_scene_photo = value;
  }

  /**
   * Gets the value of the 'other_photo' field.
   * @return The value of the 'other_photo' field.
   */
  public java.util.List<PhotoAvro> getOtherPhoto() {
    return other_photo;
  }

  /**
   * Sets the value of the 'other_photo' field.
   * @param value the value to set.
   */
  public void setOtherPhoto(java.util.List<PhotoAvro> value) {
    this.other_photo = value;
  }

  /**
   * Gets the value of the 'order_price_photo' field.
   * @return The value of the 'order_price_photo' field.
   */
  public java.util.List<PhotoAvro> getOrderPricePhoto() {
    return order_price_photo;
  }

  /**
   * Sets the value of the 'order_price_photo' field.
   * @param value the value to set.
   */
  public void setOrderPricePhoto(java.util.List<PhotoAvro> value) {
    this.order_price_photo = value;
  }

  /**
   * Gets the value of the 'product_price' field.
   * @return The value of the 'product_price' field.
   */
  public java.util.List<PhotoAvro> getProductPrice() {
    return product_price;
  }

  /**
   * Sets the value of the 'product_price' field.
   * @param value the value to set.
   */
  public void setProductPrice(java.util.List<PhotoAvro> value) {
    this.product_price = value;
  }

  /**
   * Gets the value of the 'audit_picture' field.
   * @return The value of the 'audit_picture' field.
   */
  public java.util.List<PhotoAvro> getAuditPicture() {
    return audit_picture;
  }

  /**
   * Sets the value of the 'audit_picture' field.
   * @param value the value to set.
   */
  public void setAuditPicture(java.util.List<PhotoAvro> value) {
    this.audit_picture = value;
  }

  /**
   * Creates a new Digital RecordBuilder.
   * @return A new Digital RecordBuilder
   */
  public static Builder newBuilder() {
    return new Builder();
  }

  /**
   * Creates a new Digital RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new Digital RecordBuilder
   */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }

  /**
   * Creates a new Digital RecordBuilder by copying an existing Digital instance.
   * @param other The existing instance to copy.
   * @return A new Digital RecordBuilder
   */
  public static Builder newBuilder(Digital other) {
    return new Builder(other);
  }

  /**
   * RecordBuilder for Digital instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<Digital>
    implements org.apache.avro.data.RecordBuilder<Digital> {

    private CharSequence video;
    private CharSequence business_hour;
    private CharSequence store_area;
    private Integer room_count;
    private Integer table_count;
    private CharSequence average_consumption_time;
    private CharSequence around_type;
    private CharSequence extra;
    private java.util.List<PhotoAvro> brand_photo;
    private java.util.List<PhotoAvro> brand_only_scene_photo;
    private java.util.List<PhotoAvro> indoor_material_photo;
    private java.util.List<PhotoAvro> indoor_only_scene_photo;
    private java.util.List<PhotoAvro> outdoor_material_photo;
    private java.util.List<PhotoAvro> outdoor_only_scene_photo;
    private java.util.List<PhotoAvro> other_photo;
    private java.util.List<PhotoAvro> order_price_photo;
    private java.util.List<PhotoAvro> product_price;
    private java.util.List<PhotoAvro> audit_picture;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.video)) {
        this.video = data().deepCopy(fields()[0].schema(), other.video);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.business_hour)) {
        this.business_hour = data().deepCopy(fields()[1].schema(), other.business_hour);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.store_area)) {
        this.store_area = data().deepCopy(fields()[2].schema(), other.store_area);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.room_count)) {
        this.room_count = data().deepCopy(fields()[3].schema(), other.room_count);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.table_count)) {
        this.table_count = data().deepCopy(fields()[4].schema(), other.table_count);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.average_consumption_time)) {
        this.average_consumption_time = data().deepCopy(fields()[5].schema(), other.average_consumption_time);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.around_type)) {
        this.around_type = data().deepCopy(fields()[6].schema(), other.around_type);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.extra)) {
        this.extra = data().deepCopy(fields()[7].schema(), other.extra);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.brand_photo)) {
        this.brand_photo = data().deepCopy(fields()[8].schema(), other.brand_photo);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.brand_only_scene_photo)) {
        this.brand_only_scene_photo = data().deepCopy(fields()[9].schema(), other.brand_only_scene_photo);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.indoor_material_photo)) {
        this.indoor_material_photo = data().deepCopy(fields()[10].schema(), other.indoor_material_photo);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.indoor_only_scene_photo)) {
        this.indoor_only_scene_photo = data().deepCopy(fields()[11].schema(), other.indoor_only_scene_photo);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.outdoor_material_photo)) {
        this.outdoor_material_photo = data().deepCopy(fields()[12].schema(), other.outdoor_material_photo);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.outdoor_only_scene_photo)) {
        this.outdoor_only_scene_photo = data().deepCopy(fields()[13].schema(), other.outdoor_only_scene_photo);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.other_photo)) {
        this.other_photo = data().deepCopy(fields()[14].schema(), other.other_photo);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.order_price_photo)) {
        this.order_price_photo = data().deepCopy(fields()[15].schema(), other.order_price_photo);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.product_price)) {
        this.product_price = data().deepCopy(fields()[16].schema(), other.product_price);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.audit_picture)) {
        this.audit_picture = data().deepCopy(fields()[17].schema(), other.audit_picture);
        fieldSetFlags()[17] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing Digital instance
     * @param other The existing instance to copy.
     */
    private Builder(Digital other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.video)) {
        this.video = data().deepCopy(fields()[0].schema(), other.video);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.business_hour)) {
        this.business_hour = data().deepCopy(fields()[1].schema(), other.business_hour);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.store_area)) {
        this.store_area = data().deepCopy(fields()[2].schema(), other.store_area);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.room_count)) {
        this.room_count = data().deepCopy(fields()[3].schema(), other.room_count);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.table_count)) {
        this.table_count = data().deepCopy(fields()[4].schema(), other.table_count);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.average_consumption_time)) {
        this.average_consumption_time = data().deepCopy(fields()[5].schema(), other.average_consumption_time);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.around_type)) {
        this.around_type = data().deepCopy(fields()[6].schema(), other.around_type);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.extra)) {
        this.extra = data().deepCopy(fields()[7].schema(), other.extra);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.brand_photo)) {
        this.brand_photo = data().deepCopy(fields()[8].schema(), other.brand_photo);
        fieldSetFlags()[8] = true;
      }
      if (isValidValue(fields()[9], other.brand_only_scene_photo)) {
        this.brand_only_scene_photo = data().deepCopy(fields()[9].schema(), other.brand_only_scene_photo);
        fieldSetFlags()[9] = true;
      }
      if (isValidValue(fields()[10], other.indoor_material_photo)) {
        this.indoor_material_photo = data().deepCopy(fields()[10].schema(), other.indoor_material_photo);
        fieldSetFlags()[10] = true;
      }
      if (isValidValue(fields()[11], other.indoor_only_scene_photo)) {
        this.indoor_only_scene_photo = data().deepCopy(fields()[11].schema(), other.indoor_only_scene_photo);
        fieldSetFlags()[11] = true;
      }
      if (isValidValue(fields()[12], other.outdoor_material_photo)) {
        this.outdoor_material_photo = data().deepCopy(fields()[12].schema(), other.outdoor_material_photo);
        fieldSetFlags()[12] = true;
      }
      if (isValidValue(fields()[13], other.outdoor_only_scene_photo)) {
        this.outdoor_only_scene_photo = data().deepCopy(fields()[13].schema(), other.outdoor_only_scene_photo);
        fieldSetFlags()[13] = true;
      }
      if (isValidValue(fields()[14], other.other_photo)) {
        this.other_photo = data().deepCopy(fields()[14].schema(), other.other_photo);
        fieldSetFlags()[14] = true;
      }
      if (isValidValue(fields()[15], other.order_price_photo)) {
        this.order_price_photo = data().deepCopy(fields()[15].schema(), other.order_price_photo);
        fieldSetFlags()[15] = true;
      }
      if (isValidValue(fields()[16], other.product_price)) {
        this.product_price = data().deepCopy(fields()[16].schema(), other.product_price);
        fieldSetFlags()[16] = true;
      }
      if (isValidValue(fields()[17], other.audit_picture)) {
        this.audit_picture = data().deepCopy(fields()[17].schema(), other.audit_picture);
        fieldSetFlags()[17] = true;
      }
    }

    /**
      * Gets the value of the 'video' field.
      * @return The value.
      */
    public CharSequence getVideo() {
      return video;
    }

    /**
      * Sets the value of the 'video' field.
      * @param value The value of 'video'.
      * @return This builder.
      */
    public Builder setVideo(CharSequence value) {
      validate(fields()[0], value);
      this.video = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'video' field has been set.
      * @return True if the 'video' field has been set, false otherwise.
      */
    public boolean hasVideo() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'video' field.
      * @return This builder.
      */
    public Builder clearVideo() {
      video = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'business_hour' field.
      * @return The value.
      */
    public CharSequence getBusinessHour() {
      return business_hour;
    }

    /**
      * Sets the value of the 'business_hour' field.
      * @param value The value of 'business_hour'.
      * @return This builder.
      */
    public Builder setBusinessHour(CharSequence value) {
      validate(fields()[1], value);
      this.business_hour = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'business_hour' field has been set.
      * @return True if the 'business_hour' field has been set, false otherwise.
      */
    public boolean hasBusinessHour() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'business_hour' field.
      * @return This builder.
      */
    public Builder clearBusinessHour() {
      business_hour = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'store_area' field.
      * @return The value.
      */
    public CharSequence getStoreArea() {
      return store_area;
    }

    /**
      * Sets the value of the 'store_area' field.
      * @param value The value of 'store_area'.
      * @return This builder.
      */
    public Builder setStoreArea(CharSequence value) {
      validate(fields()[2], value);
      this.store_area = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'store_area' field has been set.
      * @return True if the 'store_area' field has been set, false otherwise.
      */
    public boolean hasStoreArea() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'store_area' field.
      * @return This builder.
      */
    public Builder clearStoreArea() {
      store_area = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'room_count' field.
      * @return The value.
      */
    public Integer getRoomCount() {
      return room_count;
    }

    /**
      * Sets the value of the 'room_count' field.
      * @param value The value of 'room_count'.
      * @return This builder.
      */
    public Builder setRoomCount(Integer value) {
      validate(fields()[3], value);
      this.room_count = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'room_count' field has been set.
      * @return True if the 'room_count' field has been set, false otherwise.
      */
    public boolean hasRoomCount() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'room_count' field.
      * @return This builder.
      */
    public Builder clearRoomCount() {
      room_count = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'table_count' field.
      * @return The value.
      */
    public Integer getTableCount() {
      return table_count;
    }

    /**
      * Sets the value of the 'table_count' field.
      * @param value The value of 'table_count'.
      * @return This builder.
      */
    public Builder setTableCount(Integer value) {
      validate(fields()[4], value);
      this.table_count = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'table_count' field has been set.
      * @return True if the 'table_count' field has been set, false otherwise.
      */
    public boolean hasTableCount() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'table_count' field.
      * @return This builder.
      */
    public Builder clearTableCount() {
      table_count = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'average_consumption_time' field.
      * @return The value.
      */
    public CharSequence getAverageConsumptionTime() {
      return average_consumption_time;
    }

    /**
      * Sets the value of the 'average_consumption_time' field.
      * @param value The value of 'average_consumption_time'.
      * @return This builder.
      */
    public Builder setAverageConsumptionTime(CharSequence value) {
      validate(fields()[5], value);
      this.average_consumption_time = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'average_consumption_time' field has been set.
      * @return True if the 'average_consumption_time' field has been set, false otherwise.
      */
    public boolean hasAverageConsumptionTime() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'average_consumption_time' field.
      * @return This builder.
      */
    public Builder clearAverageConsumptionTime() {
      average_consumption_time = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'around_type' field.
      * @return The value.
      */
    public CharSequence getAroundType() {
      return around_type;
    }

    /**
      * Sets the value of the 'around_type' field.
      * @param value The value of 'around_type'.
      * @return This builder.
      */
    public Builder setAroundType(CharSequence value) {
      validate(fields()[6], value);
      this.around_type = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'around_type' field has been set.
      * @return True if the 'around_type' field has been set, false otherwise.
      */
    public boolean hasAroundType() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'around_type' field.
      * @return This builder.
      */
    public Builder clearAroundType() {
      around_type = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'extra' field.
      * @return The value.
      */
    public CharSequence getExtra() {
      return extra;
    }

    /**
      * Sets the value of the 'extra' field.
      * @param value The value of 'extra'.
      * @return This builder.
      */
    public Builder setExtra(CharSequence value) {
      validate(fields()[7], value);
      this.extra = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'extra' field has been set.
      * @return True if the 'extra' field has been set, false otherwise.
      */
    public boolean hasExtra() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'extra' field.
      * @return This builder.
      */
    public Builder clearExtra() {
      extra = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'brand_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getBrandPhoto() {
      return brand_photo;
    }

    /**
      * Sets the value of the 'brand_photo' field.
      * @param value The value of 'brand_photo'.
      * @return This builder.
      */
    public Builder setBrandPhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[8], value);
      this.brand_photo = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'brand_photo' field has been set.
      * @return True if the 'brand_photo' field has been set, false otherwise.
      */
    public boolean hasBrandPhoto() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'brand_photo' field.
      * @return This builder.
      */
    public Builder clearBrandPhoto() {
      brand_photo = null;
      fieldSetFlags()[8] = false;
      return this;
    }

    /**
      * Gets the value of the 'brand_only_scene_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getBrandOnlyScenePhoto() {
      return brand_only_scene_photo;
    }

    /**
      * Sets the value of the 'brand_only_scene_photo' field.
      * @param value The value of 'brand_only_scene_photo'.
      * @return This builder.
      */
    public Builder setBrandOnlyScenePhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[9], value);
      this.brand_only_scene_photo = value;
      fieldSetFlags()[9] = true;
      return this;
    }

    /**
      * Checks whether the 'brand_only_scene_photo' field has been set.
      * @return True if the 'brand_only_scene_photo' field has been set, false otherwise.
      */
    public boolean hasBrandOnlyScenePhoto() {
      return fieldSetFlags()[9];
    }


    /**
      * Clears the value of the 'brand_only_scene_photo' field.
      * @return This builder.
      */
    public Builder clearBrandOnlyScenePhoto() {
      brand_only_scene_photo = null;
      fieldSetFlags()[9] = false;
      return this;
    }

    /**
      * Gets the value of the 'indoor_material_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getIndoorMaterialPhoto() {
      return indoor_material_photo;
    }

    /**
      * Sets the value of the 'indoor_material_photo' field.
      * @param value The value of 'indoor_material_photo'.
      * @return This builder.
      */
    public Builder setIndoorMaterialPhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[10], value);
      this.indoor_material_photo = value;
      fieldSetFlags()[10] = true;
      return this;
    }

    /**
      * Checks whether the 'indoor_material_photo' field has been set.
      * @return True if the 'indoor_material_photo' field has been set, false otherwise.
      */
    public boolean hasIndoorMaterialPhoto() {
      return fieldSetFlags()[10];
    }


    /**
      * Clears the value of the 'indoor_material_photo' field.
      * @return This builder.
      */
    public Builder clearIndoorMaterialPhoto() {
      indoor_material_photo = null;
      fieldSetFlags()[10] = false;
      return this;
    }

    /**
      * Gets the value of the 'indoor_only_scene_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getIndoorOnlyScenePhoto() {
      return indoor_only_scene_photo;
    }

    /**
      * Sets the value of the 'indoor_only_scene_photo' field.
      * @param value The value of 'indoor_only_scene_photo'.
      * @return This builder.
      */
    public Builder setIndoorOnlyScenePhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[11], value);
      this.indoor_only_scene_photo = value;
      fieldSetFlags()[11] = true;
      return this;
    }

    /**
      * Checks whether the 'indoor_only_scene_photo' field has been set.
      * @return True if the 'indoor_only_scene_photo' field has been set, false otherwise.
      */
    public boolean hasIndoorOnlyScenePhoto() {
      return fieldSetFlags()[11];
    }


    /**
      * Clears the value of the 'indoor_only_scene_photo' field.
      * @return This builder.
      */
    public Builder clearIndoorOnlyScenePhoto() {
      indoor_only_scene_photo = null;
      fieldSetFlags()[11] = false;
      return this;
    }

    /**
      * Gets the value of the 'outdoor_material_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getOutdoorMaterialPhoto() {
      return outdoor_material_photo;
    }

    /**
      * Sets the value of the 'outdoor_material_photo' field.
      * @param value The value of 'outdoor_material_photo'.
      * @return This builder.
      */
    public Builder setOutdoorMaterialPhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[12], value);
      this.outdoor_material_photo = value;
      fieldSetFlags()[12] = true;
      return this;
    }

    /**
      * Checks whether the 'outdoor_material_photo' field has been set.
      * @return True if the 'outdoor_material_photo' field has been set, false otherwise.
      */
    public boolean hasOutdoorMaterialPhoto() {
      return fieldSetFlags()[12];
    }


    /**
      * Clears the value of the 'outdoor_material_photo' field.
      * @return This builder.
      */
    public Builder clearOutdoorMaterialPhoto() {
      outdoor_material_photo = null;
      fieldSetFlags()[12] = false;
      return this;
    }

    /**
      * Gets the value of the 'outdoor_only_scene_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getOutdoorOnlyScenePhoto() {
      return outdoor_only_scene_photo;
    }

    /**
      * Sets the value of the 'outdoor_only_scene_photo' field.
      * @param value The value of 'outdoor_only_scene_photo'.
      * @return This builder.
      */
    public Builder setOutdoorOnlyScenePhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[13], value);
      this.outdoor_only_scene_photo = value;
      fieldSetFlags()[13] = true;
      return this;
    }

    /**
      * Checks whether the 'outdoor_only_scene_photo' field has been set.
      * @return True if the 'outdoor_only_scene_photo' field has been set, false otherwise.
      */
    public boolean hasOutdoorOnlyScenePhoto() {
      return fieldSetFlags()[13];
    }


    /**
      * Clears the value of the 'outdoor_only_scene_photo' field.
      * @return This builder.
      */
    public Builder clearOutdoorOnlyScenePhoto() {
      outdoor_only_scene_photo = null;
      fieldSetFlags()[13] = false;
      return this;
    }

    /**
      * Gets the value of the 'other_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getOtherPhoto() {
      return other_photo;
    }

    /**
      * Sets the value of the 'other_photo' field.
      * @param value The value of 'other_photo'.
      * @return This builder.
      */
    public Builder setOtherPhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[14], value);
      this.other_photo = value;
      fieldSetFlags()[14] = true;
      return this;
    }

    /**
      * Checks whether the 'other_photo' field has been set.
      * @return True if the 'other_photo' field has been set, false otherwise.
      */
    public boolean hasOtherPhoto() {
      return fieldSetFlags()[14];
    }


    /**
      * Clears the value of the 'other_photo' field.
      * @return This builder.
      */
    public Builder clearOtherPhoto() {
      other_photo = null;
      fieldSetFlags()[14] = false;
      return this;
    }

    /**
      * Gets the value of the 'order_price_photo' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getOrderPricePhoto() {
      return order_price_photo;
    }

    /**
      * Sets the value of the 'order_price_photo' field.
      * @param value The value of 'order_price_photo'.
      * @return This builder.
      */
    public Builder setOrderPricePhoto(java.util.List<PhotoAvro> value) {
      validate(fields()[15], value);
      this.order_price_photo = value;
      fieldSetFlags()[15] = true;
      return this;
    }

    /**
      * Checks whether the 'order_price_photo' field has been set.
      * @return True if the 'order_price_photo' field has been set, false otherwise.
      */
    public boolean hasOrderPricePhoto() {
      return fieldSetFlags()[15];
    }


    /**
      * Clears the value of the 'order_price_photo' field.
      * @return This builder.
      */
    public Builder clearOrderPricePhoto() {
      order_price_photo = null;
      fieldSetFlags()[15] = false;
      return this;
    }

    /**
      * Gets the value of the 'product_price' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getProductPrice() {
      return product_price;
    }

    /**
      * Sets the value of the 'product_price' field.
      * @param value The value of 'product_price'.
      * @return This builder.
      */
    public Builder setProductPrice(java.util.List<PhotoAvro> value) {
      validate(fields()[16], value);
      this.product_price = value;
      fieldSetFlags()[16] = true;
      return this;
    }

    /**
      * Checks whether the 'product_price' field has been set.
      * @return True if the 'product_price' field has been set, false otherwise.
      */
    public boolean hasProductPrice() {
      return fieldSetFlags()[16];
    }


    /**
      * Clears the value of the 'product_price' field.
      * @return This builder.
      */
    public Builder clearProductPrice() {
      product_price = null;
      fieldSetFlags()[16] = false;
      return this;
    }

    /**
      * Gets the value of the 'audit_picture' field.
      * @return The value.
      */
    public java.util.List<PhotoAvro> getAuditPicture() {
      return audit_picture;
    }

    /**
      * Sets the value of the 'audit_picture' field.
      * @param value The value of 'audit_picture'.
      * @return This builder.
      */
    public Builder setAuditPicture(java.util.List<PhotoAvro> value) {
      validate(fields()[17], value);
      this.audit_picture = value;
      fieldSetFlags()[17] = true;
      return this;
    }

    /**
      * Checks whether the 'audit_picture' field has been set.
      * @return True if the 'audit_picture' field has been set, false otherwise.
      */
    public boolean hasAuditPicture() {
      return fieldSetFlags()[17];
    }


    /**
      * Clears the value of the 'audit_picture' field.
      * @return This builder.
      */
    public Builder clearAuditPicture() {
      audit_picture = null;
      fieldSetFlags()[17] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Digital build() {
      try {
        Digital record = new Digital();
        record.video = fieldSetFlags()[0] ? this.video : (CharSequence) defaultValue(fields()[0]);
        record.business_hour = fieldSetFlags()[1] ? this.business_hour : (CharSequence) defaultValue(fields()[1]);
        record.store_area = fieldSetFlags()[2] ? this.store_area : (CharSequence) defaultValue(fields()[2]);
        record.room_count = fieldSetFlags()[3] ? this.room_count : (Integer) defaultValue(fields()[3]);
        record.table_count = fieldSetFlags()[4] ? this.table_count : (Integer) defaultValue(fields()[4]);
        record.average_consumption_time = fieldSetFlags()[5] ? this.average_consumption_time : (CharSequence) defaultValue(fields()[5]);
        record.around_type = fieldSetFlags()[6] ? this.around_type : (CharSequence) defaultValue(fields()[6]);
        record.extra = fieldSetFlags()[7] ? this.extra : (CharSequence) defaultValue(fields()[7]);
        record.brand_photo = fieldSetFlags()[8] ? this.brand_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[8]);
        record.brand_only_scene_photo = fieldSetFlags()[9] ? this.brand_only_scene_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[9]);
        record.indoor_material_photo = fieldSetFlags()[10] ? this.indoor_material_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[10]);
        record.indoor_only_scene_photo = fieldSetFlags()[11] ? this.indoor_only_scene_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[11]);
        record.outdoor_material_photo = fieldSetFlags()[12] ? this.outdoor_material_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[12]);
        record.outdoor_only_scene_photo = fieldSetFlags()[13] ? this.outdoor_only_scene_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[13]);
        record.other_photo = fieldSetFlags()[14] ? this.other_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[14]);
        record.order_price_photo = fieldSetFlags()[15] ? this.order_price_photo : (java.util.List<PhotoAvro>) defaultValue(fields()[15]);
        record.product_price = fieldSetFlags()[16] ? this.product_price : (java.util.List<PhotoAvro>) defaultValue(fields()[16]);
        record.audit_picture = fieldSetFlags()[17] ? this.audit_picture : (java.util.List<PhotoAvro>) defaultValue(fields()[17]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<Digital>
    WRITER$ = (org.apache.avro.io.DatumWriter<Digital>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<Digital>
    READER$ = (org.apache.avro.io.DatumReader<Digital>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
