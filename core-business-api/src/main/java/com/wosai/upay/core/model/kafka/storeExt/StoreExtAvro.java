/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka.storeExt;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class StoreExtAvro extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 6590125434656121500L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"StoreExtAvro\",\"namespace\":\"com.wosai.upay.core.model.kafka.storeExt\",\"fields\":[{\"name\":\"id\",\"type\":[\"null\",\"long\"],\"default\":null},{\"name\":\"store_id\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"before\",\"type\":[\"null\",{\"type\":\"record\",\"name\":\"Digital\",\"fields\":[{\"name\":\"video\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"business_hour\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"store_area\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"room_count\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"table_count\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"average_consumption_time\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"around_type\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"extra\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"brand_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"PhotoAvro\",\"fields\":[{\"name\":\"id\",\"type\":[\"null\",\"string\"],\"default\":null},{\"name\":\"url\",\"type\":[\"null\",\"string\"],\"default\":null}]}}],\"default\":null},{\"name\":\"brand_only_scene_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"indoor_material_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"indoor_only_scene_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"outdoor_material_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"outdoor_only_scene_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"other_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"order_price_photo\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"product_price\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null},{\"name\":\"audit_picture\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"PhotoAvro\"}],\"default\":null}]}],\"default\":null},{\"name\":\"after\",\"type\":[\"null\",\"Digital\"],\"default\":null}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<StoreExtAvro> ENCODER =
      new BinaryMessageEncoder<StoreExtAvro>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<StoreExtAvro> DECODER =
      new BinaryMessageDecoder<StoreExtAvro>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<StoreExtAvro> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<StoreExtAvro> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<StoreExtAvro>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this StoreExtAvro to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a StoreExtAvro from a ByteBuffer. */
  public static StoreExtAvro fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public Long id;
  @Deprecated public CharSequence store_id;
  @Deprecated public Digital before;
  @Deprecated public Digital after;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public StoreExtAvro() {}

  /**
   * All-args constructor.
   * @param id The new value for id
   * @param store_id The new value for store_id
   * @param before The new value for before
   * @param after The new value for after
   */
  public StoreExtAvro(Long id, CharSequence store_id, Digital before, Digital after) {
    this.id = id;
    this.store_id = store_id;
    this.before = before;
    this.after = after;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public Object get(int field$) {
    switch (field$) {
    case 0: return id;
    case 1: return store_id;
    case 2: return before;
    case 3: return after;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: id = (Long)value$; break;
    case 1: store_id = (CharSequence)value$; break;
    case 2: before = (Digital)value$; break;
    case 3: after = (Digital)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'id' field.
   * @return The value of the 'id' field.
   */
  public Long getId() {
    return id;
  }

  /**
   * Sets the value of the 'id' field.
   * @param value the value to set.
   */
  public void setId(Long value) {
    this.id = value;
  }

  /**
   * Gets the value of the 'store_id' field.
   * @return The value of the 'store_id' field.
   */
  public CharSequence getStoreId() {
    return store_id;
  }

  /**
   * Sets the value of the 'store_id' field.
   * @param value the value to set.
   */
  public void setStoreId(CharSequence value) {
    this.store_id = value;
  }

  /**
   * Gets the value of the 'before' field.
   * @return The value of the 'before' field.
   */
  public Digital getBefore() {
    return before;
  }

  /**
   * Sets the value of the 'before' field.
   * @param value the value to set.
   */
  public void setBefore(Digital value) {
    this.before = value;
  }

  /**
   * Gets the value of the 'after' field.
   * @return The value of the 'after' field.
   */
  public Digital getAfter() {
    return after;
  }

  /**
   * Sets the value of the 'after' field.
   * @param value the value to set.
   */
  public void setAfter(Digital value) {
    this.after = value;
  }

  /**
   * Creates a new StoreExtAvro RecordBuilder.
   * @return A new StoreExtAvro RecordBuilder
   */
  public static Builder newBuilder() {
    return new Builder();
  }

  /**
   * Creates a new StoreExtAvro RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new StoreExtAvro RecordBuilder
   */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }

  /**
   * Creates a new StoreExtAvro RecordBuilder by copying an existing StoreExtAvro instance.
   * @param other The existing instance to copy.
   * @return A new StoreExtAvro RecordBuilder
   */
  public static Builder newBuilder(StoreExtAvro other) {
    return new Builder(other);
  }

  /**
   * RecordBuilder for StoreExtAvro instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<StoreExtAvro>
    implements org.apache.avro.data.RecordBuilder<StoreExtAvro> {

    private Long id;
    private CharSequence store_id;
    private Digital before;
    private Digital.Builder beforeBuilder;
    private Digital after;
    private Digital.Builder afterBuilder;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.store_id)) {
        this.store_id = data().deepCopy(fields()[1].schema(), other.store_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.before)) {
        this.before = data().deepCopy(fields()[2].schema(), other.before);
        fieldSetFlags()[2] = true;
      }
      if (other.hasBeforeBuilder()) {
        this.beforeBuilder = Digital.newBuilder(other.getBeforeBuilder());
      }
      if (isValidValue(fields()[3], other.after)) {
        this.after = data().deepCopy(fields()[3].schema(), other.after);
        fieldSetFlags()[3] = true;
      }
      if (other.hasAfterBuilder()) {
        this.afterBuilder = Digital.newBuilder(other.getAfterBuilder());
      }
    }

    /**
     * Creates a Builder by copying an existing StoreExtAvro instance
     * @param other The existing instance to copy.
     */
    private Builder(StoreExtAvro other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.id)) {
        this.id = data().deepCopy(fields()[0].schema(), other.id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.store_id)) {
        this.store_id = data().deepCopy(fields()[1].schema(), other.store_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.before)) {
        this.before = data().deepCopy(fields()[2].schema(), other.before);
        fieldSetFlags()[2] = true;
      }
      this.beforeBuilder = null;
      if (isValidValue(fields()[3], other.after)) {
        this.after = data().deepCopy(fields()[3].schema(), other.after);
        fieldSetFlags()[3] = true;
      }
      this.afterBuilder = null;
    }

    /**
      * Gets the value of the 'id' field.
      * @return The value.
      */
    public Long getId() {
      return id;
    }

    /**
      * Sets the value of the 'id' field.
      * @param value The value of 'id'.
      * @return This builder.
      */
    public Builder setId(Long value) {
      validate(fields()[0], value);
      this.id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'id' field has been set.
      * @return True if the 'id' field has been set, false otherwise.
      */
    public boolean hasId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'id' field.
      * @return This builder.
      */
    public Builder clearId() {
      id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'store_id' field.
      * @return The value.
      */
    public CharSequence getStoreId() {
      return store_id;
    }

    /**
      * Sets the value of the 'store_id' field.
      * @param value The value of 'store_id'.
      * @return This builder.
      */
    public Builder setStoreId(CharSequence value) {
      validate(fields()[1], value);
      this.store_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'store_id' field has been set.
      * @return True if the 'store_id' field has been set, false otherwise.
      */
    public boolean hasStoreId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'store_id' field.
      * @return This builder.
      */
    public Builder clearStoreId() {
      store_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'before' field.
      * @return The value.
      */
    public Digital getBefore() {
      return before;
    }

    /**
      * Sets the value of the 'before' field.
      * @param value The value of 'before'.
      * @return This builder.
      */
    public Builder setBefore(Digital value) {
      validate(fields()[2], value);
      this.beforeBuilder = null;
      this.before = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'before' field has been set.
      * @return True if the 'before' field has been set, false otherwise.
      */
    public boolean hasBefore() {
      return fieldSetFlags()[2];
    }

    /**
     * Gets the Builder instance for the 'before' field and creates one if it doesn't exist yet.
     * @return This builder.
     */
    public Digital.Builder getBeforeBuilder() {
      if (beforeBuilder == null) {
        if (hasBefore()) {
          setBeforeBuilder(Digital.newBuilder(before));
        } else {
          setBeforeBuilder(Digital.newBuilder());
        }
      }
      return beforeBuilder;
    }

    /**
     * Sets the Builder instance for the 'before' field
     * @param value The builder instance that must be set.
     * @return This builder.
     */
    public Builder setBeforeBuilder(Digital.Builder value) {
      clearBefore();
      beforeBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'before' field has an active Builder instance
     * @return True if the 'before' field has an active Builder instance
     */
    public boolean hasBeforeBuilder() {
      return beforeBuilder != null;
    }

    /**
      * Clears the value of the 'before' field.
      * @return This builder.
      */
    public Builder clearBefore() {
      before = null;
      beforeBuilder = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'after' field.
      * @return The value.
      */
    public Digital getAfter() {
      return after;
    }

    /**
      * Sets the value of the 'after' field.
      * @param value The value of 'after'.
      * @return This builder.
      */
    public Builder setAfter(Digital value) {
      validate(fields()[3], value);
      this.afterBuilder = null;
      this.after = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'after' field has been set.
      * @return True if the 'after' field has been set, false otherwise.
      */
    public boolean hasAfter() {
      return fieldSetFlags()[3];
    }

    /**
     * Gets the Builder instance for the 'after' field and creates one if it doesn't exist yet.
     * @return This builder.
     */
    public Digital.Builder getAfterBuilder() {
      if (afterBuilder == null) {
        if (hasAfter()) {
          setAfterBuilder(Digital.newBuilder(after));
        } else {
          setAfterBuilder(Digital.newBuilder());
        }
      }
      return afterBuilder;
    }

    /**
     * Sets the Builder instance for the 'after' field
     * @param value The builder instance that must be set.
     * @return This builder.
     */
    public Builder setAfterBuilder(Digital.Builder value) {
      clearAfter();
      afterBuilder = value;
      return this;
    }

    /**
     * Checks whether the 'after' field has an active Builder instance
     * @return True if the 'after' field has an active Builder instance
     */
    public boolean hasAfterBuilder() {
      return afterBuilder != null;
    }

    /**
      * Clears the value of the 'after' field.
      * @return This builder.
      */
    public Builder clearAfter() {
      after = null;
      afterBuilder = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public StoreExtAvro build() {
      try {
        StoreExtAvro record = new StoreExtAvro();
        record.id = fieldSetFlags()[0] ? this.id : (Long) defaultValue(fields()[0]);
        record.store_id = fieldSetFlags()[1] ? this.store_id : (CharSequence) defaultValue(fields()[1]);
        if (beforeBuilder != null) {
          record.before = this.beforeBuilder.build();
        } else {
          record.before = fieldSetFlags()[2] ? this.before : (Digital) defaultValue(fields()[2]);
        }
        if (afterBuilder != null) {
          record.after = this.afterBuilder.build();
        } else {
          record.after = fieldSetFlags()[3] ? this.after : (Digital) defaultValue(fields()[3]);
        }
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<StoreExtAvro>
    WRITER$ = (org.apache.avro.io.DatumWriter<StoreExtAvro>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<StoreExtAvro>
    READER$ = (org.apache.avro.io.DatumReader<StoreExtAvro>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
