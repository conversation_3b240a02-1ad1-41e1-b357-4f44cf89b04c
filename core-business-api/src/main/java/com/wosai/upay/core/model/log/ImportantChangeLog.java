package com.wosai.upay.core.model.log;

public class ImportantChangeLog {


    /**
     * 变更对象类型-商户真实性审核申请.
     */
    public static final int OBJECT_TYPE_MERCHANT_REALLY = 1;
    /**
     * 变更对象类型-微信正式商户申请.
     */
    public static final int OBJECT_TYPE_MERCHANT_WEIXIN_REALLY = 2;
    /**
     * 变更对象类型-D0配置.
     */
    public static final int OBJECT_TYPE_MERCHANT_WITHDRAW_REALTIME = 4;
    /**
     * 变更类型-创建.
     */
    public static final int TYPE_CREATE = 1;
    /**
     * 变更类型-修改.
     */
    public static final int TYPE_MODIFY = 2;
    /**
     * 变更类型-审核.
     */
    public static final int TYPE_AUDIT = 21;
    /**
     * 变更类型-删除.
     */
    public static final int TYPE_DELETE = 3;
    /**
     * 操作人类型-运营用户.
     */
    public static final int SUBJECT_TYPE_OSP_USER = 1;
    /**
     * 操作人类型-服务商用户.
     */
    public static final int SUBJECT_TYPE_VENDOR_USER = 2;
    /**
     * 操作人类型-推广者用户.
     */
    public static final int SUBJECT_TYPE_SOLICITOR_USER = 3;
    /**
     * 操作人类型-商户用户.
     */
    public static final int SUBJECT_TYPE_MERCHANT_USER = 4;

    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) DEFAULT NULL
    public static final String MERCHANT_SN = "merchant_sn"; // varchar(32) DEFAULT NULL
    public static final String OBJECT_TYPE = "object_type"; // int DEFAULT NULL COMMENT '变更对象类型 1：商户真实性审核申请 2：微信正式商户申请'
    public static final String OBJECT_ID = "object_id"; // varchar(36) DEFAULT NULL COMMENT '变更对象id'
    public static final String TYPE = "type"; // int DEFAULT NULL COMMENT '变更类型，1：创建；2：修改；21：审核；3：删除；其他自定义'
    public static final String POST_CHANGE_STATUS = "post_change_status"; // int DEFAULT NULL COMMENT '变更后的状态'
    public static final String PAYLOAD = "payload"; // blob DEFAULT NULL COMMENT '变更内容（JSON）'
    public static final String SUBJECT_TYPE = "subject_type"; // int DEFAULT NULL COMMENT '操作人类型 1：运营用户 2：服务商用户 3：推广者用户 4：商户用户'
    public static final String SUBJECT_ID = "subject_id"; // varchar(36) DEFAULT NULL COMMENT '操作人用户ID'
    public static final String SUBJECT_NAME = "subject_name"; // varchar(64) DEFAULT NULL COMMENT '操作人姓名'
    public static final String SUBJECT_LOGIN = "subject_login"; // varchar(64) DEFAULT NULL COMMENT '操作人登录账号'
    public static final String CHANGE_DESCRIPTION = "change_description"; // text DEFAULT NULL COMMENT '事件描述（前端写入）'
    public static final String CHANGE_TIME = "change_time"; // bigint(20) DEFAULT NULL COMMENT '操作日期时间戳'

}
