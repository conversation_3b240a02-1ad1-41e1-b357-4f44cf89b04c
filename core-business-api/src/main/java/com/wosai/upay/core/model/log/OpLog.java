package com.wosai.upay.core.model.log;

public class OpLog {

    /**
     * 请求系统-未知.
     */
    public static final int REQUEST_SYSTEM_UNKNOWN = 1;
    /**
     * 请求系统-OSP.
     */
    public static final int REQUEST_SYSTEM_OSP = 2;
    /**
     * 请求系统-服务商.
     */
    public static final int REQUEST_SYSTEM_VENDOR = 3;
    /**
     * 请求系统-商户服务.
     */
    public static final int REQUEST_SYSTEM_MERCHANT = 4;
    /**
     * 请求系统-推广者服务.
     */
    public static final int REQUEST_SYSTEM_SOLICITOR = 5;
    /**
     * 请求系统-其他内部服务.
     */
    public static final int REQUEST_SYSTEM_OTHER = 9;

    /**
     * 操作类型-登入.
     */
    public static final int ACTION_TYPE_LOGIN = 1;
    /**
     * 操作类型-登出.
     */
    public static final int ACTION_TYPE_LOGOUT = 2;
    /**
     * 操作类型-查询.
     */
    public static final int ACTION_TYPE_SEARCH = 3;
    /**
     * 操作类型-查看.
     */
    public static final int ACTION_TYPE_VIEW = 4;
    /**
     * 操作类型-导出.
     */
    public static final int ACTION_TYPE_EXPORT = 5;
    /**
     * 操作类型-新增.
     */
    public static final int ACTION_TYPE_ADD = 6;
    /**
     * 操作类型-编辑.
     */
    public static final int ACTION_TYPE_EDIT = 7;
    /**
     * 操作类型-修改.
     */
    public static final int ACTION_TYPE_MODIFY = 71;
    /**
     * 操作类型-审核.
     */
    public static final int ACTION_TYPE_AUDIT = 72;
    /**
     * 操作类型-删除.
     */
    public static final int ACTION_TYPE_DELETE = 8;
    /**
     * 操作类型-上传.
     */
    public static final int ACTION_TYPE_UPLOAD = 9;
    /**
     * 操作类型-下载.
     */
    public static final int ACTION_TYPE_DOWNLOAD = 10;

    public static final String REQUEST_SYSTEM = "request_system"; // int NOT NULL DEFAULT 1 COMMENT '请求系统 1:未知; 2:OSP; 3:服务商; 4:商户服务; 5:推广者服务 9:其他内部服务'
    public static final String OPERATOR_ID = "operator_id"; // varchar(36) DEFAULT NULL COMMENT '操作人id'
    public static final String OPERATOR_NAME = "operator_name"; // varchar(64) DEFAULT NULL COMMENT '操作人姓名'
    public static final String OPERATOR_LOGIN = "operator_login"; // varchar(64) DEFAULT NULL COMMENT '操作人账号'
    public static final String ACTION_CLASS = "action_class"; // varchar(64) DEFAULT NULL COMMENT '操作类'
    public static final String ACTION_METHOD = "action_method"; // varchar(64) DEFAULT NULL COMMENT '操作方法'
    public static final String DURATION = "duration"; // int DEFAULT NULL COMMENT '执行事件，单位：毫秒'
    public static final String RESULT = "result"; // tinyint(1) NOT NULL DEFAULT 1 COMMENT '执行结果，0：失败，1：成功'
    public static final String ACTION_TYPE = "action_type"; // int DEFAULT NULL COMMENT '操作类型 1:登入; 2:登出; 3:查询; 4:查看; 5:导出; 6:新增; 7:编辑; 71:修改; 72:审核; 8:删除; 9:上传; 10:下载'
    public static final String ACTION_DATE = "action_date"; // int DEFAULT NULL COMMENT '操作日期，格式yyyyMMdd'
    public static final String CLIENT_IP = "client_ip"; // varchar(32) DEFAULT NULL COMMENT '操作ip'

}
