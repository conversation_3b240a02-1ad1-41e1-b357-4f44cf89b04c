package com.wosai.upay.core.model.log;

public class TaskApplyLog {

    public static final String TYPE = "type"; // int NOT NULL DEFAULT 1 COMMENT '申请任务类型，1：对账单下载，2：订单下载，3：渠道分润报表下载'
    public static final String APPLY_SYSTEM = "apply_system"; // int NOT NULL DEFAULT 1 COMMENT '请求系统，1:未知; 2:OSP; 3:服务商; 4:商户服务; 5:推广者服务 9:其他内部服务'
    public static final String PAYLOAD = "payload"; // blob DEFAULT NULL COMMENT '申请参数（JSON）'
    public static final String USER_ID = "user_id"; // varchar(36) DEFAULT NULL COMMENT '用户id'
    public static final String APPLY_STATUS = "apply_status"; // int NOT NULL DEFAULT 0 COMMENT '任务申请状态，0：新申请，1：执行中，2：执行成功，3：执行失败'
    public static final String APPLY_DATE = "apply_date"; // date NOT NULL COMMENT '申请日期'
    public static final String APPLY_RESULT = "apply_result"; // text DEFAULT NULL COMMENT '申请结果'

}
