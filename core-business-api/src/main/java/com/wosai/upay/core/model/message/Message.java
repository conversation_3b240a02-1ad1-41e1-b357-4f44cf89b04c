package com.wosai.upay.core.model.message;

public class Message {

    /**
     * 消息类型-公告通知.
     */
    public static final int TYPE_NOTICE = 1;
    /**
     * 消息类型-系统消息.
     */
    public static final int TYPE_MESSAGE = 2;

    /**
     * 接收者类型-所有用户.
     */
    public static final int RECEIVER_TYPE_ALL = 1;
    /**
     * 接收者类型-服务商.
     */
    public static final int RECEIVER_TYPE_VENDOR = 2;
    /**
     * 接收者类型-推广者 .
     */
    public static final int RECEIVER_TYPE_SOLICITOR = 3;
    /**
     * 接收者类型-商户.
     */
    public static final int RECEIVER_TYPE_MERCHANT = 4;

    public static final String TYPE = "type"; // int DEFAULT NULL COMMENT '类型，1:公告通知;2:系统消息'
    public static final String PAYLOAD = "payload"; // blob DEFAULT NULL COMMENT '内容（JSON）'
    public static final String RECEIVER_TYPE = "receiver_type"; // int DEFAULT NULL COMMENT '接收者类型 1：所有用户 2：服务商 3：推广者 4：商户 '
    public static final String RECEIVER_IDS = "receiver_ids"; // blob DEFAULT NULL COMMENT '接收者ID （JSON数组）如果为空 则所有该类型用户都包含'
    public static final String SENT = "sent"; // tinyint(1) NOT NULL DEFAULT 0 COMMENT '0未发送，1已发送'
    public static final String SEND_TIME = "send_time"; // bigint(20) DEFAULT NULL COMMENT '发送时间戳'

}
