package com.wosai.upay.core.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * @version 1.0
 * @author: yuhai
 * @program: core-business
 * @className HopeEduMerchantConfigRequest
 * @description: 院校通商户配置请求对象
 * @create: 2025-05-23 14:28
 **/
public class HopeEduMerchantConfigRequest implements Serializable {

    @NotBlank(message = "merchant_id不能为空")
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * b2c状态，1启用，0关闭
     */
    @Min(value = 0L, message = "b2c_status这能是0或1")
    @Max(value = 1L, message = "b2c_status这能是0或1")
    @JsonProperty("b2c_status")
    private Integer b2cStatus;

    /**
     * 院校通交易参数，目前不用填
     */
    @JsonProperty("trade_params")
    private HopeEduMerchantConfigTradeParamsRequest  tradeParams;


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getB2cStatus() {
        return b2cStatus;
    }

    public void setB2cStatus(Integer b2cStatus) {
        this.b2cStatus = b2cStatus;
    }

    public HopeEduMerchantConfigTradeParamsRequest getTradeParams() {
        return tradeParams;
    }

    public void setTradeParams(HopeEduMerchantConfigTradeParamsRequest tradeParams) {
        this.tradeParams = tradeParams;
    }
}
