package com.wosai.upay.core.model.upay;

public class Withdraw {

    /**
     * 审核状态-审核中.
     */
    public static final int CHECK_STATUS_AUDITING = 1;
    /**
     * 审核状态-运营拒绝.
     */
    public static final int CHECK_STATUS_OP_REJECT = 2;
    /**
     * 审核状态-运营冻结.
     */
    public static final int CHECK_STATUS_OP_FREEZE = 3;
    /**
     * 审核状态-运营通过，等待财务审核.
     */
    public static final int CHECK_STATUS_OP_AUDIT_PASS = 4;
    /**
     * 审核状态-财务审核通过.
     */
    public static final int CHECK_STATUS_FINANCE_AUDIT_PASS = 5;
    /**
     * 审核状态-打款成功.
     */
    public static final int CHECK_STATUS_OBTAIN_SUCCESS = 6;
    /**
     * 审核状态-打款失败.
     */
    public static final int CHECK_STATUS_OBTAIN_FAIL = 7;
    /**
     * 审核状态-人工打款.
     */
    public static final int CHECK_STATUS_OBTAIN_MANUAL = 8;

    public static final String SN = "sn"; // varchar(128) NOT NULL COMMENT '提现单号'
    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) DEFAULT NULL
    public static final String AMOUNT = "amount"; // bigint(20) NOT NULL COMMENT '提现金额'
    public static final String CHECK_STATUS = "check_status"; // int NOT NULL DEFAULT 1 COMMENT '审核状态：1：审核中；2运营拒绝；3.运营冻结；4.运营通过，等待财务审核；5.财务审核通过；6.打款成功；7.打款失败； 8.人工打款'
    // TODO check_failed_reason ?
    public static final String CHECK_FAILED_REASON = "check_failed_reason"; // text DEFAULT NULL COMMENT '审核失败原因'
    public static final String OPERATORS = "operators"; // blob DEFAULT NULL COMMENT '审核操作人json, check_status和记录中的状态同步 [{check_status:,operator_id:,operator_name:,time:,remark:},{check_status:,operator_id:,operator_name:,time:,remark:}]'
    public static final String EXPECTED_CLEARANCE_DAY = "expected_clearance_day"; // bigint(20) DEFAULT NULL COMMENT '预期到账时间'
    public static final String BATCH_NO = "batch_no"; // varchar(128) DEFAULT NULL COMMENT '打款批次'
    public static final String REMARK = "remark"; // text NOT NULL COMMENT '备注'

}
