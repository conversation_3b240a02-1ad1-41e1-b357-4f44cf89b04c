package com.wosai.upay.core.model.user;

public class Account {

    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 0; //禁用
    public static final int GENDER_MAN = 1;
    public static final int GENDER_WOMAN = 2;


    public static final String USERNAME = "username"; // varchar(128) NOT NULL
    public static final String NICKNAME = "nickname"; // varchar(255) DEFAULT NULL COMMENT '昵称'
    public static final String GENDER = "gender"; // tinyint NULL COMMENT '性别：1：男；2：女'
    public static final String AVATAR = "avatar"; // varchar(255) DEFAULT NULL COMMENT '头像'
    public static final String PASSWORD = "password"; // varchar(64) NOT NULL COMMENT '密码'
    public static final String CELLPHONE = "cellphone"; // varchar(32) NOT NULL DEFAULT  COMMENT '手机号'
    public static final String STATUS = "status"; // int DEFAULT NULL COMMENT '状态：0：禁用；1:正常'
    public static final String EMAIL = "email"; // varchar(64) DEFAULT NULL COMMENT '邮箱'
    public static final String MTIME_STAMP = "mtime_stamp"; // timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

}
