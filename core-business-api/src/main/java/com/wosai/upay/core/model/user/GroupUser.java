package com.wosai.upay.core.model.user;

/**
 * 集团用户.
 */
public class GroupUser {
    public static final String ROLE_SUPER_ADMIN = "super_admin"; // 超级管理员
    public static final String ROLE_ADMIN = "admin"; // 管理员

    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 0; //禁用

    public static final String NAME = "name"; // varchar(128) DEFAULT NULL COMMENT '名称'
    public static final String GROUP_ID = "group_id"; // varchar(36) NOT NULL COMMENT '集团id'
    public static final String ACCOUNT_ID = "account_id"; // varchar(36) NOT NULL COMMENT 'account表id'
    public static final String ROLE = "role"; // varchar(36) DEFAULT NULL COMMENT '角色/职务，super_admin：超级管理员，admin：管理员'
    public static final String MERCHANT_AUTH = "merchant_auth"; // int NOT NULL DEFAULT 1 COMMENT '商户权限，1：所有商户，2：授权商户'
    public static final String EMAIL = "email"; // varchar(128) DEFAULT NULL COMMENT '邮箱'
    public static final String REMARK = "remark"; // text DEFAULT NULL COMMENT '备注'
    public static final String STATUS = "status"; // int DEFAULT NULL COMMENT '状态：0：禁用；1:正常'

}
