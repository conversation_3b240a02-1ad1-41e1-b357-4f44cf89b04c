package com.wosai.upay.core.model.user;

/**
 * 集团用户商户权限.
 */
public class GroupUserMerchantAuth {

    public static final String GROUP_ID = "group_id"; // varchar(36) NOT NULL COMMENT '集团id'
    public static final String GROUP_USER_ID = "group_user_id"; // varchar(36) NOT NULL COMMENT '集团用户id'
    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) NOT NULL COMMENT '商户id'
    public static final String MERCHANT_SN = "merchant_sn"; // varchar(36) NOT NULL COMMENT '商户编号'
    public static final String MERCHANT_NAME = "merchant_name"; // varchar(36) NOT NULL COMMENT '商户名称'
    public static final String MERCHANT_CTIME = "merchant_ctime"; // bigint(20) DEFAULT NULL COMMENT '商户创建时间'

}
