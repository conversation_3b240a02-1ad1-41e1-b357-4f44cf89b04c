package com.wosai.upay.core.model.user;

public class MerchantUser {

    public static final String ROLE_SUPER_ADMIN = "super_admin"; // 超级管理员
    public static final String ROLE_ADMIN = "admin"; // 管理员

    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 0; //禁用
    public static final String MERCHANT_ID = "merchant_id"; // varchar(36) NOT NULL COMMENT '商户id'
    public static final String ACCOUNT_ID = "account_id"; // varchar(36) NOT NULL COMMENT 'user表里面的id'
    public static final String ROLE = "role"; // varchar(36) DEFAULT NULL COMMENT '角色/职务，super_admin：超级管理员/老板，department_manager:部门主管,admin：管理员/店长，finance：财务，cashier：收银员'
    public static final String STORE_AUTH = "store_auth"; // int NOT NULL DEFAULT 1 COMMENT '门店权限，1：所有门店，2：授权门店'
    public static final String NAME = "name"; // varchar(128) DEFAULT NULL COMMENT '名称'
    public static final String EMAIL = "email"; // varchar(128) DEFAULT NULL COMMENT '邮箱'
    public static final String REMARK = "remark"; // text DEFAULT NULL COMMENT '备注'
    public static final String STATUS = "status"; // int DEFAULT NULL COMMENT '状态：0：禁用；1:正常'

    public static final String OPERATOR_ID = "operator_id"; // 操作员id，与backstage.store_user.operator_id相同


}
