package com.wosai.upay.core.model.user;

public class OspUser {

    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 0; //禁用
    public static final String ACCOUNT_ID = "account_id"; // varchar(36) NOT NULL COMMENT 'user表里面的id'
    public static final String ROLE = "role"; // int NULL DEFAULT NULL COMMENT '角色权限'
    public static final String STATUS = "status"; // int DEFAULT NULL COMMENT '状态：0：禁用；1:正常'
    public static final String REMARK = "remark"; // varchar(255) NULL DEFAULT NULL COMMENT '备注'

}
