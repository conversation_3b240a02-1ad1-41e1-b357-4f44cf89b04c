package com.wosai.upay.core.model.user;

import java.util.Arrays;
import java.util.List;

/**
 * Created by kay on 2017/10/24.
 * 特殊权限白名单表
 */
public class SpecialAuthWhitelist {
    public static final int MSP_REFUND_PERMISSION_OPEN = 1; //权限类型：1：商户后台退款
    public static final int MSP_REFUND_PERMISSION_CLOSE = 0; //权限类型：1：商户后台退款
    public static final List<Integer> MSP_REFUND_PERMISSIONS = Arrays.asList(MSP_REFUND_PERMISSION_OPEN, MSP_REFUND_PERMISSION_CLOSE);

    public static final int AUTH_TYPE_MSP_REFUND = 1; //权限类型：1：商户后台退款

    public static final int OBJECT_TYPE_MERCHANT = 1; //业务类型：1：商户
    public static final int OBJECT_TYPE_GROUP = 2; //业务类型：2：集团
    public static final int OBJECT_TYPE_MERCHANT_USER = 3; //业务类型：3：merchant_user

    public static final String AUTH_TYPE = "auth_type"; //int(11) DEFAULT NULL COMMENT '权限类型：0：商户后台退款'
    public static final String OBJECT_ID = "object_id"; //varchar(36) NOT NULL COMMENT '业务对象id'
    public static final String OBJECT_TYPE = "object_type"; // int(11) DEFAULT NULL COMMENT '业务类型：1：商户；2:集团；3.merchant_user'
}
