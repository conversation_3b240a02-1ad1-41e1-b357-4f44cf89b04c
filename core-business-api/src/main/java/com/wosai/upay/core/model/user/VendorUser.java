package com.wosai.upay.core.model.user;

public class VendorUser {

    public static final int STATUS_ENABLED = 1; //正常
    public static final int STATUS_DISABLED = 0; //禁用
    public static final String VENDOR_ID = "vendor_id"; // varchar(36) NOT NULL COMMENT '商户id'
    public static final String ACCOUNT_ID = "account_id"; // varchar(36) NOT NULL COMMENT 'user表里面的id'
    public static final String STATUS = "status"; // int DEFAULT NULL COMMENT '状态：0：禁用；1:正常'

}
