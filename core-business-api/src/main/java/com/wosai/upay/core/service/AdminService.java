package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;


@UpayCoreServiceAnnotation
@JsonRpcService("rpc/admin")
public interface AdminService {

    /**
     * 健康检查, 如果有问题，会抛异常
     */
    void health();


    /**
     * 修改数据库加解密的秘钥
     * @param lastPart
     */
    void updateDbCryptoKeyLastPart(String lastPart);

    /**
     * 刷新配置
     */
    void refreshConfig();

    /**
     * 重新加密保存商户加密存储的字段, 只用于清洗数据
     * @param merchantId
     */
    void reSaveMerchantForDbCrypto(String merchantId);

    /**
     * 重新加密保存商户银行卡加密存储的字段, 只用于清洗数据
     * @param merchantId
     */
    void reSaveMerchantBankAccountForDbCrypto(String merchantId);


    /**
     * 重新加密保存用户账号加密存储的字段, 只用于清洗数据
     * @param accountId
     */
    void reSaveAccountForDbCrypto(String accountId);

}
