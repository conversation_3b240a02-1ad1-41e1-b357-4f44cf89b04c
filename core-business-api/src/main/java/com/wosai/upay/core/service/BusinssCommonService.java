package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.bean.model.MetaBizModel;
import com.wosai.upay.core.bean.model.MetaPayPath;
import com.wosai.upay.core.bean.response.GetAllMetaResponse;
import com.wosai.upay.core.bean.response.GetAllProviderAbilityResponse;
import com.wosai.upay.core.common.CoreBusinessIgnoreTranslate;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/common")
public interface BusinssCommonService {

    /**
     * 获取异常编号及描述.
     *
     * @return
     */
    Map<Integer, String> getExceptionCodesAndDesc();

    /**
     * 根据服务商编号查找服务商ID.
     *
     * @param vendorSn
     * @return
     */
    String getVendorIdBySn(String vendorSn);

    /**
     * 根据服务商编号查找服务商最小化信息（名称、状态）.
     *
     * @param vendorSn
     * @return
     */
    Map<String, Object> getVendorMinimalInfoBySn(String vendorSn);

    /**
     * 根据服务商编号查找服务商最小化信息（名称、状态）.
     *
     * @param vendorSn
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getVendorMinimalInfoBySn(String vendorSn, boolean throwException);

    /**
     * 根据服务商ID查找服务商编号.
     *
     * @param vendorId
     * @return
     */
    String getVendorSnById(String vendorId);

    /**
     * 根据服务商ID查找服务商最小化信息（名称、状态）.
     *
     * @param vendorId
     * @return
     */
    Map<String, Object> getVendorMinimalInfoById(String vendorId);

    /**
     * 根据服务商ID查找服务商最小化信息（名称、状态）.
     *
     * @param vendorId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getVendorMinimalInfoById(String vendorId, boolean throwException);

    /**
     * 根据服务商应用的AppId查找服务商应用ID.
     *
     * @param vendorAppAppId
     * @return
     */
    String getVendorAppIdByAppId(String vendorAppAppId);

    /**
     * 根据服务商应用的AppId查找服务商应用最小化信息（服务商id、应用编号、名称）.
     *
     * @param vendorAppAppId
     * @return
     */
    Map<String, Object> getVendorAppMinimalInfoByAppId(String vendorAppAppId);

    /**
     * 根据服务商应用的AppId查找服务商应用最小化信息（服务商id、应用编号、名称）.
     *
     * @param vendorAppAppId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getVendorAppMinimalInfoByAppId(String vendorAppAppId, boolean throwException);

    /**
     * 根据服务商应用ID查找服务商应用的AppId.
     *
     * @param vendorAppId
     * @return
     */
    String getVendorAppAppIdById(String vendorAppId);

    /**
     * 根据服务商应用ID查找服务商应用最小化信息（服务商id、应用编号、名称）.
     *
     * @param vendorAppId
     * @return
     */
    Map<String, Object> getVendorAppMinimalInfoById(String vendorAppId);

    /**
     * 根据服务商应用ID查找服务商应用最小化信息（服务商id、应用编号、名称）.
     *
     * @param vendorAppId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getVendorAppMinimalInfoById(String vendorAppId, boolean throwException);

    /**
     * 根据推广渠道编号查找推广渠道ID.
     *
     * @param solicitorSn
     * @return
     */
    String getSolicitorIdBySn(String solicitorSn);

    /**
     * 根据推广渠道编号查找推广渠道最小化信息（名称、状态）.
     *
     * @param solicitorSn
     * @return
     */
    Map<String, Object> getSolicitorMinimalInfoBySn(String solicitorSn);

    /**
     * 根据推广渠道编号查找推广渠道最小化信息（名称、状态）.
     *
     * @param solicitorSn
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getSolicitorMinimalInfoBySn(String solicitorSn, boolean throwException);

    /**
     * 根据推广渠道ID查找推广渠道编号.
     *
     * @param solicitorId
     * @return
     */
    String getSolicitorSnById(String solicitorId);

    /**
     * 根据推广渠道ID查找推广渠道最小化信息（名称、状态）.
     *
     * @param solicitorId
     * @return
     */
    Map<String, Object> getSolicitorMinimalInfoById(String solicitorId);

    /**
     * 根据推广渠道ID查找推广渠道最小化信息（名称、状态）.
     *
     * @param solicitorId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getSolicitorMinimalInfoById(String solicitorId, boolean throwException);

    /**
     * 根据商户编号查找商户ID.
     *
     * @param merchantSn
     * @return
     */
    String getMerchantIdBySn(String merchantSn);

    /**
     * 根据商户编号查找商户最小化信息（名称、商户经营名称、状态、组织层级、外部编号、联系人姓名、联系固定电话号码、联系移动电话号码）.
     *
     * @param merchantSn
     * @return
     */
    Map<String, Object> getMerchantMinimalInfoBySn(String merchantSn);

    /**
     * 根据商户编号查找商户最小化信息（名称、商户经营名称、状态、组织层级、外部编号、联系人姓名、联系固定电话号码、联系移动电话号码）.
     *
     * @param merchantSn
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getMerchantMinimalInfoBySn(String merchantSn, boolean throwException);

    /**
     * 根据商户ID查找商户编号.
     *
     * @param merchantId
     * @return
     */
    String getMerchantSnById(String merchantId);

    /**
     * 根据商户ID查找商户最小化信息（名称、商户经营名称、状态、组织层级、外部编号、联系人姓名、联系固定电话号码、联系移动电话号码）.
     *
     * @param merchantId
     * @return
     */
    Map<String, Object> getMerchantMinimalInfoById(String merchantId);

    /**
     * 根据商户ID查找商户最小化信息（名称、商户经营名称、状态、组织层级、外部编号、联系人姓名、联系固定电话号码、联系移动电话号码）.
     *
     * @param merchantId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getMerchantMinimalInfoById(String merchantId, boolean throwException);

    /**
     * 根据门店编号查找门店ID.
     *
     * @param storeSn
     * @return
     */
    String getStoreIdBySn(String storeSn);

    /**
     * 根据门店编号查找门店最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param storeSn
     * @return
     */
    Map<String, Object> getStoreMinimalInfoBySn(String storeSn);

    /**
     * 根据门店编号查找门店最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param storeSn
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getStoreMinimalInfoBySn(String storeSn, boolean throwException);

    /**
     * 根据门店ID查找门店编号.
     *
     * @param storeId
     * @return
     */
    String getStoreSnById(String storeId);

    /**
     * 根据门店ID查找门店最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param storeId
     * @return
     */
    Map<String, Object> getStoreMinimalInfoById(String storeId);

    /**
     * 根据门店ID查找门店最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param storeId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getStoreMinimalInfoById(String storeId, boolean throwException);

    /**
     * 根据终端编号查找终端ID.
     *
     * @param terminalSn
     * @return
     */
    String getTerminalIdBySn(String terminalSn);

    /**
     * 根据终端编号查找终端最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param terminalSn
     * @return
     */
    Map<String, Object> getTerminalMinimalInfoBySn(String terminalSn);

    /**
     * 根据终端编号查找终端最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param terminalSn
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getTerminalMinimalInfoBySn(String terminalSn, boolean throwException);

    /**
     * 根据终端ID查找终端编号.
     *
     * @param terminalId
     * @return
     */
    String getTerminalSnById(String terminalId);

    /**
     * 根据终端ID查找终端最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param terminalId
     * @return
     */
    Map<String, Object> getTerminalMinimalInfoById(String terminalId);

    /**
     * 根据终端ID查找终端最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param terminalId
     * @param throwException 对应的对象不存在时是否抛出异常
     * @return
     */
    Map<String, Object> getTerminalMinimalInfoById(String terminalId, boolean throwException);

    /**
     * 校验服务商状态.
     *
     * @param vendorId
     * @param vendorSn
     */
    void checkVendorStatus(String vendorId, String vendorSn);

    /**
     * 校验推广渠道状态.
     *
     * @param solicitorId
     * @param solicitorSn
     */
    void checkSolicitorStatus(String solicitorId, String solicitorSn);

    /**
     * 校验商户状态.
     *
     * @param merchantId
     * @param merchantSn
     */
    void checkMerchantStatus(String merchantId, String merchantSn);

    /**
     * 校验门店状态.
     *
     * @param storeId
     * @param storeSn
     */
    void checkStoreStatus(String storeId, String storeSn);

    /**
     * 校验终端状态.
     *
     * @param terminalId
     * @param terminalSn
     */
    void checkTerminalStatus(String terminalId, String terminalSn);

    /**
     * 获取元数据信息, 不要频繁调用，业务方可做缓存，缓存过期时间至少半个小时，减少系统调用
     * @return
     */
    @CoreBusinessIgnoreTranslate
    GetAllMetaResponse getAllMeta();

    /**
     * 获取通道业务能力, 不要频繁调用，业务方可做缓存，缓存过期时间至少半个小时，减少系统调用
     * @return
     */
    @CoreBusinessIgnoreTranslate
    GetAllProviderAbilityResponse getAllProviderAbilitys();

    /**
     * 获取所有支付源信息，不要频繁调用，业务方可做缓存，缓存过期时间至少半个小时，减少系统调用
     * @return
     *  id payway
     *  name 名称
     *  is_mobile 是否是移动支付
     *  remark 备注
     */
    @CoreBusinessIgnoreTranslate
    List<Map<String,Object>> getAllMetaPayways();

    /**
     * 获取所有支付场景信息, 不要频繁调用，业务方可做缓存，缓存过期时间至少半个小时，减少系统调用
     * @return
     *  id product_flag
     *  name 名称
     *  remark 备注
     */
    @CoreBusinessIgnoreTranslate
    List<Map<String,Object>> getAllMetaProductFlags();

    /**
     * 获取所有支付通道信息, 不要频繁调用，业务方可做缓存，缓存过期时间至少半个小时，减少系统调用
     * @return
     *  id provider
     *  name 名称
     *  remark 备注
     */
    @CoreBusinessIgnoreTranslate
    List<Map<String,Object>> getAllMetaProviders();

    /**
     * 获取所有收单机构信息
     * @return
     *  id
     *  name
     *  remark
     *  support_platform_coupon 是否支持平台券 0：不支持 1: 支持
     */
    @CoreBusinessIgnoreTranslate
    List<Map<String,Object>> getAllMetaAcquirers();

    @CoreBusinessIgnoreTranslate
    Map<String, Object> getMetaAcquirerById(int acquirer);

    @CoreBusinessIgnoreTranslate
    Map<String,Object> getMetaProviderById(int provider);

    /**
     * 获取所有业务模式
     * @return
     */
    @CoreBusinessIgnoreTranslate
    List<MetaBizModel> getAllMetaBizModel();

    /**
     * 获取所有支付路径
     * @return
     */
    @CoreBusinessIgnoreTranslate
    List<MetaPayPath> getAllMetaPayPath();

    /**
     * 获取通道的交易配置key
     *
     * @param provider
     * @return
     */
    String getTradeParamsKey(int provider);
}
