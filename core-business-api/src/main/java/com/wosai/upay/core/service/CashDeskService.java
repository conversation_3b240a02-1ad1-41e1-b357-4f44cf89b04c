package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/cash_desk")
public interface CashDeskService {

    /**
     * 创建收银台
     * @param cashDesk 请求参数 uc_user_ids收银员用户id集合
     * @return 收银台基本信息
     */
    Map createCashDesk(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = "name", message = "收银台名称不可为空"),
                    @PropNotEmpty(value = "merchant_id", message = "商户id不可为空"),
                    @PropNotEmpty(value = "store_id", message = "门店id不可为空"),
                    @PropNotEmpty(value = "terminals", message = "终端列表不可为空"),
                    @PropNotEmpty(value = "operator", message = "操作人不可为空")
            }
            ) Map cashDesk);

    /**
     * 更新收银台信息
     * @param cashDesk 收银台信息(只允许修改名称,终端列表,收银员列表)
     * @return 收银台基本信息
     */
    Map updateCashDesk(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = "id", message = "收银台id不可为空"),
                    @PropNotEmpty(value = "name", message = "收银台名称不可为空"),
                    @PropNotEmpty(value = "terminals", message = "终端列表不可为空"),
                    @PropNotEmpty(value = "operator", message = "操作人不可为空")
            }
            ) Map cashDesk);

    /**
     * 删除收银台信息
     * @param id 收银台id
     * @return 1
     */
    int deleteCashDesk(@NotBlank(message = "收银台id不能为空") String id);

    /**
     * 操作人删除收银台信息 记录操作日志
     * @param cashDesk 收银台信息
     * @return 1
     */
    int deleteCashDeskFromOperator(@PropNotEmpty.List({
            @PropNotEmpty(value = "id", message = "收银台id不可为空"),
            @PropNotEmpty(value = "operator", message = "操作人不可为空")
    }
            ) Map cashDesk);

    /**
     * 删除收银台绑定的设备信息
     * @param id 设备信息id
     * @return 1
     */
    int deleteCashDeskDevice(@NotBlank(message = "收银台设备信息id不能为空") String id);

    /**
     * 根据收银台id获取对应的收银台信息  不含绑定的设备信息
     * @param id 收银台id
     * @return 收银台信息
     */
    Map getSimpleCashDeskById(@NotBlank(message = "收银台id不能为空") String id);

    /**
     * 根据收银台id获取对应的收银台信息 包括绑定的设备信息
     * @param id 收银台id
     * @return 收银台信息 devices:绑定的设备信息
     */
    Map getCashDeskWithDevicesById(@NotBlank(message = "收银台id不能为空") String id);

    /**
     * 根据商户id和名字查询对应的收银台信息
     * @param merchantId 商户id
     * @param name 名字
     * @return 收银台基本信息
     */
    Map getSimpleCashDeskByMerchantIdAndName(@NotBlank(message = "商户id不能为空") String merchantId, @NotBlank(message = "name不能为空") String name);

    /**
     * 根据设备id查询对应的cash_desk_device信息
     * @param merchantId 商户id
     * @param deviceIds 设备id集合
     * @return 设备信息
     */
    List<Map> getCashDeskDevicesByMerchantIdAndDeviceIds(@NotBlank(message = "商户id不能为空") String merchantId, @NotEmpty(message = "设备id集合不能为空") List<String> deviceIds);

    /**
     * 根据设备id查询对应的cash_desk_device信息
     * @param deviceId 设备id
     * @return 收银台绑定设备信息
     */
    Map getCashDeskDeviceByMerchantIdAndDeviceId(@NotBlank(message = "商户id不能为空") String merchantId, @NotBlank(message = "device_id不能为空") String deviceId);

    /**
     * 查询收银台信息
     * @param pageInfo  分页信息
     * @param queryFilter 查询条件
     *                    merchant_id 商户id
     *                    store_ids   门店id集合
     *                    deleted     是否删除
     *
     * @return 收银台信息
     */
    ListResult findCashDesks(PageInfo pageInfo, Map queryFilter);

    /**
     * 查询收银台信息
     * @param pageInfo  分页信息
     * @param queryFilter 查询条件
     *                    ids id集合
     *                    sn 编号
     *                    name 名字
     *                    merchant_id 商户id
     *                    store_ids   门店id集合
     *                    deleted     是否删除
     * @return 收银台基本信息
     * */
    ListResult findSimpleCashDesk(PageInfo pageInfo, Map queryFilter);


    /**
     * 获取merchant_id下device_id对应的收银台信息
     * @param deviceIds 收银台绑定的设备信息列表
     * @param merchantId 商户id
     * @return key:设备id value:收银台信息
     */
    Map getSimpleCashDesksByMerchantIdAndDeviceIds(@NotBlank(message = "商户id不能为空") String merchantId, @NotEmpty(message = "设备id集合不能为空") List<String> deviceIds);

    /**
     * 获取device_id对应的收银台信息
     * @param deviceId 收银台绑定的设备信息id
     * @return 收银台信息
     */
    Map getSimpleCashDeskByMerchantIdAndDeviceId(@NotBlank(message = "商户id不能为空") String merchantId, @NotEmpty(message = "设备id不能为空") String deviceId);

    /**
     * 查询收银台的操作历史记录
     * @param pageInfo 分页信息
     * @param cashDeskId 收银台id
     * @return 操作日志
     */
    ListResult findCashDeskOpLogs(PageInfo pageInfo, @NotEmpty(message = "收银台id不能为空")String cashDeskId);


}
