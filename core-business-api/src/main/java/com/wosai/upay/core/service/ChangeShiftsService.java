package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.bean.request.ChangeShiftsBatchQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckInRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckOutRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsQueryRequest;
import com.wosai.upay.core.bean.request.HasCashDeskChangeShiftsRequest;
import com.wosai.upay.core.bean.request.UpdateChangeShiftsExtraRequest;
import com.wosai.upay.core.bean.response.ChangeShiftsBatchQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCashierQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckInResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckOutResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsQueryResponse;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;


/**
 * 交接班相关接口定义
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/changeShifts")
public interface ChangeShiftsService {

    /**
     * 终端签到功能
     *
     * @param request
     * @return
     */
    ChangeShiftsCheckInResponse changeShiftsCheckIn(ChangeShiftsCheckInRequest request);

    /**
     * 终端签退功能
     *
     * @param request
     */
    ChangeShiftsCheckOutResponse changeShiftsCheckOut(ChangeShiftsCheckOutRequest request);

    /**
     * 查询收银台签到信息
     *
     * @param request
     * @return 
     * id 
     * merchant_id 商户ID
     * type 类型 1：终端；2：收银台
     * service_id 终端id
     * batch_sn 批次号
     * cashier_no 收银员编号
     * start_date 签到时间
     * end_date   签退时间
     * extra 扩展信息，包含统计汇总
     */
    ChangeShiftsQueryResponse getChangeShiftsInfo(ChangeShiftsQueryRequest request);

    /**
     * 分页查询终端签到信息
     *
     * @param request
     * records
     * id 
     * merchant_id 商户ID
     * type 类型 1：终端；2：收银台
     * service_id 终端id
     * batch_sn 批次号
     * cashier_no 收银员编号
     * start_date 签到时间
     * end_date   签退时间
     * extra 扩展信息，包含统计汇总
     */
    ChangeShiftsBatchQueryResponse getChangeShiftsList(ChangeShiftsBatchQueryRequest request);

    /**
     * 
     * 内部管理接口，用于更新extra中的交易数据，便于后期查询
     * 
     * @param request
     */
    void updateChangeShiftsExtra(UpdateChangeShiftsExtraRequest request);

    /**
     * 
     * 终端是否存在收银台签到批次
     * 
     * @param request
     */
    boolean hasCashDeskChangeShifts(HasCashDeskChangeShiftsRequest request);

    /**
     * 
     * 通过时间查询班次收银员信息
     * 
     * @param request
     */
    ChangeShiftsCashierQueryResponse getChangeShiftsCashier(ChangeShiftsCashierQueryRequest request);

}
