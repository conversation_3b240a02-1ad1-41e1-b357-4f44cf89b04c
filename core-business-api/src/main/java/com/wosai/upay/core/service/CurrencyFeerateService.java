package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.CurrencyFeerate;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Created by maoyu on 2018/3/12.
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/currencyFeerate")
public interface CurrencyFeerateService {


    /**
     * 创建一条币种费率关联记录
     *
     * @param request String merchant_id
     *                int payway
     *                int sub_payway
     *                int currency_codes_pairs
     *                String fee_rate
     *                String remark
     * @return
     */
    Map createCurrencyFeerateMappingRules(Map request);




    /**
     * 创建一条币种费率关联记录并记录操作日志
     *
     * @param request String merchant_id
     *                int payway
     *                int sub_payway
     *                int currency_codes_pairs
     *                String fee_rate
     *                String remark
     * @return
     */
    Map createCurrencyFeerateMappingRulesAndLog(Map request, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);
    /**
     * 根据商户ID获取商户配置的境外支付币种费率映射关系
     * @param merchantId
     * @param pageInfo
     * @return
     */
    ListResult getCurrencyFeerate(@NotNull(message = "商户Id不可为空")
                           String merchantId, PageInfo pageInfo);

    /**
     * 根据修改费率及状态
     * @return
     */
    Map updateCurrencyFeerate(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_ID, message = "规则Id不可为空"),
            @PropNotEmpty(value = CurrencyFeerate.FEE_RATE, message = "费率不能为空")
    })Map request);

    /**
     * 根据修改费率及状态 并记录操作日志
     *
     * @return
     */

    Map updateCurrencyFeerateAndLog(@PropNotEmpty.List({
            @PropNotEmpty(value = ConstantUtil.KEY_ID, message = "规则Id不可为空"),
            @PropNotEmpty(value = CurrencyFeerate.FEE_RATE, message = "费率不能为空")
    }) Map request, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);

    Map getCurrencyFeeRateById(@NotNull(message = "规则Id不可为空")String id);
}
