package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/industry")
@Deprecated
public interface IndustryService {

    /**
     * 根据industryId获取行业.
     *
     * @param industryId
     * @return
     */
    Map getIndustry(String industryId);

    /**
     * 查询行业.
     *
     * @param queryFilter level1              一级行业
     *                    level2              二级行业
     *                    depth
     * @return
     */
    List findIndustrys(Map queryFilter);

    /**
     * 查询行业. 同findIndustrys
     *
     * @param queryFilter level1              一级行业
     *                    level2              二级行业
     *                    depth
     * @return
     */
    List findIndustries(Map queryFilter);


    /**
     * 获取分级行业.
     *
     * @return
     */
    List getLevelsIndustries();


}
