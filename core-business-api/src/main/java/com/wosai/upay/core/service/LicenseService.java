package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.validation.PropNotBlank;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

@CoreBusinessValidated
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/license")
public interface LicenseService {

    int saveLicense(@PropNotBlank.List({
            @PropNotBlank(value = "business_license_id", message = "许可证必须关联营业执照id")
    })Map license);

    /**
     * 根据 businessLicenseId 获取所有的许可证信息
     */
    List getLicenseByBusinessLicenseId(@NotBlank(message = "营业执照id不能为空")String businessLicenseId);

    Map<String, Object> getLicenseById(@NotBlank(message = "id不能为空") String id);

    int updateLicense(@PropNotBlank.List({
            @PropNotBlank(value = "id", message = "id不能为空")
    }) Map license);

    /**
     * 更新许可证,会对原表进行完全覆盖 (需要先删除表内同一营业执照id下的老数据 [由商户中心保证])
     * @param licenses
     * @return
     */
    int updateLicenseComplete(List<Map> licenses);

    /**
     * 根据门店营业执照id,删除名下的所有许可证(物理) 原表+中间表
     * @param businessLicenseId
     * @return
     */
    List<Map<String, Object>> deleteAllLicenseByBusinessLicenseIdTruly(String businessLicenseId);

    /**
     * 根据id删除营业执照
     */
    int deleteLicenseById(@NotBlank(message = "id不能为空") String id);

    /**
     * 根据门店营业执照id,删除名下的所有许可证(物理) 原表
     */
    int deleteAllLicenseByBusinessLicenseId(@NotBlank(message = "营业执照id不能为空") String businessLicenseId);
}
