package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/log")
public interface LogService {

    /**
     * 创建OpLog.
     *
     * @param opLog
     */
    Map createOpLog(Map opLog);

    /**
     * 根据opLogId删除OpLog.
     *
     * @param opLogId
     */
    void deleteOpLog(String opLogId);

    /**
     * 修改OpLog.
     *
     * @param opLog
     */
    Map updateOpLog(Map opLog);

    /**
     * 根据opLogId获取OpLog.
     *
     * @param opLogId
     * @return
     */
    Map getOpLog(String opLogId);

    /**
     * 分页查询OpLog.
     *
     * @param pageInfo
     * @param queryFilter request_system      请求系统 1:未知; 2:OSP; 3:服务商; 4:商户服务; 5:推广者服务 9:其他内部服务
     *                    operator_id         操作人id
     *                    operator_name       操作人姓名
     *                    operator_login      操作人账号
     *                    action_class        操作类
     *                    action_method       操作方法
     *                    duration            执行事件，单位：毫秒
     *                    result              执行结果，0：失败，1：成功
     *                    action_type         操作类型 1:登入; 2:登出; 3:查询; 4:查看; 5:导出; 6:新增; 7:编辑; 71:修改; 72:审核; 8:删除; 9:上传; 10:下载
     *                    action_date         操作日期，格式yyyyMMdd
     *                    client_ip           操作ip
     *                    deleted
     * @return
     */
    ListResult findOpLogs(PageInfo pageInfo, Map queryFilter);

    /**
     * 创建ImportantChangeLog.
     *
     * @param importantChangeLog
     */
    Map createImportantChangeLog(Map importantChangeLog);

    /**
     * 根据importantChangeLogId删除ImportantChangeLog.
     *
     * @param importantChangeLogId
     */
    void deleteImportantChangeLog(String importantChangeLogId);

    /**
     * 修改ImportantChangeLog.
     *
     * @param importantChangeLog
     */
    Map updateImportantChangeLog(Map importantChangeLog);

    /**
     * 根据importantChangeLogId获取ImportantChangeLog.
     *
     * @param importantChangeLogId
     * @return
     */
    Map getImportantChangeLog(String importantChangeLogId);

    /**
     * 分页查询ImportantChangeLog.
     *
     * @param pageInfo
     * @param queryFilter merchant_id
     *                    merchant_sn
     *                    object_type         变更对象类型 1：商户真实性审核申请 2：微信正式商户申请
     *                    object_id           变更对象id
     *                    type                变更类型，1：创建；2：修改；21：审核；3：删除；其他自定义
     *                    types               变更类型数组
     *                    post_change_status  变更后的状态
     *                    subject_type        操作人类型 1：运营用户 2：服务商用户 3：推广者用户 4：商户用户
     *                    subject_id          操作人用户ID
     *                    subject_name        操作人姓名
     *                    subject_login       操作人登录账号
     *                    change_time         操作日期时间戳
     *                    deleted
     * @return
     */
    ListResult findImportantChangeLogs(PageInfo pageInfo, Map queryFilter);

    /**
     * 创建任务申请日志.
     *
     * @param taskApplyLog
     */
    Map createTaskApplyLog(Map taskApplyLog);

    /**
     * 根据taskApplyLogId删除任务申请日志.
     *
     * @param taskApplyLogId
     */
    void deleteTaskApplyLog(String taskApplyLogId);

    /**
     * 修改任务申请日志.
     *
     * @param taskApplyLog
     */
    Map updateTaskApplyLog(Map taskApplyLog);

    /**
     * 根据taskApplyLogId获取任务申请日志.
     *
     * @param taskApplyLogId
     * @return
     */
    Map getTaskApplyLog(String taskApplyLogId);

    /**
     * 分页查询任务申请日志.
     *
     * @param pageInfo
     * @param queryFilter
     *      type                申请任务类型，1：对账单下载，2：订单下载，3：渠道分润报表下载，9：其他任务
     *      apply_system        请求系统，1:未知; 2:OSP; 3:服务商; 4:商户服务; 5:推广者服务 9:其他内部服务
     *      user_id             用户id
     *      apply_status        任务申请状态，0：新申请，1：执行中，2：执行成功，3：执行失败
     *      apply_date          申请日期
     *      deleted
     * @return
     */
    ListResult findTaskApplyLogs(PageInfo pageInfo, Map queryFilter);

    ListResult findTaskApplyLogsByTypeList(PageInfo pageInfo, Map<String,Object> queryFilter,List<Integer> typeList);

}
