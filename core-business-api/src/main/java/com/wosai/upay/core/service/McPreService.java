package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/mc_pre")
public interface McPreService {
    void saveMcPre(Map mcPre);

    void updateMcPre(Map mcPre);

    void deleteMcPre(@NotNull(message = "id不能为空") int id);

    Map findMcPre(@NotBlank(message = "devCode不能为空") String devCode, @NotBlank(message = "tableName不能为空") String tableName, @NotBlank(message = "bizId不能为空") String bizId);

    Map findMcPre(@NotBlank(message = "tableName不能为空") String tableName, @NotBlank(message = "bizId不能为空") String bizId);


    void deleteExcessData(@NotBlank(message = "tableName不能为空") String tableName, @NotBlank(message = "bizId不能为空") String bizId);


}
