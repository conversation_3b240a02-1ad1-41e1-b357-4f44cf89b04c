package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.MerchantAudit;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import javax.validation.constraints.NotNull;

import java.util.Map;

/**
 * Created by xuchmao on 16/11/10.
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/merchantAudit")
@Deprecated
public interface MerchantAuditService {
    /**
     * 根据id获取商户真实性审核记录
     *
     * @param id
     */
    Map getAuditById(
            @NotNull(message = "真实性审核记录ID不可为空")
                    String id);

    /**
     * 根据商户id获取商户真实性审核记录
     *
     * @param merchantId
     */
    Map getAuditByMerchantId(
            @NotNull(message = "真实性审核记录商户ID不可为空")
                    String merchantId);

    /**
     * 根据商户sn获取商户真实性审核记录
     *
     * @param merchantSn
     */
    Map getAuditByMerchantSn(
            @NotNull(message = "真实性审核记录商户sn不可为空")
                    String merchantSn);

}
