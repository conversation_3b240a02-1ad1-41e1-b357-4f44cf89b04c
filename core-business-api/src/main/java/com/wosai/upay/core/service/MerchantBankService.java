package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.validation.PropNotBothNull;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.PropIsMap;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.common.validation.PropSize;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.bank.MerchantClearingBankInfo;
import com.wosai.upay.core.model.bank.MerchantClearingBankRequest;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.List;
import java.util.Map;

/**
 * Created by lihebin on 2018/12/12.
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/merchantBank")
public interface MerchantBankService {

    /**
     * 保存商户银行预存信息
     * @param merchantBankAccountPre
     * @return
     */
    Map saveMerchantBankAccountPre(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBankAccountPre.MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantBankAccountPre.HOLDER, message = "{value} 账户持有人名称不能为空"),
                    @PropNotEmpty(value = MerchantBankAccountPre.NUMBER, message = "{value} 账户卡号不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBankAccountPre.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.HOLDER, max = 50, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符")
            })
            @PropIsMap(value = MerchantBankAccountPre.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
            Map merchantBankAccountPre);

    /**
     * 修改商户银行预存信息
     * @param merchantBankAccountPre
     * @return
     */
    Map updateMerchantBankAccountPre(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value} 银行卡预存id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBankAccountPre.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.HOLDER, max = 50, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccountPre.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantBankAccountPre.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
            Map merchantBankAccountPre);


    /**
     * 更新商户证件的正反照、证件有效期
     * @param merchantBankAccount
     */
    void updateMerchantBankAccountPhotosAndIdValidity(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBankAccountPre.MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantBankAccountPre.IDENTITY, message = "{value} 证件号不能为空"),
                    @PropNotEmpty(value = MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, message = "{value} 证件正面照片不能为空"),
                    @PropNotEmpty(value = MerchantBankAccountPre.ID_VALIDITY, message = "{value} 证件有效期不能为空"),
            }) Map merchantBankAccount
    );

    /**
     * 根据id查询银行预存信息
     * @param id
     * @return
     */
    Map getMerchantBankAccountPre(String id);

    /**
     * 根据商户id和卡号修改银行预存信息
     * @param merchantId
     * @param number
     * @return
     */
    Map getMerchantBankAccountPreByMerchantIdAndNumber(String merchantId, String number);


    /**
     * 分页查询商户银行预存信息
     *
     * @param pageInfo
     * @param queryFilter merchant_id
     *                    type
     *                    holder
     *                    id_type
     *                    identity
     *                    tax_payer_id
     *                    number
     *                    verify_status
     *                    bank_name
     *                    branch_name
     *                    city
     *                    cellphone
     * @return
     */
    ListResult findMerchantBankAccountPres(PageInfo pageInfo, Map queryFilter);


    /**
     * 修改商户银行卡信息
     * @param merchantBankAccount
     */
    void updateMerchantBankAccount(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBankAccount.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.HOLDER, max = 50, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantBankAccount.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchantBankAccount);


    /**
     * 删除银行信息预存数据
     * @param id
     */
    void deletedMerchantBankAccountPre(String id);

    /**
     * 删除merchant_bank_account数据
     * 用于支付业务开通过程中数据回滚
     * 禁止随意使用!!!
     * @param merchantBankAccountId
     */
    void deletedMerchantBankAccount(String merchantBankAccountId);

    /**
     * 根据账号和持有人证件号查询银行卡信息
     *
     * @param merchantBankAccount 仅有identity number账号
     */
    List<String> getMerchantBankAccountPreMerchantIds(
            @PropNotBothNull.List({
                    @PropNotBothNull(value = "identity", value2 = "number", message = "{value}和{value2}必须有一个有值"),
                    @PropNotBothNull(value = "identity", value2 = "number", message = "{value2}和{value}必须有一个有值")

            })
                    Map merchantBankAccount
    );

    /**
     * 根据清算行号查询商户银行信息
     *
     * @param request
     */
    List<MerchantClearingBankInfo> getMerchantBankInfosByClearingNumber(MerchantClearingBankRequest request);
}
