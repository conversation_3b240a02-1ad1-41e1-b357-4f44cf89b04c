package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.PropIsMap;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.common.validation.PropSize;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.MerchantBizBankAccount;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/09/22
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/merchantBizBank")
public interface MerchantBizBankAccountService {

    /**
     * 保存
     *
     * @param merchantBizBankAccount
     * @return
     */
    Map saveMerchantBizBankAccount(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBizBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantBizBankAccount.HOLDER, message = "{value} 账户持有人名称不能为空"),
                    @PropNotEmpty(value = MerchantBizBankAccount.NUMBER, message = "{value} 账户卡号不能为空"),
                    @PropNotEmpty(value = MerchantBizBankAccount.BIZ, message = "{value} 业务方不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBizBankAccount.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.HOLDER, max = 45, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符")
            })
            @PropIsMap(value = MerchantBizBankAccount.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchantBizBankAccount);

    /**
     * 修改商户银行预存信息
     *
     * @param merchantBizBankAccount
     * @return
     */
    Map updateMerchantBizBankAccount(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value} 银行卡预存id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBizBankAccount.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.HOLDER, max = 45, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBizBankAccount.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantBizBankAccount.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchantBizBankAccount);

    /**
     * 根据id查询银行预存信息
     *
     * @param id
     * @return
     */
    Map getMerchantBizBankAccount(String id);

    /**
     * 根据商户id和卡号查询银行预存信息
     *
     * @param merchantId
     * @param biz
     * @param number
     * @return
     */
    Map getMerchantBizBankAccountByMerchantIdAndNumber(String merchantId, String biz, String number);


    /**
     * 分页查询商户银行预存信息
     *
     * @param pageInfo
     * @param queryFilter merchant_id
     *                    type
     *                    biz
     *                    holder
     *                    id_type
     *                    identity
     *                    tax_payer_id
     *                    number
     *                    verify_status
     *                    bank_name
     *                    branch_name
     *                    city
     *                    cellphone
     * @return
     */
    ListResult findMerchantBizBankAccounts(PageInfo pageInfo, @PropNotEmpty.List({
            @PropNotEmpty(value = MerchantBizBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = MerchantBizBankAccount.BIZ, message = "{value} 业务方不能为空")
    })Map queryFilter);


    /**
     * 删除银行信息预存数据
     *
     * @param id
     */
    void deletedMerchantBizBankAccount(String id);
}
