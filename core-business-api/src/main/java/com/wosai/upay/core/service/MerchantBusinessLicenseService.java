package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.validation.PropNotBothNull;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * Created by hzq on 19/4/3.
 */
@CoreBusinessValidated
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/merchant_business_license")
public interface MerchantBusinessLicenseService {
    int save(Map merchantBusinessLicense);

    Map<String, Object> getBusinessLicenseByMerchantId(String merchantId);

    Map<String, Object> getBusinessLicenseById(Long id);

    int updateMerchantBusinessLicense(Map merchantBusinessLicense);

    /**
     根据id删除营业执照
     * @param id
     * @return
     */
    int deleteMerchantBusinessLicenseById(@NotBlank(message = "id不能为空") String id);

    /**
     * 根据号码查询营业执照
     *
     * @param merchantBusinessLicense 仅有number账号,legal_person_id_number 法人证件号
     */

    List<String> getBusinessLicenseMerchantIds(
            @PropNotBothNull.List({
                    @PropNotBothNull(value = "number", value2 = "legal_person_id_number", message = "{value2}和{value}必须有一个有值")

            })
                    Map merchantBusinessLicense

    );

    /**
     * 根据号码查询营业执照
     * @param pageInfo 分页信息
     * @param merchantBusinessLicense 仅有number账号,legal_person_id_number 法人证件号
     * @return 营业执照列表
     */
    ListResult getBusinessLicenseByLegalPersonIdNumberOrNumber(PageInfo pageInfo,
                                                               @PropNotBothNull.List({
                    @PropNotBothNull(value = "number", value2 = "legal_person_id_number", message = "{value2}和{value}必须有一个有值")

            })
                    Map merchantBusinessLicense

    );
}
