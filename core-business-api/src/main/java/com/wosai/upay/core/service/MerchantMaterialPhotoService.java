package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Map;

@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/merchantMaterialPhoto")
public interface MerchantMaterialPhotoService {

    /**
     * 创建商户材料信息
     *
     * @param merchantMaterialPhoto 商户材料信息
     * @return 影响行数
     */
    int createMerchantMaterialPhoto(Map<String, Object> merchantMaterialPhoto);

    /**
     * 更新商户材料信息
     *
     * @param merchantMaterialPhoto 商户材料信息
     * @return 影响行数
     */
    int updateMerchantMaterialPhoto(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}不可为空")
            })
            Map<String, Object> merchantMaterialPhoto
    );

    /**
     * 根据商户材料信息ID查询商户材料信息
     *
     * @param merchantMaterialPhotoId 商户材料信息ID
     * @return 商户材料信息
     */
    Map<String, Object> findMerchantMaterialPhotoById(@NotBlank(message = "merchantMaterialPhotoId不能为空") Long merchantMaterialPhotoId);

    /**
     * 根据商户ID查询商户材料信息
     *
     * @param merchantId 商户ID
     * @return 商户材料信息
     */
    Map<String, Object> findMerchantMaterialPhotoByMerchantId(@NotNull(message = "merchantId不能为空") String merchantId);


    /**
     * 保存或更新商户材料信息
     *
     * @param merchantMaterialPhoto 商户材料信息
     * @return 影响行数
     */
    int saveOrUpdateMerchantMaterialPhoto(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = "merchant_id", message = "{value}不可为空")
            })
            Map<String, Object> merchantMaterialPhoto);
}
