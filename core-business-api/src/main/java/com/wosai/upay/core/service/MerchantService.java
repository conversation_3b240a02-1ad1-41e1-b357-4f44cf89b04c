package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.*;
import com.wosai.upay.core.bean.request.BaseMerchantRequest;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.bean.request.WapMiniChangeVerifyRequest;
import com.wosai.upay.core.bean.response.MerchantBankHolderSimpleIdentityInfoResponse;
import com.wosai.upay.core.bean.response.WapMiniChangeVerifyResponse;
import com.wosai.upay.core.common.CoreBusinessTranslate;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.constant.BusinessLogConstant;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Created by jianfree on 21/1/16.
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/merchant")
public interface MerchantService {

    /**
     * 创建Merchant，商户入网请使用接口：createMerchantComplete.
     *
     * @param merchant
     */
    Map createMerchant(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = Merchant.NAME, message = "{value}商户名称不可为空"),
            })
            @PropPattern.List({
                    @PropPattern(value = Merchant.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}经度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Merchant.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Merchant.STREET_ADDRESS, nullable = false, regex = ".*[\\u4e00-\\u9fa5].*", message = "{value}商户联系地址中必须包含至少一个中文"),
                    //@PropPattern(value = Merchant.STREET_ADDRESS_DESC, regex = ".*[\\u4e00-\\u9fa5].*", message = "{value}商户街道地址备注中必须包含至少一个中文"),
                    @PropPattern(value = Merchant.CONTACT_NAME, nullable = false, regex = "^((?![\\u3000-\\u3009\\u3012-\\u303F])[ \\u2E80-\\uFE4Fa-zA-Z【·】]){2,40}$", message = "{value}联系人姓名只支持中文及【·】或英文及空格,2-40个字符"),
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Merchant.LONGITUDE, value2 = Merchant.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Merchant.LATITUDE, value2 = Merchant.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Merchant.SN, max = 32, message = "{value}商户可见的商户号不可超过{max}字符"),
                    @PropSize(value = Merchant.NAME, max = 60, message = "{value}商户名不可超过{max}字符"),
                    @PropSize(value = Merchant.ALIAS, max = 60, message = "{value}商户别名/常用名不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS_NAME, max = 60, message = "{value}商户经营名称不可超过{max}字符"),
                    @PropSize(value = Merchant.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Merchant.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Merchant.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS_DESC, max = 255, message = "{value}街道地址备注不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_EMAIL, max = 30, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS, max = 255, message = "{value}经营内容不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_NAME, max = 50, message = "{value}所有人姓名不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_CELLPHONE, max = 32, message = "{value}所有人手机不可超过{max}字符"),
                    @PropSize(value = Merchant.CUSTOMER_PHONE, max = 32, message = "{value}客服电话不可超过{max}字符"),
                    @PropSize(value = Merchant.CLIENT_SN, max = 50, message = "{value}商户外部商户号 不可超过{max}字符"),
            })
            @PropIsMap(value = Merchant.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchant);

    /**
     * 创建Merchant
     *
     * @param merchant
     */
    Map createMerchantForMerchantCenter(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = Merchant.NAME, message = "{value}商户名称不可为空"),
            })
            @PropPattern.List({
                    @PropPattern(value = Merchant.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}经度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Merchant.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Merchant.STREET_ADDRESS, nullable = false, regex = ".*[\\u4e00-\\u9fa5].*", message = "{value}商户联系地址中必须包含至少一个中文"),
                    //@PropPattern(value = Merchant.STREET_ADDRESS_DESC, regex = ".*[\\u4e00-\\u9fa5].*", message = "{value}商户街道地址备注中必须包含至少一个中文"),
                    @PropPattern(value = Merchant.CONTACT_NAME, nullable = false, regex = "^((?![\\u3000-\\u3009\\u3012-\\u303F])[ \\u2E80-\\uFE4Fa-zA-Z【·】]){2,40}$", message = "{value}联系人姓名只支持中文及【·】或英文及空格,2-40个字符"),
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Merchant.LONGITUDE, value2 = Merchant.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Merchant.LATITUDE, value2 = Merchant.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Merchant.SN, max = 32, message = "{value}商户可见的商户号不可超过{max}字符"),
                    @PropSize(value = Merchant.NAME, max = 60, message = "{value}商户名不可超过{max}字符"),
                    @PropSize(value = Merchant.ALIAS, max = 60, message = "{value}商户别名/常用名不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS_NAME, max = 60, message = "{value}商户经营名称不可超过{max}字符"),
                    @PropSize(value = Merchant.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Merchant.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Merchant.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS_DESC, max = 255, message = "{value}街道地址备注不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_EMAIL, max = 30, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS, max = 255, message = "{value}经营内容不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_NAME, max = 50, message = "{value}所有人姓名不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_CELLPHONE, max = 32, message = "{value}所有人手机不可超过{max}字符"),
                    @PropSize(value = Merchant.CUSTOMER_PHONE, max = 32, message = "{value}客服电话不可超过{max}字符"),
                    @PropSize(value = Merchant.CLIENT_SN, max = 50, message = "{value}商户外部商户号 不可超过{max}字符"),
            })
            @PropIsMap(value = Merchant.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchant);

    /**
     * 创建Merchant. 包含商户费率配置, 银行卡信息, 第一个门店信息, 关联account, 真实性审核信息
     * merchant_config
     * merchant_bank_account
     * store
     * account_id
     * merchant_audit:
     * {
     * brand: {
     * "photo": "1234",
     * "province": "江苏省",
     * "city": "苏州市",
     * "district": "吴中区",
     * "address": "江苏省苏州市吴中区苏州2.5产业园",
     * "time": *************,
     * "longitude": 120.776917,
     * "latitude": 31.308309,
     * "version": "2.3.4",
     * "system": "CRM"
     * }
     * indoor_materia: {...},
     * outdoor_material: {...},
     * other: [{...},{...}],
     * remark,
     * submitter,
     * submit_platform,
     * submit_organization_path
     * }
     *
     * @param request
     */
    Map createMerchantComplete(Map request);

    /**
     * 根据merchantId启用商户.
     *
     * @param merchantId
     */
    void enableMerchant(
            @NotNull(message = "商户ID不可为空")
                    String merchantId);

    /**
     * 根据merchantId禁用商户.
     *
     * @param merchantId
     */
    void disableMerchant(
            @NotNull(message = "商户ID不可为空")
                    String merchantId);

    /**
     * 根据merchantId关闭商户.
     *
     * @param merchantId
     */
    void closeMerchant(
            @NotNull(message = "商户ID不可为空")
                    String merchantId);


    /**
     * 根据merchantId关闭商户，同时记录操作日志.
     *
     * @param merchantId
     * @param opLogCreateRequest
     */
    void closeMerchantAndLog(
            @NotNull(message = "商户ID不可为空")
            String merchantId, OpLogCreateRequest opLogCreateRequest);


    /**
     * 修改Merchant.
     *
     * @param merchant
     */
    Map updateMerchant(
            @PropPattern.List({
                    @PropPattern(value = Merchant.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Merchant.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Merchant.LONGITUDE, value2 = Merchant.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Merchant.LATITUDE, value2 = Merchant.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Merchant.SN, max = 32, message = "{value}商户可见的商户号不可超过{max}字符"),
                    @PropSize(value = Merchant.NAME, max = 128, message = "{value}商户名不可超过{max}字符"),
                    @PropSize(value = Merchant.ALIAS, max = 128, message = "{value}商户别名/常用名不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS_NAME, max = 60, message = "{value}商户经营名称不可超过{max}字符"),
                    //@PropSize(value = Merchant.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Merchant.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Merchant.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Merchant.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Merchant.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS_DESC, max = 255, message = "{value}街道地址备注不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_NAME, max = 40, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS, max = 255, message = "{value}经营内容不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_NAME, max = 50, message = "{value}所有人姓名不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_CELLPHONE, max = 32, message = "{value}所有人手机不可超过{max}字符"),
                    @PropSize(value = Merchant.CUSTOMER_PHONE, max = 32, message = "{value}客服电话不可超过{max}字符"),
                    @PropSize(value = Merchant.CLIENT_SN, max = 50, message = "{value}商户外部商户号 不可超过{max}字符"),
            })
            @PropIsMap(value = Merchant.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchant);


    /**
     * 修改Merchant并记录日志.
     *
     * @param merchant
     * @param opLogCreateRequest
     * @return
     */
    Map updateMerchantAndLog(
            @PropPattern.List({
                    @PropPattern(value = Merchant.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Merchant.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Merchant.LONGITUDE, value2 = Merchant.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Merchant.LATITUDE, value2 = Merchant.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Merchant.SN, max = 32, message = "{value}商户可见的商户号不可超过{max}字符"),
                    @PropSize(value = Merchant.NAME, max = 128, message = "{value}商户名不可超过{max}字符"),
                    @PropSize(value = Merchant.ALIAS, max = 128, message = "{value}商户别名/常用名不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS_NAME, max = 60, message = "{value}商户经营名称不可超过{max}字符"),
                    //@PropSize(value = Merchant.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Merchant.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Merchant.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Merchant.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Merchant.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
                    @PropSize(value = Merchant.STREET_ADDRESS_DESC, max = 255, message = "{value}街道地址备注不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_NAME, max = 40, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Merchant.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Merchant.BUSINESS, max = 255, message = "{value}经营内容不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_NAME, max = 50, message = "{value}所有人姓名不可超过{max}字符"),
                    @PropSize(value = Merchant.OWNER_CELLPHONE, max = 32, message = "{value}所有人手机不可超过{max}字符"),
                    @PropSize(value = Merchant.CUSTOMER_PHONE, max = 32, message = "{value}客服电话不可超过{max}字符"),
                    @PropSize(value = Merchant.CLIENT_SN, max = 50, message = "{value}商户外部商户号 不可超过{max}字符"),
            })
            @PropIsMap(value = Merchant.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
            Map merchant, OpLogCreateRequest opLogCreateRequest);


    /**
     * 根据商户id获取商户
     *
     * @param merchantId
     * @return
     */
    Map getMerchantByMerchantId(String merchantId);

    /**
     * 根据商户Id获取商户及银行卡信息.
     *
     * @param merchantId
     * @return
     */
    Map getMerchantAndBankAccount(String merchantId);

    /**
     * 从bank-info-service获取相关行业信息(缓存起来)
     *
     * @return
     */
    Map<String, String> getIndustriesNameMapFromBankInfoService();

    /**
     * 根据商户号获取商户
     *
     * @param merchantSn
     * @return
     */
    @CoreBusinessTranslate
    Map getMerchantByMerchantSn(String merchantSn);

    /**
     * 根据商户id获取商户
     *
     * @param merchantId
     * @return
     */
    Map getMerchant(String merchantId);

    /**
     * 根据商户号获取商户
     *
     * @param merchantSn
     * @return
     */
    @CoreBusinessTranslate
    Map getMerchantBySn(String merchantSn);

    /**
     * 分页查询商户信息
     *
     * @param pageInfo
     * @param queryFilter merchant_sn
     *                    merchant_sns
     *                    merchant_id
     *                    merchant_name
     *                    solicitor_id
     *                    solicitor_name
     *                    merchant_alias
     *                    contact_phone
     *                    contact_cellphone
     *                    owner_cellphone
     *                    cellphone (查询contact_cellphone or owner_cellphone)
     *                    vendor_id
     *                    // TODO 有很多额外条件，比如服务商、推广者id及sn、状态、地区等
     * @return
     */
    ListResult findMerchants(PageInfo pageInfo, Map queryFilter);

    ListResult findSimpleMerchants(PageInfo pageInfo, Map queryFilter);

    ListResult findMerchantsFromSlaveDb(PageInfo pageInfo, Map queryFilter);

    /**
     * 绑定银行卡.
     *
     * @param merchantBankAccount
     */
    Map bindMerchantBankAccount(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantBankAccount.HOLDER, message = "{value} 账户持有人名称不能为空"),
                    @PropNotEmpty(value = MerchantBankAccount.NUMBER, message = "{value} 账户卡号不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBankAccount.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.HOLDER, max = 50, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantBankAccount.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchantBankAccount);


    /**
     * 更新merchant_bank_account表相关信息
     *
     * @param merchantBankAccount
     */
    @Deprecated
    void updateMerchantBankAccountInfo(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空"),
                    @PropNotEmpty(value = MerchantBankAccount.NUMBER, message = "{value} 账户卡号不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBankAccount.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.HOLDER, max = 50, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantBankAccount.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchantBankAccount);


    /**
     * 更新merchant_bank_account表相关信息，注意此接口不会根据征信字段是否改变修改verify_status（可更新verify_status）,目前只用于存量商户风控信息预处理表的信息同步
     *
     * @param merchantBankAccount
     */
    @Deprecated
    void syncMerchantBankAccountInfoAfterVerifiedSuccess(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = MerchantBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantBankAccount.MERCHANT_ID, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.HOLDER, max = 50, message = "{value}账户持有人名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.IDENTITY, max = 45, message = "{value}身份证编号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.NUMBER, max = 45, message = "{value}账号不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BANK_NAME, max = 45, message = "{value}开户银行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.BRANCH_NAME, max = 45, message = "{value}分支行名称不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CITY, max = 32, message = "{value}分支行所在城市不可超过{max}字符"),
                    @PropSize(value = MerchantBankAccount.CELLPHONE, max = 32, message = "{value}和账号绑定的手机号不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantBankAccount.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段参数必须为Map格式")
                    Map merchantBankAccount);


    /**
     * 修改bank_acccount中的身份证照片和工商税务号等非关键信息
     */
    @Deprecated
    void updateBankAccountEdgeInfo(@PropNotEmpty.List({
            @PropNotEmpty(value = MerchantBankAccount.MERCHANT_ID, message = "{value} 商户id不能为空"),
    })
                                   @PropSize.List({
                                           @PropSize(value = MerchantBankAccount.TAX_PAYER_ID, max = 45, message = "{value}工商税务号不可超过{max}字符"),
                                           @PropSize(value = MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, max = 255, message = "{value}身份证正面照图片不可超过{max}字符"),
                                           @PropSize(value = MerchantBankAccount.HOLDER_ID_BACK_PHOTO, max = 255, message = "{value}身份证反面照图片不可超过{max}字符"),
                                   }) Map merchantBankAccount);


    /**
     * 查询绑定卡
     * holder_id_front_ocr_status 状态: 0-待认证;1 - 认证成功 2-认证失败; | 身份证正面OCR认证状态
     * holder_id_back_ocr_status  状态: 0-待认证;1 - 认证成功 2-认证失败; | 身份证反面OCR认证状态
     * holder_id_status 状态: 0-待认证;1 - 认证成功 2-认证失败; | 身份证OCR认证状态
     *
     * @param merchantId
     * @return
     */
    Map getMerchantBankAccountByMerchantId(String merchantId);

    /**
     * 获取简要银行卡持有人的证件信息
     *
     * @param request
     * @return
     */
    MerchantBankHolderSimpleIdentityInfoResponse getMerchantBankAccountHolderIdentityInfo(BaseMerchantRequest request);


    Map getMerchantBankAccount(String merchantBankAccountId);


    List<Map<String, Object>> getAutoWithdrawMerchantRollListByCtime(Long beginTime, int pageSize);


    List<Map<String, Object>> getMerchantRollListByCtime(Long beginTime, Long endTime, int pageSize);

    /**
     * 根据merchantId获取商户开发者配置
     *
     * @param merchantId
     * @return
     */
    Map getMerchantDeveloperByMerchantId(String merchantId);

    /**
     * 更新银行卡 & 写marketing.dts rmq消息，注意，此接口仅提供core-b内部及merchant-contract调用，其余场景请勿使用.
     * 参数中必须包含id.
     *
     * @param bankAccount
     */
    void updateBankAccountInnerMethod(Map bankAccount);

    /**
     * 根据merchantSn获取商户银行账户校验状态
     *
     * @param merchanSn
     * @return
     */
    Map getBankAccountVerifyStatus(String merchanSn);

    /**
     * 分页查询商户银行账户信息
     *
     * @param pageInfo
     * @param queryFilter merchant_id
     *                    merchant_ids
     *                    type
     *                    holder
     *                    id_type
     *                    identity
     *                    tax_payer_id
     *                    number
     *                    verify_status
     *                    bank_name
     *                    branch_name
     *                    city
     *                    cellphone
     */
    ListResult findMerchantBankAccounts(PageInfo pageInfo, Map queryFilter);

    /**
     * 修改提现方式
     *
     * @param update
     * @return
     */
    Map<String, Object> updateMerchantWithdrawModeById(@PropNotEmpty.List({
            @PropNotEmpty(value = DaoConstants.ID, message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = Merchant.WITHDRAW_MODE, message = "{value} 提现模式不能为空")
    }) Map<String, Object> update,
                                                       @PropNotEmpty.List({
                                                               @PropNotEmpty(value = BusinessLogConstant.PLATFORM, message = "{value} 平台不能为空"),
                                                               @PropNotEmpty(value = BusinessLogConstant.OPERATOR, message = "{value} 操作人不能为空")
                                                       }) Map<String, Object> operatorInfo);


    /**
     * 修改提现方式 添加日志
     *
     * @param update
     * @return
     */
    Map<String, Object> updateMerchantWithdrawModeByIdAndLog(@PropNotEmpty.List({
            @PropNotEmpty(value = DaoConstants.ID, message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = Merchant.WITHDRAW_MODE, message = "{value} 提现模式不能为空")
    }) Map<String, Object> update,
                                                             @PropNotEmpty.List({
                                                                     @PropNotEmpty(value = BusinessLogConstant.PLATFORM, message = "{value} 平台不能为空"),
                                                                     @PropNotEmpty(value = BusinessLogConstant.OPERATOR, message = "{value} 操作人不能为空")
                                                             }) Map<String, Object> operatorInfo, OpLogCreateRequest opLogCreateRequest);

    /**
     * 修改提现方式, 临时接口， 不要用于正常业务
     *
     * @param update
     * @return
     */
    void tmpUpdateMerchantWithdrawModeById(@PropNotEmpty.List({
            @PropNotEmpty(value = DaoConstants.ID, message = "{value} 商户id不能为空"),
            @PropNotEmpty(value = Merchant.WITHDRAW_MODE, message = "{value} 提现模式不能为空")
    }) Map<String, Object> update,
                                                       @PropNotEmpty.List({
                                                               @PropNotEmpty(value = BusinessLogConstant.PLATFORM, message = "{value} 平台不能为空"),
                                                               @PropNotEmpty(value = BusinessLogConstant.OPERATOR, message = "{value} 操作人不能为空")
                                                       }) Map<String, Object> operatorInfo);

    /**
     * 获取变化的商戶
     * 左右区间闭合
     */
    List<Map<String, Object>> getChangeMerchant(long beginMtime, long endMtime);

    /**
     * 根据商户id删除商户
     *
     * @param merchantId
     * @return
     */
    int deleteMerchantByMerchantId(@NotBlank(message = "merchantId不能为空") String merchantId);


    /**
     * 查找merchantIds中开启了自动提现的商户
     *
     * @param merchantIds
     * @return
     */
    List<String> getAutoWithdrawMerchantsByMerchantIds(List<String> merchantIds);


    /**
     * 清理redis中地区code缓存
     *
     * @param code
     */
    void clearRedisDistrict(String code);

    /**
     * 微信切换到小程序业务校验
     *
     * @param request
     * @return
     */
    WapMiniChangeVerifyResponse changeToMiniVerify(WapMiniChangeVerifyRequest request);

    /**
     * 微信切换到门店码wap业务校验
     *
     * @param request
     * @return
     */
    WapMiniChangeVerifyResponse changeToWapVerify(WapMiniChangeVerifyRequest request);


}
