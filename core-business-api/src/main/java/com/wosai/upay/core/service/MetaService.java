package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.bean.model.MetaBizModel;
import com.wosai.upay.core.bean.model.MetaPayPath;
import com.wosai.upay.core.bean.model.MetaPaySource;
import com.wosai.upay.core.bean.request.MetaBizModelCreateRequest;
import com.wosai.upay.core.bean.request.MetaPayPathCreateRequest;
import com.wosai.upay.core.bean.request.MetaPaySourceCreateRequest;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.List;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2023/12/13.
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/meta")
public interface MetaService {

    /**
     * 创建业务模式
     * @param request
     */
    void createMetaBizModel(MetaBizModelCreateRequest request);

    /**
     * 创建支付路径
     * @param request
     */
    void createMetaPayPath(MetaPayPathCreateRequest request);


    /**
     * 创建用户行为来源
     *
     * @param request
     */
    void createMetaPaySource(MetaPaySourceCreateRequest request);


    /**
     * 获取所有业务模式
     * @return
     */
    List<MetaBizModel> getAllMetaBizModel();

    /**
     * 获取所有支付路径
     * @return
     */
    List<MetaPayPath> getAllMetaPayPath();


    /**
     * 获取所有用户行为来源
     * @return
     */
    List<MetaPaySource> getAllMetaPaySource();


}
