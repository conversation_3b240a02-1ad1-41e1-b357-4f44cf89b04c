package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-24
 * @Description:
 */

@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/photoInfo")
public interface PhotoInfoService {

    /**
     * 创建照片信息
     *
     * @param photoInfo
     * @return
     */
    int createPhotoinfo(Map photoInfo);

    /**
     * 更新照片信息
     *
     * @param photoInfo
     * @return
     */
    int updatePhotoinfo(@PropNotEmpty.List({
            @PropNotEmpty(value = DaoConstants.ID, message = "{value}不可为空")
    }) Map photoInfo);

    /**
     * 物理删除单张照片
     *
     * @param photoInfoId
     * @return
     */
    int deletePhotoinfo(@NotBlank(message = "photoInfoId不能为空") String photoInfoId);

    /**
     * 物理删除多张照片，最多传入100个id
     *
     * @param photoInfoIds
     * @return
     */
    int deletePhotoInfos(@NotEmpty(message = "photoInfoIds不能为空") List<String> photoInfoIds);

    /**
     * 查找单张图片
     *
     * @param photoInfoId
     * @param devCode
     * @return
     */
    Map findPhotoinfo(@NotBlank(message = "photoinfoId不能为空") String photoInfoId, String devCode);


    /**
     * 批量查询图片
     *
     * @param photoInfoIds 图片id集合
     * @return 格式: {图片id:{图片信息}}
     */
    Map<String, Map> findPhotoinfoBatch(List<String> photoInfoIds);
}
