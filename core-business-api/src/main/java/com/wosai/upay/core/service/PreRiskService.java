package com.wosai.upay.core.service;

import java.util.Map;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.PropNotBothNull;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.common.validation.PropNotNullOnProp;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

@UpayCoreServiceAnnotation
@JsonRpcService("rpc/prerisk")
@CoreBusinessValidated
public interface PreRiskService {
    String SERVICE = "service";                 // 调用服务名称
    String WOSAI_STORE_ID = "wosai_store_id";   // 门店号
    String TERMINAL_SN = "terminal_sn";         // 终端号
    String CLIENT_SN = "client_sn";             // 下单订单号
    String IP = "ip";                           // 客户端ip
    String PAYWAY = "payway";                   // 支付通道
    String SUB_PAYWAY = "sub_payway";           // 支付模式
    String DYNAMIC_ID = "dynamic_id";           // 条码
    /**
     *  终端签到请求风控
     * 
     * @param params
     * @return
     */
    public boolean checkinCheck(
            @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空")
            @PropNotEmpty.List({
                @PropNotEmpty(value = SERVICE, message = "{value}调用服务名不能为空"),
                @PropNotEmpty(value = IP, message = "{value}客户端IP不能为空"),
            })
            Map info);

    /**
     *  交易请求风控
     * 
     * @param params
     * @return
     */
    public boolean tradeCheck(
            @PropNotBothNull.List({
                @PropNotBothNull(value=WOSAI_STORE_ID, value2=TERMINAL_SN, message="{value}喔噻商户号和{value2}喔噻终端序列号不能同时为空"),
                @PropNotBothNull(value=DYNAMIC_ID, value2=PAYWAY, message="{value}支付条码和{value2}支付通道不能同时为空"),
            })
            @PropNotEmpty.List({
                @PropNotEmpty(value = SERVICE, message = "{value}调用服务名不能为空"),
                @PropNotEmpty(value = IP, message = "{value}客户端IP不能为空"),
                
            })
            @PropNotNullOnProp.List({
                @PropNotNullOnProp(value = PAYWAY, value2 = SUB_PAYWAY, message = "{value}支付通道和支付模式{value2}必须同时有值"),
            })
            Map info);
}
