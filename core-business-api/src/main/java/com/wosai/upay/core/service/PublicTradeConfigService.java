package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.Map;

/**
 * <AUTHOR> Date: 2019-07-05 Time: 16:20
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/publicTradeConfig")
@CoreBusinessValidated
public interface PublicTradeConfigService {


    /**
     * 更新终端交易配置状态
     *
     * @param terminalConfig
     */
    void updateTerminalConfigStatus(Map terminalConfig);

}
