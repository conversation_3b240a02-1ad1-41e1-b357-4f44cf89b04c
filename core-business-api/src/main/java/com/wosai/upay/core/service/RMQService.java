package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.Map;

/**
 * <AUTHOR>
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/rmq")
public interface RMQService {

    /**
     * 写exchange marketing.dts.
     *
     * @param message
     */
    void writeMarketingDTS(Map message);

    /**
     * 延时2秒写exchange marketing.dts.
     *
     * @param message
     * @param delay
     */
    void writeMarketingDTS(Map message, int delay);

    /**
     * 写拉卡拉银行卡校验反馈结果消息exchange merchant.bank.account.verify.
     *
     * @param message
     */
    void writeMerchantBankAccountVerify(Map message);

    /**
     * 写真实性审核状态变更消息exchange merchant.audit.status.change.
     *
     * @param message
     */
    void writeMerchantAuditStatusChange(Map message);

    /**
     * 终端状态变更消息exchange core.terminal.status.change.
     *
     * @param message
     */
    void writeTerminalStatusChange(Map message);

    /**
     * 创建新商户消息exchange core.new.merchant.created.
     *
     * @param message
     */
    void writeNewMerchantCreated(Map message);

    /**
     * 创建新门店消息exchange core.new.merchant.created.
     *
     * @param message
     */
    void writeNewStoreCreated(Map message);

    /**
     * 激活终端消息exchange core.terminal.status.active
     *
     * @param message
     */
    void writeTerminalActive(Map message);

    /**
     * 写入提现模式变更消息
     *
     * @param message
     */
    void writeWithdrawModeChange(Map message, Map<String, Object> operatorInfo);

    /**
     * 写入redis变动信息
     *
     * @param message
     */
    void writeRedisChange(Map message);

    /**
     * 营业执照更新
     *
     * @param message
     */
    void writeUpdateBusinessLicense(Map message);

    /**
     *  发送store_ext表更新消息
     * @param origin
     * @param updated
     */
    void writeUpdateStoreExt(Map origin, Map updated);

}
