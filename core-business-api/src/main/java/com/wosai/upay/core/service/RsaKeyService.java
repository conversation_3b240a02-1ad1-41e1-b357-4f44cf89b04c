package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.model.RsaKey;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import com.wosai.upay.common.validation.PropIsMap;
import com.wosai.upay.common.validation.PropSize;

import java.util.Map;

/**
 * Created by jianfree on 31/12/15.
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/rsaKey")
public interface RsaKeyService {

    /**
     * 创建rsaKey
     * @param rsaKey
     * @return
     */
    Map create(
            @PropSize.List({
                    @PropSize(value = RsaKey.DIGEST, max = 32, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = RsaKey.DATA, nullable = true, emptyable = true, message = "{value}参数必须为Map格式")
            Map rsaKey);

    /**
     * 更新
     * @param rsaKey
     * @return
     */
    Map update(
            @PropSize.List({
                    @PropSize(value = RsaKey.DIGEST, max = 32, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = RsaKey.DATA, nullable = true, emptyable = true, message = "{value}参数必须为Map格式")
            Map rsaKey);

    /**
     * 获取
     * @param rsaKeyId
     * @return
     */
    Map getRsaKey(String rsaKeyId);

    /**
     * 删除
     * @param rsaKeyId
     */
    void delete(String rsaKeyId);

    /**
     * 根据digest获取
     * @param digest
     * @return
     */
    Map getRsaKeyByDigest(String digest);

    /**
     * 根据名字获取
     * @param name
     * @return
     */
    Map getRsaKeyByName(String name);

    /**
     * 存储秘钥
     * @param data 数据
     * @return 返回值
     */
    String storeRsaKey(String data);
}
