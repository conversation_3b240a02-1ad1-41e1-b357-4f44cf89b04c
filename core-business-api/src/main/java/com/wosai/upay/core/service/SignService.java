package com.wosai.upay.core.service;

import java.util.Map;

import javax.validation.constraints.NotNull;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

@UpayCoreServiceAnnotation
@JsonRpcService("rpc/sign")
@CoreBusinessValidated
public interface SignService {
    
    /**
     * 对请求进行签名
     * @param channelId
     * @param params
     * @return
     */
    public String signWithData(
            @NotNull(message = "秘钥渠道id不能为空")
            String channelId, 
            @NotNull(message = "签名内容不能为空")
            String data);
    
    /**
     * 微信扫码点餐验签
     * @param channelId
     * @param params
     * @return
     */
    public Map<String,String> wechatFoodSign(
            @NotNull(message = "秘钥渠道id不能为空")
            String channelId, 
            @NotNull(message = "签名内容不能为空")
            String data);
    
    public Map<String,Object> createSignConfig(@NotNull(message = "秘钥渠道id不能为空")String channelId, 
            @NotNull(message = "加密类型不能为空")String signType, 
            @NotNull(message = "秘钥不能为空")String sign,
            Map signData);
}
