package com.wosai.upay.core.service;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

/**
 * Created by jianfree on 14/3/16.
 */
@JsonRpcService("rpc/sn")
@UpayCoreServiceAnnotation
public interface SnGenerator {
    /**
     * 获取服务商sn
     * @return
     */
    String nextVendorSn();

    /**
     * 获取推广者sn
     * @return
     */
    String nextSolicitorSn();

    /**
     * 获取集团sn
     * @return
     */
    String nextGroupSn();

    /**
     * 获取商户sn
     * @return
     */
    String nextMerchantSn();

    /**
     * 获取门店sn
     * @return
     */
    String nextStoreSn();

    /**
     * 获取终端sn
     * @return
     */
    String nextTerminalSn(String vendorSn);

    /**
     * 获取终端激活码code
     * @return
     */
    String nextActivationCode();

    /**
     * 获取服务商appid
     * @return
     */
    String nextVendorAppid();

    /**
     * 获取部门sn
     * @return
     */
    String nextDepartmentSn();

    /**
     * 获取收单机构终端ID
     * @return
     */
    String nextProviderTerminalId();

    /**
     * 获取收银台编号
     * @return 收银台编号
     */
    String nextCashDeskSn();

    /**
     * 获取品牌编号
     * @return
     */
    String nextBrandSn();
}
