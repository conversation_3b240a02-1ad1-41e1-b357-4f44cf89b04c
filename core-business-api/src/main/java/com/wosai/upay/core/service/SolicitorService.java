package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Solicitor;
import com.wosai.upay.core.model.SolicitorConfig;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import com.wosai.upay.common.validation.PropIsMap;
import com.wosai.upay.common.validation.PropSize;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/solicitor")
public interface SolicitorService {
    /**
     * 创建推广渠道.
     *
     * 默认创建config，并且可传账号id进行关联.
     *
     * @return
     */
    Map createSolicitorComplete(Map request);

    /**
     * 创建Solicitor.
     *
     * @param solicitor
     */
    Map createSolicitor(
            @PropSize.List({
                    @PropSize(value = Solicitor.SN, max = 32, message = "{value}推广者可见的编号不可超过{max}字符"),
                    @PropSize(value = Solicitor.NAME, max = 128, message = "{value}推广者名称不可超过{max}字符"),
                    @PropSize(value = Solicitor.CELLPHONE, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_ADDRESS, max = 255, message = "{value}联系地址不可超过{max}字符"),
                    @PropSize(value = Solicitor.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Solicitor.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Solicitor.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Solicitor.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
            })
            @PropIsMap(value = Solicitor.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段 address email remark 参数必须为Map格式")
            Map solicitor);

    /**
     * 根据solicitorId启用推广渠道.
     *
     * @param solicitorId
     */
    void enableSolicitor(
            @NotNull(message = "推广渠道ID不可为空")
            String solicitorId);

    /**
     * 根据solicitorId禁用推广渠道.
     *
     * @param solicitorId
     */
    void disableSolicitor(
            @NotNull(message = "推广渠道ID不可为空")
            String solicitorId);

    /**
     * 根据solicitorId关闭推广渠道.
     *
     * @param solicitorId
     */
    void closeSolicitor(
            @NotNull(message = "推广渠道ID不可为空")
            String solicitorId);

    /**
     * 根据solicitorId删除Solicitor.
     *
     * @param solicitorId
     */
    void deleteSolicitor(String solicitorId);

    /**
     * 根据solicitorSn删除Solicitor.
     *
     * @param solicitorSn
     */
    void deleteSolicitorBySn(String solicitorSn);

    /**
     * 修改Solicitor.
     *
     * @param solicitor
     */
    Map updateSolicitor(
            @PropSize.List({
                    @PropSize(value = Solicitor.SN, max = 32, message = "{value}推广者可见的编号不可超过{max}字符"),
                    @PropSize(value = Solicitor.NAME, max = 128, message = "{value}推广者名称不可超过{max}字符"),
                    @PropSize(value = Solicitor.CELLPHONE, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Solicitor.CONTACT_ADDRESS, max = 255, message = "{value}联系地址不可超过{max}字符"),
                    @PropSize(value = Solicitor.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Solicitor.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Solicitor.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Solicitor.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
            })
            @PropIsMap(value = Solicitor.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段 address email remark 参数必须为Map格式")
            Map solicitor);

    /**
     * 根据solicitorId获取Solicitor.
     *
     * @param solicitorId
     * @return
     */
    Map getSolicitor(String solicitorId);

    /**
     * 根据solicitorSn获取Solicitor.
     *
     * @param solicitorSn
     * @return
     */
    Map getSolicitorBySn(String solicitorSn);

    /**
     * 分页查询Solicitor.
     *
     * @param pageInfo
     * @param queryFilter
     *      sn                  推广者可见的编号
     *      name                推广者名称
     *      category
     *      status
     *      cellphone
     *      contact_name        联系人姓名
     *      contact_phone       联系固定电话号码
     *      contact_cellphone   联系移动电话号码
     *      deleted
     * @return
     */
    ListResult findSolicitors(PageInfo pageInfo, Map queryFilter);

    /**
     * 创建SolicitorConfig.
     *
     * @param solicitorConfig
     */
    Map createSolicitorConfig(
            @PropSize.List({
                    @PropSize(value = SolicitorConfig.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.MINI_FEE_RATE, max = 45, message = "{value}交易模式保留字段   费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = SolicitorConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map solicitorConfig);

    /**
     * 根据solicitorConfigId删除SolicitorConfig.
     *
     * @param solicitorConfigId
     */
    void deleteSolicitorConfig(String solicitorConfigId);

    /**
     * 修改SolicitorConfig.
     *
     * @param solicitorConfig
     */
    Map updateSolicitorConfig(
            @PropSize.List({
                    @PropSize(value = SolicitorConfig.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.MINI_FEE_RATE, max = 45, message = "{value}交易模式保留字段   费率不可超过{max}字符"),
                    @PropSize(value = SolicitorConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = SolicitorConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map solicitorConfig);

    /**
     * 根据solicitorId获取SolicitorConfig.
     *
     * @param solicitorId
     * @return
     */
    Map getSolicitorConfigBySolicitorId(String solicitorId);

    /**
     * 分页查询SolicitorConfig.
     *
     * @param pageInfo
     * @param queryFilter
     *      solicitor_id
     *      payway              支付方式，1：支付宝1.0；2：支付宝2.0；3：微信；4：百付宝；5：京东钱包；6：QQ钱包；7：ApplePay；8：三星支付；9：小米支付；10：华为支付；11：翼支付；12：苏宁易钱包；13：银联云闪付（AndroidPay）；14银联钱包
     *      b2c_formal          b扫c是否正式商户 0:否  1:是
     *      b2c_satus           b扫c 是否开通关闭 0:关闭 1：开通
     *      c2b_formal          c扫b是否正式商户 0:否  1:是
     *      c2b_status          c扫b是否开通关闭 0:关闭 1：开通
     *      wap_formal          wap是否正式商户 0:否  1:是
     *      wap_status          wap 是否开通关闭 0:关闭 1：开通
     *      extend1_formal      交易模式保留字段  是否正式商户 0:否  1:是
     *      extend1_status      交易模式保留字段   是否开通关闭 0:关闭 1：开通
     *      extend2_formal      交易模式保留字段   是否正式商户 0:否  1:是
     *      extend2_status      交易模式保留字段   是否开通关闭 0:关闭 1：开通
     *      deleted
     * @return
     */
    ListResult findSolicitorConfigs(PageInfo pageInfo, Map queryFilter);

    /**
     * 渠道的配置SolicitorConfig.
     *
     * @param solicitorId
     * @return
     */
    List getSolicitorConfigs(
            @NotNull(message = "推广渠道ID不可为空")
            String solicitorId);

    /**
     * 获取翻译信息
     * @param solicitorId
     * @return
     */
    Map<String,Object> getSolicitorTranslateInfoById(String solicitorId);

    /**
     * 获取翻译信息
     * @param solicitorSn
     * @return
     */
    Map<String,Object> getSolicitorTranslateInfoBySn(String solicitorSn);

}
