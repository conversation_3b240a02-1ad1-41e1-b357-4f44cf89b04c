package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.validation.PropNotBlank;
import com.wosai.common.validation.PropNotBothNull;
import com.wosai.upay.core.common.CoreBusinessIgnoreTranslate;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import java.util.Map;

@CoreBusinessValidated
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/store_business_license")
public interface StoreBusinessLicenseService {
    /**
     * 保存营业执照信息,支持包含许可证  门店营业执照相关信息 见: com.wosai.upay.core.model.MerchantBusinessLicence
     * 当存在许可证时,Map中 key:value  -->  trade_license_list:[许可证信息1,许可证信息2 ...]
     * 一个许可证信息: {"license_name":"xxx" , "license_number":"xxx"}
     */
    int saveStoreBusinessLicense(@PropNotBlank(value = "store_id",message = "门店营业执照必须关联一个门店id") Map storeBusinessLicense);

    /**
     * 根据门店id查询营业执照(包括了许可证)
     */
    @CoreBusinessIgnoreTranslate
    Map<String, Object> getStoreBusinessLicenseByStoreId(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     * 根据营业执照ID查询
     * @param id
     * @return
     */
    Map<String, Object> getStoreBusinessLicenseById(@NotBlank(message = "id不能为空") String id);

    /**
     * 更新营业执照,不包含许可证
     */
    int updateStoreBusinessLicense(@PropNotBothNull.List({
            @PropNotBothNull(value = "id", value2 = "store_id", message = "主键和门店id至少有一个")
    }) Map storeBusinessLicense);

    /**
     * 根据id删除营业执照(包括了许可证)
     */
    int deleteStoreBusinessLicenseById(@NotBlank(message = "id不能为空") String id);

    /**
     * 根据门店id删除营业执照(包括了许可证)
     */
    int deleteStoreBusinessLicenseByStoreId(@NotBlank(message = "storeId不能为空") String storeId);



}
