package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-24
 * @Description:
 */

@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/storeExt")
public interface StoreExtService {

    /**
     * 创建门店扩展信息
     *
     * @param storeExt
     * @return
     */
    int createStoreExt(Map storeExt);

    /**
     * 更新门店扩展信息
     *
     * @param storeExt
     * @return
     */
    int updateStoreExt(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}不可为空")
            })
                    Map storeExt
    );

    /**
     * 通过id查找扩展信息
     *
     * @param storeExtId
     * @return
     */
    Map findStoreExt(@NotBlank(message = "storeExtId不能为空") String storeExtId);

    /**
     * 通过storeId查找扩展信息
     *
     * @param storeId
     * @return
     */
    Map findStoreExtByStoreId(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     * 通过storeId物理删除
     * 删除store_ext,photo_info三张表相关数据
     *
     * @param storeId
     */
    void deleteStoreExtByStoreId(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     *批量查询storeExt
     */
    List<Map> findStoreExtBatchByStoreIds(@NotEmpty(message = "门店id不能为空") List<String> storeIds);


    /**
     * @return 批量返回storeId 对应的 门头照url
     */
    Map<String, String> getBrandOnlyScenePhotoBatchByStoreIds(@NotEmpty(message = "门店id不能为空")List<String> storeIds);

}
