package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.*;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.bean.response.MerchantStoreResponse;
import com.wosai.upay.core.common.CoreBusinessIgnoreTranslate;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by jianfree on 21/1/16.
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/store")
public interface StoreService {

    /**
     * 创建Store.
     *
     * @param store
     */
    Map createStore(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = Store.NAME, message = "{value}门店名称不可为空"),
                    @PropNotEmpty(value = Store.MERCHANT_ID, message = "{value}商户ID不可为空"),
                    //@PropNotEmpty(value = Store.CLIENT_SN, message = "{value}外部门店号不可为空"),
            })
            @PropPattern.List({
                    @PropPattern(value = Store.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Store.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Store.LONGITUDE, value2 = Store.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Store.LATITUDE, value2 = Store.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Store.SN, max = 32, message = "{value}对应1.0中的wosai_store_id不可超过{max}字符"),
                    @PropSize(value = Store.NAME, max = 128, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.PROVINCE, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CITY, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.DISTRICT, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS_DESC, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Store.CLIENT_SN, max = 50, message = "{value}商户外部门店号 不可超过{max}字符"),
                    @PropSize(value = Store.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = Store.EXTRA, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
            Map store);

    /**
     * 创建Store.
     *
     * @param store
     */
    Map createStoreForMerchantCenter(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = Store.NAME, message = "{value}门店名称不可为空"),
                    @PropNotEmpty(value = Store.MERCHANT_ID, message = "{value}商户ID不可为空"),
                    //@PropNotEmpty(value = Store.CLIENT_SN, message = "{value}外部门店号不可为空"),
            })
            @PropPattern.List({
                    @PropPattern(value = Store.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Store.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Store.LONGITUDE, value2 = Store.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Store.LATITUDE, value2 = Store.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Store.SN, max = 32, message = "{value}对应1.0中的wosai_store_id不可超过{max}字符"),
                    @PropSize(value = Store.NAME, max = 128, message = "{value}不可超过{max}字符"),
                    //@PropSize(value = Store.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Store.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Store.PROVINCE, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CITY, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.DISTRICT, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS_DESC, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Store.CLIENT_SN, max = 50, message = "{value}商户外部门店号 不可超过{max}字符"),
                    @PropSize(value = Store.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = Store.EXTRA, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
            Map store);


    /**
     * 修改Store.
     *
     * @param store
     */
    Map updateStore(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}门店ID不可为空")
            })
            @PropPattern.List({
                    @PropPattern(value = Store.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Store.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Store.LONGITUDE, value2 = Store.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Store.LATITUDE, value2 = Store.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Store.SN, max = 32, message = "{value}对应1.0中的wosai_store_id不可超过{max}字符"),
                    @PropSize(value = Store.NAME, max = 128, message = "{value}不可超过{max}字符"),
                    //@PropSize(value = Store.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Store.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Store.PROVINCE, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CITY, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.DISTRICT, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS_DESC, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Store.CLIENT_SN, max = 50, message = "{value}商户外部门店号 不可超过{max}字符"),
                    @PropSize(value = Store.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = Store.EXTRA, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
            Map store);


    /**
     * 修改Store，同时记录日志。
     *
     * @param store
     * @param opLogCreateRequest
     */
    Map updateStoreAndLog(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}门店ID不可为空")
            })
            @PropPattern.List({
                    @PropPattern(value = Store.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Store.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Store.LONGITUDE, value2 = Store.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Store.LATITUDE, value2 = Store.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Store.SN, max = 32, message = "{value}对应1.0中的wosai_store_id不可超过{max}字符"),
                    @PropSize(value = Store.NAME, max = 128, message = "{value}不可超过{max}字符"),
                    //@PropSize(value = Store.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Store.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Store.PROVINCE, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CITY, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.DISTRICT, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.STREET_ADDRESS_DESC, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Store.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Store.CLIENT_SN, max = 50, message = "{value}商户外部门店号 不可超过{max}字符"),
                    @PropSize(value = Store.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Store.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = Store.EXTRA, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
            Map store, OpLogCreateRequest opLogCreateRequest);


    /**
     * 根据storeId禁用Store.
     *
     * @param storeId
     * @return
     */
    void disableStore(
            @NotNull(message = "门店ID不可为空")
            String storeId);

    /**
     * 根据storeId启用Store.
     *
     * @param storeId
     * @return
     */
    void enableStore(
            @NotNull(message = "门店ID不可为空")
            String storeId);

    /**
     * 根据storeId关闭Store.
     *
     * @param storeId
     * @return
     */
    void closeStore(
            @NotNull(message = "门店ID不可为空")
            String storeId);

    /**
     * 根据storeId获取Store.
     *
     * @param storeId
     * @return
     */
    Map getStore(String storeId);

    /**
     * 根据storeId获取Store.
     *
     * @param storeId
     * @return
     */
    Map getStoreByStoreId(String storeId);

    /**
     * 根据storeSn获取Store.
     *
     * @param storeSn
     * @return
     */
    Map getStoreByStoreSn(String storeSn);

    /**
     * 根据clientSn获取Store.
     *
     * @param merchantId
     * @param clientSn
     * @return
     */
    Map getStoreByClientSn(
            @NotNull(message = "商户ID不可为空")
            String merchantId,
            @NotNull(message = "门店外部编号不可为空")
            String clientSn);

    /**
     * 分页查询商户下面所有的门店.
     *
     * @param merchantId
     * @param pageInfo
     * @param queryFilter store_name
     *                    store_sn
     * @return
     */
    ListResult getStoreListByMerchantId(
            @NotNull(message = "商户ID不可为空")
            String merchantId, PageInfo pageInfo, Map queryFilter);


    /**
     * 分页查询商户下面所有的门店. 返回门店的基础信息
     * 只返回id,sn,name,status四个字段
     *
     * @param merchantId
     * @param pageInfo
     * @return id
     * sn
     * name
     */
    @CoreBusinessIgnoreTranslate
    ListResult getSimpleStoreListByMerchantId(
            @NotNull(message = "商户ID不可为空")
            String merchantId, PageInfo pageInfo);

    /**
     * 分页查询商户下面所有的门店. 返回门店的基础信息
     * 只返回id,sn,name,status四个字段
     *
     * @param merchantId
     * @param pageInfo
     * @return id
     * sn
     * name
     */
    @CoreBusinessIgnoreTranslate
    ListResult getSimpleStoreListByMerchantIdFromSlaveDb(
            @NotNull(message = "商户ID不可为空")
            String merchantId, PageInfo pageInfo);

    /**
     * 根据商户id查询门店id列表
     */
    @CoreBusinessIgnoreTranslate
    List<String> getStoreIdListByMerchantId(
            @NotNull(message = "商户ID不可为空")
            String merchantId, PageInfo pageInfo);

    /**
     * 分页查询商户下面所有的门店. 返回门店的基础信息
     * 只返回id,sn,name,三个字段
     *
     * @param queryFilter merchant_id, store_ids, store_name, status
     * @param pageInfo    分页信息
     * @return 门店信息
     */
    @CoreBusinessIgnoreTranslate
    ListResult getSimpleStoreListByMerchantIdOrStoreIdsFromSlaveDb(
            Map queryFilter, PageInfo pageInfo);

    /**
     * 分页查询商户下面所有的门店.
     *
     * @param merchantId
     * @param pageInfo
     * @param queryFilter store_name
     *                    store_sn
     * @return
     */
    ListResult getStoreListByMerchantIdFromSlaveDb(
            @NotNull(message = "商户ID不可为空")
            String merchantId, PageInfo pageInfo, Map queryFilter);

    /**
     * 分页查询Store.
     *
     * @param pageInfo
     * @param queryFilter store_ids
     *                    store_sn
     *                    store_name
     *                    sn                  编号
     *                    name                门店名称
     *                    status              门店状态
     *                    rank                信用等级
     *                    client_sn           商户外部门店号
     *                    contact_phone       联系固定电话号码
     *                    contact_cellphone   联系移动电话号码
     *                    merchant_id         商户ID
     *                    solicitor_id        推广渠道ID
     *                    vendor_id           服务商ID
     *                    deleted
     * @return
     */
    ListResult findStores(PageInfo pageInfo, Map queryFilter);


    /**
     * 获取变化的商戶
     * 左右区间闭合
     */
    List<Map<String, Object>> getChangeStore(long beginMtime, long endMtime);

    /**
     * 根据门店id物理删除门店信息
     * 删除store,store_ext,photo_info三张表相关数据
     *
     * @param storeId
     * @return
     */
    int deleteStoreById(@NotBlank(message = "storeId不能为空") String storeId);

    /**
     * 通过终端号查询对应门店信息
     *
     * @param terminalSn
     * @return
     */
    Map<String, Object> getStoreByTerminalSn(String terminalSn);

    /**
     * 通过门店id集，查询门店经营内容集
     *
     * @param ids
     * @return
     */
    Set<String> getStoreContentsByIds(List<String> ids);

    /**
     * 获取门店所在省份
     *
     * @param storeSn
     * @return
     */
    String getStoreProvinceBySn(@NotBlank(message = "storeSn不能为空") String storeSn);

    /**
     * 根据商户id查询每个商户对应的门店id集合（循环查询，慎用）
     * @param merchantIds 商户id集合
     * @param maxStoreSize 每个商户查询门店数量的最大值
     * @return 查询结果
     */
    List<MerchantStoreResponse> getMerchantStoreIdListByMerchantIds(List<String> merchantIds, int maxStoreSize);
}
