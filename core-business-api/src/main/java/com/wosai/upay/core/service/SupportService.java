package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.core.bean.request.MerchantAvailablePaywaysQueryRequest;
import com.wosai.upay.core.bean.response.GetTradeConfigurationResponse;
import com.wosai.upay.core.bean.response.MerchantAvailablePaywaysQueryResult;
import com.wosai.upay.core.common.CoreBusinessIgnoreTranslate;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Created by jianfree on 15/12/29.
 * 支付网关服务接口
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/support")
public interface SupportService {
    public static final int PAYWAY_ALIPAY = 1;
    public static final int PAYWAY_ALIPAY2 = 2;
    public static final int PAYWAY_WEIXIN = 3;
    public static final int PAYWAY_BAIFUBAO = 4;
    public static final int PAYWAY_JDWALLET = 5;

    /**
     * 获取支付业务方基本交易参数: terminal, store, merchant, vendor, and more.
     * @param wosaiStoreId
     * @param terminalSn
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map getBasicParams(String wosaiStoreId, String terminalSn);

    /**
     * 获取支付业务方完整交易参数表: 基本 + 支付通道参数
     * @param wosaiStoreId
     * @param terminalSn
     * @param payway
     * @param subPayway
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map getAllParams(String wosaiStoreId, String terminalSn,
                     @NotNull(message = "收款通道不能为空")Integer payway, @NotNull(message = "交易模式不能为空")Integer subPayway);

    /**
     * 
     * 获取业务方完整交易参数
     * @param wosaiStoreId
     * @param terminalSn
     * @param payway
     * @param subPayway
     * @param tradeAppId
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map getAllParamsWithTradeApp(String wosaiStoreId, String terminalSn,
                     @NotNull(message = "收款通道不能为空")Integer payway, @NotNull(message = "交易模式不能为空")Integer subPayway, String tradeAppId);

    String getRsaKeyDataById(String rsaKeyId);

    /**
     * 获取支付宝2.0授权token
     * @param authAppId
     * @return
     */
    @Deprecated
    String getAlipayV2AppAuthToken(String authAppId);

    /**
     * 获取获取支付宝2.0授权token 与 支付宝系统的shopId
     * @param authAppId
     * @param storeId
     * @return
     */
    Map getAlipayV2AppAuthInfo(String authAppId, String storeId);


    /**
     * 根据商户号删除交易参数缓存
     * @param merchantSn
     */
    void removeCachedParams(String merchantSn);

    /**
     * 根据商户号删除花呗分期参数
     * @param merchantSn
     */
    void removeCachedHuaBeiParams(String merchantSn);

    /**
     * 设置是否关闭打开交易参数缓存
     * @param flag
     */
    void setCachedParamsFlag(boolean flag);

    /**
     * 获取翻译信息 详情见cacheService
     * @param map 需要翻译的信息 会由过滤器调用cacheService进行信息翻译
     */
    Map getTranslateInfo(Map map);

    List getTranslateInfoList(List map);

    int getClearanceProvider(String merchantId);

    @CoreBusinessIgnoreTranslate
    Map<String,Object> getHuBeiTradeParams(String wosaiStoreId,String terminalSn);


    /**
     * 根据merchantId和switchKey查询商户对应的功能状态.
     *
     * @return 权限状态: 0表示开启状态，1表示关闭状态
     * @param merchantId
     */
    Integer queryStatus(
            @NotNull(message = "商户ID不可为空") String merchantId,
            @NotNull(message = "switchKey功能不可为空") String switchKey);

    /**
     * 
     * 判断商户交易配置是否是直连交易
     * 
     * 注意：
     *   1、未上送payway时，检查商户的支付宝和微信是否直连
     *   2、该接口只校验商户的支付业务是否直连
     * 
     * @param wosaiStoreId
     * @param terminalSn
     * @param payway
     * @param subPayway
     * @return boolean true:直连 false：间连
     */
    boolean tradeIsFormal(String wosaiStoreId,String terminalSn, Integer payway, Integer subPayway);

    /**
     *
     * @param wosaiStoreId
     * @param terminalSn
     * @param payway
     * @param subPayway
     * @param tradeAppId
     * @return
     */
    @CoreBusinessIgnoreTranslate
    GetTradeConfigurationResponse getTradeConfiguration(String wosaiStoreId, String terminalSn, @NotNull(message = "收款通道不能为空")Integer payway, @NotNull(message = "交易模式不能为空")Integer subPayway, String tradeAppId);

    /**
     * 判断当前商户号是否开通指定支付通道
     *
     * @param merchantSn 商户SN
     * @param payway     支付源
     * @param subPayway  支付方式
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map<String, Object> isOpenPaywayWithMerchant(
            @NotEmpty(message = "商户SN为空") String merchantSn,
            @NotEmpty(message = "payway收款通道为空") Integer payway,
            @NotEmpty(message = "sub_payway交易模式为空") Integer subPayway);

    /**
     * 根据商户号获取交易参数，这里只是简单获取一下merchant_config交易参数
     *
     * @param merchantSn 商户SN
     *                   * @param payway     支付源
     *                   * @param subPayway  支付方式
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map<String, Object> getTradeParamsWithMerchant(
            @NotEmpty(message = "商户SN为空") String merchantSn,
            @NotEmpty(message = "payway收款通道为空") Integer payway,
            @NotEmpty(message = "sub_payway交易模式为空") Integer subPayway);

    /**
     * 获取商户小微升级完成的时间 带缓存 不带缓存的查询tradeConfigService接口
     * @param merchantId
     * @return 不会为null
     */
    Map getMerchantSwitchMchTime(String merchantId);

    /**
     * 查询商户可用支付方式列表
     *
     * @param request
     * @return
     */
    @CoreBusinessIgnoreTranslate
    List<MerchantAvailablePaywaysQueryResult> queryMerchantAvailablePayways(MerchantAvailablePaywaysQueryRequest request);

    /**
     * 自动核销开关是否已开启
     * 开启后，用户在使用微信或支付宝支付时，如果用户开通了储值会员且存在余额，则使用用户的储值账户进行核销；否则走普通的微信或支付宝进行支付
     *
     * @param storeSn 门店sn
     * @param terminalSn   终端sn
     * @return 开关状态：true开启, false失败
     */
    boolean isEnabledAutoVerification(String storeSn, String terminalSn);

    /**
     * 获取24期分期配置
     * 目前不做任何业务逻辑，返回花呗和信用卡24期分期的属性
     * @param params
     * @return
     */
    Map get24InstallmentRateConfig(Map params);

    /**
     * 获取收单机构商户交易参数配置
     * @param provider 交易通道
     * @param providerMchId 交易通道商户
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map getProviderBizStatus(Integer provider, String providerMchId);

    /**
     * 移除收单机构商户缓存
     * @param provider
     * @param providerMchId
     */
   void removeCacheProviderBizStatus(Integer provider, String providerMchId);
}
