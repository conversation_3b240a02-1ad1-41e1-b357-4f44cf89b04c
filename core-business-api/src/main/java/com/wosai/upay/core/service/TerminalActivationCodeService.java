package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import java.util.Map;

/**
 * @Auther: hrx
 * @Date: 2019-11-04
 * @Description: com.wosai.upay.core.service
 * @version: 1.0
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/activationCode")
public interface TerminalActivationCodeService {

    /**
     * 根据终端激活码查询商户信息
     *
     * @param activationCode
     * @return
     */
    Map getActivationInfoByCode(String activationCode);

}
