package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.validation.*;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.bean.request.OpLogCreateRequestV2;
import com.wosai.upay.core.bean.response.SqbLklTerminalActivateVerifyResponse;
import com.wosai.upay.core.common.CoreBusinessIgnoreTranslate;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;


/**
 * 收钱吧业务系统2.0 终端相关接口定义
 */
@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/terminal")
public interface TerminalService {

    /**
     * 服务商调用这个接口为门店创建一个激活码，可以多次使用。
     *
     * @param vendorSn
     * @param storeId
     * @param limits
     * @return
     * @deprecated 被createActivationCodeV2替代
     */
    @Deprecated
    Map<String, Object> createActivationCode(String vendorSn,
                                             String storeId,
                                             long limits);


    /**
     * 绑定门店码终端.
     *
     * @param info
     * @return
     */
    Map<String, Object> bindQrcodeTerminal(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_STORE_ID, message = "门店ID不可为空"),
                    @PropNotEmpty(value = Terminal.DEVICE_FINGERPRINT, message = "qrcode不可为空"),
                    @PropNotEmpty(value = Terminal.CLIENT_SN, message = "printCode不可为空"),
            })
                    Map info);

    /**
     * 解绑门店码终端.
     *
     * @param terminalId
     */
    void unbindQrcodeTerminal(
            @NotNull(message = "终端id不能为空")
                    String terminalId);

    /**
     * 创建一个激活好的终端.
     *
     * @param vendorAppAppid
     * @param storeSn
     * @param clientSn
     * @param name
     * @param longitude
     * @param latitude
     * @param extra
     * @return
     */
    Map<String, Object> createActivated(@NotNull(message = "vendorAppAppid服务商应用appid不可为空")
                                                String vendorAppAppid,
                                        @NotNull(message = "storeSn门店SN不可为空")
                                                String storeSn,
                                        String clientSn,
                                        String deviceFp,
                                        String name,
                                        String osVer,
                                        String sdkVer,
                                        String longitude,
                                        String latitude,
                                        Object extra);

    /**
     * 终端（SDK）调用这个接口激活终端。
     *
     * @param vendorSn
     * @param code
     * @param deviceId
     * @param osInfo
     * @param sdkVersion
     * @param terminalType
     * @return
     * @deprecated 被activateV2替代
     */
    @Deprecated
    Map<String, Object> activate(String vendorSn,
                                 String code,
                                 String deviceId,
                                 String osInfo,
                                 String sdkVersion,
                                 String terminalType);

    /**
     * 根据terminalId解绑Terminal.
     *
     * @param terminalId
     */
    void unbindTerminal(String terminalId);

    /**
     * 根据terminalSn 解绑 Terminal.
     *
     * @param terminalSn
     */
    void unbindTerminalBySn(String terminalSn);


    /**
     * 根据terminalId禁用Terminal.
     *
     * @param terminalId
     */
    void disableTerminal(String terminalId);

    /**
     * 根据terminalId禁用Terminal,并记录操作日志
     *
     * @param terminalId
     * @param opLogCreateRequest
     */
    void disableTerminalAndLog(String terminalId, OpLogCreateRequest opLogCreateRequest);

    /**
     * 根据terminalId启用Terminal.
     *
     * @param terminalId
     */
    void enableTerminal(String terminalId);


    /**
     * 根据terminalId启用Terminal,并记录操作日志
     *
     * @param terminalId
     * @param opLogCreateRequest
     */
    void enableTerminalAndLog(String terminalId, OpLogCreateRequest opLogCreateRequest);


    /**
     * 修改Terminal.
     *
     * @param terminal
     */
    Map updateTerminal(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}终端ID不可为空")
            })
            @PropPattern.List({
                    @PropPattern(value = Terminal.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}经度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Terminal.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Terminal.LONGITUDE, value2 = Terminal.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Terminal.LATITUDE, value2 = Terminal.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Terminal.SN, max = 32, message = "{value}用户可见终端编号不可超过{max}字符"),
                    @PropSize(value = Terminal.DEVICE_FINGERPRINT, max = 128, message = "{value}设备指纹不可超过{max}字符"),
                    @PropSize(value = Terminal.NAME, max = 128, message = "{value}终端名不可超过{max}字符"),
                    @PropSize(value = Terminal.SDK_VERSION, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.OS_VERSION, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.CURRENT_SECRET, max = 64, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.LAST_SECRET, max = 64, message = "{value}不可超过{max}字符"),
                    //@PropSize(value = Terminal.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Terminal.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Terminal.CLIENT_SN, max = 50, message = "{value}商户终端号 不可超过{max}字符"),
                    @PropSize(value = Terminal.TARGET, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.STORE_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.VENDOR_APP_ID, max = 36, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = Terminal.EXTRA, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
                    Map terminal);


    Map updateTerminalAndLog(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}终端ID不可为空")
            })
            @PropPattern.List({
                    @PropPattern(value = Terminal.LONGITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}经度为小数，小数点前后分别最多6位"),
                    @PropPattern(value = Terminal.LATITUDE, nullable = true, regex = "\\d{1,3}\\.\\d{1,6}", message = "{value}纬度为小数，小数点前后分别最多6位")
            })
            @PropNotNullOnProp.List({
                    @PropNotNullOnProp(value = Terminal.LONGITUDE, value2 = Terminal.LATITUDE, message = "{value}经度和纬度{value2}必须同时有值"),
                    @PropNotNullOnProp(value = Terminal.LATITUDE, value2 = Terminal.LONGITUDE, message = "{value2}经度和纬度{value}必须同时有值")

            })
            @PropSize.List({
                    @PropSize(value = Terminal.SN, max = 32, message = "{value}用户可见终端编号不可超过{max}字符"),
                    @PropSize(value = Terminal.DEVICE_FINGERPRINT, max = 128, message = "{value}设备指纹不可超过{max}字符"),
                    @PropSize(value = Terminal.NAME, max = 128, message = "{value}终端名不可超过{max}字符"),
                    @PropSize(value = Terminal.SDK_VERSION, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.OS_VERSION, max = 45, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.CURRENT_SECRET, max = 64, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.LAST_SECRET, max = 64, message = "{value}不可超过{max}字符"),
                    //@PropSize(value = Terminal.LONGITUDE, max = 16, message = "{value}经度不可超过{max}字符"),
                    //@PropSize(value = Terminal.LATITUDE, max = 16, message = "{value}纬度不可超过{max}字符"),
                    @PropSize(value = Terminal.CLIENT_SN, max = 50, message = "{value}商户终端号 不可超过{max}字符"),
                    @PropSize(value = Terminal.TARGET, max = 255, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.STORE_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.SOLICITOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = Terminal.VENDOR_APP_ID, max = 36, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = Terminal.EXTRA, nullable = true, emptyable = true, message = "{value}扩展参数必须为Map格式")
            Map terminal, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    Map updateTerminalName(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = ConstantUtil.KEY_TERMINAL_ID, message = "{value}终端ID不可为空"),
                    @PropNotEmpty(value = ConstantUtil.KEY_NAME, message = "{value}终端ID不可为空")

            }) Map terminal);



    Map<String, Object> getSecret(
            @NotNull(message = "终端编号不可为空")
                    String terminalSn);

    /**
     * 预备签到
     *
     * @param terminalSn
     * @return
     */
    Map<String, Object> prepareCheckin(@NotNull(message = "终端编号不可为空")
                                               String terminalSn);


    /**
     * 预备签到，升级版，附带终端收银软件环境信息
     *
     * @param terminalSn
     * @param extraEnvInfo
     * @return
     */
    Map<String, Object> prepareCheckinWithTermEnvInfo(@NotNull(message = "终端编号不可为空")
                                                              String terminalSn, Map<String, Object> extraEnvInfo);

    /**
     * 确认签到成功
     *
     * @param terminalSn
     * @return
     */
    Map<String, Object> commitCheckin(@NotNull(message = "终端编号不可为空")
                                              String terminalSn,
                                      String nextSecret);

    /**
     * @param terminalId
     * @return
     */
    Map getTerminalByTerminalId(String terminalId);

    Map getTerminalByTerminalSn(String terminalSn);

    Map getTerminalByDeviceFingerprint(
            @NotNull(message = "设备指纹不可为空")
                    String deviceFingerprint);

    /**
     * 获取终端激活码
     *
     * @param terminalId
     * @return
     */
    Map getTerminalActivationCodeByTerminalId(
            @NotNull(message = "终端ID不可为空")
                    String terminalId);

    /**
     * 获取终端
     * merchantId 与 storeId不能同时为空
     *
     * @param merchantId
     * @param storeId
     * @param pageInfo
     * @param queryFilter sn
     *                    device_fingerprint
     *                    client_sn
     *                    name
     *                    type
     *                    status
     *                    statuses  多个状态值传数组[]
     *                    store_id
     *                    merchant_id
     *                    vendor_app_id
     *                    vendor_app_appid
     * @return
     */
    ListResult getTerminals(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter);

    /**
     * 终端数.
     *
     * @param merchantId
     * @param storeId
     * @param queryFilter sn
     *                    device_fingerprint
     *                    client_sn
     *                    name
     *                    type
     *                    status
     *                    statuses  多个状态值传数组[]
     *                    store_id
     *                    store_ids           门店IDs
     *                    merchant_id
     *                    vendor_app_id
     *                    vendor_app_appid
     * @return
     */
    long countTerminals(String merchantId, String storeId, Map queryFilter);

    /**
     * 获取门店终端激活码
     * merchantId 与 storeId不能同时为空
     *
     * @param merchantId
     * @param storeId
     * @param pageInfo
     * @param queryFilter terminal_sn
     *                    status
     * @return
     */
    ListResult getActivationCodes(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter);

    /**
     * 根据terminalId获取Terminal.
     *
     * @param terminalId
     * @return
     */
    Map getTerminal(String terminalId);

    /**
     * 根据terminalSn获取Terminal.
     *
     * @param terminalSn
     * @return
     */
    Map getTerminalBySn(String terminalSn);

    /**
     * 分页查询Terminal.
     *
     * @param pageInfo
     * @param queryFilter sn                  用户可见终端编号
     *                    device_fingerprint  设备指纹
     *                    name                终端名
     *                    type                类型  1: SQB_APP  2: SQB_SDK   3: SQB_POS  10: SATURN
     *                    status              状态
     *                    statuses            多个状态值传数组[]
     *                    client_sn           商户外部终端号
     *                    target_type         回调通知方式
     *                    store_id            门店ID
     *                    store_ids           门店IDs
     *                    merchant_id         商户ID
     *                    solicitor_id        推广渠道ID
     *                    vendor_id           服务商ID
     *                    vendor_app_id       服务商应用ID
     *                    deleted             删除标志
     *                    vendor_app_appids   服务商应用APP ID列表
     *                    terminal_category   终端类别
     *                    isQrcode            查询vendor_app_appids为收款门店码的终端
     *                    isPOS               查询vendor_app_appids为POS的终端
     *                    isSMW               查询vendor_app_appids为扫码王的终端
     *                    isCashierPlugin     查询vendor_app_appids为收银插件的终端
     *                    isCafeteria         查询vendor_app_appids为自助点餐码的终端
     *                    isJJZ               查询vendor_app_appids为九九折的终端
     *                    isApp               查询vendor_app_appids为收钱吧APP的终端
     *                    isFace              查询vendor_app_appids为刷脸设备的终端
     *                    isCampusTerminal    查询vendor_app_appids为校园终端的终端
     * @return
     */
    ListResult findTerminals(PageInfo pageInfo, Map queryFilter);



    /**
     * 修改激活码信息，仅提供授权人员使用.
     * 参数：情况1：id，情况2：code（这种情况尝试查找有几个，只有1个的时候才更新，否则抛异常）
     * 只更新remaining、expire_time、status
     *
     * @param activationCode
     */
    Map updateActivationCode(Map activationCode);

    /**
     * 推广渠道创建激活码
     *
     * @param solicitorSn         如果为NULL，不校验门店/商户的solicitor_id
     * @param code                指定激活码。如果为NULL，系统自动分配。
     * @param storeSn             门店SN。不能为空。
     * @param defaultTerminalName 默认终端名，如果为NULL，并且调用激活接口的时候也不指定终端名，则系统会给新激活的终端自动分配名字。
     * @param limit               可以使用次数
     * @return
     */
    Map<String, Object> createActivationCodeV2(String solicitorSn, String code, String storeSn, String defaultTerminalName, @Min(value = 1, message = "limit必须大于0") long limit);


    /**
     * 推广渠道创建激活码，并记录操作日志
     *
     * @param solicitorSn         如果为NULL，不校验门店/商户的solicitor_id
     * @param code                指定激活码。如果为NULL，系统自动分配。
     * @param storeSn             门店SN。不能为空。
     * @param defaultTerminalName 默认终端名，如果为NULL，并且调用激活接口的时候也不指定终端名，则系统会给新激活的终端自动分配名字。
     * @param limit               可以使用次数
     * @return
     */
    Map<String, Object> createActivationCodeV2AndLog(String solicitorSn, String code, String storeSn, String defaultTerminalName, @Min(value = 1, message = "limit必须大于0") long limit, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);

    /**
     * 终端是否能激活判断 （收钱吧、拉卡拉终端只能激活在自己的商户下面）
     * merchantId 与 code 不能同时为空
     * @param merchantId 商户id
     * @param code 激活码
     * @param vendorAppAppid 服务商应用
     * @return
     */
    SqbLklTerminalActivateVerifyResponse sqbLklTerminalActivateVerify(String merchantId, String code, String vendorAppAppid);

    /**
     * 激活设备
     *
     * @param vendorAppAppid    必填参数
     * @param code              必填参数
     * @param deviceFingerprint 设备硬件ID可以为NULL
     * @param osVer             OS版本信息可以为NULL
     * @param sdkVer            SDK版本信息可以为NULL
     * @param extraInfo         应用相关的其它自定义字段
     * @return
     */
    Map<String, Object> activateV2(@NotNull(message = "vendorAppAppid服务商应用appid不可为空")
                                           String vendorAppAppid,
                                   @NotNull(message = "code激活码不可为空")
                                           String code,
                                   String clientSn,
                                   @Size(max = 128, message = "deviceFingerprint设备硬件ID最大只能128个字符") String deviceFingerprint,
                                   String name,
                                   String osVer,
                                   String sdkVer,
                                   String longitude,
                                   String latitude,
                                   Object extraInfo);

    /**
     * 根据设备硬件ID获取设备信息
     *
     * @param vendorAppAppid    如果为NULL，deviceFingerprint必须全局唯一
     * @param deviceFingerprint
     * @return
     */
    @CoreBusinessIgnoreTranslate
    Map<String, Object> getTerminalByDeviceFp(String vendorAppAppid, String deviceFingerprint);

    /**
     * 解绑设备(status=0)
     *
     * @param vendorAppAppid    如果为NULL，deviceFingerprint必须全局唯一
     * @param deviceFingerprint
     * @return
     */
    Map<String, Object> unbindTerminalByDeviceFp(String vendorAppAppid, String deviceFingerprint);

    /**
     * 通过旧sdk的参数获取在业务系统中的对应终端sn
     *
     * @param wosaiStoreId
     * @param customStoreId
     * @param terminalId
     * @return
     */
    String getTerminalSnByLegacySdkParam(String wosaiStoreId, String customStoreId, String terminalId);

    /**
     * 通过旧sdk的参数获取在业务系统中的对应门店sn
     *
     * @param wosaiStoreId
     * @param customStoreId
     * @param terminalId
     * @return
     */
    String getStoreSnByLegacySdkParam(String wosaiStoreId, String customStoreId, String terminalId);

    /**
     * 通过旧pos的pos编号获取在业务系统中的对应终端sn
     *
     * @param wosaiStoreId
     * @param posSn
     * @return
     */
    String getTerminalSnByLegacyPosSn(String wosaiStoreId, String posSn);

    /**
     * 通过旧业务系统的用户uuid获取对应的对应终端sn
     *
     * @param wosaiStoreId
     * @param legacyUserId
     * @return
     */
    String getTerminalSnByLegacyUserId(String wosaiStoreId, String legacyUserId);

    /**
     * 玛雅终端签到功能
     *
     * @param terminalSn 终端编号
     * @return
     * @see ChangeShiftsService.changeShiftsCheckIn()
     */
    @Deprecated
    Map<String, Object> changeShiftsCheckIn(@NotEmpty(message = "终端号不能为空") String terminalSn);

    /**
     * 玛雅终端签退功能
     *
     * @param terminalSn 终端编号
     * @param batchSn    批次号
     * @param cashierNo  收银员编号
     * @see ChangeShiftsService.changeShiftsCheckOut()
     */
    @Deprecated
    Map<String, Object> changeShiftsCheckOut(@NotEmpty(message = "终端号不能为空") String terminalSn,
                                             String batchSn,
                                             String cashierNo);


    /**
     * 查询终端签到信息
     *
     * @param terminalSn 终端sn
     * @param batchSn    批次号
     * @return id UUID
     * merchant_id 商户ID
     * store_id 门店ID
     * terminal_id 终端id
     * batch_sn 批次号
     * start_date 签到时间
     * end_date   签退时间
     * ctime 创建时间
     * mtime 修改时间
     * delete 是否删除
     * version 版本号
     * terminal_sn 终端号
     * terminal_name 终端名称
     * solicitor_id 推广者id
     * vendor_id 开发者id
     * store_sn 门店号
     * store_name 门店名称
     * merchant_sn 商户号
     * merchant_name 商户名称
     * 
     * @see ChangeShiftsService.getTerminalChangeShiftsInfo()
     */
    @Deprecated
    Map getTerminalChangeShiftsInfo(@NotEmpty(message = "终端号不能为空") String terminalSn, String batchSn);

    /**
     * 分页查询终端签到信息
     *
     * @param String   terminalSn 终端号
     * @param Long     startDate 签到区间开始时间（unix时间戳）
     * @param Long     endDate 签到区间结束时间（unix时间戳)
     * @param PageInfo pageInfo
     * @return total
     * records
     * id UUID
     * merchant_id 商户ID
     * store_id 门店ID
     * terminal_id 终端id
     * batch_sn 批次号
     * start_date 签到时间
     * end_date   签退时间
     * ctime 创建时间
     * mtime 修改时间
     * delete 是否删除
     * version 版本号
     * terminal_sn 终端号
     * terminal_name 终端名称
     * solicitor_id 推广者id
     * vendor_id 开发者id
     * store_sn 门店号
     * store_name 门店名称
     * merchant_sn 商户号
     * merchant_name 商户名称
     * 
     * @see ChangeShiftsService.getTerminalChangeShiftsList()
     */
    @Deprecated
    ListResult getTerminalChangeShiftsList(@NotEmpty(message = "终端号不能为空") String terminalSn, Long startDate, Long endDate, PageInfo pageInfo);

    /**
     * 获取终端相关连的终端，门店，商户信息，用于upay qrcode项目调用
     * @param terminalId
     * @return
     *  terminal_id
     *  terminal_sn
     *  terminal_name
     *  vendor_app_appid
     *  store_id
     *  store_name
     *  store_sn
     *  store_city
     *  store_logo
     *  merchant_id
     *  merchant_sn
     *  merchant_name
     *  merchant_alias
     *  merchant_business_name
     *  merchant_logo
     */
    @CoreBusinessIgnoreTranslate
    Map<String,Object> getTerminalExtendedInfoForUpayQrcode(String terminalId);

    /**
     * 获取终端相关连的终端，门店，商户信息，用于upay qrcode项目调用
     * @param terminalSn
     * @return
     *  terminal_id
     *  terminal_sn
     *  terminal_name
     *  vendor_app_appid
     *  store_id
     *  store_name
     *  store_sn
     *  store_city
     *  store_logo
     *  merchant_id
     *  merchant_sn
     *  merchant_name
     *  merchant_alias
     *  merchant_business_name
     *  merchant_logo
     */
    @CoreBusinessIgnoreTranslate
    Map<String,Object> getTerminalExtendedInfoByTerminalSn(String terminalSn);

    Map<String, Object> getTerminalVendorAppIdMapping();

    void transforTerminal(String terminalId,String merchantId,String storeId);

    /**
     *
     * 返回venderapp的相关信息
     *
     * @param category 为空查询所有
     * @param
     * @return
     *  name
     *  type
     *  category
     *  appid
     */
    List<Map> getVendorAppsByCategory(Integer category);

    /**
     * 判断该类型终端是否禁止解绑
     *
     * @param vendorAppAppid
     * @return
     */
    boolean isProhibitedUnbound(@NotNull(message = "vendorAppAppid服务商应用appid不可为空")
                                   String vendorAppAppid);
    /**
     * 查询terminalIds列表对应的终端信息
     * @param terminalIds 终端id列表
     * @return 终端信息 id\sn\name\device_fingerprint\vendor_app_appid\type\client_sn
     */
    @CoreBusinessIgnoreTranslate
    List<Map> getSimpleTerminalsByTerminalIds(@NotEmpty(message = "终端id集合不能为空") List<String> terminalIds);

    /**
     * 查询deviceFingerprint列表对应的终端信息
     * @param merchantId 商户id
     * @param storeId 门店id
     * @param queryFilter 过滤条件 sn\type\status\client_sn\target_type\solicitor_id\vendor_id\vendor_app_id\vendor_app_appid\deleted
     * @param deviceFingerprints 设备指纹列表
     * @return 终端信息 id\sn\name\device_fingerprint\vendor_app_appid\type\client_sn
     */
    @CoreBusinessIgnoreTranslate
    List<Map> getSimpleTerminalsByDeviceFingerprints(String merchantId, String storeId, @NotEmpty(message = "终端id集合不能为空") List<String> deviceFingerprints, Map queryFilter);


    /**
     * 根据merchant_sn, terminal_sn,dev 查询vendorAppName
     * @param key
     * @param type 0 1 2
     * @return
     */
    List<String> findAllVendorApp(String key , String type);
}
