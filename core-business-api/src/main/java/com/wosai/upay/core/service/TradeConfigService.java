package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.common.validation.PropIsMap;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.common.validation.PropSize;
import com.wosai.upay.core.bean.model.TradeAppConfig;
import com.wosai.upay.core.bean.request.*;
import com.wosai.upay.core.bean.response.AllMerchantConfigResponse;
import com.wosai.upay.core.bean.response.MerchantAvailablePaywaysQueryResult;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.request.HopeEduMerchantConfigRequest;
import com.wosai.upay.core.model.request.LakalaMerchantConfigRequest;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Created by jianfree on 30/12/15.
 */
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/tradeConfig")
@CoreBusinessValidated
public interface TradeConfigService {

    String AGENT_NAME = "agent_name"; //用于接口参数

    int PAYWAY_ALIPAY = 1;
    int PAYWAY_ALIPAY2 = 2;
    int PAYWAY_WEIXIN = 3;
    int PAYWAY_BAIFUBAO = 4;
    int PAYWAY_JD = 5;
    int PAYWAY_QQWALLET = 6;
    int PAYWAY_APPLEPAY = 7;
    int PAYWAY_LAKALAWALLET = 8;
    int PAYWAY_CMCC = 9; //和支付
    @Deprecated
    int PAYWAY_LKL_UNIONPAY = 17;// 拉卡拉银联二维码支付
    int PAYWAY_UNIONPAY = 17;// 银联云闪付
    int PAYWAY_BESTPAY = 18;// 翼支付
    int PAYWAY_WEIXIN_HK = 19;// 微信香港本地支付
    int PAYWAY_ALIPAY_INTL = 20;// 支付宝国际版
    int PAYWAY_BANKCARD = 21;// 银行卡
    int PAYWAY_SODEXO = 22;// 索迪斯
    int PAYWAY_DCEP = 23;// 中国数字货币
    int PAYWAY_FOXCONN = 25;// 富士康钱包
    int PAYWAY_GRABPAY = 26;// grabpay钱包
    int PAYWAY_BANKACCOUNT = 27;// 银行转账
    int PAYWAY_CAMPUS_CARD = 28;// 校园卡
    int PAYWAY_MACAU_PASS = 29;// 澳门通
    int PAYWAY_HOPE_EDU = 32;// 院校通-一码通

    int PAYWAY_GIFT_CARD = 101;// 礼品卡
    int PAYWAY_PREPAID_CARD = 105;// 储值卡
    int PAYWAY_WELFARE_CARD = 111; //福利卡
    int PAYWAY_DOUYINQUAN = 112; //抖音券
    int PAYWAY_MEITUANQUAN = 113; //美团券
    int PAYWAY_CCB_GIFT_CARD = 114; //建行福利卡


    int PROVIDER_CIBBANK = 1001;
    int PROVIDER_LAKALA = 1002;
    int PROVIDER_CITICBANK = 1003;
    int PROVIDER_CIBGZBANK = 1008;
    int PROVIDER_CMBCBANK = Provider.CMBC_BANK.getCode(); // 民生银行
    int PROVIDER_LKLWANMA = 1011;
    int PROVIDER_NUCC = 1013;
    int PROVIDER_UNIONPAY = 1014;
    int PROVIDER_CIBSHBANK = 1015;
    int PROVIDER_DIRECT_UNIONPAY = 1016;
    int PROVIDER_UNIONPAY_OPEN = 1017; //银联开放平台
    int PROVIDER_CHINAUMS = 1018; //银联商务
    int PROVIDER_UNIONPAY_ONLINE = 1019; //银联网银支付
    int PROVIDER_UNIONPAY_TL = 1020; //通联
    int PROVIDER_UEPAY = 1021; //澳门极易付
    int PROVIDER_CMB = 1022; //招商银行
    int PROVIDER_PSBCBANK = 1023; //邮储银行
    int PROVIDER_CGBBANK = 1024; //广发银行
    int PROVIDER_FOXCONN = 1025; //富士康-富圈圈
    int PROVIDER_CCB = 1026; //招商银行
    int PROVIDER_HXBANK = 1028; //华夏银行
    int PROVIDER_CIBHZBANK = 1029; //杭州兴业
    int PROVIDER_ICBCBANK = 1030; //工商银行
    int LAKALA_UNION_PAY_V3 = 1034; //拉卡拉
    int PROVIDER_SYB = 1035; //收银宝
    int PROVIDER_CCB_GIFT_CARD = 1036;//建行福利卡
    int PROVIDER_HAIKE_UNION_PAY = 1037; //海科银联
    int PROVIDER_FUYOU = 1038; //富友
    int PROVIDER_GUOTONG = 1048; //国通
    int PROVIDER_ZTKX = Provider.ZTKX.getCode();    //中投
    int PROVIDER_BOCOM = Provider.BOCOM.getCode(); //交通银行
    int PROVIDER_ENTPAY = Provider.ENTPAY.getCode(); //微企付

    int PROVIDER_ZJTLCB = Provider.ZJTLCB.getCode(); //浙江泰隆
    int PROVIDER_TL_S2P = Provider.TL_S2P.getCode();


    int PROVIDER_LAKALA_UNION_PAY = Provider.LAKALA_UNION_PAY.getCode(); //拉卡拉银联
    int PROVIDER_LAKALA_UNION_PAY_V3 = Provider.LAKALA_UNION_PAY_V3.getCode();


    int SUB_PAYWAY_BARCODE = 1; //b2c
    int SUB_PAYWAY_QRCODE = 2;//c2b
    int SUB_PAYWAY_WAP = 3; //wap
    int SUB_PAYWAY_MINI = 4; //mini
    int SUB_PAYWAY_APP = 5; //app支付
    int SUB_PAYWAY_H5 = 6; //h5支付

    int CLEARANCE_PROVIDER_SWITCH = -1;
    int CLEARANCE_PROVIDER_LKL = 2;
    int CLEARANCE_PROVIDER_TL = 3;
    int CLEARANCE_PROVIDER_YS = 4;
    int CLEARANCE_PROVIDER_HAIKE = 7; // 海科银联 间连提现模式

    /**
     * 创建VendorConfig
     *
     * @param vendorConfig
     */
    Map createVendorConfig(Map vendorConfig);

    /**
     * 删除VendorConfig
     *
     * @param vendorConfigId
     */
    void deleteVendorConfig(String vendorConfigId);

    /**
     * 修改VendorConfig
     *
     * @param vendorConfig
     */
    Map updateVendorConfig(Map vendorConfig);

    /**
     * 根据VendorConfigid获取VendorConfig
     *
     * @param vendorConfigId
     * @return
     */
    Map getVendorConfig(String vendorConfigId);


    /**
     * 获取服务商交易参数
     *
     * @param vendorId
     * @return
     */
    Map getVendorConfigByVendorId(String vendorId);

    /**
     * 创建受理商
     *
     * @return
     */
    Map createAgent(
            @PropNotEmpty(value = Agent.NAME, message = "{value}不能为空")
                    Map agent);

    /**
     * 删除受理商
     *
     * @param agentId
     * @return
     */
    void deleteAgent(String agentId);

    /**
     * 更新受理商
     *
     * @param agent
     * @return
     */
    Map updateAgent(Map agent);

    /**
     * 获取受理商
     *
     * @param agentId
     * @return
     */
    Map getAgent(String agentId);

    /**
     * 查询受理商
     *
     * @param pageInfo
     * @param queryFilter
     * @return
     */
    ListResult findAgents(PageInfo pageInfo, Map queryFilter);

    /**
     * 根据名字获取受理商
     *
     * @param name
     * @return
     */
    Map getAgentByName(String name);

    /**
     * 根据名字获取受理商信息
     *
     * @param name
     * @return
     */
    Map getChannelByAgentName(String name);

    /**
     * 根据商户信息获取试用商户的默认受理商名称
     *
     * @param provider
     * @param merchantInfo province
     *                     city
     * @return
     */
    String getMerchantDefaultTrailAgentName(
            @NotNull(message = "结算通道不能为空")
                    Integer provider,
            Map merchantInfo);


    /**
     * 获取交易参数key与活动号key的映射关系
     *
     * @return
     */
    Map getTradeParamsKeyActivityKeyMap();


    /**
     * 创建MerchantConfig
     *
     * @param merchantConfig
     */
    Map createMerchantConfig(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                    Map merchantConfig);


    /**
     * 删除MerchantConfig
     *
     * @param merchantConfigId
     * @return
     */
    void deleteMerchantConfig(String merchantConfigId);

    /**
     * 修改MerchantConfig, 不写消息
     *
     * @param merchantConfig
     */
    Map updateMerchantConfigWithoutMessage(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropNotEmpty(value = DaoConstants.VERSION, message = "版本号不能为空")
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map merchantConfig);

    /**
     * 修改MerchantConfig
     *
     * @param merchantConfig
     */
    Map updateMerchantConfig(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                    Map merchantConfig);


    /**
     * 修改MerchantConfig, 并记录日志
     * @param merchantConfig
     * @param opLogCreateRequest
     * @return
     */
    Map updateMerchantConfigAndLog(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map merchantConfig,OpLogCreateRequest opLogCreateRequest);



    /**
     * 修改MerchantConfig, 并记录日志 agent fromal 变化
     * @param merchantConfig
     * @param opLogCreateRequest
     * @return
     */
    Map updateMerchantConfigAndLogV2(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map merchantConfig,@NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 根据商户号、payway修改费率配置，payway可为null.
     * merchant_sn、payway必须传递，payway可为null
     *
     * @param merchantConfig
     */
    Map updateMerchantConfigByMerchantSnAndPayway(Map merchantConfig);



    /**
     * 根据商户号、payway修改费率配置，payway可为null.
     * merchant_sn、payway必须传递，payway可为null
     * 并记录日志
     *
     * @param merchantConfig
     */
    Map updateMerchantConfigByMerchantSnAndPaywayAndLog(Map merchantConfig,@NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 根据merchantConfigId获取MerchantConfig
     *
     * @param merchantConfigId
     * @return
     */
    Map getMerchantConfig(String merchantConfigId);


    /**
     * 获取指定时间内的merchantconfigs
     * 左右区间闭合
     */
    List<String> getAlipayV2ChangeMerchantIds(long beginMtime, long endMtime);


    /**
     * 获取商户交易参数
     *
     * @param merchantId
     * @return
     */
    List getMerchantConfigsByMerchantId(String merchantId);

    /**
     * 获取门店交易参数
     *
     * @param merchantId
     * @return
     */
    List getStoreConfigsByStoreId(String storeId);

    /**
     * 获取终端交易参数
     *
     * @return
     */
    List getTerminalConfigsByTerminalId(String terminalId);

    /**
     * 获取原始的商户交易参数
     *
     * @param merchantId
     * @param payway
     * @return
     */
    Map getMerchantConfigByMerchantIdAndPayway(String merchantId, Integer payway);

    /**
     * 获取原始的门店交易参数
     *
     * @param merchantId
     * @param payway
     * @return
     */
    Map getStoreConfigByStoreIdAndPayway(String storeId, Integer payway);

    /**
     * 获取终端交易参数
     *
     * @param terminalId
     * @param payway
     * @return
     */
    Map getTerminalConfigByTerminalIdAndPayway(String terminalId, Integer payway);

    /**
     * 获取解析后的商户交易参数
     *
     * @param merchantId
     * @param payway
     * @return
     */
    Map getAnalyzedMerchantConfigByMerchantIdAndPayway(String merchantId, Integer payway);


    /**
     * 获取解析后的门店交易参数
     *
     * @param storeId
     * @param payway
     * @return
     */
    Map getAnalyzedStoreConfigByStoreIdAndPayway(String storeId, Integer payway);

    /**
     * 获取商户某一收款通道某一交易模式的开通状态(是否被关闭)
     *
     * @param merchantId
     * @param payway
     * @param subPayway
     * @return 0 已关闭 1 开通
     */
    @Deprecated
    int getAnalyzedMerchantConfigStatus(String merchantId, Integer payway, Integer subPayway);


    /**
     * 创建MerchantConfigCustom.
     *
     * @param merchantConfigCustom
     */
    Map createMerchantConfigCustom(
            @PropSize.List({
                    @PropSize(value = MerchantConfigCustom.MERCHANT_ID, max = 37, message = "{value}商户id不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.STORE_ID, max = 37, message = "{value}门店id不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.B2C_VALUE, max = 64, message = "{value}b扫c交易的值不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.C2B_VALUE, max = 64, message = "{value}c扫b交易的值不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.WAP_VALUE, max = 64, message = "{value}wap支付的值不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.MINI_VALUE, max = 64, message = "{value}mini支付不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.MINI_AGENT_NAME, max = 64, message = "{value}mini支付不可超过{max}字符"),
            })
                    Map merchantConfigCustom);


    /**
     * 根据merchantConfigCustomId删除MerchantConfigCustom.
     *
     * @param merchantConfigCustomId
     */
    void deleteMerchantConfigCustom(String merchantConfigCustomId);

    /**
     * 修改MerchantConfigCustom.
     *
     * @param merchantConfigCustom
     */
    Map updateMerchantConfigCustom(
            @PropSize.List({
                    @PropSize(value = MerchantConfigCustom.MERCHANT_ID, max = 37, message = "{value}商户id不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.STORE_ID, max = 37, message = "{value}门店id不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.B2C_VALUE, max = 64, message = "{value}b扫c交易的值不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.C2B_VALUE, max = 64, message = "{value}c扫b交易的值不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.WAP_VALUE, max = 64, message = "{value}wap支付的值不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.MINI_VALUE, max = 64, message = "{value}mini支付不可超过{max}字符"),
                    @PropSize(value = MerchantConfigCustom.MINI_AGENT_NAME, max = 64, message = "{value}mini支付不可超过{max}字符"),
            })
                    Map merchantConfigCustom);

    /**
     * 根据merchantConfigCustomId获取MerchantConfigCustom.
     *
     * @param merchantConfigCustomId
     * @return
     */
    Map getMerchantConfigCustom(String merchantConfigCustomId);


    /**
     * 根据商户id, 门店id, 类型获取自定义配置
     * 注意： 如果storeId 传空， 那么只会查询storeId为空的记录出来，而不是普通意义上的，不根据storeId来进行过滤。
     *
     * @param merchantId
     * @param storeId
     * @param type
     * @return
     */
    Map getMerchantConfigCustomByMerchantIdAndStoreIdAndType(@NotNull(message = "商户id不能为空") String merchantId, String storeId, int type);

    /**
     * 分页查询MerchantConfigCustomComment.
     *
     * @param pageInfo
     * @param queryFilter type                类型 1: 微信子商户号 2:京东商户号
     *                    merchant_id         商户id
     *                    store_id            门店id
     *                    b2c_value           b扫c交易的值
     *                    c2b_value           c扫b交易的值
     *                    wap_value           wap支付的值
     *                    mini_value          mini支付的值
     *                    deleted
     * @return
     */
    ListResult findMerchantConfigCustoms(PageInfo pageInfo, Map queryFilter);


    /**
     * 创建StoreConfig
     *
     * @param storeConfig
     */
    Map createStoreConfig(Map storeConfig);

    /**
     * 删除StoreConfig
     *
     * @param storeConfigId
     */
    void deleteStoreConfig(String storeConfigId);

    /**
     * 修改StoreConfig
     *
     * @param storeConfig
     */
    Map updateStoreConfig(Map storeConfig);


    /**
     * 根据storeConfigId获取StoreConfig
     *
     * @param storeConfigId
     * @return
     */
    Map getStoreConfig(String storeConfigId);


    /**
     * 创建TerminalConfig
     *
     * @param terminalConfig
     */
    Map createTerminalConfig(Map terminalConfig);

    /**
     * 删除TerminalConfig
     *
     * @param terminalConfigId
     */
    void deleteTerminalConfig(String terminalConfigId);

    /**
     * 修改TerminalConfig
     *
     * @param terminalConfig
     */
    Map updateTerminalConfig(Map terminalConfig);

    /**
     * 根据terminalConfigId获取TerminalConfig
     *
     * @param terminalConfigId
     * @return
     */
    Map getTerminalConfig(String terminalConfigId);

    /**
     * 更新终端配置
     *
     * @param terminalConfig
     */
    void updateTerminalConfigStatus(Map terminalConfig);

    /**
     *  更新终端配置并记录日志
     * @param terminalConfig
     * @param opLogCreateRequest
     */

    void updateTerminalConfigStatusAndLog(Map terminalConfig, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);

    /**
     * 批量更新终端配置
     *
     * @param terminalConfigs
     */
    void batchUpdateTerminalConfigStatus(List<Map> terminalConfigs);


    /**
     * 创建SolicitorConfig
     *
     * @param solicitorConfig
     */
    Map createSolicitorConfig(Map solicitorConfig);

    /**
     * 修改SolicitorConfig
     *
     * @param solicitorConfig
     */
    Map updateSolicitorConfig(Map solicitorConfig);

    /**
     * @param solicitorConfigId
     */
    void deleteSolicitorConfig(String solicitorConfigId);

    /**
     * 根据solicitorConfigId获取SolicitorConfig
     *
     * @param solicitorConfigId
     * @return
     */
    Map getSolicitorConfig(String solicitorConfigId);


    /**
     * 获取推广者交易参数
     *
     * @param solicitorId
     * @return
     */
    List getSolicitorConfigsBySolicitorId(String solicitorId);

    /**
     * 获取推广者交易参数
     *
     * @param solicitorId
     * @param payway
     * @return
     */
    Map getSolicitorConfigsBySolicitorIdAndPayway(String solicitorId, Integer payway);

    /**
     * 获取解析后的推广者交易参数
     *
     * @param solicitorId
     * @param payway
     * @return
     */
    Map getAnalyzedSolicitorConfigsBySolicitorIdAndPayway(String solicitorId, Integer payway);

    /**
     * 获取门店的交易参数，如果门店没有设置，则取商户的配置
     *
     * @param payWay
     * @param subPayway
     * @param context
     * @return
     */
    @Deprecated
    Map getTradeParams(int payWay, int subPayway, Map context);

    /**
     * 获取业务方门店的交易参数，如果门店没有设置，则取商户的配置
     *
     * @param payWay
     * @param subPayway
     * @param context
     * @return
     */
    Map getTradeParamsWithTradeApp(int payway, int subPayway, String tradeApp, Map context);

    /**
     * 获取商户备用渠道的配置参数
     *
     * @param context
     * @param payway
     * @param subPayway
     * @return
     */
    Map<String, Object> getBypassTradeParams(Map<String, Object> context, Integer payway, Integer subPayway);

    /**
     * 获取商户基本的交易校验信息 如商户限额
     *
     * @param merchantId
     * @return
     */
    Map getMerchantTradeValidateParams(
            @NotNull(message = "商户id不能为空")
                    String merchantId);


    /***
     * 修改商户基本的交易校验信息 如商户限额, 值为元，类型字符串
     * @param merchantId
     * @param validateParams
     *  store_daily_max_sum_of_trans
     *  merchant_daily_max_sum_of_trans
     *  merchant_daily_payway_max_sum_of_trans  {payway:{sub_payway: sum}} sub_payway为空字符串时，表示设置payway的限额, 值全部为字符串
     *  merchant_single_max_of_tran
     *  deposit
     *  merchant_daily_max_credit_limit_trans 商户交易日信用限额
     *  merchant_monthly_max_credit_limit_trans 商户交易月信用限额
     *  payway_day_credit_limits 商户payway 日信用限额
     *  payway_month_credit_limits 商户payway 月信用限额
     *  merchant_bankcard_single_max_limit 商户银行卡单笔限额
     *  merchant_bankcard_day_max_limit 商户银行卡单日限额
     *  merchant_bankcard_month_max_limit 商户银行卡单月限额
     */
    void updateMerchantTradeValidateParams(
            @NotNull(message = "商户id不能为空")
                    String merchantId, Map validateParams);

    /**
     * 设置商户当日支付方式限额
     *
     * @param merchantId
     * @param payway
     * @param subPaywayConfig {sub_payway: sum} sub_payway为空字符串时，表示设置payway的限额, 值全部为字符串
     */
    void updateMerchantDailyPaywayMaxSumOfTrans(@NotNull(message = "商户id不能为空") String merchantId, int payway, Map<String, String> subPaywayConfig);

    /**
     * 获取商户是否保护消费者的隐私的值
     *
     * @param merchantId
     * @return
     */
    Boolean getMerchantIsProtectPayerPrivacy(String merchantId);

    /**
     * 设置商户是否保护消费者的隐私的值
     *
     * @param merchantId
     * @param isProtectPayerPrivacy
     */
    void updateMerchantIsProtectPayerPrivacy(@NotEmpty(message = "商户id不能为空")
                                                     String merchantId, boolean isProtectPayerPrivacy);

    /**
     * 获取商户各支付方式下是否允许使用信用卡
     *
     * @param merchantId
     * @return
     */
    Map getMerchantIsLimitCreditCard(@NotEmpty(message = "商户id不能为空")
                                             String merchantId);


    void updateMerchantIsLimitCreditCard(@NotEmpty(message = "商户id不能为空")
                                                 String merchantId, Integer payway, String creditCardPayLimit);

    /**
     * 配置支付宝b2c c2b的 正式参数
     *
     * @param merchantId
     * @param alipayTradeParams partner
     *                          app_key
     *                          agent_name
     */
    void updateAlipayV1TradeParams(String merchantId, Map alipayTradeParams);



    /**
     * 配置支付宝b2c c2b的 正式参数  并记录日志
     *
     * @param merchantId
     * @param alipayTradeParams partner
     *                          app_key
     *                          agent_name
     */
    void updateAlipayV1TradeParamsAndLog(String merchantId,Map alipayTradeParams, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 更新商户支付宝2.0b2c c2b支付参数
     *
     * @param merchantId
     * @param alipayTradeParams "auth_app_id":"xxx"
     *                          app_auth_token
     *                          category
     *                          mch_id
     *                          agent_name
     */
    void updateAlipayV2TradeParams(String merchantId, Map alipayTradeParams);

    /**
     * 更新商户支付宝2.0b2c c2b支付参数 并记录日志
     *
     * @param merchantId
     * @param alipayTradeParams  "auth_app_id":"xxx"
     *                           app_auth_token
     *                           category
     *                           mch_id
     *                           agent_name
     * @param opLogCreateRequest
     */
    void updateAlipayV2TradeParamsAndLog(String merchantId, Map alipayTradeParams, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 更新商户支付宝2.0b2c c2b支付参数
     *
     * @param merchantId
     * @param alipayTradeParams "auth_app_id":"xxx"
     *                          app_auth_token
     *                          category
     *                          mch_id
     *                          agent_name
     * @param restoreDefaultFeeRate 是否恢复默认费率
     */
    void updateByAlipayV2TradeParams(String merchantId, Map alipayTradeParams, boolean restoreDefaultFeeRate);

    /**
     * 更新商户支付宝2.0 wap支付参数
     *
     * @param merchantId
     * @param alipayWapTradeParams "auth_app_id":"xxx"
     *                             app_auth_token
     *                             category
     *                             mch_id
     *                             agent_name
     */
    void updateAlipayV2WapTradeParams(String merchantId, Map alipayWapTradeParams);

    /**
     * 新增支付宝直连小程序appid 配置
     * @param merchantId
     * @param miniOpAppId
     */
    void addAlipayV2WapMiniAppId(String merchantId, String miniOpAppId);

    /**
     * 更新商户支付宝2.0 app支付参数
     *
     * @param merchantId
     * @param alipayAppTradeParams "app_id":"xxx"
     *                             "private_key":"xxx"
     */
    void updateAlipayV2AppTradeParams(String merchantId,
                                      @PropNotEmpty.List({
                                              @PropNotEmpty(value = "app_id", message = "{value}不可为空"),
                                              @PropNotEmpty(value = "private_key", message = "{value}不可为空")
                                      }) Map alipayAppTradeParams);

    /**
     * 更新商户支付宝2.0 h5支付参数
     *
     * @param merchantId
     * @param alipayH5TradeParams "app_id":"xxx"
     *                            "private_key":"xxx"
     */
    void updateAlipayV2H5TradeParams(String merchantId,
                                     @PropNotEmpty.List({
                                             @PropNotEmpty(value = "app_id", message = "{value}不可为空"),
                                             @PropNotEmpty(value = "private_key", message = "{value}不可为空")
                                     }) Map alipayH5TradeParams);

    /**
     * 更新支付宝2.0b2c c2b wap支付参数
     *
     * @param alipayTradeParams "auth_app_id":"xxx"
     *                          app_auth_token
     *                          category
     *                          mch_id
     *                          agent_name
     */
    void updateAlipayV2AllTradeParams(String merchantId, Map alipayTradeParams);

    /**
     * 更新微信b2c c2b 正式支付参数
     *
     * @param merchantId
     * @param weixinTradeParams weixin_sub_appid
     *                          weixin_sub_mch_id
     *                          agent_name
     */
    void updateWeixinTradeParams(String merchantId, Map weixinTradeParams);



    /**
     * 更新微信b2c c2b 正式支付参数 并记录日志
     *
     * @param merchantId
     * @param weixinTradeParams weixin_sub_appid
     *                          weixin_sub_mch_id
     *                          agent_name
     */
    void updateWeixinTradeParamsAndLog(String merchantId, Map weixinTradeParams,@NotNull @Valid OpLogCreateRequest opLogCreateRequest);
    /**
     * 更新微信wap  正式支付参数
     *
     * @param merchantId
     * @param weixinWapTradeParams weixin_sub_appid
     *                             weixin_sub_mch_id
     *                             weixin_sub_appsecret
     *                             agent_name
     */
    void updateWeixinWapTradeParams(String merchantId, Map weixinWapTradeParams);


    /**
     * 更新微信wap  正式支付参数
     *
     * @param merchantId
     * @param weixinWapTradeParams weixin_sub_appid
     *                             weixin_sub_mch_id
     *                             weixin_sub_appsecret
     *                             agent_name
     */
    void updateWeixinWapTradeParamsAndLog(String merchantId, Map weixinWapTradeParams, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);

    /**
     * 更新微信mini 正式支付参数
     *
     * @param merchantId
     * @param weixinMiniTradeParams weixin_sub_appid
     *                              weixin_sub_mch_id
     *                              agent_name
     */
    void updateWeixinMiniTradeParams(String merchantId, Map weixinMiniTradeParams);

    void updateWeixinHKTradeParams(String merchantId, Map weixinHkTradeParams);


    void updateWeixinHKTradeParamsAndLog(String merchantId, Map weixinHkTradeParams, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);



    /**
     * 更新微信 h5 正式支付参数
     *
     * @param merchantId
     * @param weixinH5TradeParams weixin_sub_mch_id
     *                            weixin_sub_appid
     */
    void updateWeixinH5TradeParams(String merchantId, Map weixinH5TradeParams);

    /**
     * 更新微信 app 正式支付参数
     *
     * @param merchantId
     * @param weixinAppTradeParams weixin_sub_mch_id
     *                             weixin_sub_appid
     */
    void updateWeixinAppTradeParams(String merchantId, Map weixinAppTradeParams);

    void updateAlipayIntlTradeParams(String merchantId,
                                     @PropNotEmpty.List({
                                             @PropNotEmpty(value = "merchant_id", message = "{value}不可为空"),
                                             @PropNotEmpty(value = "client_id", message = "{value}不可为空"),
                                             @PropNotEmpty(value = "private_key", message = "{value}不可为空")
                                     }) Map alipayTradeParams);

    /**
     * 更新翼支付的交易参数信息
     *
     * @param merchantId         商户id
     * @param bestPayTradeParams 翼支付交易参数
     *                           merchant_id
     *                           merchant_key
     *                           merchant_pwd
     *                           store_id
     */
    void updateBestPayTradeParams(String merchantId, Map bestPayTradeParams);

    /**
     * 更新和支付b2c正式支付参数
     *
     * @param merchantId
     * @param cmccTradeParams
     */
    void updateCMCCTradeParams(String merchantId, Map cmccTradeParams);

    /**
     * 更新索迪斯b2c正式支付参数
     *
     * @param merchantId
     * @param needClear
     * @param sodexoTradeParams
     */
    void updateSodexoTradeParams(String merchantId, boolean needClear, Map sodexoTradeParams);

    /**
     * 更新院校通b2c正式支付参数
     *
     * @param request
     *
     * @return
     */
    void updateHopeEduTradeParams(HopeEduMerchantConfigRequest request);

    /**
     * 更新拉卡拉apple pay 交易参数
     * @param request
     * @return
     */
    Map<String, Object> updateLakalaApplePayParams(LakalaMerchantConfigRequest request);

    /**
     * 更新商户兴业银行支付通道的参数信息
     *
     * @param merchantId
     * @param cibbankTradeParams cibbank_mch_id
     */
    @Deprecated
    void updateCIBBankTradeParams(String merchantId, Map cibbankTradeParams);

    /**
     * 获取商户兴业银行支付通道的参数信息
     *
     * @param merchantId cibbank_mch_id
     */
    @Deprecated
    Map getCIBBankTradeParams(String merchantId);

    /**
     * 更新商户中信银行支付通道的参数信息
     *
     * @param merchantId
     * @param citicbankTradeParams citicbank_mch_id
     */
    @Deprecated
    void updateCITICBankTradeParams(String merchantId, Map citicbankTradeParams);

    /**
     * 获取商户中信银行支付通道的参数信息
     *
     * @param merchantId
     * @return citicbank_mch_id
     */
    @Deprecated
    Map getCITICBankTradeParams(String merchantId);

    /**
     * 更新商户华夏银行支付通道的参数信息
     *
     * @param hxBankMerchantConfig
     *          merchant_id
     *          payway
     *          b2c_status
     *          b2c_agent_name
     *          c2b_status
     *          c2b_agent_name
     *          wap_status
     *          wap_agent_name
     *          mini_status
     *          mini_agent_name
     *          provider
     *          hxbank_trade_params
     *              provider_mch_id
     *              develop_app_id
     *              sqb_private_key (可选)
     *              provider_term_id (可选)
     *
     */
    void updateHXBankMerchantConfig(Map<String, Object> hxBankMerchantConfig);

    /**
     * 更新商户华夏银行支付通道的参数信息
     *
     * @param hxBankMerchantConfig
     *          merchant_id
     *          payway
     *          b2c_status
     *          b2c_agent_name
     *          c2b_status
     *          c2b_agent_name
     *          wap_status
     *          wap_agent_name
     *          mini_status
     *          mini_agent_name
     *          provider
     *          hxbank_trade_params
     *              provider_mch_id
     *              develop_app_id
     *              sqb_private_key (可选)
     *              provider_term_id (可选)
     *
     */
    void updateHXBankMerchantAppConfig(Map<String, Object> hxBankMerchantConfig);

    /**
     * 设置provider的交易参数
     *
     * @param merchantId
     * @param provider
     * @param tradeParams
     */
    void updateProviderTradeParams(String merchantId, Integer provider, Map tradeParams);


    /**
     * 获取provider的交易参数
     *
     * @param merchantId
     * @param provider
     * @return
     */
    Map getProviderTradeParams(String merchantId, Integer provider);

    /**
     * 更新拉卡拉支付通道的参数信息
     *
     * @param merchantId
     * @param lakalaTradeParams lakala_merc_id
     *                          lakala_term_id
     */
    @Deprecated
    void updateLakalaTradeParams(String merchantId, Map lakalaTradeParams);

    /**
     * 更新通联结算参数信息
     *
     * @param merchantId
     */
    void updateTLTradeParams(String merchantId, Map tlTradeParams);

    /**
     * 更新银商结算参数信息
     *
     * @param merchantId
     */
    void updateYSTradeParams(String merchantId, Map tlTradeParams);

    /**
     * 更新海科结算参数信息
     *
     * @param merchantId
     * @param hkTradeParams
     */
    void updateHKTradeParams(String merchantId, Map hkTradeParams);

    /**
     * 更新国通结算参数信息
     *
     * @param merchantId
     * @param gtTradeParams
     */
    void updateGuotongTradeParams(String merchantId, Map gtTradeParams);


    /**
     * 更新中投更新结算信息
     * @param merchantId
     * @param ztkxTradeParams  需要填入{"platform_mch_id":"","provider_mch_id":""}
     *
     */
    void updateZTKXTradeParams(String merchantId, Map ztkxTradeParams);

    /**
     * 更新清算标志
     *
     * @param merchantId
     * @param clearanceProvider -1:通道切换中   2：拉卡拉   3：通联   4：银商
     */
    void updateClearanceProvider(String merchantId, int clearanceProvider);


    int getClearanceProvider(String merchantId);

    /**
     * 获取拉卡拉支付通道的参数信息
     *
     * @param merchantId
     * @return lakala_merc_id
     * lakala_term_id
     */
    @Deprecated
    Map getLakalaTradeParams(String merchantId);

    /**
     * 获取威富通的参数信息
     *
     * @param merchantId
     * @return citicbank_group_no
     * citicbank_mch_id
     * citicbank_mch_pay_id
     */
    Map getWftTradeParams(String merchantId);

    /**
     * 添加 商户入网到provider下面的groupNo大商户后，返回的商户号等信息
     * 存在payway为null的记录里面
     *
     * @param merchantId
     * @param provider
     * @param groupNo
     * @param tradeParams (tradeParamKey:{"key":"value"})
     */
    void addProviderEnrolledInfo(String merchantId, Integer provider, String groupNo, Map tradeParams);


    /**
     * 获取商户是否开通,是否正式（包括正式商户的配置），以及费率 的信息
     *
     * @param merchantId
     * @return
     */
    List getAnalyzedMerchantConfigs(String merchantId);

    /**
     * 获取门店是否开通,是否正式（包括正式商户的配置），以及费率 的信息
     *
     * @param merchantId
     * @return
     */
    List getAnalyzedStoreConfigs(String storeId);

    /**
     * 获取终端配置信息
     *
     * @param terminalId
     * @return
     */
    List getAnalyzedTerminalConfigs(String terminalId);

    /**
     * 获取商户是否开通,是否正式（包括正式商户的配置），以及费率 的信息
     *
     * @param merchantId
     * @return
     */
    List getAnalyzedMerchantConfigsByPayWayList(String merchantId, int[] payWayList);


    /**
     * 获取商户解析的配置列表
     * 针对　getAnalyzedMerchantConfigsByPayWayList 查询条件扩展延伸
     *
     * @param request 入参
     * @return
     * @see TradeConfigService#getAnalyzedMerchantConfigsByPayWayList(String, int[])
     */
    List getAnalyzedMerchantConfigList(@Valid @NotNull(message = "入参为空") GetAnalyzedMerchantAppConfigRequest request);

    /**
     * 获取门店是否开通,是否正式（包括正式商户的配置），以及费率 的信息
     *
     * @param merchantId
     * @return
     */
    List getAnalyzedStoreConfigsByPayWayList(String storeId, int[] payWayList);

    /**
     * 获取终端配置信息
     *
     * @param terminalId
     * @param payWayList
     * @return
     */
    List getAnalyzedTerminalConfigsByPayWayList(String terminalId, int[] payWayList);


    /**
     * 更新商户收款通道是否开通以及费率的信息-校验服务商费率
     *
     * @param merchantId
     * @param merchantConfigRequest
     */
    void updateMerchantConfigStatusAndFeeRateObeySolicitor(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId,
            @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空")
                    Map merchantConfigRequest);

    /**
     * 更新商户收款通道是否开通以及费率的信息
     *
     * @param merchantId
     * @param merchantConfig
     */
    void updateMerchantConfigStatusAndFeeRate(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId,
            @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空")
                    Map merchantConfig);


    /**
     * 更新商户收款通道是否开通以及费率的信息
     *
     * @param merchantId
     * @param merchantConfig
     *
     */

    void updateMerchantConfigStatusAndFeeRateAndLog(
            @NotNull(message = "merchantId 商户id不能为空")
            String merchantId,
            @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空")
            Map merchantConfig, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    void updateMerchantConfigStatus(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId,
            List<Map> updateMerchantConfigs
            );

    /**
     * 更新商户所有支付方式的B2C状态
     *
     * @param merchantId
     * @param b2cStatus  B2C状态,0关闭，1打开
     */
    void updateMerchantB2cStatus(String merchantId, int b2cStatus);

    /**
     * 更新商户收款通道的支付通道信息,
     *
     * @param merchantId
     * @param payway
     */
    @Deprecated
    void updateMerchantConfigProvider(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId, Integer payway, Integer provider);

    /**
     * 更新商户收款通道的支付通道信息
     *
     * @param merchantId
     * @param payway
     * @param provider
     * @param agentName
     */
    void updateMerchantConfigProvider(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId, Integer payway, Integer provider,
            String agentName);


    /**
     * 切换商户通道通用接口
     * merchant_id
     * payway
     * provider
     * agent_name
     * <p>
     * provider_merchant_id
     * alipay_merchant_id
     * weixin_merchant_id
     * weixin_app_id
     * weixin_sub_appsecret
     * weixin_mini_app_id
     * weixin_mini_sub_appsecret
     *
     * @param params
     * @return
     */
    boolean updateMerchantConfigProviderCommon(@PropNotEmpty.List({
            @PropNotEmpty(value = MerchantConfig.MERCHANT_ID, message = "{value} 商户Id不能为空"),
            @PropNotEmpty(value = MerchantConfig.PAYWAY, message = "{value} 支付方式不能为空"),
            @PropNotEmpty(value = MerchantConfig.PROVIDER, message = "{value} provider不能为空"),
            @PropNotEmpty(value = AGENT_NAME, message = "{value} agent_name不能为空")
    }) Map params);

    /**
     * 更新商户微信收款通道的支付通道信息
     * 兼容下同一个渠道下，报备了多次威富通，但是关注的公众号不一样的情况
     *
     * @param merchantId
     * @param provider
     * @param agentName
     * @param subAppid
     * @param subAppsecret
     */
    void updateWeixinMerchantConfigProvider(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId, Integer provider,
            String agentName, String subAppid, String subAppsecret
    );

    /**
     * 更新商户微信收款通道的支付通道信息（包括小程序），如果小程序不传参数，默认不配置
     * 兼容下同一个渠道下，报备了多次威富通，但是关注的公众号不一样的情况
     *
     * @param merchantId
     * @param provider
     * @param agentName
     * @param subAppid
     * @param subAppsecret
     * @param miniSubAppid
     * @param miniSubAppsecret
     */
    void updateWeixinAllMerchantConfigProvider(
            @NotNull(message = "merchantId 商户id不能为空")
                    String merchantId, Integer provider,
            String agentName, String subAppid, String subAppsecret, String miniSubAppid, String miniSubAppsecret
    );

    /**
     * 更新推广者收款通道的支付通道信息
     *
     * @param solicitorId
     * @param payway
     */
    @Deprecated
    void updateSolicitorConfigProvider(
            @NotNull(message = "solicitorId 推广者id不能为空")
                    String solicitorId, Integer payway, Integer provider);

    /**
     * 更新推广者收款通道的支付通道信息
     *
     * @param solicitorId
     * @param payway
     * @param provider
     * @param agentName
     */
    void updateSolicitorConfigProvider(
            @NotNull(message = "solicitorId 推广者id不能为空")
                    String solicitorId, Integer payway, Integer provider,
            String agentName);

    /**
     * 获取推广者基本的交易校验信息 如商户限额
     *
     * @param solicitorId
     * @return
     */
    Map getSolicitorTradeValidateParams(
            @NotNull(message = "推广者id不能为空")
                    String solicitorId);


    /***
     * 修改推广者基本的交易校验信息 如商户限额
     * @param solicitorId
     * @param validateParams
     *
     */
    void updateSolicitorTradeValidateParams(
            @NotNull(message = "推广者id不能为空")
                    String solicitorId, Map validateParams);

    /**
     * 获取推广者开通商户时 是否 开通,以及费率的信息
     *
     * @param solicitorId
     * @return
     */
    List getAnalyzedSolicitorConfigs(String solicitorId);


    /**
     * 更新商户收款通道是否开通以及费率的信息
     *
     * @param solicitorId
     * @param solicitorConfig
     */
    void updateSolicitorConfigStatusAndFeeRate(
            @NotNull(message = "solicitorId 推广者id不能为空")
                    String solicitorId,
            @PropNotEmpty(value = SolicitorConfig.PAYWAY, message = "{value} 支付方式不能为空")
                    Map solicitorConfig);



    /**
     * 获取支付宝2.0 授权token
     *
     * @param authAppId
     * @return
     */
    @Deprecated
    String getAlipayV2AppAuthToken(String authAppId);

    /**
     * 获取获取支付宝2.0授权token 与 支付宝系统的shopId
     *
     * @param authAppId
     * @param storeId
     * @return
     */
    Map getAlipayV2AppAuthInfo(String authAppId, String storeId);

    /**
     * todo vendorConfig没有存在的必要了。以后替换掉
     */
    @Deprecated
    Map getDefaultVendorConfig();

    /**
     * 获取系统参数配置
     *
     * @param name
     * @return
     */
    Map<String, Object> getSystemConfigContentByName(String name);

    Map<String, Object> updateMerchantSingleMaxOfTran(@NotNull(message = "商户编号不能为空") String merchantSn, Map config);

    Map<String, Object> updateMerchantSingleMaxOfTranAndLog(@NotNull(message = "商户编号不能为空") String merchantSn, Map config, OpLogCreateRequest opLogCreateRequest);

    Map<String, Object> updateUseClientStoreSn(@NotNull(message = "商户编号不能为空") String merchantSn, int useClientStoreSn);

    void updateHuabeiParamsBySn(@NotNull(message = "商户编号不能为空") String merchantId, Map<String, Object> params);

    void updateHuabeiParamsById(@NotNull(message = "商户Id不能为空") String merchantId, Map<String, Object> params);

    Map<String, Object> getHuabeiParamsBySn(@NotNull(message = "商户编号不能为空") String merchantSn);

    Map<String, Object> getHuabeiParamsById(@NotNull(message = "商户编号不能为空") String merchantSn);

    /**
     * 更新fee_rate_tag
     * @param merchantId
     * @param payway
     * @param version
     * @param feeRateTag
     * @param ladderFeeRateTag
     */
    void updateMerchantConfigFeeRateTag(String merchantId, int payway, long version, Map<String,Object> feeRateTag, Map<String,Object> ladderFeeRateTag);

    /**
     * 更新或初始化阶梯费率
     *
     * @param merchantId
     * @param payWay
     * @param ladderInfo
     */
    void saveMerchantLadderInfoByPayWay(@NotNull(message = "商户id不能为空") String merchantId, @NotNull(message = "payway不能为空") Integer payWay, Map<String, Object> ladderInfo);

    void updateMerchantLadderInfoByPayWay(@NotNull(message = "商户id不能为空") String merchantId, @NotNull(message = "payway不能为空") Integer payWay, Map<String, Object> ladderInfo);


    /**
     * 设置商户退款时间
     *
     * @param merchantId 商户id
     * @param flagStatus 退款状态 0：默认 1：同支付源
     * @return
     */
    Map<String, Object> updateMerchantHistoryTradeRefundFlag(@NotNull(message = "商户id不能为空") String merchantId, int flagStatus);


    /**
     * 设置商户退款状态并记录日志
     *
     * @param merchantId 商户id
     * @param flagStatus 退款状态 0：默认 1：同支付源
     * @return
     */
    Map<String, Object> updateMerchantHistoryTradeRefundFlagAndLog(@NotNull(message = "商户id不能为空") String merchantId, int flagStatus, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 修改商户预授权配置
     *
     * @param merchantId    商户id
     * @param depositConfig 预授权配置
     *                      weixin: 1:开启 0：关闭
     *                      alipay: 1:开启 0：关闭
     * @return
     */
    Map<String, Object> updateMerchantDeposit(@NotNull(message = "商户id不能为空") String merchantId, Map depositConfig);


    /**
     * 修改商户预授权配置并记录日志
     *
     * @param merchantId    商户id
     * @param depositConfig 预授权配置
     *                      weixin: 1:开启 0：关闭
     *                      alipay: 1:开启 0：关闭
     * @return
     */
    Map<String, Object> updateMerchantDepositAndLog(@NotNull(message = "商户id不能为空") String merchantId, Map depositConfig, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 获取是否上送商户storeId的值
     *
     * @param merchantId
     * @return
     */
    Boolean getMerchantIsSentStoreId(@NotNull(message = "商户id不能为空") String merchantId);

    /**
     * 设置是否上送商户storeId的值
     *
     * @param merchantId
     * @param isSentStoreId
     */
    void updateMerchantIsSentStoreId(@NotNull(message = "商户id不能为空") String merchantId
            , boolean isSentStoreId);

    void updateGiftCardParams(String merchantId, boolean open);

    boolean getGiftCardStatus(String merchantId);

    void updateWelfareCardParams(String merchantId, boolean open);

    boolean getWelfareCardStatus(String merchantId);

    void configureChinaumsTradeParams(Map<String, Object> params);

    void configureBestpayStoreTradeParams(Map<String, Object> params);

    void configureWxGoodsTag(Map<String, Object> params);

    /**
     * 根据merchantId切换statusTag对应的功能的状态为status.
     *
     * @param merchantId
     * @param switchKey
     * @param status
     */
    void switchStatus(
            @NotNull(message = "商户ID不可为空") String merchantId,
            @NotNull(message = "switchKey功能不可为空") String switchKey,
            @NotNull(message = "需要修改的状态不可为空") int status);

    /**
     * 根据merchantId和switchKey查询商户对应的功能状态.
     *
     * @param merchantId
     * @return 权限状态: 0表示开启状态，1表示关闭状态
     */
    Integer queryStatus(
            @NotNull(message = "商户ID不可为空") String merchantId,
            @NotNull(message = "switchKey功能不可为空") String switchKey);

    /**
     * 获取商户switch开关
     *
     * @param merchantId
     * @return
     */
    Map<String, Object> getSwitches(String merchantId);

    /**
     * 根据storeId切换statusTag对应的功能的状态为status.
     *
     * @param storeId
     * @param switchKey
     * @param status
     */
    void switchStoreStatus(
            @NotNull(message = "门店ID不可为空") String storeId,
            @NotNull(message = "switchKey功能不可为空") String switchKey,
            @NotNull(message = "需要修改的状态不可为空") int status);

    /**
     * 根据storeId和switchKey查询商户对应的功能状态.
     *
     * @param storeId
     * @return 权限状态: 0表示开启状态，1表示关闭状态
     */
    Integer queryStoreStatus(
            @NotNull(message = "门店ID不可为空") String storeId,
            @NotNull(message = "switchKey功能不可为空") String switchKey);

    /**
     * 获取门店switch开关
     *
     * @param storeId
     * @return
     */
    Map<String, Object> getStoreSwitches(String storeId);

    Map<String, Object> updateAlipaySellerId(Map<String, Object> params);


    Map<String, Object> updateAlipaySellerIdAndLog(Map<String, Object> params, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);



    /**
     * 批量更新支付宝门店号
     *
     * @param params
     */
    void batchUpdateAlipayStoreId(Map<String, Object> params);

    /**
     * 配置商户退款时是否需要退换手续费的标记
     *
     * @param merchantSn
     * @param config
     * @return
     */
    Map<String, Object> updateRefundFeeFlag(@NotNull(message = "商户号不能为空") String merchantSn, Map config);

    /**
     * 创建MerchantAppConfig
     *
     * @param merchantAppConfig
     */
    Map createMerchantAppConfig(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                    Map merchantAppConfig);

    /**
     * 查询业务方交易配置
     *
     * @param merchantAppConfigId
     * @return
     */
    public Map getMerchantAppConfig(@NotNull(message = "id不能为空") String merchantAppConfigId);

    /**
     * 查询业务方交易配置
     *
     * @param merchantId
     * @return
     */
    List getMerchantAppConfigsByMerchantId(@NotNull(message = "merchantId不能为空") String merchantId);
    /**
     * 是否存在三方通道(间连间清通道)
     *
     * @param merchantId
     * @return
     */
    boolean existThirdClearanceProvider(@NotBlank(message = "merchantId不能为空") String merchantId);

    /**
     * 查询业务方交易配置
     *
     * @param
     * @return
     */
    Map getMerchantAppConfigByMerchantIdAndPaywayAndApp(@NotNull(message = "商户id不能为空") String merchantId, Integer payway, String appId);

    /**
     * 根据merchantId和provider查询业务方交易配置
     *
     * @param
     * @return
     */
    List<Map<String, Object>> getMerchantAppConfigByMerchantIdAndProvider(@NotBlank(message = "商户id不能为空") String merchantId,
                                                                          @NotNull(message = "provider不能为null")Integer provider);

    /**
     * 查询业务方交易配置
     *
     * @param
     * @return
     */
    List<Map<String, Object>> getMerchantAppConfigByMerchantIdAndApp(@NotNull(message = "商户id不能为空") String merchantId, String appId);

    /**
     * 修改MerchantAppConfig
     *
     * @param merchantAppConfig
     */
    Map updateMerchantAppConfig(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}主键id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                    Map merchantAppConfig);

    /**
     * 修改MerchantAppConfig 并记录操作日志
     *
     * @param merchantAppConfig
     */
    Map updateMerchantAppConfigAndLog(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}主键id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map merchantAppConfig, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);


    /**
     * 删除MerchantAppConfig
     *
     * @param merchantAppConfigId
     * @return
     */
    void deleteMerchantAppConfig(@NotNull(message = "主键id不能为空") String merchantAppConfigId);

    /**
     * 创建StoreAppConfig
     *
     * @param storeAppConfig
     */
    Map createStoreAppConfig(
            @PropSize.List({
                    @PropSize(value = StoreAppConfig.STORE_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropNotEmpty.List({
                    @PropNotEmpty(value = StoreAppConfig.APP_ID, message = "app_id 应用方不能为空")
            })
            @PropIsMap(value = StoreAppConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                    Map storeAppConfig);


    /**
     * 查询业务方交易配置
     * @param storeId
     * @param payway
     * @param appId
     * @return
     */
    Map getStoreAppConfigByStoreIdAndPaywayAndApp(@NotNull(message = "门店id不能为空") String storeId, Integer payway, String appId);

    /**
     * 查询业务方交易配置
     * @param storeId
     * @param appId
     * @return
     */
    List<Map<String, Object>> getStoreAppConfigByStoreIdAndApp(@NotNull(message = "门店id不能为空") String storeId, String appId);

    /**
     * 修改StoreAppConfig
     *
     * @param storeAppConfig
     */
    Map updateStoreAppConfig(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "{value}主键id不能为空")
            })
            @PropSize.List({
                    @PropSize(value = StoreAppConfig.STORE_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = StoreAppConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            })
            @PropIsMap(value = StoreAppConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                    Map storeAppConfig);

    /**
     * 删除StoreAppConfig
     *
     * @param storeAppConfigId
     * @return
     */
    void deleteStoreAppConfig(@NotNull(message = "主键id不能为空") String storeAppConfigId);

    /**
     * 获取门店基本的交易校验信息 如门店限额
     *
     * @param storeId
     * @return
     */
    Map getStoreTradeValidateParams(
            @NotNull(message = "门店id不能为空")
                    String storeId);


    /***
     * 修改门店基本的交易校验信息 如门店限额, 值为元，类型字符串
     * @param storeId
     * @param validateParams
     *  store_daily_max_sum_of_trans
     */
    void updateStoreTradeValidateParams(
            @NotNull(message = "门店id不能为空")
                    String storeId, Map validateParams);

    /***
     * 删除门店限额, 值为元，类型字符串
     * @param storeId
     *  store_daily_max_sum_of_trans
     */
    void closeStoreTradeQuota(
            @NotNull(message = "门店id不能为空")
                    String storeId);


    /* 商户层级更新支付宝门店号
     *
     * @param params
     */
    Map updateAlipayStoreIdOnMerchant(Map<String, Object> params);


    /* 商户层级更新支付宝门店号并记录操作日志
     *
     * @param params
     */
    Map updateAlipayStoreIdOnMerchantAndLog(Map<String, Object> params, @NotNull @Valid OpLogCreateRequestV2 opLogCreateRequest);

    /**
     * 配置通用开关
     *
     * @param request
     */
    void configCommonSwitch(Map<String, Object> request);

    /**
     * 配置通用开关、添加日志
     *
     * @param request
     */
    void configCommonSwitchAndLog(Map<String, Object> request, OpLogCreateRequestV2 opLogCreateRequestV2);

    /**
     * 获取通用开关状态
     *
     * @param request
     * @return
     */
    int getCommonSwitchStatus(Map<String, Object> request);

    /**
     * 储值交易参数配置
     *
     * @param merchantId
     */
    void updatePrepaidTradeParams(String merchantId);

    /**
     * 风控接口  商户 category终端类型限额配置
     * 更新商户category终端类型限额配置，quota为null 则清除商户 category终端类型限额配置
     */
    Map<String, Object> updateCategoryMerchantSingleMax(String merchantSn, Long quota, String category);

    /**
     * 创建扩展交易参数
     *
     * @param request
     */
    void createTradeExtConfig(TradeExtConfigCreateRequest request);

    /**
     * 修改扩展交易参数
     *
     * @param request
     */
    void updateTradeExtConfig(TradeExtConfigUpdateRequest request);

    /**
     * 查询扩展交易参数
     *
     * @param request
     * @return
     */
    TradeExtConfigQueryResponse queryTradeExtConfig(TradeExtConfigQueryRequest request);

    /**
     * 设置payway 信用限额
     * @param quota 额度 单位元
     *
     */
    void updateMerchantCreditLimit(String merchantId, int payway, int creditType, String quota);

    /**
     * 更新招行生活APP配置
     *
     * @param merchantId 商户号
     * @param config     待变更配置
     */
    void updateCmbLifeAppConfig(String merchantId, Map<String, String> config);

    /**
     * 拉卡拉交易参数管理(商户入网后添加)
     *
     * 迁移到接口updateBankCardMerchantConfig上
     * @param lklOpenMerchantConfig
     */
    @Deprecated
    Map updateLklOpenMerchantConfig(@PropSize.List({
            @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
            @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
    })@PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                                     Map lklOpenMerchantConfig);


    Map updateBankCardMerchantConfigFeeRate(
            @PropSize.List({
                    @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
                    @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
            }) @PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map merchantConfig);

    /**
     * 关闭指定费率类型
     *
     * @param merchantId
     * @param payWay
     * @param feeRateStatusKeys
     * @return
     */
    void closeMerchantConfigFeeRate(@NotNull(message = "商户id不能为空") String merchantId,
                                   @NotNull(message = "payWay不能为空") Integer payWay,
                                   @NotNull(message = "待关闭费率类型列表不能为空") List<String> feeRateStatusKeys);

    /**
     * 终端银行卡配置(智能pos银行卡收款功能开启/关闭时调用)
     *
     * 迁移到updateBankCardTerminalConfig上
     * @param lklOpenTerminalConfig
     */
    @Deprecated
    Map updateLklOpenTerminalConfig(Map lklOpenTerminalConfig);

    /**
     * 批量获取拉卡拉终商号 切到新接口 listTerminalConfigByTerminalIds
     * @param terminalIds
     * @return
     */
    @Deprecated
    List<Map>getTerminalLaklaConfigBatch(String merchantId ,List<String> terminalIds);

    /**
     * 根据商户号获取
     * @param merchantSn 商户sn
     * @param payway
     * @return
     */
    Map<String,Object> getBankcardFee(String merchantSn,Integer payway);

    /**
     * 查询商户可用支付方式列表
     *
     * @param request
     * @return
     */
    List<MerchantAvailablePaywaysQueryResult> queryMerchantAvailablePayways(MerchantAvailablePaywaysQueryRequest request);

    Map<String,Object> updateFitnessMerchantAppConfig(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = TransactionParam.MERCHANT_ID, message = "{value}不能为空"),
                    @PropNotEmpty(value = TransactionParam.FITNESS_PARAMS_ALIPAY_MERCHANT_PID, message = "{value}不能为空"),
                    @PropNotEmpty(value = TransactionParam.FEE_RATE, message = "{value}不能为空"),
            })@PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map params);

    /**
     * 现在没有获取shopId的接口,阶段付需要手动更新
     * @param params
     * @return
     */
    boolean updateFitnessShopId(
        @PropNotEmpty.List({
                @PropNotEmpty(value = TransactionParam.MERCHANT_ID, message = "{value}不能为空"),
                @PropNotEmpty(value = TransactionParam.FITNESS_PARAMS_ALIPAY_SHOP_ID, message = "{value}不能为空"),
        })
        Map params
    );

    /**
     * 更新开关
     * @param merchantId
     * @param status
     * @return
     */
    Map<String, Object> updateFitnessMerchantSwitch(String merchantId, int status);

    /**
     *
     * @param merchantId
     * @param products
     */
    @Deprecated
    void updateFitnessProducts(String merchantId, List<Map> products, boolean exclusive);

    /**
     * 更新先享后付业业务配置
     * @param merchantId
     * @param fitnessParams
     */
    void upsertFitnessParams(String merchantId, Map fitnessParams);


    /**
     * 创建应用配置
     * @param request
     */
    void createTradeAppConfig(TradeAppConfigCreateRequest request);


    /**
     * 获取所有的应用配置
     * @return
     */
    List<TradeAppConfig> getAllTradeAppConfig();

    /**
     * 银行卡交易参数
     *
     *
     * @param merchantConfig
     */
    Map updateBankCardMerchantConfig(@PropSize.List({
            @PropSize(value = MerchantConfig.MERCHANT_ID, max = 37, message = "{value}不可超过{max}字符"),
            @PropSize(value = MerchantConfig.B2C_FEE_RATE, max = 45, message = "{value}b扫c 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.C2B_FEE_RATE, max = 45, message = "{value}c扫b 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.WAP_FEE_RATE, max = 45, message = "{value}wap 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.MINI_FEE_RATE, max = 45, message = "{value}mini 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.H5_FEE_RATE, max = 45, message = "{value}h5 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.APP_FEE_RATE, max = 45, message = "{value}app 费率不可超过{max}字符"),
            @PropSize(value = MerchantConfig.EXTEND2_FEE_RATE, max = 45, message = "{value}交易模式保留字段  费率不可超过{max}字符"),
    })@PropIsMap(value = MerchantConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
                                            Map merchantConfig);

    /**
     * 终端银行卡配置(智能pos银行卡收款功能开启/关闭时调用)
     *
     * @param terminalConfig
     */
    Map updateBankCardTerminalConfig(@PropSize.List({
            @PropSize(value = TerminalConfig.TERMINAL_ID, max = 37, message = "{value}不可超过{max}字符")
    })Map terminalConfig);

    /**
     * 获取商户端终信息
     * @param merchantId
     * @param terminalIds
     * @return
     */
    List<Map> listTerminalConfigByTerminalIds(String merchantId, List<String> terminalIds);

    /**
     * 查询商户返回的paywayLimtis
     * @param merchantId
     * @return
     */
    Map queryMerchantPaywayLimit(String merchantId);


    /**
     * 更新payway限额，注：云闪付是云闪付境外钱包的限额
     * @param merchantId
     * @param key
     * @param type 0 单笔 1 单日
     * @param quota 额度 单位元
     */
    void updateMerchantPaywayLimit(String merchantId, String key, int type, Integer quota);


    /**
     * 更新payway限额，注：云闪付是云闪付境外钱包的限额,添加日志
     *
     * @param merchantId
     * @param key
     * @param type       0 单笔 1 单日
     * @param quota      额度 单位元
     */
    void updateMerchantPaywayLimitAndLog(String merchantId, String key, int type, Integer quota, OpLogCreateRequest opLogCreateRequest);


    /**
     * 查询商户的全部数据，包括基础业务和多业务的数据
     *
     * @param merchantSn
     * @return
     */
    AllMerchantConfigResponse queryAllMerchantConfigs(@NotNull(message = "商户编号不能为空") String merchantSn);
    /**
    *
    * 变更商户收付通品牌信息
    *
    * @param request
    */
   void updateMerchantSFTBrandInfo(UpdateMerchantSFTBrandInfoRequest request);
    /**
     * 是否能进行分期的交易 （防止商户没有开通对应的通道能力 查询getAllParams里会报错 每次都会打到drds上 需要通过该接口判断能进行交易后才可
     *
     * @param merchantId
     * @return
     */
    boolean isCanFqTradeWithPaywayAndTradeApp(@NotNull(message = "商户Id不能为空") String merchantId, @NotNull(message = "支付方式不能为空") Integer payway, String tradeAppId);

    /**
     * 更新京东白条开启状态 及最低金额
     * @param merchantId
     * @param payway
     * @param params
     * @return
     */
    void updateFqParamsById(@NotNull(message = "商户Id不能为空") String merchantId, Integer payway, Map<String, Object> params);

    /**
     * 商户小微升级替换通道商户号时间
     * @param merchantId
     * @param clearanceProvider
     * @param successSwitchTime
     */
   void updateMerchantSwitchMchTime(String merchantId,Integer clearanceProvider,long successSwitchTime);

    /**
     * 获取商户小微升级完成的时间
     * @param merchantId
     * @return 不会为null
     */
    Map getMerchantSwitchMchTime(String merchantId);

    /**
     * 删除终端配置
     ** @param request
     */
    void deleteTradeExtConfig(TradeExtConfigRemoveRequest request);

    /**
     * 增加商户层级营业对账数据配置
     * @param request
     */
    void updateMerchantCustomBusinessData(Map<String, Object> request);

    /**
     * 获取商户层级营业对账营业数据配置
     * @param merchantId
     */
    List<Map<String, Object>> getMerchantCustomBusinessData(String merchantId);

    /**
     * 清除商户层级营业对账营业数据配置
     * @param merchantId
     */
    void removeMerchantCustomBusinessData(String merchantId);

    /**
     * 更新收单参数管理配置
     * @param externalExtraConfig
     */
    void updateExternalExtraConfig(@PropSize.List({
            @PropSize(value = ExternalExtraConfig.SN, max = 37, message = "{value}不可超过{max}字符")
    })Map externalExtraConfig);

    /**
     * 获取收单机构商户配置
     * @param provider
     * @param providerMchId
     * @return
     */
    public Map getProviderBizStatus(@NotNull(message = "provider不能为空")Integer provider,@NotNull(message = "providerMchId不能为空") String providerMchId);
}
