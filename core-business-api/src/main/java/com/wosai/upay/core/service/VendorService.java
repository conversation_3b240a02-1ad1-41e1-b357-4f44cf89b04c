package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Vendor;
import com.wosai.upay.core.model.VendorApp;
import com.wosai.upay.core.model.VendorConfig;
import com.wosai.upay.core.model.VendorDeveloper;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import com.wosai.upay.common.validation.PropIsMap;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.common.validation.PropSize;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

@UpayCoreServiceAnnotation
@JsonRpcService("rpc/vendor")
@CoreBusinessValidated
public interface VendorService {

    /**
     * 创建开发者.
     *
     * 默认创建config，developer，并且可传账号id进行关联.
     *
     * @return
     */
    Map createVendorComplete(Map request);

    /**
     * 创建Vendor.
     *
     * @param vendor
     */
    Map createVendor(
            @PropNotEmpty.List(
                    @PropNotEmpty(value = Vendor.NAME, message = "{name} 开发者名称不能为空")
            )
            @PropSize.List({
                    @PropSize(value = Vendor.NAME, max = 128, message = "{value}名称不可超过{max}字符"),
                    @PropSize(value = Vendor.SN, max = 32, message = "{value}开发者可见的编号不可超过{max}字符"),
                    @PropSize(value = Vendor.CELLPHONE, max = 32, message = "{value}绑定的手机号不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_ADDRESS, max = 255, message = "{value}联系地址不可超过{max}字符"),
                    @PropSize(value = Vendor.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Vendor.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Vendor.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Vendor.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
            })
            @PropIsMap(value = Vendor.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段 remark 参数必须为Map格式")
            Map vendor);


    /**
     * 根据vendorId删除Vendor.
     *
     * @param vendorId
     */
    void deleteVendor(String vendorId);

    /**
     * 根据vendorSn删除Vendor.
     *
     * @param vendorSn
     */
    void deleteVendorBySn(String vendorSn);

    /**
     * 修改Vendor.
     *
     * @param vendor
     */
    Map updateVendor(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "开发者ID不能为空")
            })
            @PropSize.List({
                    @PropSize(value = Vendor.NAME, max = 128, message = "{value}名称不可超过{max}字符"),
                    @PropSize(value = Vendor.SN, max = 32, message = "{value}开发者可见的编号不可超过{max}字符"),
                    @PropSize(value = Vendor.CELLPHONE, max = 32, message = "{value}绑定的手机号不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_NAME, max = 32, message = "{value}联系人姓名不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_PHONE, max = 32, message = "{value}联系固定电话号码不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_CELLPHONE, max = 32, message = "{value}联系移动电话号码不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_EMAIL, max = 64, message = "{value}联系邮箱不可超过{max}字符"),
                    @PropSize(value = Vendor.CONTACT_ADDRESS, max = 255, message = "{value}联系地址不可超过{max}字符"),
                    @PropSize(value = Vendor.PROVINCE, max = 32, message = "{value}省不可超过{max}字符"),
                    @PropSize(value = Vendor.CITY, max = 32, message = "{value}市不可超过{max}字符"),
                    @PropSize(value = Vendor.DISTRICT, max = 32, message = "{value}区不可超过{max}字符"),
                    @PropSize(value = Vendor.STREET_ADDRESS, max = 255, message = "{value}街道门牌号不可超过{max}字符"),
            })
            @PropIsMap(value = Vendor.EXTRA, nullable = true, emptyable = true, message = "{value}扩展字段 remark 参数必须为Map格式")
            Map vendor);

    /**
     * 根据vendorId获取Vendor.
     *
     * @param vendorId
     * @return
     */
    Map getVendor(String vendorId);

    /**
     * 根据vendorSn获取Vendor.
     *
     * @param vendorSn
     * @return
     */
    Map getVendorBySn(String vendorSn);

    /**
     * 根据vendorId禁用Vendor（调用此接口后不可以增加商户、激活设备）.
     *
     * @param vendorId
     * @return
     */
    void disableVendor(
            @NotNull(message = "开发者ID不可为空")
            String vendorId);


    /**
     * 根据vendorId禁用Vendor（调用此接口后不可以增加商户、激活设备）.
     *
     * @param vendorId
     * @return
     */
    void disableVendorAndLog(
            @NotNull(message = "开发者ID不可为空")
            String vendorId, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);

    /**
     * 根据vendorId启用Vendor（禁用状态的开发者可重新启用）.
     *
     * @param vendorId
     * @return
     */
    void enableVendor(
            @NotNull(message = "开发者ID不可为空")
            String vendorId);


    /**
     * 根据vendorId启用Vendor（禁用状态的开发者可重新启用）.
     *
     * @param vendorId
     * @return
     */
    void enableVendorAndLog(
            @NotNull(message = "开发者ID不可为空")
            String vendorId, @NotNull @Valid OpLogCreateRequest opLogCreateRequest);
    /**
     * 根据vendorId关闭Vendor（必须开发者下所有商户都关闭后才可以关闭开发者）.
     *
     * @param vendorId
     * @return
     */
    void closeVendor(
            @NotNull(message = "开发者ID不可为空")
            String vendorId);

    /**
     * 分页查询Vendor.
     *
     * @param pageInfo
     * @param queryFilter
     *      name                名称
     *      sn                  开发者可见的编号
     *      status
     *      contact_phone       联系固定电话号码
     *      contact_cellphone   联系移动电话号码
     *      deleted
     * @return
     */
    ListResult findVendors(PageInfo pageInfo, Map queryFilter);

    /**
     * 重置开发者vendorKey
     * @param vendorSn
     * @return
     */
    String resetAppKey(String vendorSn);

    /**
     * 获取开发者vendorKey
     * @param vendorSn
     * @return
     */
    String getAppKey(String vendorSn);

    /**
     * 创建VendorApp.
     *
     * @param vendorApp
     */
    Map createVendorApp(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = VendorApp.VENDOR_ID, message = "开发者ID不能为空"),
                    @PropNotEmpty(value = VendorApp.NAME, message = "{value} 应用名称不能为空")
            })
            @PropSize.List({
                    @PropSize(value = VendorApp.VENDOR_ID, max = 36, message = "{value}开发者id不可超过{max}字符"),
                    @PropSize(value = VendorApp.APPID, max = 36, message = "{value}应用编号不可超过{max}字符"),
                    @PropSize(value = VendorApp.APPKEY, max = 36, message = "{value}应用密钥不可超过{max}字符"),
                    @PropSize(value = VendorApp.NAME, max = 128, message = "{value}应用名称不可超过{max}字符"),
            })
            Map vendorApp);

    /**
     * 根据vendorAppId删除VendorApp.
     *
     * @param vendorAppId
     */
    void deleteVendorApp(String vendorAppId);

    /**
     * 修改VendorApp.
     *
     * @param vendorApp
     */
    Map updateVendorApp(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "开发者应用ID不能为空"),
                    @PropNotEmpty(value = VendorApp.NAME, message = "{value} 应用名称不能为空")
            })
            @PropSize.List({
                    @PropSize(value = VendorApp.VENDOR_ID, max = 36, message = "{value}开发者id不可超过{max}字符"),
                    @PropSize(value = VendorApp.APPID, max = 36, message = "{value}应用编号不可超过{max}字符"),
                    @PropSize(value = VendorApp.APPKEY, max = 36, message = "{value}应用密钥不可超过{max}字符"),
                    @PropSize(value = VendorApp.NAME, max = 128, message = "{value}应用名称不可超过{max}字符"),
            })
            Map vendorApp);

    /**
     * 根据vendorAppId获取VendorApp.
     *
     * @param vendorAppId
     * @return
     */
    Map getVendorApp(String vendorAppId);


    /**
     * 获取vendor app appkey
     *
     * @param vendorAppAppid
     * @return
     */
    String getAppKeyByVendorAppAppid(String vendorAppAppid);

    /**
     * 分页查询VendorApp.
     *
     * @param pageInfo
     * @param queryFilter
     *      vendor_id           开发者id
     *      appid               应用编号
     *      name                应用名称
     *      type                产品类型
     * @return
     */
    ListResult findVendorApps(PageInfo pageInfo, Map queryFilter);

    /**
     * 创建VendorConfig.
     *
     * @param vendorConfig
     */
    Map createVendorConfig(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = VendorConfig.VENDOR_ID, message = "开发者ID不能为空")
            })
            @PropSize.List({
                    @PropSize(value = VendorConfig.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = VendorConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map vendorConfig);

    /**
     * 修改VendorConfig.
     *
     * @param vendorConfig
     */
    Map updateVendorConfig(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "开发者配置ID不能为空"),
                    @PropNotEmpty(value = VendorConfig.PARAMS, message = "开发者配置不能为空")
            })
            @PropSize.List({
                    @PropSize(value = VendorConfig.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
            })
            @PropIsMap(value = VendorConfig.PARAMS, nullable = true, emptyable = true, message = "{value}配置参数（JSON）参数必须为Map格式")
            Map vendorConfig);

    /**
     * 根据vendorId获取VendorConfig.
     *
     * @param vendorId
     * @return
     */
    Map getVendorConfigByVendorId(String vendorId);

    /**
     * 分页查询VendorConfig.
     *
     * @param pageInfo
     * @param queryFilter
     *      vendor_id
     *      deleted
     * @return
     */
    ListResult findVendorConfigs(PageInfo pageInfo, Map queryFilter);

    /**
     * 创建VendorDeveloper.
     *
     * @param vendorDeveloper
     */
    Map createVendorDeveloper(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = VendorDeveloper.VENDOR_ID, message = "开发者ID不能为空")
            })
            @PropSize.List({
                    @PropSize(value = VendorDeveloper.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = VendorDeveloper.VENDOR_SN, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = VendorDeveloper.APP_KEY, max = 64, message = "{value}开发者密钥不可超过{max}字符"),
            })
            @PropIsMap.List({
                    @PropIsMap(value = VendorDeveloper.PUBLIC_KEY, nullable = true, emptyable = true, message = "{value}开发者公钥参数必须为Map格式"),
                    @PropIsMap(value = VendorDeveloper.EXTRA, nullable = true, emptyable = true, message = "{value}参数必须为Map格式")
            })
            Map vendorDeveloper);

    /**
     * 修改VendorDeveloper.
     *
     * @param vendorDeveloper
     */
    Map updateVendorDeveloper(
            @PropNotEmpty.List({
                    @PropNotEmpty(value = DaoConstants.ID, message = "开发者开发者ID不能为空")
            })
            @PropSize.List({
                    @PropSize(value = VendorDeveloper.VENDOR_ID, max = 37, message = "{value}不可超过{max}字符"),
                    @PropSize(value = VendorDeveloper.VENDOR_SN, max = 32, message = "{value}不可超过{max}字符"),
                    @PropSize(value = VendorDeveloper.APP_KEY, max = 64, message = "{value}开发者密钥不可超过{max}字符"),
            })
            @PropIsMap.List({
                    @PropIsMap(value = VendorDeveloper.PUBLIC_KEY, nullable = true, emptyable = true, message = "{value}开发者公钥参数必须为Map格式"),
                    @PropIsMap(value = VendorDeveloper.EXTRA, nullable = true, emptyable = true, message = "{value}参数必须为Map格式")
            })
            Map vendorDeveloper);

    /**
     * 根据vendorId获取VendorDeveloper.
     *
     * @param vendorId
     * @return
     */
    Map getVendorDeveloperByVendorId(String vendorId);

    /**
     * 根据vendorSn获取VendorDeveloper.
     *
     * @param vendorSn
     * @return
     */
    Map getVendorDeveloperByVendorSn(String vendorSn);

    /**
     * 根据vendorSn获取VendorAppKey.
     *
     * @param vendorSn
     * @return
     */
    String getVendorAppKeyByVendorSn(String vendorSn);

    /**
     * 分页查询VendorDeveloper.
     *
     * @param pageInfo
     * @param queryFilter
     *      vendor_id
     *      vendor_sn
     *      deleted
     * @return
     */
    ListResult findVendorDevelopers(PageInfo pageInfo, Map queryFilter);

    /**
     * 查看应用类型
     * @return
     */
    Map<Integer, String> getVendorAppTypesAndDesc();

    /**
     * 获取翻译信息
     * @return
     */
    Map<String, Object> getVendorAppTranslateInfoById(String vendorAppId);

    Map<String, Object> getVendorAppTranslateInfoByAppId(String vendorAppAppId);

    /**
     * 获取翻译信息
     * @param vendorId
     * @return
     */
    Map<String, Object> getVendorTranslateInfoById(String vendorId);

    Map<String, Object> getVendorTranslateInfoBySn(String vendorSn);

}
