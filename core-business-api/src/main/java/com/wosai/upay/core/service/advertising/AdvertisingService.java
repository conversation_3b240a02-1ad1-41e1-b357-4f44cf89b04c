package com.wosai.upay.core.service.advertising;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.core.bean.response.MerchantResponse;
import com.wosai.upay.core.bean.response.StoreResponse;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

@UpayCoreServiceAnnotation
@CoreBusinessValidated
@JsonRpcService("rpc/advertising")
public interface AdvertisingService {


    /**
     * 根据商户id获取商户（广告组专用）
     *
     * @param merchantId 商户ID
     * @return MerchantResponse
     */
    MerchantResponse getMerchantByMerchantId(String merchantId);

    /**
     * 根据storeSn获取Store（广告组专用）
     *
     * @param storeSn 门店编号
     * @return StoreResponse
     */
    StoreResponse getStoreByStoreSn(String storeSn);

}