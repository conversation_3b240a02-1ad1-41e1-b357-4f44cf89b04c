package com.wosai.upay.core.service.department;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.*;
import com.wosai.upay.core.common.CoreBusinessTranslate;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by lihebin on 07/03/2018.
 * 部门相关接口
 */
@CoreBusinessValidated
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/department")
@Deprecated
public interface DepartmentService {

    /**
     * 根据departmentSn获取department_store.
     *
     * @param departmentSn
     * @return
     */
    List<Map> getDepartmentStoreByDepartmentSn(String departmentSn);


}
