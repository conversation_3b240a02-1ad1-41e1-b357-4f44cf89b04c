package com.wosai.upay.core.service.user;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.common.CoreBusinessTranslate;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;

import com.wosai.upay.core.model.user.VendorUser;
import com.wosai.upay.common.validation.PropNotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Created by jianfree on 22/3/16.
 * 用户相关的接口放在里面
 */
@CoreBusinessValidated
@UpayCoreServiceAnnotation
@JsonRpcService("rpc/user")
@Deprecated
public interface UserService {

    /**
     * 根据accountId获取Account.
     *
     * @param accountId
     * @return
     */
    Map getAccount(String accountId);

    /**
     * 通过手机号码获取账号信息
     * @param cellphone
     * @return
     */
    Map getAccountByCellphone(String cellphone);

    /**
     * 创建VendorUser.
     *
     * @param vendorUser
     */
    Map createVendorUser(
            @PropNotEmpty.List({
                    @PropNotEmpty(value= VendorUser.ACCOUNT_ID, message="{value}账号id不能为空"),
                    @PropNotEmpty(value= VendorUser.VENDOR_ID, message="{value}渠道商id不能为空")})

            Map vendorUser);

    /**
     * 根据vendorUserId删除VendorUser.
     *
     * @param vendorUserId
     */
    void deleteVendorUser(String vendorUserId);

    /**
     * 修改VendorUser.
     *
     * @param vendorUser
     */
    Map updateVendorUser(Map vendorUser);

    /**
     * 根据vendorUserId获取VendorUser.
     *
     * @param vendorUserId
     * @return
     */
    Map getVendorUser(String vendorUserId);

    /**
     * 根据accountId获取VendorUser.
     *
     * @param accountId
     * @return
     */
    Map getVendorUserByAccountId(String accountId);

    /**
     * 根据vendorUserId禁用VendorUser.
     *
     * @param vendorUserId
     * @return
     */
    void disableVendorUser(String vendorUserId);

    /**
     * 分页查询VendorUser.
     *
     * @param pageInfo
     * @param queryFilter
     *      vendor_id           商户id
     *      account_id          user表里面的id
     *      deleted
     * @return
     */
    ListResult findVendorUsers(PageInfo pageInfo, Map queryFilter);


    /**
     * 根据merchantUserId删除MerchantUser.
     *
     * @param merchantUserId
     */
    void deleteMerchantUser(
            @NotNull(message = "用户id不能为空")
            String merchantUserId);


    /**
     * 根据merchantUserId获取MerchantUser.
     *
     * @param merchantUserId
     * @return
     */
    Map getMerchantUser(String merchantUserId);

    /**
     * 根据accountId获取MerchantUser.
     *
     * @param accountId
     * @return
     */
    Map getMerchantUserByAccountId(String accountId);

    /**
     * 获取超级管理员用户账号.
     * @param merchantId
     * @return
     */
    Map getMerchantSuperAdminUserAccount(String merchantId);

    /**
     * 获取商户用户门店授权.
     *
     * @param queryFilter
     *      merchant_id         商户id
     *      account_id          账号id
     *      merchant_user_id    商户用户id
     *      store_id            门店id
     *      deleted
     * @return
     */
    List<Map> getMerchantUserStoreAuths(Map queryFilter);

    /**
     * 分页查询商户用户门店授权.
     *
     * @param pageInfo
     * @param queryFilter
     *      merchant_id         商户id
     *      merchant_user_id    商户用户id
     *      store_id            门店id
     *      store_ids           门店ids []
     *      deleted
     * @return
     */
    ListResult findMerchantUserStoreAuths(PageInfo pageInfo, Map queryFilter);

    /**
     * 获取商户用户部门授权.
     *
     * @param queryFilter merchant_id         商户id
     *                    account_id          账号id
     *                    merchant_user_id    商户用户id
     *                    department_id            部门id
     *                    deleted
     * @return
     */
    List<Map> getMerchantUserDepartmentAuths(Map queryFilter);

    /**
     * 分页查询商户用户部门授权.
     *
     * @param pageInfo
     * @param queryFilter merchant_id         商户id
     *                    merchant_user_id    商户用户id
     *                    department_id            部门id
     *                    department_ids           部门ids []
     *                    deleted
     * @return
     */
    ListResult findMerchantUserDepartmentAuths(PageInfo pageInfo, Map queryFilter);


}
