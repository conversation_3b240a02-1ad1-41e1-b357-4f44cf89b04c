package com.wosai.upay.core.meta;

import java.util.List;

/**
 * 间连结算机构定义
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/25.
 */
public class ClearanceProvider extends Meta<Integer>{

    public static final ClearanceProvider SWITCH = new ClearanceProvider(-1, "切换中");
    public static final ClearanceProvider LKL = new ClearanceProvider(2, "拉卡拉");
    public static final ClearanceProvider TL = new ClearanceProvider(3, "通联");
    public static final ClearanceProvider YS = new ClearanceProvider(4, "银商");

    public ClearanceProvider() {
    }

    public ClearanceProvider(Integer code, String name) {
        super(code, name);
    }

    public static String getNameByCode(Integer code){
        return MetaManager.getNameByCode(ClearanceProvider.class.getName(), code);
    }

    public static List<ClearanceProvider> getAll(){
        return MetaManager.getMetaList(ClearanceProvider.class.getName());
    }
}
