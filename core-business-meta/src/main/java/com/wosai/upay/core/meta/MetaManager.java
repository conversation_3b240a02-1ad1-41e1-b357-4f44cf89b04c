package com.wosai.upay.core.meta;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/25.
 */
public class MetaManager {
    public static Map<String, List<Meta>> map = new HashMap<>();

    public synchronized static void addMeta(final Meta meta){
        String key = meta.getClass().getName();
        List<Meta> list = map.get(key);
        if(list == null){
            list = new ArrayList<>();
            map.put(key, list);
        }
        for (int i = 0; i < list.size(); i++) {
            Meta exist = list.get(i);
            if(Objects.equals(exist.getCode(), meta.getCode())){
                //如果有相同的，则直接覆盖后返回
                list.set(i, meta);
                return;
            }
        }
        list.add(meta);
    }

    public static <T> String getNameByCode(String className, T code){
        List<Meta> list = map.get(className);
        if(list != null){
            for(Meta meta: list){
                if(Objects.equals(code, meta.getCode())){
                    return meta.getName();
                }
            }
        }
        return null;
    }

    public static <E> List<E> getMetaList(String className){
        return (List<E>) map.get(className);
    }
}
