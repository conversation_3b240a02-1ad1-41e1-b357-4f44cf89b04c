package com.wosai.upay.core.meta;


import java.util.List;

/**
 * 支付源定义
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/25.
 */
public class Payway extends Meta<Integer> {

    public static final Payway ALIPAY = new Payway(1, "支付宝1.0");
    public static final Payway ALIPAY2 = new Payway(2, "支付宝");
    public static final Payway WEIXIN = new Payway(3, "微信");
    public static final Payway BAIFUBAO = new Payway(4, "百度钱包");
    public static final Payway JDPAY = new Payway(5, "京东支付");
    public static final Payway QQWALLET = new Payway(6, "QQ钱包");
    public static final Payway APPLEPAY = new Payway(7, "Apple Pay");
    public static final Payway LKLWALLET = new Payway(8, "拉卡拉钱包");
    public static final Payway CMCC = new Payway(9, "移动和包");
    public static final Payway UNIONPAY = new Payway(17, "银联云闪付");
    public static final Payway BESTPAY = new Payway(18, "翼支付");
    public static final Payway WEIXIN_HK = new Payway(19, "WeChat-Local");
    public static final Payway ALIPAY_INTL = new Payway(20, "支付宝国际");
    public static final Payway BANKCARD = new Payway(21, "银行卡");
    public static final Payway SODEXO = new Payway(22, "索迪斯预付卡");
    public static final Payway DCEP = new Payway(23, "数字人民币");
    public static final Payway FOXCONN = new Payway(25, "富圈圈钱包");
    public static final Payway GRABPAY = new Payway(26, "grabpay钱包");
    public static final Payway BANKACCOUNT = new Payway(27, "银行转账");

    // 澳門通
    public static final Payway MACAU_PASS = new Payway(29, "澳门通");
    public static final Payway HOPE_EDU = new Payway(32, "一码通"); // 院校通使用
    public static final Payway DASH_PAY = new Payway(33, "DashPay"); // DashPay
    public static final Payway LIQUID_PAY = new Payway(34, "LiquidPay"); // LiquidPay

    public static final Payway ALIPAY_HK_LOCAL = new Payway(98, "Alipay-HK-Local");
    public static final Payway ALIPAY_HK_INTL = new Payway(99, "Alipay-HK-Intl");
    @Deprecated
    public static final Payway PREPAID_CARD_OLD = new Payway(100, "储值支付"); //老的储值支付，已废弃
    public static final Payway GIFT_CARD = new Payway(101, "礼品卡");

    /**
     * 记账相关
     */
    public static final Payway CASH = new Payway(102, "现金");
    public static final Payway ELE = new Payway(103, "饿了么");
    public static final Payway MEI_TUAN = new Payway(104, "美团");
    public static final Payway DIAN_PING = new Payway(107, "大众点评");
    public static final Payway OTHER_CHARGE = new Payway(108, "其他记账方式");

    public static final Payway PREPAID_CARD = new Payway(105, "储值卡");

    public static final Payway CCB_APP = new Payway(109, "建行生活APP");

    public static final Payway CMB_APP = new Payway(110, "招商生活APP");
    public static final Payway WELFARE_CARD = new Payway(111, "福利卡");
    public static final Payway DOUYINQUAN = new Payway(112, "抖音券");
    public static final Payway MEITUANQUAN = new Payway(113, "美团券");
    public static final Payway CCB_GIFT_CARD = new Payway(114, "建行福利卡");


    public static final Payway PAY_NOW_PAY = new Payway(3001, "PayNowPay"); // PayNowPay 新加坡国家码


    public Payway() {
    }

    public Payway(Integer code, String name) {
        super(code, name);
    }

    public static String getNameByCode(Integer code){
        return MetaManager.getNameByCode(Payway.class.getName(), code);
    }

    public static List<Payway> getAll(){
        return MetaManager.getMetaList(Payway.class.getName());
    }

}
