package com.wosai.upay.core.meta;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/8.
 */
public class ProductFlag extends Meta<String>{

    public static final ProductFlag MARKET_PROGRAM_DISCOUNT = new ProductFlag("a1", "扫码点餐");
    public static final ProductFlag REDPACK_DISCOUNT = new ProductFlag("a2", "红包优惠");
    public static final ProductFlag HUABEI = new ProductFlag("a3", "花呗分期");
    public static final ProductFlag UFOOD = new ProductFlag("a4", "扫码点餐");
    public static final ProductFlag DBB = new ProductFlag("a5", "电饱饱");
    public static final ProductFlag JJZ_WM = new ProductFlag("a7", "九九折外卖");
    public static final ProductFlag JJZ_ZQ = new ProductFlag("a8", "九九折自取");
    public static final ProductFlag I_STORE = new ProductFlag("a9", "智慧门店");
    public static final ProductFlag MINI = new ProductFlag("ab", "小程序");
    public static final ProductFlag PLATFORM_DELIVERY = new ProductFlag("ac", "平台外卖");
    public static final ProductFlag HBFQ_DISCOUNT = new ProductFlag("ad", "花呗商家贴息");
    public static final ProductFlag PASSWORDLESS_PAY = new ProductFlag("ae", "免密支付");
    public static final ProductFlag FORMPAY_GENERAL = new ProductFlag("af", "通用收款单");
    public static final ProductFlag FORMPAY_EDU = new ProductFlag("ag", "课时中心");
    public static final ProductFlag SMART_FEE_UP = new ProductFlag("ah", "智慧门店收费试点");
    public static final ProductFlag CREDIT_CARD_INSTALMENT = new ProductFlag("ai", "信用卡分期");
    public static final ProductFlag ACQUIRING_BIZ = new ProductFlag("aj", "收单业务");
    public static final ProductFlag COMBINED_PAYMENT = new ProductFlag("ak", "组合支付");
    public static final ProductFlag MERCHANT_MINI = new ProductFlag("am", "商家小程序");
    public static final ProductFlag FIENESS = new ProductFlag("an", "周期付");
    public static final ProductFlag DEPOSIT_TYPE_SQB = new ProductFlag("aq", "收钱吧预授权");
    public static final ProductFlag DEPOSIT_TYPE_PAY_SOURCE = new ProductFlag("ap", "支付源预授权");

    public static final ProductFlag ISTORE_ORDER = new ProductFlag("c1", "智慧门店扫码点单");
    public static final ProductFlag ISTORE_ORDER_PHONE = new ProductFlag("c2", "智慧门店手机点单");
    public static final ProductFlag ISTORE_ORDER_CASHREGISTER = new ProductFlag("c3", "智慧门店收银机点单");
    public static final ProductFlag ISTORE_DELIVERY = new ProductFlag("c4", "智慧门店外卖配送");
    public static final ProductFlag ISTORE_TAKEOUT = new ProductFlag("c5", "智慧门店到店自取");
    public static final ProductFlag CAMPUS = new ProductFlag("c6", "校园外卖入驻");
    public static final ProductFlag CAMPUS_DELIVERY = new ProductFlag("c7", "校园外卖配送");
    public static final ProductFlag THIRDPARTY_DELIVERY = new ProductFlag("c8", "第三方配送外卖");
    public static final ProductFlag PREPAID_CARD = new ProductFlag("c9", "储值核销");
    public static final ProductFlag GROUP_NOTE = new ProductFlag("ca", "群接龙");
    public static final ProductFlag PREPAID_CARD_BUY = new ProductFlag("aa", "储值充值");
    public static final ProductFlag COUPON_BUY = new ProductFlag("d2", "券包购买");
    public static final ProductFlag COUPON = new ProductFlag("d3", "优惠券核销");
    public static final ProductFlag MEMBER_CARD_BUY = new ProductFlag("d4", "会员卡购买");
    public static final ProductFlag MEMBER_CARD = new ProductFlag("d5", "会员卡核销");
    public static final ProductFlag DISCOUNT = new ProductFlag("d6", "全场折扣核销");
    public static final ProductFlag ITEM_ACTIVITY = new ProductFlag("d7", "单品活动核销");
    public static final ProductFlag SECOND_HALF_OFF = new ProductFlag("d8", "第二份半价核销");
    public static final ProductFlag SQB_COUPON = new ProductFlag("d9", "平台券核销");
    public static final ProductFlag CROSS_CARD_PAY = new ProductFlag("ar", "跨境卡刷卡支付");
    public static final ProductFlag LOCAL_CARD_PAY = new ProductFlag("as", "本地卡刷卡支付");
    public static final ProductFlag FQ_VERSION_2 = new ProductFlag("at", "分期2.0");
    public static final ProductFlag DIRECT_PAY = new ProductFlag("au", "直接收款");
    public static final ProductFlag CROSS_CARD_PAY_INDIRECT = new ProductFlag("av", "跨境卡刷卡支付(直清)");


    public ProductFlag() {
    }

    public ProductFlag(String code, String name) {
        super(code, name);
    }

    public static String getNameByCode(String code){
        return MetaManager.getNameByCode(ProductFlag.class.getName(), code);
    }

    public static List<ProductFlag> getAll(){
        return MetaManager.getMetaList(ProductFlag.class.getName());
    }
}
