package com.wosai.upay.core.meta;

import java.util.List;

/**
 * 支付通道定义
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/25.
 */
public class Provider extends Meta<Integer>{

    public static final Provider CIB_BANK = new Provider(1001, "兴业银行");
    public static final Provider LAKALA = new Provider(1002, "拉卡拉");
    public static final Provider CITIC_BANK = new Provider(1003, "中信银行");
    public static final Provider CEB_BANK = new Provider(1004, "光大银行");
    public static final Provider CZB_BANK = new Provider(1005, "浙商银行");
    public static final Provider SSRCB_BANK = new Provider(1006, "石狮农商行");
    public static final Provider CMBC_XM_BANK = new Provider(1007, "厦门民生银行");
    public static final Provider CIB_GZ_BANK = new Provider(1008, "广州兴业银行");
    public static final Provider CITIC_ZJ_BANK = new Provider(1009, "镇江中信银行");
    public static final Provider CMBC_BANK = new Provider(1010, "民生银行");
    public static final Provider LAKALA_WANMA_ACCOUNT = new Provider(1011, "拉卡拉万码账户方模式");
    public static final Provider LAKALA_WANMA_DIRECT = new Provider(1012, "拉卡拉万码直连模式");
    public static final Provider NUCC = new Provider(1013, "网联");
    public static final Provider UNION_PAY = new Provider(1014, "银联");
    public static final Provider CIB_SH_BANK = new Provider(1015, "上海兴业银行");
    public static final Provider UNION_PAY_DIRECT = new Provider(1016, "内蒙银联");
    public static final Provider UNION_PAY_OPEN = new Provider(1017, "银联开放平台");
    public static final Provider CHINA_UMS = new Provider(1018, "银联商务");
    public static final Provider UNION_PAY_ONLINE = new Provider(1019, "银联网银支付");
    public static final Provider TONG_LIAN = new Provider(1020, "通联");
    public static final Provider UEPAY = new Provider(1021, "极易付");
    public static final Provider CMB_BANK = new Provider(1022, "招商银行");
    public static final Provider PSBC_BANK = new Provider(1023, "邮储银行");
    public static final Provider CGB_BANK = new Provider(1024, "广发银行");
    public static final Provider FOXCONN = new Provider(1025, "富士康富圈圈");
    public static final Provider CCB_BANK = new Provider(1026, "建设银行");
    public static final Provider HX_BANK = new Provider(1028, "华夏银行");
    public static final Provider CIB_HZ_BANK = new Provider(1029, "杭州兴业银行");
    public static final Provider ICBC_BANK = new Provider(1030, "工商银行");
    public static final Provider LAKALA_UNION_PAY = new Provider(1033, "拉卡拉银联");
    public static final Provider LAKALA_UNION_PAY_V3 = new Provider(1034, "拉卡拉银联");
    public static final Provider TL_SYB = new Provider(1035, "通联收银宝");
    public static final Provider CCB_GIFT_CARD = new Provider(1036, "建行福利卡");
    public static final Provider HAIKE_UNION_PAY = new Provider(1037, "海科银联");
    public static final Provider FUYOU = new Provider(1038, "富友");
    public static final Provider BOCOM = new Provider(1039, "交通银行");
    public static final Provider PAB = new Provider(1040, "平安银行");
    public static final Provider ABC = new Provider(1041, "农业银行(旧)");
    public static final Provider ENTPAY = new Provider(1042, "微企付");
    public static final Provider ZJTLCB = new Provider(1043, "泰隆银行");
    public static final Provider FJNX = new Provider(1044, "福建农信");
    public static final Provider JY_CARD = new Provider(1045, "锦医一卡通");
    public static final Provider SPDB = new Provider(1046, "浦发银行");
    public static final Provider JSB_BANK = new Provider(1047, "江苏银行");
    public static final Provider GUOTONG = new Provider(1048, "国通");

    public static final Provider LZCCB_BANK = new Provider(1049, "泸州银行");
    public static final Provider ZTKX = new Provider(1050, "中投科信");
    public static final Provider TL_S2P = new Provider(1051, "收银宝海外");
    public static final Provider YOP = new Provider(1052, "易宝支付");
    public static final Provider PKX_AIRPORT = new Provider(1055, "大兴机场");
    public static final Provider PSBC_BANK_SX = new Provider(1054, "邮储银行山西分行");
    public static final Provider AIRWALLEX = new Provider(1056, "空中云汇");
    public static final Provider XZX = new Provider(1057, "新中新");
    public static final Provider MACAU_PASS = new Provider(1059, "澳门通");
    public static final Provider WECARD = new Provider(1060, "腾讯微卡支付");

    public static final Provider HOPE_EDU = new Provider(1061, "院校通");
    public static final Provider HAIKE_DUO = new Provider(1062, "嗨客多");

    public static final Provider YEAH_PAY = new Provider(1063, "移卡支付");
    public static final Provider BOC = new Provider(1053, "中国银行");
    public static final Provider BCS = new Provider(1064, "长沙银行");
    public static final Provider ABCBANK = new Provider(1065, "农业银行");


    public Provider() {
    }

    public Provider(Integer code, String name) {
        super(code, name);
    }

    public static String getNameByCode(Integer code){
        return MetaManager.getNameByCode(Provider.class.getName(), code);
    }

    public static List<Provider> getAll(){
        return MetaManager.getMetaList(Provider.class.getName());
    }
}
