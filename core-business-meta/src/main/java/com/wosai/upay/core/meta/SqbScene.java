package com.wosai.upay.core.meta;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/2/8.
 */
public class SqbScene extends Meta<String>{

    public static final SqbScene JJZ_MARKET_PROGRAM_DISCOUNT = new SqbScene("market_program_discount", "久久折优惠");
    public static final SqbScene JJZ_COMMON_DISCOUNT = new SqbScene("jjz_common_discount", "久久折通用折扣");
    public static final SqbScene JJZ_TIME_DISCOUNT = new SqbScene("jjz_time_discount", "久久折时段折扣");
    public static final SqbScene JJZ_COLLECTION_DISCOUNT = new SqbScene("jjz_collection_discount", "久久折关注折扣");
    public static final SqbScene JJZ_PRICE_DISCOUNT = new SqbScene("jjz_price_discount", "久久折满减折扣");
    public static final SqbScene JJZ_DISCOUNT_CARD = new SqbScene("jjz_discount_card", "久久折优惠卡券");
    public static final SqbScene JJZ_WEEK_CARD = new SqbScene("jjz_week_card", "久久折轻会员周卡");
    public static final SqbScene JJZ_MONTH_CARD = new SqbScene("jjz_month_card", "久久折轻会员月卡");
    public static final SqbScene JJZ_COUPON_CARD = new SqbScene("jjz_coupon_card", "特惠商品券");
    public static final SqbScene JJZ_PRICE_CARD = new SqbScene("jjz_price_card", "满减优惠券");
    public static final SqbScene JJZ_RETURN_CARD = new SqbScene("jjz_return_card", "返店优惠券");
    public static final SqbScene JJZ_WM = new SqbScene("jjz_wm", "九九折外卖");
    public static final SqbScene JJZ_ZQ = new SqbScene("jjz_zq", "久久折优惠");
    public static final SqbScene I_STORE = new SqbScene("i_store", "智慧门店");
    public static final SqbScene REDPACK_DISCOUNT = new SqbScene("redpack_discount", "红包优惠");

    public static final SqbScene UFOOD = new SqbScene("ufood", "扫码点餐");
    public static final SqbScene MINI = new SqbScene("mini", "小程序");
    public static final SqbScene FORMPAY_GENERAL = new SqbScene("fmp_general", "通用收款单");
    public static final SqbScene FORMPAY_EDU = new SqbScene("fmp_edu", "课时中心");
    public static final SqbScene SMART_FEE_UP = new SqbScene("service_fee_up", "智慧门店收费试点");
    public static final SqbScene DBB = new SqbScene("dbb", "电饱饱");
    public static final SqbScene FACE2FACE = new SqbScene("face2face", "刷脸支付");
    public static final SqbScene HM_FACE2FACE = new SqbScene("hm_face2face", "海马终端刷脸交易");
    public static final SqbScene HM_QRCODE = new SqbScene("hm_qrcode", "海马终端扫码交易");
    public static final SqbScene ACQUIRING_BIZ = new SqbScene("acquiring_biz", "收单业务");
    public static final SqbScene COMBINED_PAYMENT = new SqbScene("combined_payment", "组合支付");
    public static final SqbScene MERCHANT_MINI = new SqbScene("merchant_mini", "商家小程序");


    public static final SqbScene ISTORE_ORDER = new SqbScene("istore_order", "智慧门店扫码点单");
    public static final SqbScene ISTORE_ORDER_PHONE = new SqbScene("istore_order_phone", "智慧门店手机点单");
    public static final SqbScene ISTORE_ORDER_CASHREGISTER = new SqbScene("istore_order_cashregister", "智慧门店收银机点单");
    public static final SqbScene ISTORE_DELIVERY = new SqbScene("istore_delivery", "智慧门店外卖配送");
    public static final SqbScene ISTORE_TAKEOUT = new SqbScene("istore_takeout", "智慧门店到店自取");
    public static final SqbScene CAMPUS = new SqbScene("campus", "校园外卖入驻");
    public static final SqbScene CAMPUS_DELIVERY = new SqbScene("campus_delivery", "校园外卖配送");
    public static final SqbScene THIRDPARTY_DELIVERY = new SqbScene("thirdparty_delivery", "第三方配送外卖");
    public static final SqbScene GROUP_NOTE = new SqbScene("group_note", "群接龙");


    public static final SqbScene PREPAID_CARD_BUY = new SqbScene("cz", "储值充值");
    public static final SqbScene COUPON_BUY = new SqbScene("coupon_buy", "券包购买");

    public static final SqbScene COUPON = new SqbScene("coupon", "优惠券核销");
    public static final SqbScene MEMBER_CARD_BUY = new SqbScene("member_card_buy", "会员卡购买");
    public static final SqbScene MEMBER_CARD = new SqbScene("member_card", "会员卡核销");
    public static final SqbScene DISCOUNT = new SqbScene("discount", "全场折扣核销");
    public static final SqbScene ITEM_ACTIVITY = new SqbScene("item_activity", "单品活动核销");
    public static final SqbScene SECOND_HALF_OFF = new SqbScene("2nd50%off", "第二份半价核销");
    public static final SqbScene SQB_COUPON = new SqbScene("sqb_coupon", "平台券核销");

    public static final SqbScene MIS_PUSH_TRADE = new SqbScene("mis_push_trade", "MIS推送交易");



    public SqbScene() {
    }

    public SqbScene(String code, String name) {
        super(code, name);
    }

    public static String getNameByCode(String code){
        return MetaManager.getNameByCode(SqbScene.class.getName(), code);
    }

    public static List<SqbScene> getAll(){
        return MetaManager.getMetaList(SqbScene.class.getName());
    }

    /**
     * 拼接多个支付场景
     * @param scenes
     * @return
     */
    public static String joinScenes(SqbScene ... scenes){
        StringBuffer sb = new StringBuffer();
        for (SqbScene scene : scenes) {
            if(scene != null && scene.getCode() != null && !scene.getCode().isEmpty()){
                sb.append(scene.getCode()).append(",");
            }
        }
        String result = sb.toString();
        return result.length() == 0 ? "" : result.substring(0, result.length() - 1);
    }

}
