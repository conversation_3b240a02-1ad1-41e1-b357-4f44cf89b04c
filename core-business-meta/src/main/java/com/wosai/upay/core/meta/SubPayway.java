package com.wosai.upay.core.meta;

import java.util.List;

/**
 * 交易模式定义
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/1/25.
 */
public class SubPayway extends Meta<Integer>{

    public static final SubPayway BARCODE = new SubPayway(1, "B扫C");
    public static final SubPayway QRCODE = new SubPayway(2, "C扫B");
    public static final SubPayway WAP = new SubPayway(3, "WAP支付");
    public static final SubPayway MINI = new SubPayway(4, "小程序支付");
    public static final SubPayway APP = new SubPayway(5, "APP支付");
    public static final SubPayway H5 = new SubPayway(6, "H5支付");

    public SubPayway() {
    }

    public SubPayway(Integer code, String name) {
        super(code, name);
    }

    public static String getNameByCode(Integer code){
        return MetaManager.getNameByCode(SubPayway.class.getName(), code);
    }

    public static List<SubPayway> getAll(){
        return MetaManager.getMetaList(SubPayway.class.getName());
    }


}
