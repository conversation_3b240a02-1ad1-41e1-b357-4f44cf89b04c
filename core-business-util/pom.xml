<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.wosai.upay</groupId>
    <artifactId>core-business-parent</artifactId>
    <version>3.9.40-SNAPSHOT</version>
  </parent>

  <artifactId>core-business-util</artifactId>
  <packaging>jar</packaging>

  <dependencies>

    <dependency>
      <groupId>com.wosai.nextgen</groupId>
      <artifactId>data-jdbc</artifactId>
      <version>${nextgen.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.wosai</groupId>
      <artifactId>spring4-boot-starter-mvc</artifactId>
      <type>pom</type>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <scope>compile</scope>
    </dependency>

	<dependency>
      <groupId>com.wosai.common</groupId>
      <artifactId>wosai-common</artifactId>
      <version>*******-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.ctrip.framework.apollo</groupId>
          <artifactId>apollo-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
        </exclusion>
        <exclusion>
         <groupId>com.wosai</groupId>
         <artifactId>wosai-brave-api</artifactId>
        </exclusion>
        <exclusion>
         <groupId>com.wosai</groupId>
         <artifactId>wosai-brave153-api</artifactId>
        </exclusion>
        <exclusion>
         <groupId>com.github.briandilley.jsonrpc4j</groupId>
         <artifactId>jsonrpc4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
        <groupId>javax.xml.bind</groupId>
        <artifactId>jaxb-api</artifactId>
        <version>2.3.1</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
  </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
