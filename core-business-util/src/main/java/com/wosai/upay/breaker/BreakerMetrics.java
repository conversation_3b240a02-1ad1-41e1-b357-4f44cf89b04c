package com.wosai.upay.breaker;

import com.wosai.common.utils.WosaiJsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.concurrent.atomic.AtomicLong;

public class BreakerMetrics {

    public static final Logger logger = LoggerFactory.getLogger(BreakerMetrics.class);

    private final String apiName;

    private final BreakerProperty property;

    private final MetricsBucketArray array;

    private volatile boolean open = false;

    private volatile boolean inTestPhase = false;

    private AtomicLong lastSingleTestTime = new AtomicLong();
    
    private static String lastCheck = "";

    public BreakerMetrics(String apiName, BreakerProperty property)
    {
        this.apiName = apiName;
        this.property = property;
        this.array = new MetricsBucketArray(property.getBucketNumber());
    }

    public void increment(BreakerStatus status)
    {
        this.array.peek().increment(status);
    }

    public boolean isOpen()
    {
        if ((this.open) && (!letSingleTest())) {
            return true;
        }
        return false;
    }

    private boolean letSingleTest()
    {
        long last = this.lastSingleTestTime.get();
        long next = last + this.property.getSingleTestWindowInMillis();
        long now = System.currentTimeMillis();
        if ((now > next) && (this.lastSingleTestTime.compareAndSet(last, now)))
        {
            this.inTestPhase = true;
            return this.inTestPhase;
        }
        return false;
    }

    public boolean inTestPhase()
    {
        return this.inTestPhase;
    }

    public void calc()
    {
        MetricsBucketArray.MetricSnapshot check = this.array.calcCheck();
        String checkStr = check.getError() +""+ check.getCircuitBreak();
        if(!checkStr.equals(lastCheck)){
            logger.info("MetricSnapshot -> {}, open -> {}", WosaiJsonUtils.toJSON(check), open);
            lastCheck = checkStr;
        }
        if ((this.open) && (check.getCircuitBreak() + check.getTotal() < 1.0F))
        {
            logger.error("Api({}), Action(Close), Info(No request for a while)!", new Object[] { this.apiName });
            this.open = false;
            return;
        }
        if (this.open) {
            return;
        }
        if (check.getTotal() >= this.property.getRequestCountThreshold())
        {
            float total = check.getTotal();
            float error = check.getError();
            float errorPercentage = error / total;
            if (errorPercentage > this.property.getErrorPercentageThreshold())
            {
                this.lastSingleTestTime = new AtomicLong(System.currentTimeMillis());

                logger.error("Api({}), Action(Open), Info(Error percentage: {}, Total request: {}, all requests will try to invoke fallback instead, single test will start after {}ms)!", new Object[] { this.apiName,

                Float.valueOf(errorPercentage), Float.valueOf(total), Long.valueOf(this.property.getSingleTestWindowInMillis()) });

                this.open = true;
            }
            else
            {
                logger.debug("Api({}), Action(Check), Info(Error percentage: {}, Total request: {})!", new Object[] { this.apiName, Float.valueOf(errorPercentage), Float.valueOf(total) });
            }
        }
    }

    public void singleTestPass(boolean flag)
    {
        this.inTestPhase = false;
        if (flag)
        {
            logger.error("Api({}), Action(RECOVER_SUCCEED), Info(Single test passed)!", new Object[] { this.apiName });
            this.open = false;
        }
        else
        {
            logger.error("Api({}), Action(RECOVER_FAIL), Info(Single test failed)!", new Object[] { this.apiName });
        }
    }
}
