package com.wosai.upay.breaker;

import java.util.concurrent.*;

public class BreakerMetricsManager {

    private static final ConcurrentHashMap<String, BreakerMetrics> BREAKER_METRICS_MAP = new ConcurrentHashMap<>();

    private volatile static BreakerMetricsManager instance =  new BreakerMetricsManager();

    private ScheduledExecutorService pool = Executors.newScheduledThreadPool(1);//启用1个线程

    public static synchronized BreakerMetricsManager getInstance(){
        return instance;
    }

    public void register(String apiName, BreakerMetrics breakerMetrics){
        BREAKER_METRICS_MAP.put(apiName, breakerMetrics);
    }

    public BreakerMetrics getBreakerMetrics(String apiName){
      return BREAKER_METRICS_MAP.get(apiName);
    }

    private BreakerMetricsManager(){

        pool.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                for(BreakerMetrics breakerMetrics : BREAKER_METRICS_MAP.values()){
                    breakerMetrics.calc();
                }

            }
        },3000,3000 ,TimeUnit.MILLISECONDS);
    }
}
