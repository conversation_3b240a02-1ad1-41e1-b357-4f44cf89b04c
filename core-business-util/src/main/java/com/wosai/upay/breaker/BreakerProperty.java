package com.wosai.upay.breaker;

public class BreakerProperty {

    private float errorPercentageThreshold = 0.5F;

    private long checkWindowInMillis = 3000L;

    private int bucketNumber = 10;

    private int requestCountThreshold = 60;

    private long singleTestWindowInMillis = 20000L;

    public BreakerProperty(float errorPercentageThreshold, long checkWindowInMillis, long calculateWindowInMillis, int bucketNumber, int requestCountThreshold, long singleTestWindowInMillis) {
        this.errorPercentageThreshold = errorPercentageThreshold;
        this.checkWindowInMillis = checkWindowInMillis;
        this.bucketNumber = bucketNumber;
        this.requestCountThreshold = requestCountThreshold;
        this.singleTestWindowInMillis = singleTestWindowInMillis;
    }

    public BreakerProperty() {

    }


    public float getErrorPercentageThreshold() {
        return errorPercentageThreshold;
    }

    public void setErrorPercentageThreshold(float errorPercentageThreshold) {
        this.errorPercentageThreshold = errorPercentageThreshold;
    }

    public long getCheckWindowInMillis() {
        return checkWindowInMillis;
    }

    public void setCheckWindowInMillis(long checkWindowInMillis) {
        this.checkWindowInMillis = checkWindowInMillis;
    }

    public int getBucketNumber() {
        return bucketNumber;
    }

    public void setBucketNumber(int bucketNumber) {
        this.bucketNumber = bucketNumber;
    }

    public int getRequestCountThreshold() {
        return requestCountThreshold;
    }

    public void setRequestCountThreshold(int requestCountThreshold) {
        this.requestCountThreshold = requestCountThreshold;
    }


    public long getSingleTestWindowInMillis() {
        return singleTestWindowInMillis;
    }

    public void setSingleTestWindowInMillis(long singleTestWindowInMillis) {
        this.singleTestWindowInMillis = singleTestWindowInMillis;
    }
}
