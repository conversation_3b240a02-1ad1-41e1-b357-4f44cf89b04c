package com.wosai.upay.breaker;

import java.util.concurrent.atomic.AtomicLong;

public class MetricsBucket {
    final AtomicLong errorCount = new AtomicLong(0L);
    final AtomicLong successCount = new AtomicLong(0L);
    final AtomicLong breakerRejcetCount = new AtomicLong(0L);

    void increment(BreakerStatus type)
    {
        switch (type)
        {
            case SUCCESS:
                this.successCount.getAndIncrement();
                break;
            case ERROR:
                this.errorCount.getAndIncrement();
                break;
            case BREAKER_REJECT:
                this.breakerRejcetCount.getAndIncrement();
                break;
        }
    }
}
