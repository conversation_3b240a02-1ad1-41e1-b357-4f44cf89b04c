package com.wosai.upay.breaker;

public class MetricsBucketArray {

    private MetricsBucket[] buckets;

    private volatile int currentIndex;

    private int size;

    private MetricSnapshot lastCheck;

    public MetricSnapshot getLastCheck()
    {
        return this.lastCheck;
    }

    MetricsBucketArray(int size)
    {
        this.size = size;
        this.buckets = new MetricsBucket[size + 1];
        for (int i = 0; i <= size; i++) {
            this.buckets[i] = new MetricsBucket();
        }
        this.currentIndex = 0;
        this.lastCheck = new MetricSnapshot();
    }

    MetricSnapshot calcCheck()
    {
        long sum = 0L;
        long error = 0L;
        long circuitBreak = 0L;
        int temp = this.currentIndex == this.size ? 0 : this.currentIndex + 1;
        this.buckets[temp] = new MetricsBucket();
        this.currentIndex = temp;
        for (int i = 0; i <= this.size; i++) {
            if (i != this.currentIndex)
            {
                MetricsBucket bucket = this.buckets[i];
                error += bucket.errorCount.get();
                circuitBreak += bucket.breakerRejcetCount.get();
                sum += bucket.errorCount.get() + bucket.successCount.get();
            }
        }
        MetricSnapshot check = new MetricSnapshot();
        check.error = ((float)error);
        check.total = ((float)sum);
        check.circuitBreak = ((float)circuitBreak);
        this.lastCheck = check;
        return check;
    }

    MetricsBucket peek()
    {

        return this.buckets[this.currentIndex];
    }

    class MetricSnapshot
    {
        private float total;
        private float error;
        private float circuitBreak;

        MetricSnapshot() {}

        public float getTotal()
        {
            return this.total;
        }

        public float getError()
        {
            return this.error;
        }

        public float getCircuitBreak()
        {
            return this.circuitBreak;
        }

        @Override
        public String toString() {
            return "MetricSnapshot{" +
                    "total=" + total +
                    ", error=" + error +
                    ", circuitBreak=" + circuitBreak +
                    '}';
        }
    }
}
