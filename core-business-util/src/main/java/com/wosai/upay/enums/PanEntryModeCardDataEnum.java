package com.wosai.upay.enums;


import com.wosai.data.util.StringUtil;

/**
 * @version 1.0
 * @author: yuhai
 * @program: core-business
 * @className PanEntryModelCardDataEnum
 * @description:
 * @create: 2025-06-13 13:52
 **/
public enum PanEntryModeCardDataEnum {

    UNSPECIFIED("00", "未指定输入模式"),
    MANUAL_KEY_IN("01", "手动输入卡号"),
    MAGNETIC_STRIPE("02", "刷磁条卡"),
    CHIP("05", "芯片卡（接触式）"),
    CHIP_CONTACTLESS("07", "芯片卡非接触式（感应式）"),
    FALLBACK_CHIP_TO_MAG_STRIPE_80("80", "芯片卡降级到磁条卡（由于芯片读取失败）"),
    FALLBACK_CHIP_TO_MAG_STRIPE_90("90", "芯片卡降级到磁条卡，磁条读入信息可靠，第二磁道信息必须出现"),
    SWIPE_CONTACTLESS_MAGSTRIPE("91", "非接触式刷卡输入 - 磁条 Paypass");

    private final String code;
    private final String description;

    PanEntryModeCardDataEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过代码获取枚举实例
     * @param code 两位数字代码
     * @return 对应的枚举实例，找不到时抛出IllegalArgumentException
     */
    public static PanEntryModeCardDataEnum fromCode(String code) {
        if (StringUtil.empty(code)) {
            return null;
        }

        if (code.length() == 3) {
            code = code.substring(0, 2);
        }

        for (PanEntryModeCardDataEnum mode : values()) {
            if (mode.code.equals(code)) {
                return mode;
            }
        }
        return null;
    }

    /**
     * 获取模式代码
     * @return 两位数字代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取模式描述
     * @return 人类可读的描述信息
     */
    public String getDescription() {
        return description;
    }

    /**
     * 是否手动键入
     * @return
     */
    public boolean isManuallyKeyed() {
        return this == MANUAL_KEY_IN;
    }

    /**
     * 是否芯片卡
     * @return
     */
    public boolean isChip() {
        return this == CHIP || this == FALLBACK_CHIP_TO_MAG_STRIPE_80 || this == FALLBACK_CHIP_TO_MAG_STRIPE_90;
    }

    /**
     * 是否芯片卡降级到磁条卡
     * @return
     */
    public boolean isChipFallbackToMagstripe() {
        return this == FALLBACK_CHIP_TO_MAG_STRIPE_80 || this == FALLBACK_CHIP_TO_MAG_STRIPE_90;
    }

    /**
     * 是否非接触式芯片卡
     * @return
     */
    public boolean isChipContactless() {
        return this == CHIP_CONTACTLESS;
    }

    /**
     * 是否磁条卡
     * @return
     */
    public boolean isMagstripe() {
        return this == MAGNETIC_STRIPE;
    }

    /**
     * 是否非接触式磁条卡
     * @return
     */
    public boolean isMagstripeContactless() {
        return this == SWIPE_CONTACTLESS_MAGSTRIPE;
    }


    @Override
    public String toString() {
        return code + " - " + description;
    }
}
