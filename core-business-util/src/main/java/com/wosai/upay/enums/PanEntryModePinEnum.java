package com.wosai.upay.enums;

import com.wosai.data.util.StringUtil;

/**
 * @version 1.0
 * @author: yuhai
 * @program: core-business
 * @className PanEntryModelPinEnum
 * @description:
 * @create: 2025-06-13 13:58
 **/
public enum PanEntryModePinEnum {

    UNSPECIFIED("0", "未指定 PIN 输入能力"),
    PIN_ENTRY_CAPABLE("1", "终端具有 PIN 输入功能"),
    NO_PIN_ENTRY("2", "终端不具有 PIN 输入功能"),
    OFFLINE_PIN_CVV2_COMBINATION("3", "终端具有离线 PIN 输入能力");

    private final String code;
    private final String description;

    PanEntryModePinEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过字符代码获取枚举实例
     * @param code 字符代码（'0' - '3'）
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 无效代码时抛出
     */
    public static PanEntryModePinEnum fromCode(String code) {
        if (StringUtil.empty(code)) {
            return null;
        }

        if (code.length() == 3) {
            code = code.substring(code.length() - 1);
        }

        for (PanEntryModePinEnum capability : values()) {
            if (capability.code.equals(code)) {
                return capability;
            }
        }
        return null;
    }

    /**
     * 获取代码字符
     * @return 单字符代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取完整能力描述
     * @return 原始描述文本
     */
    public String getDescription() {
        return description;
    }

    /**
     * 是否支持PIN输入
     * @return true表示支持PIN输入
     */
    public boolean supportsPinEntry() {
        return this == PIN_ENTRY_CAPABLE
                || this == OFFLINE_PIN_CVV2_COMBINATION;
    }


    /**
     * 是否支持在线PIN验证
     * @return true表示支持PIN输入
     */
    public boolean supportsOnlinePin() {
        return this == PIN_ENTRY_CAPABLE;
    }

    /**
     * 是否支持离线PIN验证
     * @return true表示支持离线PIN
     */
    public boolean supportsOfflinePin() {
        return this == NO_PIN_ENTRY || this == OFFLINE_PIN_CVV2_COMBINATION;
    }

    @Override
    public String toString() {
        return code + " - " + description;
    }
}
