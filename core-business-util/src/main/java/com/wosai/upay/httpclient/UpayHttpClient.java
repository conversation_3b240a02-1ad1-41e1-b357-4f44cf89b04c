package com.wosai.upay.httpclient;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.util.Digest;
import com.wosai.upay.util.WebUtil;

public class UpayHttpClient {

    private RestTemplate restTemplate;
    private ObjectMapper objectMapper;
    
    @Autowired
    public UpayHttpClient(RestTemplate restTemplate,
                          ObjectMapper objectMapper) {

        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    public Map<String, Object> call(String principal, String secretKey, String url, Map<String, Object> body) throws IOException {
        byte[] raw = objectMapper.writeValueAsBytes(body);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(raw);
        baos.write(secretKey.getBytes());
        String signature = Digest.md5(baos.toByteArray());
        
        try {
            return restTemplate.execute(url, HttpMethod.POST,
                                        new SignRequestCallback(raw, principal+" "+signature),
                                        new JsonMapResponseExtractor(objectMapper));
        }catch(RestClientException ex) {
            throw new IOException("rest client i/o error", ex);
        }
    }

    public static class SignRequestCallback implements RequestCallback {
        
        private byte[] raw;
        private String sign;
        public SignRequestCallback(byte[] raw, String sign) {
            this.raw = raw;
            this.sign = sign;
        }

        @Override
        public void doWithRequest(ClientHttpRequest request) throws IOException {
            request.getHeaders().set("Authorization", sign);
            request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            request.getHeaders().setContentLength(raw.length);
            request.getBody().write(raw);
            request.getBody().flush();
        }
        
    }
    public static class JsonMapResponseExtractor implements ResponseExtractor<Map<String,Object>> {

        private ObjectMapper objectMapper;
        public JsonMapResponseExtractor(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public Map<String, Object> extractData(ClientHttpResponse response)
                throws IOException {

            return (Map<String, Object>)objectMapper.readValue(response.getBody(), Map.class);
        }
        
    }
    
    public static void main(String[] args) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        SslClientHttpRequestFactory requestFactory = new SslClientHttpRequestFactory();
        requestFactory.setSslContext(WebUtil.getAllowAllCertsSSLContext());
        requestFactory.setHostNameVerifier(WebUtil.getAllowAllHostnameVerifier());
        
        RestTemplate restTemplate = new RestTemplate(requestFactory);

        UpayHttpClient client = new UpayHttpClient(restTemplate, objectMapper);
        Map<String, Object> body = new LinkedHashMap<>();
        body.put("terminal_sn", "20361779759");
        body.put("sn", "7895259247309646");
        Map<String, Object> response = client.call("20361779759", "", "https://api.shouqianba.com/upay/v2/query", body);
        System.out.println("response " + response);
    }
}
