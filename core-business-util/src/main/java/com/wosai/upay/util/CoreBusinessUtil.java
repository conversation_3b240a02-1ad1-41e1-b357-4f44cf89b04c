package com.wosai.upay.util;

import java.util.Map;

public class CoreBusinessUtil {

    public static void putNotNullPropertiesToDestMap(Map source, String[] keys, Map dest) {
        for (String key : keys) {
            putNotNullPropertiesToDestMap(source, key, dest, key);
        }
    }

    public static void putNotNullPropertiesToDestMap(Map source, String key, Map dest) {
        putNotNullPropertiesToDestMap(source, key, dest, key);
    }

    public static void putNotNullPropertiesToDestMap(Map source, String key, Map dest, String destKey) {
        if (source.get(key) != null) {
            dest.put(destKey, source.get(key));
        }
    }

}
