package com.wosai.upay.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static final String FORMATTER_DATE_INT = "yyyyMMdd";

    public static String formatDate(Date date, String formatter) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(formatter);
        return sdf.format(date);
    }
    
    public static Date parseDate(String source, String formatter){
    	SimpleDateFormat sdf = new SimpleDateFormat(formatter);
        try {
			return sdf.parse(source);
		} catch (ParseException e) {
            LOGGER.error(e.getMessage(),e);
		}
        return null;
    }

}
