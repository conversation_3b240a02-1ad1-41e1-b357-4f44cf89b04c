package com.wosai.upay.util;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Digest {
    public static String md5(byte[] input) throws IOException {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            
            return new String(Hex.encode(digest.digest(input)));
        }catch(NoSuchAlgorithmException ex) {
            throw new IOException(ex);
        }
        
    }

    public static String md5(InputStream input) throws IOException {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[1024];
            int length;
            while( (length = input.read(buffer)) > 0) {
                digest.update(buffer, 0, length);
            }
            return new String(Hex.encode(digest.digest()));
        }catch(NoSuchAlgorithmException ex){
            throw new IOException(ex);
        }
    }
}
