package com.wosai.upay.util;

import com.wosai.data.util.StringUtil;

/**
 * Created by xuchmao on 17/6/19.
 */
public class DistrictUtil {
    /**
     * 从省市区名称中提取行政单位之外的有效信息
     * @param name
     * @return
     */
    public static String getShortName(String name){
        if(StringUtil.empty(name)){
            return name;
        }
        if(name.endsWith("内蒙古自治区")){
            return name.replace("自治区","");
        }
        if(name.endsWith("维吾尔自治区")){
            return name.replace("维吾尔自治区","");
        }
        if(name.endsWith("壮族自治区")){
            return name.replace("壮族自治区","");
        }
        if(name.endsWith("壮族自治区")){
            return name.replace("壮族自治区","");
        }
        if(name.endsWith("回族自治区")){
            return name.replace("回族自治区","");
        }
        if(name.endsWith("回族自治区")){
            return name.replace("回族自治区","");
        }
        if(name.endsWith("自治区")){
            return name.replace("自治区","");
        }
        if(name.endsWith("自治州")){
            return name.replace("自治州","");
        }
        if(name.endsWith("自治县")){
            return name.replace("自治县","");
        }
        if(name.endsWith("省")){
            return name.replace("省", "");
        }
        if(name.endsWith("市")){
            return name.replace("市", "");
        }
        if(name.endsWith("地区")){
            return name.replace("地区","");
        }
        if(name.endsWith("区")){
            return name.replace("区","");
        }
        if(name.endsWith("县")){
            return name.replace("县","");
        }
        return name;
    }
}
