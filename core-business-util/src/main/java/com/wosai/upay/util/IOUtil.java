package com.wosai.upay.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class IOUtil {

    public static byte[] getBytesFromInputStream(InputStream input) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while( (length = input.read(buffer)) > 0) {
            baos.write(buffer,  0, length);
        }
        return baos.toByteArray();
    }

    public static void copyStream(InputStream input, OutputStream output) throws IOException {
        byte[] buffer = new byte[1024];
        int length;
        while( (length = input.read(buffer)) > 0) {
            output.write(buffer,  0, length);
        }
    }
}
