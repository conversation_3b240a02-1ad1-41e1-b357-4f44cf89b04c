package com.wosai.upay.util;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.sql.Timestamp;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;
import java.util.TimeZone;

public class DateTimeUtilTest {

    private static Long now;

    private static LocalDateTime nowDateTime;

    private Calendar calendar = Calendar.getInstance();

    @Before
    public void setUp() {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        now = System.currentTimeMillis();
        nowDateTime = Instant.ofEpochMilli(now).atZone(ZoneOffset.systemDefault()).toLocalDateTime();
    }


    private static final DateTimeFormatter fmtHHmm = DateTimeFormatter.ofPattern("HH:mm");

    private static final DateTimeFormatter fmtHHmmss = DateTimeFormatter.ofPattern("HH:mm:ss");

    private static final DateTimeFormatter fmtyyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final DateTimeFormatter fmtyyyyMMddDot = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    private static final DateTimeFormatter fmtyyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    //今天开始时间的毫秒数
    private long toDayStartMilliSecond() {
        return nowDateTime.toLocalDate().atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
    }

    @Test
    public void testGetDate() {
        Assert.assertEquals(toDayStartMilliSecond(), DateTimeUtil.getDate(now));
    }

    @Test
    public void testGetOneDayStart() {
        Assert.assertEquals(toDayStartMilliSecond(), DateTimeUtil.getOneDayStart(now));
    }

    @Test
    public void testGetOneDayEnd() {
        Assert.assertEquals(toDayStartMilliSecond() + DateTimeUtil.ONE_DAY_MILLIS - 941, DateTimeUtil.getOneDayEnd(now));
    }

    @Test
    public void testGetHour() {
        Assert.assertEquals(nowDateTime.getHour(), DateTimeUtil.getHour(now));
    }

    @Test
    public void testGetMonthString() {
        Assert.assertEquals(String.format("%d月", nowDateTime.toLocalDate().getMonth().ordinal() + 1), DateTimeUtil.getMonthString(now));
    }

    @Test
    public void testGetMonth() {
        long now = System.currentTimeMillis();
        Assert.assertEquals(nowDateTime.toLocalDate().getMonth().ordinal() + 1, DateTimeUtil.getMonth(now));

    }

    @Test
    public void testGetYear() {
        Assert.assertEquals(nowDateTime.toLocalDate().getYear(), DateTimeUtil.getYear(now));
    }

    @Test
    public void testGetDayOfMonthString() {
        Assert.assertEquals(nowDateTime.toLocalDate().getDayOfMonth() + "日", DateTimeUtil.getDayOfMonthString(now));
    }

    @Test
    public void testGetDayOfMonth() {
        Assert.assertEquals(nowDateTime.toLocalDate().getDayOfMonth(), DateTimeUtil.getDayOfMonth(now));
    }

    @Test
    public void testGetDayOfWeek() {
        Assert.assertEquals(nowDateTime.toLocalDate().getDayOfWeek().ordinal() + 1, DateTimeUtil.getDayOfWeek(now));
        Assert.assertEquals(7, DateTimeUtil.getDayOfWeek(1593878400000L));
    }


    @Test
    public void testGetDayOfWeekString() {
        Assert.assertEquals(DateTimeUtil.weekdays[(nowDateTime.toLocalDate().getDayOfWeek().ordinal() + 1) % 7], DateTimeUtil.getDayOfWeekString(now));
    }


    @Test
    public void testGetTimeOfDayString() {
        Assert.assertEquals(nowDateTime.format(fmtHHmm), DateTimeUtil.getTimeOfDayString(now));
    }

    @Test
    public void testGetFullTimeString() {
        Assert.assertEquals("今天 " + nowDateTime.format(fmtHHmm), DateTimeUtil.getFullTimeString(now));
        LocalDateTime yesterday = nowDateTime.plusDays(-1);
        Assert.assertEquals(yesterday.getYear() + "." + yesterday.getMonthValue() + "." + yesterday.getDayOfMonth() + "  " + yesterday.format(fmtHHmm),
                DateTimeUtil.getFullTimeString(now - DateTimeUtil.ONE_DAY_MILLIS));
    }


    @Test
    public void testGetSimpleTimeString() {
        Assert.assertEquals(nowDateTime.getYear() + "年" + nowDateTime.getMonthValue() + "月" + nowDateTime.getDayOfMonth() + "日", DateTimeUtil.getSimpleTimeString(now));
    }

    @Test
    public void testGetBirthdayString() {
        Assert.assertEquals(nowDateTime.getMonthValue() + "月" + nowDateTime.getDayOfMonth() + "日", DateTimeUtil.getBirthdayString(now));
    }

    @Test
    public void testGetDigitalTimeString() {
        Assert.assertEquals(nowDateTime.getYear() + "." + nowDateTime.getMonthValue() + "." + nowDateTime.getDayOfMonth() + "  " + nowDateTime.format(fmtHHmmss),
                DateTimeUtil.getDigitalTimeString(now));
    }

    @Test
    public void testGetDateString() {
        Assert.assertEquals(nowDateTime.format(fmtyyyyMMdd),
                DateTimeUtil.getDateString(now));
    }

    @Test
    public void testGetTimeString() {
        Assert.assertEquals(nowDateTime.format(fmtyyyyMMddHHmmss),
                DateTimeUtil.getTimeString(now));
    }

    @Test
    public void testGetSimpleDigitalTimeString() {
        Assert.assertEquals(nowDateTime.getYear() + "." + nowDateTime.getMonthValue() + "." + nowDateTime.getDayOfMonth(),
                DateTimeUtil.getSimpleDigitalTimeString(now));
    }

    @Test
    public void testGetDaysBetween() {
        int between = new Random().nextInt(600);
        Assert.assertEquals(between,
                DateTimeUtil.getDaysBetween(now - DateTimeUtil.ONE_DAY_MILLIS * between, now));
    }

    @Test
    public void testGetTodayStart() {
        Assert.assertEquals(toDayStartMilliSecond(),
                DateTimeUtil.getTodayStart().toInstant().getEpochSecond() * 1000);
    }


    @Test
    public void testGetYesterdayStart() {
        Assert.assertEquals(toDayStartMilliSecond() - DateTimeUtil.ONE_DAY_MILLIS,
                DateTimeUtil.getYesterdayStart().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testGetTomorrowStart() {
        Assert.assertEquals(toDayStartMilliSecond() + DateTimeUtil.ONE_DAY_MILLIS,
                DateTimeUtil.getTomorrowStart().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testGetThisWeekStart() {
        Assert.assertEquals(toDayStartMilliSecond() - nowDateTime.toLocalDate().getDayOfWeek().ordinal() * DateTimeUtil.ONE_DAY_MILLIS,
                DateTimeUtil.getThisWeekStart().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testGetThisMonthStart() {
        Assert.assertEquals(nowDateTime.toLocalDate().withDayOfMonth(1).atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000,
                DateTimeUtil.getThisMonthStart().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testGetThisQuarterStart() {
        LocalDateTime time = nowDateTime;
        int month = time.toLocalDate().getMonthValue();
        time = time.plusMonths(-1 * (month % 3 == 0 ? 3 : month % 3) + 1);
        Assert.assertEquals(time.toLocalDate().withDayOfMonth(1).atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000,
                DateTimeUtil.getThisQuarterStart().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testGetLastQuarterStart() {
        LocalDateTime time = nowDateTime.plusMonths(-3);
        int month = time.toLocalDate().getMonthValue();
        time = time.plusMonths(-1 * (month % 3 == 0 ? 3 : month % 3) + 1);
        Assert.assertEquals(time.toLocalDate().withDayOfMonth(1).atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000,
                DateTimeUtil.getLastQuarterStart().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testGetLastQuarterEnd() {
        LocalDateTime time = nowDateTime;
        int month = time.toLocalDate().getMonthValue();
        time = time.plusMonths(-1 * (month % 3 == 0 ? 3 : month % 3) + 1);
        Assert.assertEquals(time.toLocalDate().withDayOfMonth(1).plusDays(-1).atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond() * 1000,
                DateTimeUtil.getLastQuarterEnd().toInstant().getEpochSecond() * 1000);
    }

    @Test
    public void testFormatDate() {
        Assert.assertEquals(nowDateTime.format(fmtyyyyMMddDot),
                DateTimeUtil.formatDate(calendar));
    }

    @Test
    public void testGetToday() {
        Assert.assertEquals(nowDateTime.format(fmtyyyyMMddDot),
                DateTimeUtil.getToday());
    }

    @Test
    public void testAddMonth() {
        int month = new Random().nextInt(50);
        Assert.assertEquals(nowDateTime.plusMonths(month).atZone(ZoneId.systemDefault()).toEpochSecond(),
                DateTimeUtil.addMonth(new Date(now), month).getTime() / 1000);
    }


    @Test
    public void testStringToLong() throws ParseException {
        Assert.assertEquals(toDayStartMilliSecond(),
                DateTimeUtil.stringToLong());
    }

    @Test
    public void testValueOf() throws Exception {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String format = nowDateTime.format(fmt);
        Assert.assertEquals(now / 1000,
                DateTimeUtil.valueOf("\"" + format + "\"").getTime() / 1000);

        fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        format = nowDateTime.format(fmt);
        Assert.assertEquals(toDayStartMilliSecond() / 1000,
                DateTimeUtil.valueOf(format).getTime() / 1000);

        Assert.assertEquals(1577889005,
                DateTimeUtil.valueOf("2020-01-01T22:30:05+08:00").getTime() / 1000);

        Assert.assertNull(DateTimeUtil.valueOf(""));
        Assert.assertNull(DateTimeUtil.valueOf(null));
    }


    @Test
    public void testParseDate() throws Exception {
        Object obj = 1594011017325L;
        Assert.assertEquals(Long.parseLong(obj.toString()),
                DateTimeUtil.parseDate(obj).getTime());

        obj = Integer.parseInt("15940110");
        Assert.assertEquals(Long.parseLong(obj.toString()),
                DateTimeUtil.parseDate(obj).getTime());


        obj = "1594011017325";
        Assert.assertEquals(Long.parseLong(obj.toString()),
                DateTimeUtil.parseDate(obj).getTime());

        obj = new Date(1594011017325L);
        Assert.assertEquals(((Date) obj).getTime(),
                DateTimeUtil.parseDate(obj).getTime());

        obj = new Timestamp(1594011017325L);
        Assert.assertEquals(((Timestamp) obj).getTime(),
                DateTimeUtil.parseDate(obj).getTime());

        Assert.assertNull(DateTimeUtil.parseDate(""));
        Assert.assertNull(DateTimeUtil.parseDate(null));
    }

    @Test
    public void testGetDayStart() {
        Assert.assertEquals(toDayStartMilliSecond(),
                DateTimeUtil.getDayStart(new Date(now)).getTime());
    }


    @Test
    public void testGetNextDayStart() {
        Assert.assertEquals(toDayStartMilliSecond() + DateTimeUtil.ONE_DAY_MILLIS,
                DateTimeUtil.getNextDayStart(new Date(now)).getTime());
    }


    @Test
    public void testGetMonthStart() {
        Assert.assertEquals(nowDateTime.toLocalDate().atStartOfDay().withDayOfMonth(1).atZone(ZoneId.systemDefault()).toEpochSecond(),
                DateTimeUtil.getMonthStart(new Date(now)).getTime() / 1000);
    }


    @Test
    public void testGetNextMonthStart() {
        Assert.assertEquals(nowDateTime.toLocalDate().atStartOfDay().withDayOfMonth(1).plusMonths(1).atZone(ZoneId.systemDefault()).toEpochSecond(),
                DateTimeUtil.getNextMonthStart(new Date(now)).getTime() / 1000);
    }

}
