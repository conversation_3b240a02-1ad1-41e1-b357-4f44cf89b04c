package com.wosai.upay.core;

import com.wosai.database.instrumentation.springboot.v2.EnableDataSourceTranslate;
import com.wosai.oss.configuration.OssConfiguration;
import com.wosai.web.rpc.EnableJsonRpc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;

@EnableDataSourceTranslate
@EnableJsonRpc
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class,
        OssConfiguration.class
})
public class CoreBusinessApplication {

    public static void main(String[] args) {
        SpringApplication.run(CoreBusinessApplication.class, args);
    }
}
