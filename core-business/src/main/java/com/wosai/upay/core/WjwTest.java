package com.wosai.upay.core;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.MerchantConfig;
import okhttp3.Credentials;
import org.apache.commons.beanutils.BeanUtils;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/21.
 */
public class WjwTest {

    public static void main(String[] args) {
        System.out.println(Credentials.basic("elastic", "Wosai2017"));
        Map merchantConfig = null;
//        System.out.println(BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false));
        System.out.println(BeanUtil.getPropString(merchantConfig, MerchantConfig.B2C_FORMAL));


        String curl = "curl -v -X --header 'Content-Type: application/json' --location --request POST 'https://en97upxuj0kfp.x.pipedream.net/rpc/businessLog' -d @- <<EOF \n" +
                "'{\n" +
                "   \"method\":\"sendBusinessLog\",\n" +
                "   \"params\":[\n" +
                "       {\n" +
                "      \"logType\": {\n" +
                "        \"functionCode\": \"20220427\",\n" +
                "        \"objectCode\": \"merchant\",\n" +
                "        \"tableName\": \"profit_sharing_fix\",\n" +
                "        \"remark\": \"因拉卡拉分账失败，补扣分账服务费（笔数$count笔，金额￥$amount元），实际补扣金额金额和笔数以商户余额对账单为准\"\n" +
                "      },\n" +
                "      \"platform\": \"SP\",\n" +
                "      \"objectId\": \"$merchantId\",\n" +
                "      \"before\": {\n" +
                "        \"content\":\"\"\n" +
                "      },\n" +
                "      \"after\": {\n" +
                "       \"content\":\"无\"\n" +
                "      },\n" +
                "      \"userId\": \"系统定时触发\",\n" +
                "      \"userName\": \"邬建伟\",\n" +
                "      \"opId\": null,\n" +
                "      \"objectSn\": null,\n" +
                "      \"includeKey\": null\n" +
                "      \n" +
                "    }\n" +
                "       ],\n" +
                "    \"jsonrpc\": \"2.0\",\n" +
                "    \"id\": 0\n" +
                "}'\n" +
                "\n" +
                "EOF" +
                "";

        


    }
}
