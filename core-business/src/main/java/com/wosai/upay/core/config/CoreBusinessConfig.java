package com.wosai.upay.core.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.googlecode.jsonrpc4j.DefaultErrorResolver;
import com.googlecode.jsonrpc4j.MultipleErrorResolver;
import com.wosai.upay.common.helper.CommonServicePostProcessor;
import com.wosai.upay.common.helper.UpayMethodValidationPostProcessor;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.common.CoreBusinessValidated;
import com.wosai.upay.core.helper.ExceptionBaseErrorResolver;
import com.wosai.upay.core.helper.UpayServiceMethodInterceptor;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.helper.UpayCoreServiceAnnotation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping;

import java.util.Map;


/**
 * Core Business Configuration
 */
@Configuration
@EnableAspectJAutoProxy
public class CoreBusinessConfig {

    @Bean
    public ApolloConfigurationCenterUtil apolloConfigurationCenterUtil() {
        return new ApolloConfigurationCenterUtil();
    }

    @Bean
    public UpayMethodValidationPostProcessor upayMethodValidationPostProcessor() {
        UpayMethodValidationPostProcessor processor = new UpayMethodValidationPostProcessor();
        processor.setValidatedAnnotationType(CoreBusinessValidated.class);
        return processor;
    }

    @Bean
    public UpayServiceMethodInterceptor serviceMethodInterceptor() {
        return new UpayServiceMethodInterceptor();
    }

    @Bean
    public CommonServicePostProcessor commonServicePostProcessor() {
        CommonServicePostProcessor processor = new CommonServicePostProcessor();
        processor.setAdvice(serviceMethodInterceptor());
        processor.setAnnotationTypeClass(UpayCoreServiceAnnotation.class.getName());
        return processor;
    }

    @Bean
    public BeanNameUrlHandlerMapping beanNameUrlHandlerMapping() {
        return new BeanNameUrlHandlerMapping();
    }

    @Bean
    public MultipleErrorResolver rpcErrorResolver() {
        return new MultipleErrorResolver(
                ExceptionBaseErrorResolver.INSTANCE,
                DefaultErrorResolver.INSTANCE
        );
    }

    @Bean
    public SpringContextHolder springContextHolder() {
        return new SpringContextHolder();
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
        // map类型为null时也要进行返回
        objectMapper.configOverride(Map.class).setInclude(JsonInclude.Value.construct(JsonInclude.Include.NON_NULL, JsonInclude.Include.ALWAYS));
        return objectMapper;
    }

}
