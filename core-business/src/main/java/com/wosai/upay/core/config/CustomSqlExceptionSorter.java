package com.wosai.upay.core.config;

import com.alibaba.druid.pool.vendor.MySqlExceptionSorter;
import com.mysql.cj.jdbc.exceptions.MySQLTimeoutException;

import java.sql.SQLException;

public class CustomSqlExceptionSorter extends MySqlExceptionSorter {
    @Override
    public boolean isExceptionFatal(SQLException e) {
        if (e instanceof MySQLTimeoutException) {
            return true;
        }
        return super.isExceptionFatal(e);
    }
}
