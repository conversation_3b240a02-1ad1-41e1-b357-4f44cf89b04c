package com.wosai.upay.core.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据源配置类
 */
@Configuration
@EnableTransactionManagement
public class DatasourceConfig {

    @Value("${db.maxActive}")
    private int maxActive;

    @Value("${db.minIdle}")
    private int minIdle;

    @Value("${master.jdbc.driverClassName}")
    private String masterDriverClassName;

    @Value("${master.jdbc.url}")
    private String masterJdbcUrl;

    @Value("${master.jdbc.connection.eviction.interval}")
    private long masterEvictionInterval;

    @Bean
    public Vault vault() throws VaultException {
        return Vault.autoload();
    }

    @Bean
    public List<String> initSqls() {
        List<String> initSqls = new ArrayList<>();
        initSqls.add("set names utf8mb4");
        return initSqls;
    }

    @Bean("masterDatasource")
    public DataSource masterDatasource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(masterDriverClassName);
        dataSource.setUrl(masterJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(masterEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("slaveDatasource")
    public DataSource slaveDatasource(
        @Value("${slave.jdbc.driverClassName}") String slaveDriverClassName,
        @Value("${slave.jdbc.url}") String slaveJdbcUrl,
        @Value("${slave.jdbc.connection.eviction.interval}") long slaveEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(slaveDriverClassName);
        dataSource.setUrl(slaveJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(slaveEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("messageDatasource")
    public DataSource messageDatasource(
        @Value("${jdbc_message.driverClassName}") String messageDriverClassName,
        @Value("${jdbc_message.url}") String messageJdbcUrl,
        @Value("${jdbc_message.connection.eviction.interval}") long messageEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(messageDriverClassName);
        dataSource.setUrl(messageJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(messageEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("logDatasource")
    public DataSource logDatasource(
        @Value("${jdbc_log.driverClassName}") String logDriverClassName,
        @Value("${jdbc_log.url}") String logJdbcUrl,
        @Value("${jdbc_log.connection.eviction.interval}") long logEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(logDriverClassName);
        dataSource.setUrl(logJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(logEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("masterUserDatasource")
    public DataSource masterUserDatasource(
        @Value("${master.jdbc_user.driverClassName}") String masterUserDriverClassName,
        @Value("${master.jdbc_user.url}") String masterUserJdbcUrl,
        @Value("${master.jdbc_user.connection.eviction.interval}") long masterUserEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(masterUserDriverClassName);
        dataSource.setUrl(masterUserJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(masterUserEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("slaveUserDatasource")
    public DataSource slaveUserDatasource(
        @Value("${slave.jdbc_user.driverClassName}") String slaveUserDriverClassName,
        @Value("${slave.jdbc_user.url}") String slaveUserJdbcUrl,
        @Value("${slave.jdbc_user.connection.eviction.interval}") long slaveUserEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(slaveUserDriverClassName);
        dataSource.setUrl(slaveUserJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(slaveUserEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("commonLoginDatasource")
    public DataSource commonLoginDatasource(
        @Value("${common_login.driverClassName}") String commonLoginDriverClassName,
        @Value("${common_login.url}") String commonLoginJdbcUrl,
        @Value("${common_login.connection.eviction.interval}") long commonLoginEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(commonLoginDriverClassName);
        dataSource.setUrl(commonLoginJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(commonLoginEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("masterBankInfoDatasource")
    public DataSource masterBankInfoDatasource(
        @Value("${master.jdbc.bank-info.driverClassName}") String masterBankInfoDriverClassName,
        @Value("${master.jdbc.bank-info.url}") String masterBankInfoJdbcUrl,
        @Value("${master.jdbc.bank-info.connection.eviction.interval}") long masterBankInfoEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(masterBankInfoDriverClassName);
        dataSource.setUrl(masterBankInfoJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(masterBankInfoEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }

    @Bean("slaveBankInfoDatasource")
    public DataSource slaveBankInfoDatasource(
        @Value("${slave.jdbc.bank-info.driverClassName}") String slaveBankInfoDriverClassName,
        @Value("${slave.jdbc.bank-info.url}") String slaveBankInfoJdbcUrl,
        @Value("${slave.jdbc.bank-info.connection.eviction.interval}") long slaveBankInfoEvictionInterval
    ) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setConnectionInitSqls(initSqls());
        dataSource.setMaxActive(maxActive);
        dataSource.setMinIdle(minIdle);
        dataSource.setDriverClassName(slaveBankInfoDriverClassName);
        dataSource.setUrl(slaveBankInfoJdbcUrl);
        dataSource.setTimeBetweenEvictionRunsMillis(slaveBankInfoEvictionInterval);
        dataSource.setValidationQuery("SELECT 1");
        return dataSource;
    }
}