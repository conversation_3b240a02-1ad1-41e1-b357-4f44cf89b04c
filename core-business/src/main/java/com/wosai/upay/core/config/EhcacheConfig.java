package com.wosai.upay.core.config;

import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.config.CacheConfiguration;
import net.sf.ehcache.config.DiskStoreConfiguration;
import net.sf.ehcache.config.MemoryUnit;
import net.sf.ehcache.store.MemoryStoreEvictionPolicy;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Ehcache configuration class
 */
@Configuration
@EnableCaching
public class EhcacheConfig {

    /**
     * Cache manager bean
     */
    @Bean
    public org.springframework.cache.CacheManager cacheManager() {
        return new EhCacheCacheManager(ehCacheManager());
    }

    /**
     * EhCache manager bean
     */
    @Bean
    public CacheManager ehCacheManager() {
        net.sf.ehcache.config.Configuration config = new net.sf.ehcache.config.Configuration();
        config.setUpdateCheck(false);

        // Configure disk store
        config.addDiskStore(new DiskStoreConfiguration().path("java.io.tmpdir"));

        // Create cache manager
        CacheManager manager = CacheManager.create(config);

        // Add all caches
        manager.addCache(createRsaKeyDataCache());
        manager.addCache(createGetMerchantSnByWosaiStoreIdOrTerminalSnCache());
        manager.addCache(createIndustriesCache());
        manager.addCache(createLevel1IndustriesCache());
        manager.addCache(createLevelsIndustriesCache());
        manager.addCache(createIndustriesNameMapCache());
        manager.addCache(createIndustriesNameMapFromBankInfoServiceCache());
        manager.addCache(createSystemConfigContentCache());
        manager.addCache(createGetVendorAppMinimalInfoByIdCache());
        manager.addCache(createGetVendorAppMinimalInfoByAppIdCache());
        manager.addCache(createGetVendorMinimalInfoByIdCache());
        manager.addCache(createGetVendorMinimalInfoBySnCache());
        manager.addCache(createGetSolicitorMinimalInfoByIdCache());
        manager.addCache(createGetSolicitorMinimalInfoBySnCache());
        manager.addCache(createGetVendorAppAppIdsCache());
        manager.addCache(createGetAllProviderAbilitysCache());
        manager.addCache(createGetAllMetaCache());
        manager.addCache(createGetAllMetaPaywaysCache());
        manager.addCache(createGetAllMetaProductFlagsCache());
        manager.addCache(createGetAllMetaProvidersCache());
        manager.addCache(createGetMetaProviderCache());
        manager.addCache(createGetMetaAcquirerCache());
        manager.addCache(createGetAllMetaAcquirersCache());
        manager.addCache(createGetAllMetaBizModelCache());
        manager.addCache(createGetAllMetaPayPathCache());
        manager.addCache(createGetStoreProvinceBySnCache());
        manager.addCache(createGetTradeParamsKey());

        return manager;
    }

    private Cache createRsaKeyDataCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("RsaKeyData")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(28800)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(5)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetMerchantSnByWosaiStoreIdOrTerminalSnCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getMerchantSnByWosaiStoreIdOrTerminalSn")
                .timeToLiveSeconds(28800)
                .eternal(false)
                .overflowToDisk(false)
                .maxBytesLocalHeap(300, MemoryUnit.MEGABYTES)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(5)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createIndustriesCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("Industries")
                .maxEntriesLocalHeap(100)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createLevel1IndustriesCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("Level1Industries")
                .maxEntriesLocalHeap(100)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createLevelsIndustriesCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("LevelsIndustries")
                .maxEntriesLocalHeap(100)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createIndustriesNameMapCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("industriesNameMap")
                .maxEntriesLocalHeap(100)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createIndustriesNameMapFromBankInfoServiceCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("industriesNameMapFromBankInfoService")
                .maxEntriesLocalHeap(100)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createSystemConfigContentCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("systemConfigContent")
                .maxEntriesLocalHeap(200)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetVendorAppMinimalInfoByIdCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getVendorAppMinimalInfoById")
                .maxEntriesLocalHeap(2000)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetVendorAppMinimalInfoByAppIdCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getVendorAppMinimalInfoByAppId")
                .maxEntriesLocalHeap(2000)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetVendorMinimalInfoByIdCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getVendorMinimalInfoById")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetVendorMinimalInfoBySnCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getVendorMinimalInfoBySn")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetSolicitorMinimalInfoByIdCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getSolicitorMinimalInfoById")
                .maxEntriesLocalHeap(500)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetSolicitorMinimalInfoBySnCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getSolicitorMinimalInfoBySn")
                .maxEntriesLocalHeap(500)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetVendorAppAppIdsCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getVendorAppAppIds")
                .maxEntriesLocalHeap(500)
                .timeToLiveSeconds(86400)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllProviderAbilitysCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllProviderAbilitys")
                .maxEntriesLocalHeap(500)
                .timeToLiveSeconds(180)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMeta")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaPaywaysCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMetaPayways")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaProductFlagsCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMetaProductFlags")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaProvidersCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMetaProviders")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetMetaProviderCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getMetaProvider")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetMetaAcquirerCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getMetaAcquirer")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaAcquirersCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMetaAcquirers")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaBizModelCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMetaBizModel")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetAllMetaPayPathCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getAllMetaPayPath")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(1)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetStoreProvinceBySnCache() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getStoreProvinceBySn")
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .maxBytesLocalHeap(50, MemoryUnit.MEGABYTES)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(5)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }

    private Cache createGetTradeParamsKey() {
        CacheConfiguration config = new CacheConfiguration()
                .name("getTradeParamsKey")
                .maxEntriesLocalHeap(1000)
                .timeToLiveSeconds(600)
                .eternal(false)
                .overflowToDisk(false)
                .diskPersistent(false)
                .diskExpiryThreadIntervalSeconds(5)
                .memoryStoreEvictionPolicy(MemoryStoreEvictionPolicy.LRU);
        return new Cache(config);
    }
}
