package com.wosai.upay.core.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.core.crypto.dao.CryptoAwareMapDao;
import com.wosai.data.dao.common.TimedDaoBase;
import com.wosai.data.dao.jdbc.JdbcDaoBase;
import com.wosai.oss.OssUrlEncrypt;
import com.wosai.oss.crypto.CryptoAuthDao;
import com.wosai.upay.common.dao.JsonBlobAwareDao;
import com.wosai.upay.core.dao.FakeRequestDao;
import com.wosai.upay.core.datasource.DataSourceConstant;
import com.wosai.upay.core.datasource.DynamicDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.ReflectionUtils;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * JDBC DAO 配置类
 * <p>
 * 说明：使用JsonBlobAwareDao且不是return new JsonBlobAwareDao()时需要手工注入objectMapper，参考merchantConfigDao实现
 */
@Configuration
public class JdbcDaoConfig {

    @Autowired
    @Qualifier("masterDatasource")
    private DataSource masterDatasource;

    @Autowired
    @Qualifier("slaveDatasource")
    private DataSource slaveDatasource;

    @Autowired
    @Qualifier("messageDatasource")
    private DataSource messageDatasource;

    @Autowired
    @Qualifier("logDatasource")
    private DataSource logDatasource;

    @Autowired
    @Qualifier("masterUserDatasource")
    private DataSource masterUserDatasource;

    @Autowired
    @Qualifier("slaveUserDatasource")
    private DataSource slaveUserDatasource;

    @Autowired
    @Qualifier("commonLoginDatasource")
    private DataSource commonLoginDatasource;

    @Autowired
    @Qualifier("masterBankInfoDatasource")
    private DataSource masterBankInfoDatasource;

    @Autowired
    @Qualifier("slaveBankInfoDatasource")
    private DataSource slaveBankInfoDatasource;

    @Autowired
    private CryptoClient cryptoClient;

    @Autowired
    private OssUrlEncrypt ossUrlEncrypt;

    @Autowired
    private ObjectMapper objectMapper;

    // 动态数据源配置
    @Bean("dynamicDataSource")
    public DynamicDataSource dynamicDataSource() {
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceConstant.MASTER, masterDatasource);
        targetDataSources.put(DataSourceConstant.SLAVE, slaveDatasource);

        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setTargetDataSources(targetDataSources);
        return dynamicDataSource;
    }

    @Bean("transactionManager")
    public DataSourceTransactionManager transactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dynamicDataSource());
        return transactionManager;
    }

    @Bean("jdbcTemplate")
    public JdbcTemplate jdbcTemplate() {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dynamicDataSource());
        jdbcTemplate.setQueryTimeout(10);
        return jdbcTemplate;
    }

    @Bean("namedParameterJdbcTemplate")
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate() {
        return new NamedParameterJdbcTemplate(jdbcTemplate());
    }

    // 消息数据源配置
    @Bean("messageTransactionManager")
    public DataSourceTransactionManager messageTransactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(messageDatasource);
        return transactionManager;
    }

    @Bean("messageJdbcTemplate")
    public JdbcTemplate messageJdbcTemplate() {
        return new JdbcTemplate(messageDatasource);
    }

    // 日志数据源配置
    @Bean("logTransactionManager")
    public DataSourceTransactionManager logTransactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(logDatasource);
        return transactionManager;
    }

    @Bean("logJdbcTemplate")
    public JdbcTemplate logJdbcTemplate() {
        return new JdbcTemplate(logDatasource);
    }

    // 用户动态数据源配置
    @Bean("dynamicUserDataSource")
    public DynamicDataSource dynamicUserDataSource() {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceConstant.MASTER, masterUserDatasource);
        targetDataSources.put(DataSourceConstant.SLAVE, slaveUserDatasource);
        dynamicDataSource.setTargetDataSources(targetDataSources);
        return dynamicDataSource;
    }

    @Bean("userTransactionManager")
    public DataSourceTransactionManager userTransactionManager() {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(dynamicUserDataSource());
        return transactionManager;
    }

    @Bean("userJdbcTemplate")
    public JdbcTemplate userJdbcTemplate() {
        return new JdbcTemplate(dynamicUserDataSource());
    }

    @Bean("commonLoginJdbcTemplate")
    public JdbcTemplate commonLoginJdbcTemplate() {
        return new JdbcTemplate(commonLoginDatasource);
    }

    // 银行信息动态数据源配置
    @Bean("bankInfoDynamicDataSource")
    public DynamicDataSource bankInfoDynamicDataSource() {
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceConstant.MASTER, masterBankInfoDatasource);
        targetDataSources.put(DataSourceConstant.SLAVE, slaveBankInfoDatasource);

        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setTargetDataSources(targetDataSources);
        return dynamicDataSource;
    }

    @Bean("bankInfoJdbcTemplate")
    public JdbcTemplate bankInfoJdbcTemplate() {
        return new JdbcTemplate(bankInfoDynamicDataSource());
    }

    // DAO 配置
    @Bean("terminalDao")
    public JsonBlobAwareDao terminalDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("geo_location");
        jsonFields.add("extra");

        JdbcDaoBase terminalJdbcDao = new JdbcDaoBase("terminal", jdbcTemplate());
        JdbcDaoBase terminalFakeJdbcDao = new JdbcDaoBase("terminal_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(terminalJdbcDao, terminalFakeJdbcDao);
        TimedDaoBase timedDaoBase = new TimedDaoBase(fakeRequestDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("terminalActivationCodeDao")
    public JsonBlobAwareDao terminalActivationCodeDao() {
        Set<String> jsonFields = new HashSet<>();

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("terminal_activation_code", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("changeShiftsDao")
    public JsonBlobAwareDao changeShiftsDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("change_shifts", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("storeDao")
    public JsonBlobAwareDao storeDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("contact_cellphone");
        cryptoFields.add("street_address");
        cryptoFields.add("street_address_desc");
        cryptoFields.add("contact_phone");
        cryptoFields.add("contact_email");
        cryptoFields.add("poi_name");
        cryptoFields.add("poi_simple_address");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("contact_info");
        jsonFields.add("pictures");
        jsonFields.add("geo_location");
        jsonFields.add("extra");

        JdbcDaoBase storeJdbcDao = new JdbcDaoBase("store", jdbcTemplate());
        JdbcDaoBase storeFakeJdbcDao = new JdbcDaoBase("store_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(storeJdbcDao, storeFakeJdbcDao);
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(fakeRequestDao, cryptoFields, cryptoClient);
        TimedDaoBase timedDaoBase = new TimedDaoBase(cryptoAwareMapDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("merchantDao")
    public TimedDaoBase merchantDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("legal_person_id_number");
        cryptoFields.add("legal_person_id_card_front_photo");
        cryptoFields.add("legal_person_id_card_back_photo");
        cryptoFields.add("street_address");
        cryptoFields.add("street_address_desc");
        cryptoFields.add("contact_phone");
        cryptoFields.add("contact_cellphone");
        cryptoFields.add("contact_email");
        cryptoFields.add("owner_cellphone");
        cryptoFields.add("customer_phone");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("legal_person_id_card_front_photo");
        ossFields.add("legal_person_id_card_back_photo");
        ossFields.add("business_license_photo");
        ossFields.add("concat_id_card_front_photo");
        ossFields.add("logo");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("contact_info");
        jsonFields.add("legal_representative");
        jsonFields.add("extra");

        JdbcDaoBase merchantJdbcDao = new JdbcDaoBase("merchant", jdbcTemplate());
        JdbcDaoBase merchantFakeJdbcDao = new JdbcDaoBase("merchant_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(merchantJdbcDao, merchantFakeJdbcDao);
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(fakeRequestDao, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("vendorDao")
    public JsonBlobAwareDao vendorDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("cellphone");
        cryptoFields.add("contact_phone");
        cryptoFields.add("contact_cellphone");
        cryptoFields.add("contact_email");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("contact_info");
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("vendor", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        TimedDaoBase timedDaoBase = new TimedDaoBase(cryptoAwareMapDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("merchantGalleryDao")
    public JsonBlobAwareDao merchantGalleryDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("images");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_gallery", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("terminalConfigDao")
    public JsonBlobAwareDao terminalConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase terminalConfigJdbcDao = new JdbcDaoBase("terminal_config", jdbcTemplate());
        JdbcDaoBase terminalConfigFakeJdbcDao = new JdbcDaoBase("terminal_config_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(terminalConfigJdbcDao, terminalConfigFakeJdbcDao);
        TimedDaoBase timedDaoBase = new TimedDaoBase(fakeRequestDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("storeConfigDao")
    public JsonBlobAwareDao storeConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase storeConfigJdbcDao = new JdbcDaoBase("store_config", jdbcTemplate());
        JdbcDaoBase storeConfigFakeJdbcDao = new JdbcDaoBase("store_config_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(storeConfigJdbcDao, storeConfigFakeJdbcDao);
        TimedDaoBase timedDaoBase = new TimedDaoBase(fakeRequestDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("storeAppConfigDao")
    public JsonBlobAwareDao storeAppConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase storeAppConfigJdbcDao = new JdbcDaoBase("store_app_config", jdbcTemplate());
        JdbcDaoBase storeAppConfigFakeJdbcDao = new JdbcDaoBase("store_app_config_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(storeAppConfigJdbcDao, storeAppConfigFakeJdbcDao);
        TimedDaoBase timedDaoBase = new TimedDaoBase(fakeRequestDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("merchantConfigDao")
    public TimedDaoBase merchantConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase merchantConfigJdbcDao = new JdbcDaoBase("merchant_config", jdbcTemplate());
        JdbcDaoBase merchantConfigFakeJdbcDao = new JdbcDaoBase("merchant_config_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(merchantConfigJdbcDao, merchantConfigFakeJdbcDao);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(fakeRequestDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantConfigCustomDao")
    public TimedDaoBase merchantConfigCustomDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_config_custom", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("storeExtDao")
    public TimedDaoBase storeExtDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("store_ext", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("photoInfoDao")
    public TimedDaoBase photoInfoDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("photo_info", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("mcPreDao")
    public TimedDaoBase mcPreDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("mc_pre", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("cashDeskDao")
    public TimedDaoBase cashDeskDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("cash_desk", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("cashDeskDeviceDao")
    public TimedDaoBase cashDeskDeviceDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("cash_desk_device", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("cashDeskOpLogDao")
    public JdbcDaoBase cashDeskOpLogDao() {
        return new JdbcDaoBase("cash_desk_op_log", jdbcTemplate());
    }

    @Bean("merchantAppConfigDao")
    public TimedDaoBase merchantAppConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase merchantAppConfigJdbcDao = new JdbcDaoBase("merchant_app_config", jdbcTemplate());
        JdbcDaoBase merchantAppConfigFakeJdbcDao = new JdbcDaoBase("merchant_app_config_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(merchantAppConfigJdbcDao, merchantAppConfigFakeJdbcDao);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(fakeRequestDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("vendorConfigDao")
    public JsonBlobAwareDao vendorConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("vendor_config", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("solicitorDao")
    public JsonBlobAwareDao solicitorDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("solicitor", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("solicitorUserDao")
    public TimedDaoBase solicitorUserDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("solicitor_user", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("solicitorConfigDao")
    public JsonBlobAwareDao solicitorConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("solicitor_config", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("solicitorBankAccountDao")
    public JsonBlobAwareDao solicitorBankAccountDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("solicitor_bank_account", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("vendorDeveloperDao")
    public JsonBlobAwareDao vendorDeveloperDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("public_key");
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("vendor_developer", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("vendorUserDao")
    public TimedDaoBase vendorUserDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("vendor_user", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("vendorAppDao")
    public TimedDaoBase vendorAppDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("vendor_app", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("solicitorDeveloperDao")
    public JsonBlobAwareDao solicitorDeveloperDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("solicitor_developer", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("merchantDeveloperDao")
    public JsonBlobAwareDao merchantDeveloperDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_developer", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("merchantUserDao")
    public JsonBlobAwareDao merchantUserDao() {
        Set<String> jsonFields = new HashSet<>();

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_user", userJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("merchantUserStoreAuthDao")
    public JsonBlobAwareDao merchantUserStoreAuthDao() {
        Set<String> jsonFields = new HashSet<>();

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_user_store_auth", userJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("specialAuthWhitelistDao")
    public TimedDaoBase specialAuthWhitelistDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("special_auth_whitelist", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("tradeExtConfigDao")
    public JsonBlobAwareDao tradeExtConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("content");

        JdbcDaoBase tradeExtraConfigJdbcDao = new JdbcDaoBase("trade_extra_config", jdbcTemplate());
        JdbcDaoBase tradeExtraConfigFakeJdbcDao = new JdbcDaoBase("trade_extra_config_fake", jdbcTemplate());
        FakeRequestDao fakeRequestDao = new FakeRequestDao(tradeExtraConfigJdbcDao, tradeExtraConfigFakeJdbcDao);
        TimedDaoBase timedDaoBase = new TimedDaoBase(fakeRequestDao);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("mspRefundStoreTerminalWhitelistDao")
    public TimedDaoBase mspRefundStoreTerminalWhitelistDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("msp_refund_store_terminal_whitelist", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("merchantBankAccountReadDao")
    public TimedDaoBase merchantBankAccountReadDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("identity");
        cryptoFields.add("number");
        cryptoFields.add("holder_id_front_photo");
        cryptoFields.add("holder_id_back_photo");
        cryptoFields.add("bank_card_image");
        cryptoFields.add("id_validity");
        cryptoFields.add("cellphone");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("holder_id_front_photo");
        ossFields.add("holder_id_back_photo");
        ossFields.add("bank_card_image");
        ossFields.add("letter_of_authorization");
        ossFields.add("hand_letter_of_authorization");
        ossFields.add("relation_or_legal_rep_held_auth");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");
        jsonFields.add("extend");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_bank_account", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBankAccountWriteDao")
    public TimedDaoBase merchantBankAccountWriteDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("identity");
        cryptoFields.add("number");
        cryptoFields.add("holder_id_front_photo");
        cryptoFields.add("holder_id_back_photo");
        cryptoFields.add("bank_card_image");
        cryptoFields.add("cellphone");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("holder_id_front_photo");
        ossFields.add("holder_id_back_photo");
        ossFields.add("bank_card_image");
        ossFields.add("letter_of_authorization");
        ossFields.add("hand_letter_of_authorization");
        ossFields.add("relation_or_legal_rep_held_auth");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");
        jsonFields.add("extend");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_bank_account", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBizBankAccountReadDao")
    public TimedDaoBase merchantBizBankAccountReadDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("identity");
        cryptoFields.add("number");
        cryptoFields.add("holder_id_front_photo");
        cryptoFields.add("holder_id_back_photo");
        cryptoFields.add("bank_card_image");
        cryptoFields.add("id_validity");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("holder_id_front_photo");
        ossFields.add("holder_id_back_photo");
        ossFields.add("bank_card_image");
        ossFields.add("letter_of_authorization");
        ossFields.add("hand_letter_of_authorization");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");
        jsonFields.add("extend");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_biz_bank_account", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBizBankAccountWriteDao")
    public TimedDaoBase merchantBizBankAccountWriteDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("identity");
        cryptoFields.add("number");
        cryptoFields.add("holder_id_front_photo");
        cryptoFields.add("holder_id_back_photo");
        cryptoFields.add("bank_card_image");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("holder_id_front_photo");
        ossFields.add("holder_id_back_photo");
        ossFields.add("bank_card_image");
        ossFields.add("letter_of_authorization");
        ossFields.add("hand_letter_of_authorization");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");
        jsonFields.add("extend");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_biz_bank_account", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBankAccountPreReadDao")
    public TimedDaoBase merchantBankAccountPreReadDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("identity");
        cryptoFields.add("number");
        cryptoFields.add("holder_id_front_photo");
        cryptoFields.add("holder_id_back_photo");
        cryptoFields.add("bank_card_image");
        cryptoFields.add("id_validity");
        cryptoFields.add("cellphone");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("holder_id_front_photo");
        ossFields.add("holder_id_back_photo");
        ossFields.add("bank_card_image");
        ossFields.add("transfer_voucher");
        ossFields.add("letter_of_authorization");
        ossFields.add("hand_letter_of_authorization");
        ossFields.add("relation_or_legal_rep_held_auth");
        ossFields.add("business_license_photo");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("change_extra");
        jsonFields.add("extra");
        jsonFields.add("extend");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_bank_account_pre", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBankAccountPreWriteDao")
    public TimedDaoBase merchantBankAccountPreWriteDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("identity");
        cryptoFields.add("number");
        cryptoFields.add("holder_id_front_photo");
        cryptoFields.add("holder_id_back_photo");
        cryptoFields.add("bank_card_image");
        cryptoFields.add("cellphone");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("holder_id_front_photo");
        ossFields.add("holder_id_back_photo");
        ossFields.add("bank_card_image");
        ossFields.add("transfer_voucher");
        ossFields.add("letter_of_authorization");
        ossFields.add("hand_letter_of_authorization");
        ossFields.add("relation_or_legal_rep_held_auth");
        ossFields.add("business_license_photo");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("change_extra");
        jsonFields.add("extra");
        jsonFields.add("extend");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_bank_account_pre", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBankAccountChangeLogReadDao")
    public TimedDaoBase merchantBankAccountChangeLogReadDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("new_number");
        cryptoFields.add("new_identity");
        cryptoFields.add("old_number");
        cryptoFields.add("old_identity");

        Set<String> ossFields = new HashSet<>();

        Set<String> jsonFields = new HashSet<>();

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_bank_account_change_log", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);
        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBankAccountChangeLogWriteDao")
    public TimedDaoBase merchantBankAccountChangeLogWriteDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("new_number");
        cryptoFields.add("new_identity");
        cryptoFields.add("old_number");
        cryptoFields.add("old_identity");

        Set<String> ossFields = new HashSet<>();

        Set<String> jsonFields = new HashSet<>();

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_bank_account_change_log", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBusinessLicenseReadDao")
    public TimedDaoBase merchantBusinessLicenseReadDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("legal_person_id_number");
        cryptoFields.add("legal_person_id_card_front_photo");
        cryptoFields.add("legal_person_id_card_back_photo");
        cryptoFields.add("photo");
        cryptoFields.add("id_validity");
        cryptoFields.add("trade_license");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("legal_person_id_card_front_photo");
        ossFields.add("legal_person_id_card_back_photo");
        ossFields.add("photo");
        ossFields.add("trade_license");
        ossFields.add("letter_of_authorization");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_business_license", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("merchantBusinessLicenseWriteDao")
    public TimedDaoBase merchantBusinessLicenseWriteDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("legal_person_id_number");
        cryptoFields.add("legal_person_id_card_front_photo");
        cryptoFields.add("legal_person_id_card_back_photo");
        cryptoFields.add("photo");
        cryptoFields.add("trade_license");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("legal_person_id_card_front_photo");
        ossFields.add("legal_person_id_card_back_photo");
        ossFields.add("photo");
        ossFields.add("trade_license");
        ossFields.add("letter_of_authorization");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_business_license", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("storeBusinessLicenseReadDao")
    public TimedDaoBase storeBusinessLicenseReadDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("legal_person_id_number");
        cryptoFields.add("legal_person_id_card_front_photo");
        cryptoFields.add("legal_person_id_card_back_photo");
        cryptoFields.add("photo");
        cryptoFields.add("id_validity");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("legal_person_id_card_front_photo");
        ossFields.add("legal_person_id_card_back_photo");
        ossFields.add("photo");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("store_business_license", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("storeBusinessLicenseWriteDao")
    public TimedDaoBase storeBusinessLicenseWriteDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("legal_person_id_number");
        cryptoFields.add("legal_person_id_card_front_photo");
        cryptoFields.add("legal_person_id_card_back_photo");
        cryptoFields.add("photo");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("legal_person_id_card_front_photo");
        ossFields.add("legal_person_id_card_back_photo");
        ossFields.add("photo");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("store_business_license", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("licenseDao")
    public TimedDaoBase licenseDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("license_number");
        cryptoFields.add("license_photo");

        Set<String> ossFields = new HashSet<>();
        ossFields.add("license_photo");

        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("license", jdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);
        CryptoAuthDao cryptoAuthDao = new CryptoAuthDao(cryptoAwareMapDao, ossFields, ossUrlEncrypt);
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(cryptoAuthDao, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("storeDeveloperDao")
    public JsonBlobAwareDao storeDeveloperDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("store_developer", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("rsaKeyDao")
    public TimedDaoBase rsaKeyDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("rsa_key", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("signConfigDao")
    public JsonBlobAwareDao signConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("data");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("sign_config", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("systemConfigDao")
    public TimedDaoBase systemConfigDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("system_config", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("withdrawDao")
    public JsonBlobAwareDao withdrawDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("operators");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("withdraw", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("messageDao")
    public JsonBlobAwareDao messageDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("payload");
        jsonFields.add("receiver_ids");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("message", messageJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("importantChangeLogDao")
    public JsonBlobAwareDao importantChangeLogDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("payload");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("important_change_log", logJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("opLogDao")
    public JsonBlobAwareDao opLogDao() {
        Set<String> jsonFields = new HashSet<>();

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("op_log", logJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("taskApplyLogDao")
    public JsonBlobAwareDao taskApplyLogDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("payload");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("task_apply_log", logJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("permissionDao")
    public TimedDaoBase permissionDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("permission", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("userRoleDao")
    public TimedDaoBase userRoleDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("user_role", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("merchantRoleDao")
    public TimedDaoBase merchantRoleDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_role", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("ospRoleDao")
    public TimedDaoBase ospRoleDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("osp_role", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("rolePermissionDao")
    public TimedDaoBase rolePermissionDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("role_permission", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("accountDao")
    public TimedDaoBase accountDao() {
        Set<String> cryptoFields = new HashSet<>();
        cryptoFields.add("cellphone");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("account", userJdbcTemplate());
        CryptoAwareMapDao cryptoAwareMapDao = new CryptoAwareMapDao(jdbcDaoBase, cryptoFields, cryptoClient);

        return new TimedDaoBase(cryptoAwareMapDao);
    }

    @Bean("oauthTokenDao")
    public TimedDaoBase oauthTokenDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("oauth_token", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("ospUserDao")
    public TimedDaoBase ospUserDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("osp_user", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("accountCommonDao")
    public TimedDaoBase accountCommonDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("account", commonLoginJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("bankInfoDao")
    public JdbcDaoBase bankInfoDao() {
        return new JdbcDaoBase("bank_info", bankInfoJdbcTemplate());
    }

    @Bean("wftBankCodeDao")
    public JdbcDaoBase wftBankCodeDao() {
        return new JdbcDaoBase("cib_bank_code", bankInfoJdbcTemplate());
    }

    @Bean("wftDistrictCodeDao")
    public JdbcDaoBase wftDistrictCodeDao() {
        return new JdbcDaoBase("wft_district_code", jdbcTemplate());
    }

    @Bean("industryDao")
    public JdbcDaoBase industryDao() {
        return new JdbcDaoBase("industry", bankInfoJdbcTemplate());
    }

    @Bean("agentDao")
    public JsonBlobAwareDao agentDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("agent", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("baseConfigDao")
    public TimedDaoBase baseConfigDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("params");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("base_config", jdbcTemplate());
        JsonBlobAwareDao jsonBlobAwareDao = new JsonBlobAwareDao(jdbcDaoBase, jsonFields);
        appendObjectMapperConfig(jsonBlobAwareDao);

        return new TimedDaoBase(jsonBlobAwareDao);
    }

    @Bean("groupDao")
    public JsonBlobAwareDao groupDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("extra");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("group", userJdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("groupUserDao")
    public TimedDaoBase groupUserDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("group_user", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("groupUserMerchantAuthDao")
    public TimedDaoBase groupUserMerchantAuthDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("group_user_merchant_auth", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("currencyFeerateMappingDao")
    public TimedDaoBase currencyFeerateMappingDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("currency_feerate_mapping", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("departmentDao")
    public TimedDaoBase departmentDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("department", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("departmentStoreDao")
    public TimedDaoBase departmentStoreDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("department_store", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("merchantUserDepartmentAuthDao")
    public TimedDaoBase merchantUserDepartmentAuthDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_user_department_auth", userJdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("providerAbilityDao")
    public JsonBlobAwareDao providerAbilityDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("sub_payway");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("provider_ability", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("metaPaywayDao")
    public TimedDaoBase metaPaywayDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_payway", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("metaPaySourceDao")
    public TimedDaoBase metaPaySourceDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_pay_source", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("metaProductFlagDao")
    public TimedDaoBase metaProductFlagDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_product_flag", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("metaProviderDao")
    public JsonBlobAwareDao metaProviderDao() {
        Set<String> jsonFields = new HashSet<>();
        jsonFields.add("support_payways");

        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_provider", jdbcTemplate());
        TimedDaoBase timedDaoBase = new TimedDaoBase(jdbcDaoBase);

        return new JsonBlobAwareDao(timedDaoBase, jsonFields);
    }

    @Bean("metaAcquirerDao")
    public TimedDaoBase metaAcquirerDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_acquirer", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("metaBizModelDao")
    public TimedDaoBase metaBizModelDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_biz_model", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("metaPayPathDao")
    public TimedDaoBase metaPayPathDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("meta_pay_path", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("tradeAppConfigDao")
    public TimedDaoBase tradeAppConfigDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("trade_app_config", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("externalExtraConfigDao")
    public TimedDaoBase externalExtraConfigDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("external_extra_config", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    @Bean("merchantMaterialPhotoDao")
    public TimedDaoBase merchantMaterialPhotoDao() {
        JdbcDaoBase jdbcDaoBase = new JdbcDaoBase("merchant_material_photo", jdbcTemplate());
        return new TimedDaoBase(jdbcDaoBase);
    }

    /**
     * 设置JsonBlobAwareDao的objectMapper
     * <p>
     * 说明：使用new方式创建的类且不是return new *** 时，不会自动注入objectMapper
     *
     * @param jsonBlobAwareDao
     */
    private void appendObjectMapperConfig(JsonBlobAwareDao jsonBlobAwareDao) {
        ReflectionUtils.setField(ReflectionUtils.findField(JsonBlobAwareDao.class, "objectMapper"), jsonBlobAwareDao, objectMapper);
    }
}
