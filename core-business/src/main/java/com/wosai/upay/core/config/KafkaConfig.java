package com.wosai.upay.core.config;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.Map;

/***
 * @ClassName: KafkaConfig
 * @Description: Kafka configuration
 * @Auther: dabuff
 * @Date: 2024/8/6 14:25
 */
@Configuration
public class KafkaConfig {
    @Value("${kafka.send.batch.size}")
    private String kafkaSendBatchSize;

    @Value("${kafka.send.acks}")
    private String kafkaSendAcks;

    @Value("${kafka.send.linger.ms}")
    private String kafkaSendLingerMs;

    @Value("${kafka.send.max.block.ms}")
    private String kafkaSendMaxBlockMs;

    private KafkaProducer kafkaProducerInstance;
    private KafkaProducer aLiKafkaProducerInstance;
    private KafkaProducer dataBusKafkaProducerInstance;

    public KafkaConfig() {
        // 发送的kafka消息中有带有header信息，下游消费者如果版本过低，会导致消费报错, 故需要在启动参数中加上 -Dskywalking.plugin.kafka.enable_inject_kafka_header=false
        // 如果该值为true, 那么启动报错
        boolean property = Boolean.parseBoolean(System.getProperty("skywalking.plugin.kafka.enable_inject_kafka_header", "true"));
        if (property) {
            throw new RuntimeException("kafka消息中不允许带有header信息！！！");
        }
    }

    /**
     * Standard Kafka producer
     */
    @Bean("kafkaProducer")
    public KafkaProducer kafkaProducer(
            @Value("${kafka.send.registry.url}") String kafkaSendRegistryUrl,
            @Value("${kafka.send.brokers}") String kafkaSendBrokers) {
        Map<String, Object> configs = new HashMap<>();
        configs.put("bootstrap.servers", kafkaSendBrokers);
        configs.put("batch.size", kafkaSendBatchSize);
        configs.put("acks", kafkaSendAcks);
        configs.put("linger.ms", kafkaSendLingerMs);
        configs.put("max.block.ms", kafkaSendMaxBlockMs);
        configs.put("schema.registry.url", kafkaSendRegistryUrl);
        configs.put("key.serializer", "io.confluent.kafka.serializers.KafkaAvroSerializer");
        configs.put("value.serializer", "io.confluent.kafka.serializers.KafkaAvroSerializer");

        kafkaProducerInstance = new KafkaProducer(configs);
        return kafkaProducerInstance;
    }

    /**
     * Alibaba Kafka producer
     */
    @Bean("aLiKafkaProducer")
    public KafkaProducer aLiKafkaProducer(
            @Value("${ali.kafka.send.brokers}") String aliKafkaSendBrokers,
            @Value("${ali.kafka.send.batch.size}") String aliKafkaSendBatchSize,
            @Value("${ali.kafka.send.acks}") String aliKafkaSendAcks,
            @Value("${ali.kafka.send.linger.ms}") String aliKafkaSendLingerMs,
            @Value("${ali.kafka.send.max.block.ms}") String aliKafkaSendMaxBlockMs,
            @Value("${ali.kafka.send.registry.url}") String aliKafkaSendRegistryUrl) {
        Map<String, Object> configs = new HashMap<>();
        configs.put("bootstrap.servers", aliKafkaSendBrokers);
        configs.put("batch.size", aliKafkaSendBatchSize);
        configs.put("acks", aliKafkaSendAcks);
        configs.put("linger.ms", aliKafkaSendLingerMs);
        configs.put("max.block.ms", aliKafkaSendMaxBlockMs);
        configs.put("schema.registry.url", aliKafkaSendRegistryUrl);
        configs.put("key.serializer", "io.confluent.kafka.serializers.KafkaAvroSerializer");
        configs.put("value.serializer", "io.confluent.kafka.serializers.KafkaAvroSerializer");

        aLiKafkaProducerInstance = new KafkaProducer(configs);
        return aLiKafkaProducerInstance;
    }

    /**
     * DataBus Kafka producer
     */
    @Bean("dataBusKafkaProducer")
    public KafkaProducer dataBusKafkaProducer(
            @Value("${kafka.databus.brokers}") String kafkaDatabusbrokers,
            @Value("${kafka.databus.registry.url}") String kafkaDatabusRegistryUrl) {
        Map<String, Object> configs = new HashMap<>();
        configs.put("bootstrap.servers", kafkaDatabusbrokers);
        configs.put("batch.size", kafkaSendBatchSize);
        configs.put("acks", kafkaSendAcks);
        configs.put("linger.ms", kafkaSendLingerMs);
        configs.put("max.block.ms", kafkaSendMaxBlockMs);
        configs.put("schema.registry.url", kafkaDatabusRegistryUrl);
        configs.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        configs.put("value.serializer", "io.confluent.kafka.serializers.KafkaAvroSerializer");

        dataBusKafkaProducerInstance = new KafkaProducer(configs);
        return dataBusKafkaProducerInstance;
    }

    /**
     * Lifecycle method to close Kafka producers
     */
    @PreDestroy
    public void close() {
        if (kafkaProducerInstance != null) {
            kafkaProducerInstance.close(5, java.util.concurrent.TimeUnit.SECONDS);
        }
        if (aLiKafkaProducerInstance != null) {
            aLiKafkaProducerInstance.close(5, java.util.concurrent.TimeUnit.SECONDS);
        }
        if (dataBusKafkaProducerInstance != null) {
            dataBusKafkaProducerInstance.close(5, java.util.concurrent.TimeUnit.SECONDS);
        }
    }
}
