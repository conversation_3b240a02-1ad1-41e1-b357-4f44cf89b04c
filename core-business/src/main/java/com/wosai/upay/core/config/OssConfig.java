package com.wosai.upay.core.config;


import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import com.wosai.oss.OssStsClient;
import com.wosai.oss.OssUpload;
import com.wosai.oss.OssUploadPro;
import com.wosai.oss.OssUrlEncrypt;
import com.wosai.oss.configuration.OssProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssConfig {

    @Bean
    public OssProperties ossProperties(@Value("${com.wosai.oss.internal:true}") boolean internal,
                                        @Value("${com.wosai.oss.staticBucket}") String staticBucket,
                                        @Value("${com.wosai.oss.staticBaseUrl}") String staticBaseUrl,
                                        @Value("${com.wosai.oss.imgBucket}") String imgBucket,
                                        @Value("${com.wosai.oss.imgBaseUrl}") String imgBaseUrl,
                                        @Value("${com.wosai.oss.group}") String group) {
        OssProperties ossProperties = new OssProperties();
        ossProperties.setGroup(group);
        ossProperties.setInternal(internal);
        ossProperties.setStaticBucket(staticBucket);
        ossProperties.setStaticBaseUrl(staticBaseUrl);
        ossProperties.setImgBucket(imgBucket);
        ossProperties.setImgBaseUrl(imgBaseUrl);
        return ossProperties;
    }

    @Bean
    public OssUrlEncrypt ossUrlEncrypt() {
        return new OssUrlEncrypt();
    }

    @Bean
    public OssStsClient ossStsClient() {
        return new OssStsClient();
    }

    @Bean
    public OssUpload ossUpload() {
        return new OssUpload();
    }

    @Bean
    public OssUploadPro ossUploadPro() {
        return new OssUploadPro();
    }

}
