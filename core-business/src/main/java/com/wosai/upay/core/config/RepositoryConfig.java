package com.wosai.upay.core.config;

import com.wosai.data.dao.Dao;
import com.wosai.upay.core.repository.DataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * Repository configuration
 */
@Configuration
public class RepositoryConfig {

    @Autowired
    @Qualifier("vendorDao")
    private Dao<Map<String, Object>> vendorDao;

    @Autowired
    @Qualifier("vendorUserDao")
    private Dao<Map<String, Object>> vendorUserDao;

    @Autowired
    @Qualifier("vendorConfigDao")
    private Dao<Map<String, Object>> vendorConfigDao;

    @Autowired
    @Qualifier("vendorDeveloperDao")
    private Dao<Map<String, Object>> vendorDeveloperDao;

    @Autowired
    @Qualifier("vendorAppDao")
    private Dao<Map<String, Object>> vendorAppDao;

    @Autowired
    @Qualifier("merchantDao")
    private Dao<Map<String, Object>> merchantDao;

    @Autowired
    @Qualifier("merchantConfigDao")
    private Dao<Map<String, Object>> merchantConfigDao;

    @Autowired
    @Qualifier("merchantConfigCustomDao")
    private Dao<Map<String, Object>> merchantConfigCustomDao;

    @Autowired
    @Qualifier("merchantDeveloperDao")
    private Dao<Map<String, Object>> merchantDeveloperDao;

    @Autowired
    @Qualifier("merchantUserDao")
    private Dao<Map<String, Object>> merchantUserDao;

    @Autowired
    @Qualifier("merchantUserStoreAuthDao")
    private Dao<Map<String, Object>> merchantUserStoreAuthDao;

    @Autowired
    @Qualifier("merchantBankAccountReadDao")
    private Dao<Map<String, Object>> merchantBankAccountReadDao;

    @Autowired
    @Qualifier("merchantBankAccountWriteDao")
    private Dao<Map<String, Object>> merchantBankAccountWriteDao;

    @Autowired
    @Qualifier("merchantBizBankAccountReadDao")
    private Dao<Map<String, Object>> merchantBizBankAccountReadDao;

    @Autowired
    @Qualifier("merchantBizBankAccountWriteDao")
    private Dao<Map<String, Object>> merchantBizBankAccountWriteDao;

    @Autowired
    @Qualifier("merchantBankAccountPreReadDao")
    private Dao<Map<String, Object>> merchantBankAccountPreReadDao;

    @Autowired
    @Qualifier("merchantBankAccountPreWriteDao")
    private Dao<Map<String, Object>> merchantBankAccountPreWriteDao;

    @Autowired
    @Qualifier("merchantBusinessLicenseReadDao")
    private Dao<Map<String, Object>> merchantBusinessLicenseReadDao;

    @Autowired
    @Qualifier("merchantBusinessLicenseWriteDao")
    private Dao<Map<String, Object>> merchantBusinessLicenseWriteDao;

    @Autowired
    @Qualifier("storeBusinessLicenseReadDao")
    private Dao<Map<String, Object>> storeBusinessLicenseReadDao;

    @Autowired
    @Qualifier("storeBusinessLicenseWriteDao")
    private Dao<Map<String, Object>> storeBusinessLicenseWriteDao;

    @Autowired
    @Qualifier("licenseDao")
    private Dao<Map<String, Object>> licenseDao;

    @Autowired
    @Qualifier("merchantGalleryDao")
    private Dao<Map<String, Object>> merchantGalleryDao;

    @Autowired
    @Qualifier("merchantAppConfigDao")
    private Dao<Map<String, Object>> merchantAppConfigDao;

    @Autowired
    @Qualifier("storeDao")
    private Dao<Map<String, Object>> storeDao;

    @Autowired
    @Qualifier("storeExtDao")
    private Dao<Map<String, Object>> storeExtDao;

    @Autowired
    @Qualifier("photoInfoDao")
    private Dao<Map<String, Object>> photoInfoDao;

    @Autowired
    @Qualifier("mcPreDao")
    private Dao<Map<String, Object>> mcPreDao;

    @Autowired
    @Qualifier("storeConfigDao")
    private Dao<Map<String, Object>> storeConfigDao;

    @Autowired
    @Qualifier("storeAppConfigDao")
    private Dao<Map<String, Object>> storeAppConfigDao;

    @Autowired
    @Qualifier("storeDeveloperDao")
    private Dao<Map<String, Object>> storeDeveloperDao;

    @Autowired
    @Qualifier("solicitorDao")
    private Dao<Map<String, Object>> solicitorDao;

    @Autowired
    @Qualifier("solicitorConfigDao")
    private Dao<Map<String, Object>> solicitorConfigDao;

    @Autowired
    @Qualifier("solicitorDeveloperDao")
    private Dao<Map<String, Object>> solicitorDeveloperDao;

    @Autowired
    @Qualifier("solicitorBankAccountDao")
    private Dao<Map<String, Object>> solicitorBankAccountDao;

    @Autowired
    @Qualifier("solicitorUserDao")
    private Dao<Map<String, Object>> solicitorUserDao;

    @Autowired
    @Qualifier("terminalDao")
    private Dao<Map<String, Object>> terminalDao;

    @Autowired
    @Qualifier("terminalConfigDao")
    private Dao<Map<String, Object>> terminalConfigDao;

    @Autowired
    @Qualifier("terminalActivationCodeDao")
    private Dao<Map<String, Object>> terminalActivationCodeDao;

    @Autowired
    @Qualifier("changeShiftsDao")
    private Dao<Map<String, Object>> changeShiftsDao;

    @Autowired
    @Qualifier("rsaKeyDao")
    private Dao<Map<String, Object>> rsaKeyDao;

    @Autowired
    @Qualifier("signConfigDao")
    private Dao<Map<String, Object>> signConfigDao;

    @Autowired
    @Qualifier("withdrawDao")
    private Dao<Map<String, Object>> withdrawDao;

    @Autowired
    @Qualifier("messageDao")
    private Dao<Map<String, Object>> messageDao;

    @Autowired
    @Qualifier("baseConfigDao")
    private Dao<Map<String, Object>> baseConfigDao;

    @Autowired
    @Qualifier("importantChangeLogDao")
    private Dao<Map<String, Object>> importantChangeLogDao;

    @Autowired
    @Qualifier("opLogDao")
    private Dao<Map<String, Object>> opLogDao;

    @Autowired
    @Qualifier("taskApplyLogDao")
    private Dao<Map<String, Object>> taskApplyLogDao;

    @Autowired
    @Qualifier("permissionDao")
    private Dao<Map<String, Object>> permissionDao;

    @Autowired
    @Qualifier("userRoleDao")
    private Dao<Map<String, Object>> userRoleDao;

    @Autowired
    @Qualifier("merchantRoleDao")
    private Dao<Map<String, Object>> merchantRoleDao;

    @Autowired
    @Qualifier("ospRoleDao")
    private Dao<Map<String, Object>> ospRoleDao;

    @Autowired
    @Qualifier("rolePermissionDao")
    private Dao<Map<String, Object>> rolePermissionDao;

    @Autowired
    @Qualifier("accountDao")
    private Dao<Map<String, Object>> accountDao;

    @Autowired
    @Qualifier("ospUserDao")
    private Dao<Map<String, Object>> ospUserDao;

    @Autowired
    @Qualifier("oauthTokenDao")
    private Dao<Map<String, Object>> oauthTokenDao;

    @Autowired
    @Qualifier("accountCommonDao")
    private Dao<Map<String, Object>> accountCommonDao;

    @Autowired
    @Qualifier("departmentDao")
    private Dao<Map<String, Object>> departmentDao;

    @Autowired
    @Qualifier("departmentStoreDao")
    private Dao<Map<String, Object>> departmentStoreDao;

    @Autowired
    @Qualifier("merchantUserDepartmentAuthDao")
    private Dao<Map<String, Object>> merchantUserDepartmentAuthDao;

    @Autowired
    @Qualifier("systemConfigDao")
    private Dao<Map<String, Object>> systemConfigDao;

    @Autowired
    @Qualifier("bankInfoDao")
    private Dao<Map<String, Object>> bankInfoDao;

    @Autowired
    @Qualifier("wftBankCodeDao")
    private Dao<Map<String, Object>> wftBankCodeDao;

    @Autowired
    @Qualifier("wftDistrictCodeDao")
    private Dao<Map<String, Object>> wftDistrictCodeDao;

    @Autowired
    @Qualifier("industryDao")
    private Dao<Map<String, Object>> industryDao;

    @Autowired
    @Qualifier("agentDao")
    private Dao<Map<String, Object>> agentDao;

    @Autowired
    @Qualifier("groupDao")
    private Dao<Map<String, Object>> groupDao;

    @Autowired
    @Qualifier("groupUserDao")
    private Dao<Map<String, Object>> groupUserDao;

    @Autowired
    @Qualifier("groupUserMerchantAuthDao")
    private Dao<Map<String, Object>> groupUserMerchantAuthDao;

    @Autowired
    @Qualifier("currencyFeerateMappingDao")
    private Dao<Map<String, Object>> currencyFeerateMappingDao;

    @Autowired
    @Qualifier("specialAuthWhitelistDao")
    private Dao<Map<String, Object>> specialAuthWhitelistDao;

    @Autowired
    @Qualifier("tradeExtConfigDao")
    private Dao<Map<String, Object>> tradeExtConfigDao;

    @Autowired
    @Qualifier("providerAbilityDao")
    private Dao<Map<String, Object>> providerAbilityDao;

    @Autowired
    @Qualifier("cashDeskDao")
    private Dao<Map<String, Object>> cashDeskDao;

    @Autowired
    @Qualifier("cashDeskDeviceDao")
    private Dao<Map<String, Object>> cashDeskDeviceDao;

    @Autowired
    @Qualifier("cashDeskOpLogDao")
    private Dao<Map<String, Object>> cashDeskOpLogDao;

    @Autowired
    @Qualifier("metaPaywayDao")
    private Dao<Map<String, Object>> metaPaywayDao;

    @Autowired
    @Qualifier("metaPaySourceDao")
    private Dao<Map<String, Object>> metaPaySourceDao;

    @Autowired
    @Qualifier("metaProductFlagDao")
    private Dao<Map<String, Object>> metaProductFlagDao;

    @Autowired
    @Qualifier("metaProviderDao")
    private Dao<Map<String, Object>> metaProviderDao;

    @Autowired
    @Qualifier("metaAcquirerDao")
    private Dao<Map<String, Object>> metaAcquirerDao;

    @Autowired
    @Qualifier("metaBizModelDao")
    private Dao<Map<String, Object>> metaBizModelDao;

    @Autowired
    @Qualifier("metaPayPathDao")
    private Dao<Map<String, Object>> metaPayPathDao;

    @Autowired
    @Qualifier("tradeAppConfigDao")
    private Dao<Map<String, Object>> tradeAppConfigDao;

    @Autowired
    @Qualifier("externalExtraConfigDao")
    private Dao<Map<String, Object>> externalExtraConfigDao;

    @Autowired
    @Qualifier("merchantBankAccountChangeLogReadDao")
    private Dao<Map<String, Object>> merchantBankAccountChangeLogReadDao;

    @Autowired
    @Qualifier("merchantBankAccountChangeLogWriteDao")
    private Dao<Map<String, Object>> merchantBankAccountChangeLogWriteDao;

    @Bean
    public DataRepository repository() {
        DataRepository repository = new DataRepository();

        // Vendor related DAOs
        repository.setVendorDao(vendorDao);
        repository.setVendorUserDao(vendorUserDao);
        repository.setVendorConfigDao(vendorConfigDao);
        repository.setVendorDeveloperDao(vendorDeveloperDao);
        repository.setVendorAppDao(vendorAppDao);

        // Merchant related DAOs
        repository.setMerchantDao(merchantDao);
        repository.setMerchantConfigDao(merchantConfigDao);
        repository.setMerchantConfigCustomDao(merchantConfigCustomDao);
        repository.setMerchantDeveloperDao(merchantDeveloperDao);
        repository.setMerchantUserDao(merchantUserDao);
        repository.setMerchantUserStoreAuthDao(merchantUserStoreAuthDao);

        // Bank account related DAOs
        repository.setMerchantBankAccountReadDao(merchantBankAccountReadDao);
        repository.setMerchantBankAccountWriteDao(merchantBankAccountWriteDao);
        repository.setMerchantBizBankAccountReadDao(merchantBizBankAccountReadDao);
        repository.setMerchantBizBankAccountWriteDao(merchantBizBankAccountWriteDao);
        repository.setMerchantBankAccountPreReadDao(merchantBankAccountPreReadDao);
        repository.setMerchantBankAccountPreWriteDao(merchantBankAccountPreWriteDao);
        repository.setMerchantBankAccountChangeLogReadDao(merchantBankAccountChangeLogReadDao);
        repository.setMerchantBankAccountChangeLogWriteDao(merchantBankAccountChangeLogWriteDao);

        // Business license related DAOs
        repository.setMerchantBusinessLicenseReadDao(merchantBusinessLicenseReadDao);
        repository.setMerchantBusinessLicenseWriteDao(merchantBusinessLicenseWriteDao);
        repository.setStoreBusinessLicenseReadDao(storeBusinessLicenseReadDao);
        repository.setStoreBusinessLicenseWriteDao(storeBusinessLicenseWriteDao);

        // Other merchant related DAOs
        repository.setLicenseDao(licenseDao);
        repository.setMerchantGalleryDao(merchantGalleryDao);
        repository.setMerchantAppConfigDao(merchantAppConfigDao);

        // Store related DAOs
        repository.setStoreDao(storeDao);
        repository.setStoreExtDao(storeExtDao);
        repository.setPhotoInfoDao(photoInfoDao);
        repository.setMcPreDao(mcPreDao);
        repository.setStoreConfigDao(storeConfigDao);
        repository.setStoreAppConfigDao(storeAppConfigDao);
        repository.setStoreDeveloperDao(storeDeveloperDao);

        // Solicitor related DAOs
        repository.setSolicitorDao(solicitorDao);
        repository.setSolicitorConfigDao(solicitorConfigDao);
        repository.setSolicitorDeveloperDao(solicitorDeveloperDao);
        repository.setSolicitorBankAccountDao(solicitorBankAccountDao);
        repository.setSolicitorUserDao(solicitorUserDao);

        // Terminal related DAOs
        repository.setTerminalDao(terminalDao);
        repository.setTerminalConfigDao(terminalConfigDao);
        repository.setTerminalActivationCodeDao(terminalActivationCodeDao);
        repository.setChangeShiftsDao(changeShiftsDao);

        // Security related DAOs
        repository.setRsaKeyDao(rsaKeyDao);
        repository.setSignConfigDao(signConfigDao);

        // Financial related DAOs
        repository.setWithdrawDao(withdrawDao);

        // Communication related DAOs
        repository.setMessageDao(messageDao);

        // Configuration related DAOs
        repository.setBaseConfigDao(baseConfigDao);

        // Logging related DAOs
        repository.setImportantChangeLogDao(importantChangeLogDao);
        repository.setOpLogDao(opLogDao);
        repository.setTaskApplyLogDao(taskApplyLogDao);

        // Permission related DAOs
        repository.setPermissionDao(permissionDao);
        repository.setUserRoleDao(userRoleDao);
        repository.setMerchantRoleDao(merchantRoleDao);
        repository.setOspRoleDao(ospRoleDao);
        repository.setRolePermissionDao(rolePermissionDao);

        // Account related DAOs
        repository.setAccountDao(accountDao);
        repository.setOspUserDao(ospUserDao);
        repository.setOauthTokenDao(oauthTokenDao);
        repository.setAccountCommonDao(accountCommonDao);

        // Department related DAOs
        repository.setDepartmentDao(departmentDao);
        repository.setDepartmentStoreDao(departmentStoreDao);
        repository.setMerchantUserDepartmentAuthDao(merchantUserDepartmentAuthDao);

        // System related DAOs
        repository.setSystemConfigDao(systemConfigDao);
        repository.setBankInfoDao(bankInfoDao);
        repository.setWftBankCodeDao(wftBankCodeDao);
        repository.setWftDistrictCodeDao(wftDistrictCodeDao);
        repository.setIndustryDao(industryDao);
        repository.setAgentDao(agentDao);

        // Group related DAOs
        repository.setGroupDao(groupDao);
        repository.setGroupUserDao(groupUserDao);
        repository.setGroupUserMerchantAuthDao(groupUserMerchantAuthDao);

        // Other DAOs
        repository.setCurrencyFeerateMappingDao(currencyFeerateMappingDao);
        repository.setSpecialAuthWhitelistDao(specialAuthWhitelistDao);
        repository.setTradeExtConfigDao(tradeExtConfigDao);
        repository.setProviderAbilityDao(providerAbilityDao);

        // Cash desk related DAOs
        repository.setCashDeskDao(cashDeskDao);
        repository.setCashDeskDeviceDao(cashDeskDeviceDao);
        repository.setCashDeskOpLogDao(cashDeskOpLogDao);

        // Meta data related DAOs
        repository.setMetaPaywayDao(metaPaywayDao);
        repository.setMetaPaySourceDao(metaPaySourceDao);
        repository.setMetaProductFlagDao(metaProductFlagDao);
        repository.setMetaProviderDao(metaProviderDao);
        repository.setMetaAcquirerDao(metaAcquirerDao);
        repository.setMetaBizModelDao(metaBizModelDao);
        repository.setMetaPayPathDao(metaPayPathDao);

        // Trade related DAOs
        repository.setTradeAppConfigDao(tradeAppConfigDao);
        repository.setExternalExtraConfigDao(externalExtraConfigDao);

        return repository;
    }
}
