package com.wosai.upay.core.config;

import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.elasticsearch.DynamicCredentialsProvider;
import org.apache.http.HttpHost;
import org.apache.http.client.CredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RestHighLevelClientConfig {
    @Bean
    public RestHighLevelClient elasticsearchClient(Vault vault, ElasticsearchConfig elasticsearchConfig) {
        try {
            CredentialsProvider credentialsProvider = new DynamicCredentialsProvider(vault, elasticsearchConfig.getInstance());
            RestClientBuilder builder = RestClient.builder(new HttpHost(elasticsearchConfig.getHostname(), elasticsearchConfig.getPort(), "http"))
                    .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
            return new RestHighLevelClient(builder);
        } catch (Exception ex) {
            // 可以用lombok的@SneakyThrows替代这种写法
            throw new RuntimeException(ex);
        }
    }
}
