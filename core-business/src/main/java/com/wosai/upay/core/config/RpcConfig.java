package com.wosai.upay.core.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.upay.core.model.SensitiveProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.wosai.core.crypto.service.CryptoService;


@Configuration
public class RpcConfig {

    @Value("${alipay.authinto.rpc.server}")
    private String alipayAuthintoRpcServer;

    @Value("${merchant.contract.service}")
    private String merchantContractService;

    @Value("${bankInfo.service}")
    private String bankInfoService;

    @Value("${merchant.audit.service}")
    private String merchantAuditService;

    @Value("${user.service}")
    private String userService;

    @Value("${core-crypto.service}")
    private String coreCryptoService;

    @Value("${core-crypto.access_id}")
    private String coreCryptoAccessId;

    @Value("${shouqianba-risk-service}")
    private String shouqianbaRiskService;

    @Value("${signature.service}")
    private String signatureService;

    @Value("${crm-customer-relation.service}")
    private String crmCustomerRelationService;

    @Value("${trade-manage.service}")
    private String tradeManageService;

    @Value("${merchant-user.service}")
    private String merchantUserService;

    @Value("${business-logstash.service}")
    private String businessLogstashService;

    @Value("${aop-backend.service}")
    private String aopBackendService;

    /**
     * Alipay Auth Store Service
     */
    @Bean("externalAlipayAuthIntoAlipayStoreService")
    public JsonProxyFactoryBean externalAlipayAuthIntoAlipayStoreService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(alipayAuthintoRpcServer + "rpc/alipaystore");
        factory.setServiceInterface(com.wosai.pub.alipay.authinto.service.AlipayStoreService.class);
        factory.setServerName("alipay-authinto");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Provider Trade Params Service
     */
    @Bean("externalProviderTradeParamsService")
    public JsonProxyFactoryBean externalProviderTradeParamsService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantContractService + "/rpc/providerTradeParams");
        factory.setServiceInterface(com.wosai.upay.merchant.contract.service.ProviderTradeParamsService.class);
        factory.setServerName("merchant-contract");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Lakala Service
     */
    @Bean("externalLakalaService")
    public JsonProxyFactoryBean externalLakalaService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantContractService + "/rpc/lakala");
        factory.setServiceInterface(com.wosai.upay.merchant.contract.service.LakalaService.class);
        factory.setServerName("merchant-contract");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(10000);
        return factory;
    }

    /**
     * Wanma Trade Params Service
     */
    @Bean("externalWanmaTradeParamsService")
    public JsonProxyFactoryBean externalWanmaTradeParamsService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantContractService + "/rpc/wanmaTradePatams");
        factory.setServiceInterface(com.wosai.upay.merchant.contract.service.WanmaTradeParamsService.class);
        factory.setServerName("merchant-contract");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Bank Info Service
     */
    @Bean("externalBankInfoService")
    public JsonProxyFactoryBean externalBankInfoService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(bankInfoService + "/rpc/bankinfo");
        factory.setServiceInterface(com.wosai.upay.bank.info.api.service.BankInfoService.class);
        factory.setServerName("bankInfo-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Industry Service
     */
    @Bean("externalIndustryService")
    public JsonProxyFactoryBean externalIndustryService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(bankInfoService + "/rpc/industry");
        factory.setServiceInterface(com.wosai.upay.bank.info.api.service.IndustryService.class);
        factory.setServerName("bankInfo-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Districts Service V2
     */
    @Bean("externalDistrictsServiceV2")
    public JsonProxyFactoryBean externalDistrictsServiceV2() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(bankInfoService + "/rpc/districtsv2");
        factory.setServiceInterface(com.wosai.upay.bank.info.api.service.DistrictsServiceV2.class);
        factory.setServerName("bankInfo-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Merchant Audit Service
     */
    @Bean("externalMerchantAuditService")
    public JsonProxyFactoryBean externalMerchantAuditService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantAuditService + "/rpc/merchantAudit");
        factory.setServiceInterface(com.wosai.upay.merchant.audit.api.service.MerchantAuditService.class);
        factory.setServerName("merchant-audit-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Application Service
     */
    @Bean("externalApplicationService")
    public JsonProxyFactoryBean externalApplicationService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantAuditService + "/rpc/application");
        factory.setServiceInterface(com.wosai.upay.merchant.audit.api.service.ApplicationService.class);
        factory.setServerName("merchant-audit-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * User Service
     */
    @Bean("externalUserService")
    public JsonProxyFactoryBean externalUserService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(userService + "/rpc/user");
        factory.setServiceInterface(com.wosai.upay.user.api.service.UserService.class);
        factory.setServerName("user-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(3000);
        return factory;
    }

    /**
     * Crypto Service
     */
    @Bean("externalCryptoService")
    public JsonProxyFactoryBean externalCryptoService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(coreCryptoService + "/rpc/crypto");
        factory.setServiceInterface(com.wosai.core.crypto.service.CryptoService.class);
        factory.setServerName("core-crypto");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(1000);
        return factory;
    }

    /**
     * Crypto Client
     */
    @Bean("cryptoClient")
    public CryptoClient cryptoClient(@Autowired CryptoService coreCryptoService, SensitiveProperties sensitiveProperties) {
        CryptoClient client = new CryptoClient();
        client.setCryptoService(coreCryptoService);
        client.setAccessId(coreCryptoAccessId);
        client.setAccessSecret(sensitiveProperties.getCoreCryptoAccessSecret());
        client.setDecryptIgnoreException(true);
        return client;
    }

    /**
     * Risk Blist Scene Service
     */
    @Bean("externalIRiskBlistSceneService")
    public JsonProxyFactoryBean externalIRiskBlistSceneService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(shouqianbaRiskService + "/rpc/blistScene");
        factory.setServiceInterface(com.wosai.risk.service.IRiskBlistSceneService.class);
        factory.setServerName("shouqianba-risk-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(5000);
        return factory;
    }

    /**
     * CCB Key Service
     */
    @Bean("ccbKeyService")
    public JsonProxyFactoryBean ccbKeyService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(signatureService + "/rpc/ccb");
        factory.setServiceInterface(com.wosai.upay.signature.service.CcbKeyService.class);
        factory.setServerName("signature-proxy");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(5000);
        return factory;
    }

    /**
     * Customer Relation Validate Facade
     */
    @Bean("iCustomerRelationValidateFacade")
    public JsonProxyFactoryBean iCustomerRelationValidateFacade() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(crmCustomerRelationService + "/rpc/relationvalidate");
        factory.setServiceInterface(facade.ICustomerRelationValidateFacade.class);
        factory.setServerName("crm-customer-relation");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(5000);
        return factory;
    }

    /**
     * Merchant Config Bypass Service
     */
    @Bean("merchantConfigBypassService")
    public JsonProxyFactoryBean merchantConfigBypassService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(tradeManageService + "/rpc/merchantConfigBypass");
        factory.setServiceInterface(com.wosai.trade.service.MerchantConfigBypassService.class);
        factory.setServerName("trade-manager");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(500);
        return factory;
    }

    /**
     * Merchant User Service V2
     */
    @Bean("merchantUserServiceV2")
    public JsonProxyFactoryBean merchantUserServiceV2() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(merchantUserService + "/rpc/merchantuserV2");
        factory.setServiceInterface(com.wosai.app.service.v2.MerchantUserServiceV2.class);
        factory.setServerName("user-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(500);
        return factory;
    }

    /**
     * Business Op Log Service
     */
    @Bean("businessOpLogService")
    public JsonProxyFactoryBean businessOpLogService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(businessLogstashService + "/rpc/businessOpLog");
        factory.setServiceInterface(com.wosai.sp.business.logstash.service.BusinessOpLogService.class);
        factory.setServerName("user-service");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(500);
        return factory;
    }

    /**
     * Operation Home Page Service
     */
    @Bean("operationHomePageService")
    public JsonProxyFactoryBean operationHomePageService() {
        JsonProxyFactoryBean factory = new JsonProxyFactoryBean();
        factory.setServiceUrl(aopBackendService + "/rpc/pay");
        factory.setServiceInterface(com.wosai.aop.backend.service.OperationHomePageService.class);
        factory.setServerName("aop-backend");
        factory.setConnectionTimeoutMillis(500);
        factory.setReadTimeoutMillis(500);
        return factory;
    }
}
