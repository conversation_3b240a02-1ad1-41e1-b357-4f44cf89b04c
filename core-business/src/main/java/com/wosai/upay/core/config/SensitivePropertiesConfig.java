package com.wosai.upay.core.config;

import com.wosai.pay.common.sensitive.apollo.SensitivePropertiesLoader;
import com.wosai.upay.core.model.SensitiveProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SensitivePropertiesConfig {
    @Bean
    public SensitiveProperties sensitiveProperties() {
        return SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);
    }
}

