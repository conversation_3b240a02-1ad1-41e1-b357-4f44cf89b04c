package com.wosai.upay.core.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * Created by jianfree on 4/8/17.
 */
public class CoreConstant {
    /** 终端表的商户终端号字段的最大长度 **/
    public static final int TERMINAL_CLIENT_SN_COLUMN_MAX_LENGTH = 50;
    /** 终端表的商户设备号字段的最大长度 **/
    public static final int TERMINAL_DEVICE_FINGERPRINT_COLUMN_MAX_LENGTH = 128;
    //trace_id
    public static final String TRACE_ID_KEY = "trace_id";
    // 请求ip
    public static final String IP = "ip";

    public static String SYSTEM_CONFIG_NAME_INFORMAL_FORBIDDEN_PAYWAYS = "informal.forbidden.payways"; //收款通道不允许有试用商户配置
    public static String SYSTEM_CONFIG_NAME_DEFAULT_MERCHANT_TRADE_VALIDATE_PARAMS = "default_merchant_trade_validate_params"; //交易限额默认配置
    public static String SYSTEM_CONFIG_NAME_WEIXIN_CITY_GOODS_TAG = "weixin_city_goods_tag"; //设置微信活动参数goods_tag
    public static String SYSTEM_CONFIG_NAME_AGENT_SELECT_INFO = "agent_select_info"; //受理商选择配置信息
    public static String SYSTEM_CONFIG_NAME_TRADE_PARAMS_KEY_ACTIVITY_KEY = "trade_params_key_activity_key"; //交易参数与活动号的key的映射关系
    public static String SYSTEM_CONFIG_NAME_PROVIDER_CHANNELS = "provider_channels"; //provider与渠道信息
    public static String SYSTEM_CONFIG_NAME_PROVIDER_GROUP_NOS_FOR_ENROLL = "provider_group_nos_for_enroll"; //provider需要入网到威富通的大商户号
    public static String SYSTEM_CONFIG_NAME_HUABEI_PARAMS = "huabei_params"; // 花呗参数信息

    public static List<String> BYPASS_USE_FIELDS = Arrays.asList(TransactionParam.FEE_RATE, TransactionParam.FEE_RATE_TAG, TransactionParam.LADDER_FEE_RATES, TransactionParam.LADDER_FEE_RATE_TAG);

    public static String TERMINAL_SECRET_REDIS_KEY = "terminal_secret:";
}
