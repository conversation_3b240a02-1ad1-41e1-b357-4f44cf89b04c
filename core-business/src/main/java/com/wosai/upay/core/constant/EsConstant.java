package com.wosai.upay.core.constant;

import okhttp3.MediaType;

/**
 * <AUTHOR> by wkx
 * @date 2018/3/2
 **/
public class EsConstant {

    public static final String PREFIX="prefix";

    public static final String WILDCARD="wildcard";

    public static final String MATCH="match";

    public static final String MATCH_PHRASE = "match_phrase";

    public static final String TERM="term";

    public static final String TERMS="terms";

    public static final String LTE="lte";

    public static final String LT="lt";

    public static final String GTE="gte";

    public static final String GT="gt";

    public static final String SIZE="size";

    public static final String FROM="from";

    public static final String SORT="sort";

    public static final String QUERY="query";

    public static final String RANGE="range";

    public static final String _SOURCE="_source";

    public static final String HITS="hits";

    public static final String _SCROLL_ID="_scroll_id";

    public static final String SCROLL_ID="scroll_id";

    public static final String SCROLL="scroll";

    public static final String AUTHORIZATION="Authorization";

    public static final String MUST = "must";

    public static final String BOOL = "bool";

    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

}
