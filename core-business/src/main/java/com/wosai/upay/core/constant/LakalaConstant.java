package com.wosai.upay.core.constant;

import com.wosai.data.util.CollectionUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by xuchmao on 16/6/18.
 */
public class LakalaConstant {
    public static final String FUNCOD_CREATE_MERCHANT = "6001";
    public static final String FUNCOD_UPDATE_MERCHANT = "6002";
    public static final String FUNCOD_QUERY_BANK = "6004";
    public static final String FUNCOD_QUERY_CONTRACT = "6005";
    public static final String FUNCOD_QUERY_MERCHANT = "6006";
    public static final String COMPORG_CODE = "WSST";
    public static final String SECRET = "";
    public static final String CHARSET_GBK = "GBK";
    public static final String CHARSET_UTF = "UTF-8";
    public static final String RESP_SUCC_CODE = "000000";
    public static final String WOSAI_MCCCODE = "9001";
    public static final String PUBLIC_ACCOUNT = "57";
    public static final String PRIVATE_ACCOUNT = "58";
    public static final String SQB_MERCHANT_PROVINCE= "992900";
    public static final String SQB_MERCHANT_CITY= "2900";
    public static final String SQB_MERCHANT_COUNTRY= "290005";
    public static final String SQB_MERCHANT_ADDRESS= "上海市中江路";
    public static final String SQB_MERCHANT_CHANNELTYPE = "TP_MERCHANT";
    public static final String SQB_MERCHANT_POSTYPE = "WECHAT_PAY";
    public static final String SQB_MERCHANT_PHONE= "************";
    public static final String CONTRACT_SUCCESS = "SUCCESS";
    public static final String CONTRACT_APPLYING = "APPLYING";
    public static final String CONTRACT_FAILURE = "FAILURE";
    public static final String CONTRACT_UNKNOWN = "UNKNOWN";
    public static final String CONTRACT_NOT = "NOT_CONTRACT";
    public static final String TP_MERCHANT = "TP_MERCHANT";
    public static final String TP_PERSONAL = "TP_PERSONAL";
    public static final String DEFAULT_UNION_PAY_RATE = "0.006";
    
    public static final String WECHAT_PAY_FEE = "WECHAT_PAY_FEE";
    public static final String ALIPAY_WALLET_FEE = "ALIPAY_WALLET_FEE";
    public static final String UNIONPAY_WALLET_DEBIT_FEE = "UNIONPAY_WALLET_DEBIT_FEE";
    public static final String UNIONPAY_WALLET_CREDIT_FEE = "UNIONPAY_WALLET_CREDIT_FEE";
    
    public static final String SQB_CR_LICENSE_NO = "***************";
    public static final String SQB_MERCHANT_ACCOUNT_NO = "*********************";
    public static final String SQB_MERCHANT_ACCOUNT_HOLDER = "苏州喔噻互联网有限公司";
    public static final String SQB_MERCHANT_OPENING_BANK_NO = "************";
    public static final String SQB_MERCHANT_CLEARING_BANK_NO = "************";
    public static final String SQB_MERCHANT_OPENING_BANK_NAME = "中国光大银行苏州工业园区支行";

    public static final String SQB_LICENSE_NAME = "喔噻";
    public static final String SQB_LICENSE_NO = "*****************";

    public static final String LKL_COMPORTCODE = "WSJG";
    public static final String LKL_SECRET = "";

    public static final List<String> QUERY_BANK_SIGNED_COLUMS = Arrays.asList(
            LakalaBusinessFileds.COMP_ORG_CODE);

    public static final List<String> CREATE_MERCHANT_SIGNED_COLUMS = Arrays.asList(
            LakalaBusinessFileds.COMP_ORG_CODE,
            LakalaBusinessFileds.CHANNEL_TYPE,
            LakalaBusinessFileds.MER_LICENSE_NO,
            LakalaBusinessFileds.PROVINCE_CODE,
            LakalaBusinessFileds.CITY_CODE,
            LakalaBusinessFileds.COUNTY_CODE,
            LakalaBusinessFileds.CR_LICENSE_NO,
            LakalaBusinessFileds.CONTACT_MOBILE,
            LakalaBusinessFileds.OPENING_BANK_NO,
            LakalaBusinessFileds.CLEARING_BANK_NO,
            LakalaBusinessFileds.ACCOUNT_NO,
            LakalaBusinessFileds.MCC_CODE,
            LakalaBusinessFileds.TERM_NUM);

    public static final List<String> UPDATE_MERCHANT_SIGNED_COLUMS = Arrays.asList(
            LakalaBusinessFileds.COMP_ORG_CODE,
            LakalaBusinessFileds.SHOP_NO);

    public static final List<String> QUERY_MERCHANT_SIGNED_COLUMS = Arrays.asList(
            LakalaBusinessFileds.COMP_ORG_CODE);

    public static final List<String> QUERY_CONTRACT_SIGNED_COLUMS = Arrays.asList(
            LakalaBusinessFileds.COMP_ORG_CODE,
            LakalaBusinessFileds.CONTRACT_ID);
    
    public static final List<Map> ATTACHMENTS = Arrays.asList(
            CollectionUtil.hashMap(LakalaBusinessFileds.ATTACHMENTS_NAME, "WOSAI", LakalaBusinessFileds.ATTACHMENTS_KIND, "ICP_ATTACH", LakalaBusinessFileds.ATTACHMENTS_TYPE, "OTHERS")
    );

    public static final Map<String,String> MERCHANT_CITY_CODE = CollectionUtil.hashMap(
            "北京","991000|1000|100001",
            "上海","992900|2900|290005",
            "天津","991100|1100|110001",
            "重庆","996900|6900|6901",
            "黑龙江","2600|2610|982610",
            "吉林","2400|2410|982410",
            "辽宁","2200|2210|982210",
            "内蒙","1900|1910|981910",
            "宁夏","8700|8710|988710",
            "甘肃","8200|8210|988210",
            "新疆","8800|8810|988810",
            "青海","8500|8510|988510",
            "西藏","7700|7710|987710",
            "四川","6500|6510|986510",
            "贵州","7000|7010|987010",
            "云南","7300|7310|987310",
            "陕西","7900|7910|987910",
            "山西","1600|1610|981610",
            "河北","1200|1210|981210",
            "山东","4500|4510|984510",
            "河南","4900|4910|984910",
            "安徽","3600|3610|983610",
            "江苏","3000|3010|983010",
            "湖北","5200|5210|985210",
            "湖南","5500|5510|985510",
            "江西","4200|4210|984210",
            "浙江","3300|3310|983310",
            "福建","3900|3910|983910",
            "广东","5800|5810|985810",
            "广西","6100|6110|986110",
            "海南","6400|6410|986410"
//            "台湾","",
//            "香港","",
//            "澳门",""
// 拉卡拉给的文件中没有台湾 香港 澳门的省市编码
            );
}
