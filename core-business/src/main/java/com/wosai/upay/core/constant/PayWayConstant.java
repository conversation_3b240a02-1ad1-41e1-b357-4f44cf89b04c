package com.wosai.upay.core.constant;

import java.util.Arrays;
import java.util.List;

import static com.wosai.upay.core.meta.Payway.*;


/**
 * <AUTHOR>
 * @description
 * @date 2024-06-13
 */

public class PayWayConstant {
    /**
     * 三方通道(间连间清通道)需要检查的payWay集合
     */
    private static final List<Integer> THIRD_CLEARANCE_PROVIDER_OF_PAYWAY_LIST = Arrays.asList(
            ALIPAY2.getCode(),
            WEIXIN.getCode(),
            UNIONPAY.getCode(),
            BANKCARD.getCode()
    );

    /**
     * 判断是否需要检查该payWay
     *
     * @param payWay
     * @return
     */
    public static boolean isNeedCheckPayWayOfThirdClearanceProvider(Integer payWay) {
        if (null == payWay) {
            return false;
        }
        return THIRD_CLEARANCE_PROVIDER_OF_PAYWAY_LIST.contains(payWay);
    }
}
