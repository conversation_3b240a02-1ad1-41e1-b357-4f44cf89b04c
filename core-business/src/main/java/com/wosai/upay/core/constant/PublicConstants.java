package com.wosai.upay.core.constant;

import com.google.common.collect.ImmutableMap;

import java.util.Map;

public interface PublicConstants {

    String NULL_STR = "null";

    //通过门店号或终端号获取商户号
    String TETMINAL_SN_OR_WOSAL_STORE_ID_MERCHANT_SN_EHCACHE = "getMerchantSnByWosaiStoreIdOrTerminalSn";

    String VENDOR_APP_INFO_BY_ID = "getVendorAppMinimalInfoById";

    String VENDOR_APP_INFO_BY_APP_ID = "getVendorAppMinimalInfoByAppId";

    String VENDOR_INFO_BY_ID = "getVendorMinimalInfoById";

    String VENDOR_INFO_BY_SN = "getVendorMinimalInfoBySn";

    String SOLICITOR_INFO_BY_ID = "getSolicitorMinimalInfoById";

    String SOLICITOR_INFO_BY_SN = "getSolicitorMinimalInfoBySn";

    String VENDOR_APP_APPIDS = "getVendorAppAppIds";

    //交易基本参数
    String UPAY_BASIC_PARAM = "basicParam";

    String FAKE = "fake";

    //全部交易参数
    String UPAY_ALL_PARAM = "allParam";
    
    //商户直连参数
    String UPAY_FORMAL = "upayFormal";


    //eh cache 分隔符
    String EHCACHE_SEPARATOR = "::";

    //redis cache 分隔符
    String REDIS_CACHE_SEPARATOR = "_";

    String PARAM_WOSAI_STORE_ID = "wosaiStoreId";

    String PARAM_TERMINAL_SN = "terminalSn";

    String PARAM_PAYWAY = "payway";

    String PARAM_SUBPAYWAY = "subPayway";
    
    String PARAM_TRADEAPP = "tradeApp";


    String REDIS_CACHE_UPY_BASIC_PARAM_KEY = "redisCacheUpyBasicParamKey";

    String BASIC_FIELD_KEY = "basicFieldKey";

    //新的交易参数查询开关
    String USED_NEW_GET_PARAM_SWITCH = "USED_NEW_GET_PARAM_SWITCH";

    //基本交易参数限流参数名称
    String UPAY_BASIC_RATE_LIMITER_PARAM = "UPAY_BASIC_RATE_LIMITER_PARAM";

    //全交易参数限流参数名称
    String UPAY_ALL_RATE_LIMITER_PARAM = "UPAY_ALL_RATE_LIMITER_PARAM";

    //默认交易参数
    String DEFAULT_RATE_LIMITER_PARAM = "{\"rateLimiterType\":\"TOKEN_BUCKET\",\"threshold\":100,\"timeout\":3000,\"unit\":\"MILLISECONDS\"}";

    String GET_PARAMS_SEMAPHORE_KEY = "get_params_semaphore";  // 获取交易参数限流配置（信号量-固定线程）
    String SEMAPHORE_THRESHOLD = "threshold";   // 线程个数
    String SEMAPHORE_TIMEOUT = "timeout";       // 超时时间
    String GET_PARAMS_MEMORY_CACHE_KEY = "get_params_memory_cache"; // 获取交易参数本地缓存配置
    String MEMORY_CACHE_MAX_SIZE = "max_size";  // 本地缓存最大值
    String MEMORY_CACHE_EXPIRE_SECONDS = "expire_seconds";  // 缓存过期时间，单位：秒
    
    String HUABEI_PARAM = "huabei_params:";
    String FQ_STATUS_FORMAT = "fq_status:%s:%d";
    //收单机构业务状态
    String PROVIDER_BIZ_STATUS = "provider_biz_status:%d:%s";
    //限流参数
    Map<String,String> RATE_LIMITER_PARAM_MAP = new ImmutableMap.Builder<String, String>()
            .put(UPAY_BASIC_RATE_LIMITER_PARAM, UPAY_BASIC_PARAM)
            .put(UPAY_ALL_RATE_LIMITER_PARAM, UPAY_ALL_PARAM)
            .build();;

    String SWITCH_MERCHANT_BYPASS = "switche_merchant_bypass"; // 备用通道是否存在配置

    String REDIS_CACHE_SWITCH_MCH_TIME = "switch_mch_time:"; //商户小微升级切换时间点

}
