package com.wosai.upay.core.constant;

import java.util.Arrays;
import java.util.List;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/6/15.
 */
public class WftConstant {

    public static final String IDENTITY_CARD = "1";
    public static final String PASSPORT = "2";
    public static final String BANKACCOUNT_PUBLIC = "1";
    public static final String BANKACCOUNT_PRIVATE = "2";
    public static final String SQB_MERCHANT_PRINCIPAL = "喔噻";
    public static final String SQB_MERCHANT_PROVINCE = "090000";
    public static final String SQB_MERCHANT_CITY = "090900";
    public static final String SQB_MERCHANT_COUNTY = "090920";

    public static final String SQB_MERCHANT_ADDRESS = "上海市中江路879弄天地软件园4号楼2楼";
    public static final String SQB_MERCHANT_MOBILE = "***********";
    public static final String SQB_MERCHANT_INDUSTRY = "220";
    public static final String CHARSET_UTF8 = "UTF-8";

    public static final String RESP_CODE_SUCCESS = "T";
    public static final String RESP_CODE_FAIL = "F";
    public static final String RESP_ERROR_CODE_INVALID_PARAM = "S0001";
    public static final String RESP_ERROR_CODE_SIGN_FAIL = "S0002";
    public static final String RESP_ERROR_CODE_SERVICE_NOT_AUTH = "S0003";
    public static final String RESP_ERROR_CODE_PARTNER_NOT_EXIST = "S0005";
    public static final String RESP_ERROR_CODE_INVALID_SERVICE_NAME = "S0006";
    public static final String RESP_ERROR_CODE_SYSTEM_ERROR = "S9999";


    public static final String SQB_ACCOUNT_NO = "*****************";
    public static final String SQB_ACCOUNT_HOLDER = "苏州喔噻互联网科技有限公司";
    public static final String SQB_ACCOUNT_OPENING_BANK_NO = "************";
    public static final String SQB_ACCOUNT_CLEARING_BANK_NO = "************";
    public static final String SQB_ACCOUNT_BANK_ID = "7";
    public static final String SQB_ACCOUNT_BANK_NAME = "中国光大银行";
    public static final String SQB_ACCOUNT_BRANCH_NAME = "中国光大银行股份有限公司上海大宁支行";

    public static final String DATA_TYPE_XML = "xml";
    public static final String DATA_TYPE_JSON = "json";

    public static final String FUNCTION_MERCHANT_CREATE = "two_clean_store_add";
    public static final String FUNCTION_UPLOAD_PIC = "pic_upload";
    public static final String FUNCTION_MCH_PARTNER_SEARCH = "mch_partner_search";

    public static final List<String> CIB_PAYWAY_LIST = Arrays.asList(
//            "pay.alipay.micropay",
//            "pay.alipay.micropayv2",
//            "pay.alipay.native",
//            "pay.alipay.nativev2",
//            "pay.alipay.wappay",
//            "pay.alipay.wappayv2",
//            "pay.alipay.webpay",
//            "pay.jdpay.micropay",
//            "pay.jdpay.native",
//            "pay.qq.jspay",
//            "pay.qq.micropay",
//            "pay.qq.proxy.micropay",
//            "pay.weixin.jspay",
//            "pay.weixin.micropay",
//            "pay.weixin.micropay.intl",
//            "pay.weixin.native",
//            "pay.weixin.native.intl",
//            "pay.weixin.proxy.micropay.intl",
//            "pay.weixin.scancode",
//            "pay.weixin.wappay",
            "pay.weixin.jspay",
            "pay.weixin.micropay",
            "pay.weixin.native"
    );

    public static final List<String> CITIC_PAYWAY_LIST = Arrays.asList(
            "pay.alipay.nativev3",
            "pay.alipay.micropayv3",
            "pay.alipay.jspayv3",
            "pay.weixin.jspay",
            "pay.weixin.micropay",
            "pay.weixin.native"
    );

    public static final List<String> PAY_WAY_LIST = Arrays.asList(
            "pay.alipay.nativev3",
            "pay.alipay.micropayv3",
            "pay.alipay.jspayv3"
    );
}
