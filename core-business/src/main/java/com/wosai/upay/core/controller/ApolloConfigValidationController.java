package com.wosai.upay.core.controller;

import com.google.common.collect.Sets;
import com.wosai.core.crypto.client.CryptoClient;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.SensitiveProperties;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.util.EsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Controller
@RequestMapping("")
public class ApolloConfigValidationController {
    public static final Logger log = LoggerFactory.getLogger(ApolloConfigValidationController.class);
    @Autowired
    SensitiveProperties sensitiveProperties;

    @Autowired
    CryptoClient cryptoClient;

    @RequestMapping("/testApolloConfig")
    @ResponseBody
    public String testApolloConfig(@RequestParam("key") String key) {
        if (!key.equals("keycnadncpanvvprgnqpifbpiqqnfnqifjnpqiwunfp")) {
            return "密钥错误";
        }
        // 测试es allLikeConvertCriteria
        EsUtil.allLikeConvertCriteria(new EsUtil.QueryInfo("merchant", "name"), "两元店商户", "id");
        log.info("allLikeConvertCriteria 测试完毕");

        // 测试 cryptoClient
        log.info("AccessSecret:" + cryptoClient.getAccessSecret());

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sensitiveProperties.toString().getBytes(StandardCharsets.UTF_8));
            return new BigInteger(1, md.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密失败：" + e.getMessage(), e);
            return "MD5加密错误";
        }
    }

    private Set<String> getMerchantIds(Map queryFilter) {
        Set<String> merchantIds = Sets.newHashSet();
        //兼容老业务
        String merchantId = BeanUtil.getPropString(queryFilter, Store.MERCHANT_ID);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(merchantId)) {
            merchantIds.add(merchantId);
        }
        //新业务都用列表形式
        List<String> merchantIds0 = (List<String>) BeanUtil.getProperty(queryFilter, "merchant_ids");
        if (CollectionUtils.isNotEmpty(merchantIds0)) {
            merchantIds.addAll(merchantIds0);
        }
        return merchantIds;
    }
}
