package com.wosai.upay.core.controller;

import java.util.Collections;
import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;


@Controller
@RequestMapping("/example")
public class ExampleController 
{

    @RequestMapping(value="/echo", method=RequestMethod.GET, produces="application/json")
    @ResponseBody
    public Map<String, String> say() {
        return Collections.singletonMap("message", "Hello, World!");
    }

}
