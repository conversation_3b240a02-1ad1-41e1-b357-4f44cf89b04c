package com.wosai.upay.core.controller;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
 
@Controller
@RequestMapping("")
public class MetricsController {

    // https://stackoverflow.com/a/7672987
    @RequestMapping(method = RequestMethod.GET, value = "/metrics", produces = {"text/plain;version=0.0.4;charset=utf-8"})
    public void endpoint(HttpServletResponse response) {
        try {
            MetricsManager.scrape(response.getWriter());
        } catch (IOException ioEx) {
            // do nothing
        }
    }
}