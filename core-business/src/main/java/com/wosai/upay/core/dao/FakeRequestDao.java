package com.wosai.upay.core.dao;

import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.Filter;
import com.wosai.upay.core.util.FakeRequestUtil;

import java.util.Collection;
import java.util.Map;

public class FakeRequestDao implements Dao<Map<String, Object>> {

    private Dao<Map<String, Object>> delegate;

    private Dao<Map<String, Object>> fakeDelegate;

    private Dao<Map<String, Object>> getDelegate() {
        if (FakeRequestUtil.isFakeRequest()) {
            return fakeDelegate;
        } else {
            return delegate;
        }
    }

    public FakeRequestDao(Dao<Map<String, Object>> delegate, Dao<Map<String, Object>> fakeDelegate) {
        this.delegate = delegate;
        this.fakeDelegate = fakeDelegate;
    }

    @Override
    public void updatePart(Map<String, Object> map) {
        getDelegate().updatePart(map);
    }

    @Override
    public Map<String, Object> getPart(String s, Collection<String> collection) {
        return getDelegate().getPart(s, collection);
    }

    @Override
    public Filter<Map<String, Object>> filter(Criteria criteria, Collection<String> collection) {
        return getDelegate().filter(criteria, collection);
    }

    @Override
    public Map<String, Object> findAndModify(Criteria criteria, Map<String, Object> map) {
        return getDelegate().findAndModify(criteria, map);
    }

    @Override
    public Map<String, Object> findAndModify(Criteria criteria, Map<String, Object> map, Map<String, Number> map1) {
        return getDelegate().findAndModify(criteria, map, map1);
    }

    @Override
    public void save(Map<String, Object> stringObjectMap) {
        getDelegate().save(stringObjectMap);
    }

    @Override
    public void delete(String s) {
        getDelegate().delete(s);
    }

    @Override
    public void softDelete(String s) {
        getDelegate().softDelete(s);
    }

    @Override
    public Map<String, Object> get(String s) {
        return getDelegate().get(s);
    }

    @Override
    public Filter<Map<String, Object>> filter(Criteria criteria) {
        return getDelegate().filter(criteria);
    }
}
