package com.wosai.upay.core.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-04-25
 */
public abstract class AbstractDataBusBiz {

    private static Logger logger = LoggerFactory.getLogger(AbstractDataBusBiz.class);

    /**
     * 获取表名
     *
     * @return
     */
    protected abstract String getTableName();

    /**
     * 获取topic
     *
     * @return
     */
    protected abstract String getTopic();

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("dataBusKafkaProducer")
    private KafkaProducer dataBusKafkaProducer;

    private static SerializeConfig serializeConfig = new SerializeConfig();

    static {
        serializeConfig.setPropertyNamingStrategy(PropertyNamingStrategy.SnakeCase);
    }


    private ExecutorService kafkaSendPool = new ThreadPoolExecutor(1, 5,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(100));


    protected void saveEvent(AbstractEvent event) {
        if (ApolloConfigurationCenterUtil.databusDirectToKafka()) {
            try {
                AvroEventEntry entry = new AvroEventEntry();
                long currentTimeMillis = System.currentTimeMillis();
                entry.setSeq(currentTimeMillis);
                entry.setTs(currentTimeMillis);
                entry.setEvent(ByteBuffer.wrap(JSON.toJSONBytes(event, serializeConfig)));
                sendToDataBus(getTopic(), entry);
            } catch (Exception e) {
                logger.error("sendToDataBus fail save to DB {}", event, e);
                jdbcTemplate.update("INSERT INTO " + getTableName() + " (`ts`, `event`) VALUES (?, ?)", System.currentTimeMillis(), JSON.toJSONString(event, serializeConfig));
            }
        } else {
            jdbcTemplate.update("INSERT INTO " + getTableName() + " (`ts`, `event`) VALUES (?, ?)", System.currentTimeMillis(), JSON.toJSONString(event, serializeConfig));
        }
    }

    private void sendToDataBus(final String topic, final Object message) {
        boolean isActive = TransactionSynchronizationManager.isActualTransactionActive();
        if (!isActive) {
            publish(topic, message);
            return;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCompletion(int status) {
                if (status == TransactionSynchronization.STATUS_COMMITTED) {
                    try {
                        publish(topic, message);
                    } catch (Exception e) {
                        logger.error("sendToDataBus error {} {}", topic, message, e);
                        throw e;
                    }
                }
            }
        });
    }

    private void publish(final String topic, final Object message) {
        kafkaSendPool.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    ProducerRecord record = new ProducerRecord<>(topic, null, message);
                    dataBusKafkaProducer.send(record, new Callback() {
                        @Override
                        public void onCompletion(RecordMetadata recordMetadata, Exception e) {
                            if (e != null) {
                                logger.error("sendToDataBus error {} {}", topic, message, e);
                            } else {
                                logger.info("sendToDataBus success {} {}", topic, message);
                            }
                        }
                    });
//                    dataBusKafkaProducer.flush();
                } catch (Exception ex) {
                    logger.error("sendToDataBus error {} {}", topic, message, ex);
                }
            }
        });
    }

    protected <T> T mapToBean(Map map, Class<T> clazz) {
        return JSON.parseObject(JSON.toJSONString(map), clazz);
    }

}
