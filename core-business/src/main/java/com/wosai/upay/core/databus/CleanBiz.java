package com.wosai.upay.core.databus;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.util.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 清除过期的事件
 *
 * <AUTHOR>
 * @date 2019-04-26
 */
@Component
@Slf4j
public class CleanBiz {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private List<AbstractDataBusBiz> dataBusBizs;

    private static Config config = ConfigService.getAppConfig();

    @Scheduled(cron = "0 0 2 * * ?")
    public void clean() {
        if (redisLock.lock("cb-databus-clean", 60)) {
            log.info("clean expired event");
            long expiredTime = config.getLongProperty("event-expired-time", 7 * 24 * 60 * 60 * 1000L);
            long endTime = System.currentTimeMillis() - expiredTime;
            for (AbstractDataBusBiz dataBusBiz : dataBusBizs) {
                doClean(dataBusBiz.getTableName(), endTime);
            }
        }
    }

    private void doClean(String tableName, long endTime) {
        log.info("begin clean {}", tableName);
        long nextSeq = 0L;
        try {
            Map firstEvent = jdbcTemplate.queryForMap("SELECT seq, ts FROM " + tableName + " ORDER BY seq ASC limit 1");

            nextSeq = BeanUtil.getPropLong(firstEvent, "seq") + 1000;

            Map nextEvent;

            while (true) {
                nextEvent = jdbcTemplate.queryForMap("SELECT seq, ts FROM " + tableName + " WHERE seq = ?", nextSeq);

                if (BeanUtil.getPropLong(nextEvent, "ts", System.currentTimeMillis()) >= endTime) {
                    break;
                }
                jdbcTemplate.update("DELETE FROM " + tableName + " WHERE seq < ?", nextSeq);
                log.info("clean {} seq less than {}", tableName, nextSeq);
                nextSeq += 1000;
            }
        } catch (EmptyResultDataAccessException e) {
            log.info("query {} EmptyResultDataAccessException, nextSeq {}", tableName, nextSeq);
        } catch (IncorrectResultSizeDataAccessException e) {
            log.info("query {} IncorrectResultSizeDataAccessException, nextSeq {}", tableName, nextSeq);
        } catch (Exception e) {
            log.error("clean {} error", tableName, e);
        }

        log.info("end clean {}", tableName);
    }
}
