package com.wosai.upay.core.databus;


import com.wosai.data.bean.BeanUtil;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.merchant.config.Field;
import com.wosai.databus.event.merchant.config.MerchantAppConfigChangeEvent;
import com.wosai.databus.event.merchant.config.MerchantConfigChangeEvent;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * Date 2019/9/29 9:57 上午
 **/
@Component
public class MerchantConfigDataBusBiz extends AbstractDataBusBiz {

    @Value("${topic.databus.merchant_config}")
    private String topic;

    @Override
    protected String getTableName() {
        return "merchant_config_event_log";
    }

    @Override
    protected String getTopic() {
        return topic;
    }

    public void feeRateChange(Map before, Map after) {
        if (CollectionUtils.isEmpty(before)) {
            return;
        }
        FeeRateEvent feeRateEvent = new FeeRateEvent();
        feeRateEvent.setMerchantId(BeanUtil.getPropString(after, MerchantConfig.MERCHANT_ID));
        feeRateEvent.setPayway(BeanUtil.getPropInt(after, MerchantConfig.PAYWAY));

        Field b2c = new Field(before.get("b2c_fee_rate"), after.get("b2c_fee_rate"));
        Field c2b = new Field(before.get("c2b_fee_rate"), after.get("c2b_fee_rate"));
        Field wap = new Field(before.get("wap_fee_rate"), after.get("wap_fee_rate"));
        Field mini = new Field(before.get("mini_fee_rate"), after.get("mini_fee_rate"));
        if (b2c.validateChange() || c2b.validateChange() || wap.validateChange() || mini.validateChange()) {
            feeRateEvent.setB2cFeeRate(b2c);
            feeRateEvent.setC2bFeeRate(c2b);
            feeRateEvent.setWapFeeRate(wap);
            feeRateEvent.setMiniFeeRate(mini);
            saveEvent(feeRateEvent);
        } else {
            //阶梯费率处理
            Map<String, Object> beforeParams = MapUtil.getMap(before, MerchantConfig.PARAMS, Collections.emptyMap());
            Map<String, Object> afterParams = MapUtil.getMap(after, MerchantConfig.PARAMS, Collections.emptyMap());
            int beforeStatus = BeanUtil.getPropInt(beforeParams, MerchantConfig.LADDER_STATUS);
            int afterStatus = BeanUtil.getPropInt(afterParams, MerchantConfig.LADDER_STATUS);
            Object beforeFeeRate = beforeStatus == MerchantConfig.STATUS_OPENED ? beforeParams.get(MerchantConfig.LADDER_FEE_RATES) : null;
            Object afterFeeRate = afterStatus == MerchantConfig.STATUS_OPENED ? afterParams.get(MerchantConfig.LADDER_FEE_RATES) : null;
            if (!Objects.equals(JsonUtil.toJsonStr(beforeFeeRate), JsonUtil.toJsonStr(afterFeeRate))) {
                feeRateEvent.setB2cFeeRate(new Field(ternaryOperator(beforeFeeRate, before.get("b2c_fee_rate")), ternaryOperator(afterFeeRate, after.get("b2c_fee_rate"))));
                feeRateEvent.setC2bFeeRate(new Field(ternaryOperator(beforeFeeRate, before.get("c2b_fee_rate")), ternaryOperator(afterFeeRate, after.get("c2b_fee_rate"))));
                feeRateEvent.setWapFeeRate(new Field(ternaryOperator(beforeFeeRate, before.get("wap_fee_rate")), ternaryOperator(afterFeeRate, after.get("wap_fee_rate"))));
                feeRateEvent.setMiniFeeRate(new Field(ternaryOperator(beforeFeeRate, before.get("mini_fee_rate")), ternaryOperator(afterFeeRate, after.get("mini_fee_rate"))));
                saveEvent(feeRateEvent);
            }
        }
    }

    public void merchantConfigChange(Map before, Map after) {
        saveEvent(new MerchantConfigChangeEvent(before, after));
    }

    /**
     * 发送多业务的商户配置变更事件
     *
     * @param before
     * @param after
     */
    public void merchantAppConfigChange(Map before, Map after) {
        saveEvent(new MerchantAppConfigChangeEvent(before, after));
    }

    private Object ternaryOperator(Object first, Object other) {
        return first != null ? first : other;
    }


}
