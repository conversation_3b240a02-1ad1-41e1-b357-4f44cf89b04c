package com.wosai.upay.core.databus;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.databus.event.merchant.DataBusMerchant;
import com.wosai.databus.event.merchant.basic.MerchantBasicInsertEvent;
import com.wosai.databus.event.merchant.basic.MerchantBasicStatusChangeEvent;
import com.wosai.databus.event.merchant.basic.MerchantBasicUpdateEvent;
import com.wosai.upay.core.model.Merchant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-04-25
 */
@Component
public class MerchantDataBusBiz extends AbstractDataBusBiz {

    @Value("${topic.databus.merchant}")
    private String topic;

    @Override
    protected String getTableName() {
        return "merchant_event_log";
    }

    @Override
    protected String getTopic() {
        return topic;
    }

    public void insert(Map merchant) {
        if (WosaiMapUtils.isNotEmpty(merchant)) {
            MerchantBasicInsertEvent event = new MerchantBasicInsertEvent();
            event.setMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
            event.setMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
            event.setData(mapToBean(merchant, DataBusMerchant.class));

            saveEvent(event);
        }
    }

    public void update(Map before, Map after) {
        if (WosaiMapUtils.isNotEmpty(before)) {
            MerchantBasicUpdateEvent event = new MerchantBasicUpdateEvent();
            event.setMerchantId(BeanUtil.getPropString(before, DaoConstants.ID));
            event.setMerchantSn(BeanUtil.getPropString(before, Merchant.SN));
            event.setBefore(mapToBean(before, DataBusMerchant.class));
            event.setAfter(mapToBean(after, DataBusMerchant.class));

            saveEvent(event);
        }
    }

    public void statusChange(Map merchant, int preStatus, int status) {
        if (WosaiMapUtils.isNotEmpty(merchant)) {
            MerchantBasicStatusChangeEvent event = new MerchantBasicStatusChangeEvent();
            event.setMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
            event.setMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
            event.setPreStatus(preStatus);
            event.setStatus(status);

            saveEvent(event);
        }
    }

}
