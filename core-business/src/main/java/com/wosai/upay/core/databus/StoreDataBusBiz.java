package com.wosai.upay.core.databus;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.databus.event.store.DataBusStore;
import com.wosai.databus.event.store.basic.StoreBasicDeleteEvent;
import com.wosai.databus.event.store.basic.StoreBasicInsertEvent;
import com.wosai.databus.event.store.basic.StoreBasicStatusChangeEvent;
import com.wosai.databus.event.store.basic.StoreBasicUpdateEvent;
import com.wosai.upay.core.model.Store;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-04-25
 */
@Component
public class StoreDataBusBiz extends AbstractDataBusBiz {

    @Value("${topic.databus.store}")
    private String topic;

    @Override
    protected String getTableName() {
        return "store_event_log";
    }

    @Override
    protected String getTopic() {
        return topic;
    }

    public void insert(Map store) {
        if (WosaiMapUtils.isNotEmpty(store)) {
            StoreBasicInsertEvent event = new StoreBasicInsertEvent();
            event.setStoreId(BeanUtil.getPropString(store, DaoConstants.ID));
            event.setStoreSn(BeanUtil.getPropString(store, Store.SN));
            event.setData(mapToBean(store, DataBusStore.class));

            saveEvent(event);
        }
    }


    public void update(Map before, Map after) {
        if (WosaiMapUtils.isNotEmpty(before)) {
            StoreBasicUpdateEvent event = new StoreBasicUpdateEvent();
            event.setStoreId(BeanUtil.getPropString(before, DaoConstants.ID));
            event.setStoreSn(BeanUtil.getPropString(before, Store.SN));
            event.setBefore(mapToBean(before, DataBusStore.class));
            event.setAfter(mapToBean(after, DataBusStore.class));

            saveEvent(event);
        }
    }

    public void statusChange(Map store, int preStatus, int status) {
        if (WosaiMapUtils.isNotEmpty(store)) {
            StoreBasicStatusChangeEvent event = new StoreBasicStatusChangeEvent();
            event.setStoreId(BeanUtil.getPropString(store, DaoConstants.ID));
            event.setStoreSn(BeanUtil.getPropString(store, Store.SN));
            event.setPreStatus(preStatus);
            event.setStatus(status);

            saveEvent(event);
        }
    }

}
