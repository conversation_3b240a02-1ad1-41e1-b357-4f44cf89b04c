package com.wosai.upay.core.databus;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.databus.event.terminal.DataBusTerminal;
import com.wosai.databus.event.terminal.basic.TerminalBasicDeleteEvent;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.databus.event.terminal.basic.TerminalBasicStatusChangeEvent;
import com.wosai.databus.event.terminal.basic.TerminalBasicUpdateEvent;
import com.wosai.upay.core.model.Terminal;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-04-25
 */
@Component
public class TerminalDataBusBiz extends AbstractDataBusBiz {

    @Value("${topic.databus.terminal}")
    private String topic;

    @Override
    protected String getTableName() {
        return "terminal_event_log";
    }

    @Override
    protected String getTopic() {
        return topic;
    }

    public void insert(Map terminal) {
        if (WosaiMapUtils.isNotEmpty(terminal)) {
            TerminalBasicInsertEvent event = new TerminalBasicInsertEvent();
            event.setTerminalId(BeanUtil.getPropString(terminal, DaoConstants.ID));
            event.setTerminalSn(BeanUtil.getPropString(terminal, Terminal.SN));
            event.setData(mapToBean(terminal, DataBusTerminal.class));

            saveEvent(event);
        }
    }

    public void delete(Map terminal) {
        if (WosaiMapUtils.isNotEmpty(terminal)) {
            TerminalBasicDeleteEvent event = new TerminalBasicDeleteEvent();
            event.setTerminalId(BeanUtil.getPropString(terminal, DaoConstants.ID));
            event.setTerminalSn(BeanUtil.getPropString(terminal, Terminal.SN));

            saveEvent(event);
        }
    }

    public void update(Map before, Map after) {
        if (WosaiMapUtils.isNotEmpty(before)) {
            TerminalBasicUpdateEvent event = new TerminalBasicUpdateEvent();
            event.setTerminalId(BeanUtil.getPropString(before, DaoConstants.ID));
            event.setTerminalSn(BeanUtil.getPropString(before, Terminal.SN));
            event.setBefore(mapToBean(before, DataBusTerminal.class));
            event.setAfter(mapToBean(after, DataBusTerminal.class));

            saveEvent(event);
        }
    }

    public void statusChange(Map terminal, int preStatus, int status) {
        if (WosaiMapUtils.isNotEmpty(terminal)) {
            TerminalBasicStatusChangeEvent event = new TerminalBasicStatusChangeEvent();
            event.setTerminalId(BeanUtil.getPropString(terminal, DaoConstants.ID));
            event.setTerminalSn(BeanUtil.getPropString(terminal, Terminal.SN));
            event.setPreStatus(preStatus);
            event.setStatus(status);

            saveEvent(event);
        }
    }

}
