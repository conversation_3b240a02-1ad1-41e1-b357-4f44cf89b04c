package com.wosai.upay.core.datasource;

import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;

/**
 * <AUTHOR>
 * @since 1.0
 */
public class DBContextHolder {

    private static final ThreadLocal<String> contextHolder = new ThreadLocal<>();

    public static String getDbType() {
        String db;
        if ((db = ApolloConfigurationCenterUtil.getChangeToAppointDB()) != null) {
            return db;
        } else {
            db = contextHolder.get();
            return db != null ? db : DataSourceConstant.MASTER;
        }
    }

    public static void setDbType(String dbType) {
        if (!(dbType.equals(DataSourceConstant.MASTER) || dbType.equals(DataSourceConstant.SLAVE))) {
            contextHolder.set(DataSourceConstant.MASTER);
        } else {
            contextHolder.set(dbType);
        }
    }

    public static void clearDbType() {
        contextHolder.set(null);
    }
}
