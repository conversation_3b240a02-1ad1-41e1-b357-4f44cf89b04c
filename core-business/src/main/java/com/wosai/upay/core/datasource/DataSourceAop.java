package com.wosai.upay.core.datasource;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR> by wkx
 * @date 2018/2/2
 **/
@Aspect
@Component
public class DataSourceAop {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataSourceAop.class);

    @Pointcut("@annotation(DataSourceType)")
    public void aspect() {
    }

    @Before("aspect()")
    public void before(JoinPoint point) {
        Object target = point.getTarget();
        String methodName = point.getSignature().getName();
        Class<?> clazz = target.getClass();
        Class<?>[] parameterTypes = ((MethodSignature) point.getSignature()).getMethod().getParameterTypes();
        try {
            Method method = clazz.getMethod(methodName, parameterTypes);
            if (method != null && method.isAnnotationPresent(DataSourceType.class)) {
                DataSourceType dataSource = method.getAnnotation(DataSourceType.class);
                DBContextHolder.setDbType(dataSource.sourceType());
            }
        } catch (Exception e) {
            LOGGER.error("数据源切换切面异常", e);
        }
    }

    @After("aspect()")
    public void after() {
        DBContextHolder.clearDbType();
    }

    @AfterThrowing("aspect()")
    public void afterThrow() {
        DBContextHolder.clearDbType();
    }

}
