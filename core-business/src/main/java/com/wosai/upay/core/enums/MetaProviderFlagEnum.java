package com.wosai.upay.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 通道标志
 * @date 2024-06-06
 */
@Getter
public enum MetaProviderFlagEnum {
    INVALID(-1, "未知"),
    DIRECT(1, "直连"),
    INDIRECT(2, "间连直清"),
    WITHDRAW(3, "间连提现"),
    INDIRECT_OR_WITHDRAW(4, "间连直清或间连提现");

    private final int type;
    private final String remark;

    MetaProviderFlagEnum(int type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public static MetaProviderFlagEnum of(Integer type) {
        if (type == null) {
            return INVALID;
        }
        for (MetaProviderFlagEnum e : MetaProviderFlagEnum.values()) {
            if (type.equals(e.type)) {
                return e;
            }
        }
        return INVALID;
    }

    /**
     * 是否间连提现
     */
    public static boolean isWithdrawFlag(Integer type) {
        MetaProviderFlagEnum typeEnum = of(type);
        return (typeEnum == WITHDRAW) || (typeEnum == INDIRECT_OR_WITHDRAW);
    }
}
