package com.wosai.upay.core.helper;

import avro.shaded.com.google.common.base.Joiner;
import com.wosai.upay.core.constant.PublicConstants;

public class CacheKeyHelper {


    /**
     * 获取ehcache key
     *
     * @param key
     * @return
     */
    public static String getEhCacheKey(Object... key) {
        return Joiner.on(PublicConstants.EHCACHE_SEPARATOR).join(key);
    }


    /**
     * 获取RedisCache 全部交易参数 key
     *
     * @param key
     * @return
     */
    public static String getRedisCacheUpyAllParamKey(String key) {
        return PublicConstants.UPAY_ALL_PARAM + key;
    }

    /**
     * 获取RedisCache 基本交易参数 key
     *
     * @param key
     * @return
     */
    public static String getRedisCacheUpyBaseParamKey(String key) {
        return PublicConstants.UPAY_BASIC_PARAM + key;
    }

    /**
     * 获取RedisCache key
     *
     * @param key
     * @return
     */
    public static String getRedisCacheHashKey(Object... key) {
        return Joiner.on(PublicConstants.REDIS_CACHE_SEPARATOR).join(key);
    }
}
