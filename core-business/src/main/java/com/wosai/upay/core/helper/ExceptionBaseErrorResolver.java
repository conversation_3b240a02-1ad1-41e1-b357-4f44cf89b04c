package com.wosai.upay.core.helper;

import com.fasterxml.jackson.databind.JsonNode;

import com.googlecode.jsonrpc4j.ErrorData;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.wosai.upay.common.constant.ProjectConstant;
import com.wosai.upay.common.exception.CommonException;
import com.wosai.upay.common.exception.RuntimeWithCodeException;

import java.lang.reflect.Method;
import java.util.List;

public enum ExceptionBaseErrorResolver implements ErrorResolver {

    INSTANCE;

    ExceptionBaseErrorResolver() {
        CommonException.resetCurrentProject(ProjectConstant.CORE_BUSINESS_CODE);
    }

    public ErrorResolver.JsonError resolveError(Throwable t, Method method, List<JsonNode> arguments) {
        if (t instanceof RuntimeWithCodeException) {
            RuntimeWithCodeException exception = (RuntimeWithCodeException)t;
            return new ErrorResolver.JsonError(exception.getCode(), t.getMessage(), new MyErrorData(t.getClass().getName(), t.getMessage(), exception.getProject(), exception.getProjectCode()));
        } else {
            return new JsonError(JsonError.ERROR_NOT_HANDLED.code, t.getMessage(), new ErrorData(t.getClass().getName(), t.getMessage()));
        }
    }

    public static class MyErrorData extends ErrorData {

        private String project;
        private int projectCode;

        public MyErrorData(String exceptionTypeName, String message, String project, int projectCode) {
            super(exceptionTypeName, message);
            this.project = project;
            this.projectCode = projectCode;
        }

        public String getProject() {
            return this.project;
        }

        public void setProject(String project) {
            this.project = project;
        }

        public int getProjectCode() {
            return this.projectCode;
        }

        public void setProjectCode(int projectCode) {
            this.projectCode = projectCode;
        }

    }

}
