package com.wosai.upay.core.helper;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.MDC;

import com.wosai.upay.core.constant.CoreConstant;

public class RequestFilter implements Filter{
    private static final List<Function<HttpServletRequest, String>> GET_IP_FUNCTIONS = Arrays.asList(
                (request) -> request.getHeader("X-Original-Forwarded-For"),
                (request) -> request.getHeader("x-forwarded-for"),
                (request) -> request.getHeader("Proxy-Client-IP"),
                (request) -> request.getHeader("WL-Proxy-Client-IP"),
                (request) -> request.getRemoteAddr()
            );
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        try {
            setRequestIpAddress((HttpServletRequest) request);
            chain.doFilter(request, response);
        }finally {
            MDC.remove(CoreConstant.IP);
        }
    }

    @Override
    public void destroy() {
        
    }

    private void setRequestIpAddress(HttpServletRequest request) {
        try {
            String ip = null;
            for (Function<HttpServletRequest, String> fc : GET_IP_FUNCTIONS) {
                ip = fc.apply(request);
                if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                    break;
                }
            }
            int idx;
            if (ip != null && (idx = ip.indexOf(",")) > 0) {
                ip = ip.substring(0, idx);
            }
            MDC.put(CoreConstant.IP, ip);
        } catch (Exception e) {
        }
    }

}
