package com.wosai.upay.core.helper;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.upay.core.exception.CoreDataAccessException;

import java.lang.reflect.Method;


public class SentinelClientFallBack extends JsonRPCFallbackDefine {
    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[]{new JsonRPCMethodFallbackHandler() {
            public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                return ElementMatchers.any();
            }

            @Override
            public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                throw new CoreDataAccessException("请求过于频繁，请稍后再试", exception);
            }
        }};
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return ElementMatchers.any();
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}