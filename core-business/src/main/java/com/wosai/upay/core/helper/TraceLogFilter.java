package com.wosai.upay.core.helper;

import org.slf4j.MDC;
import org.slf4j.Marker;

import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.upay.core.constant.CoreConstant;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;

public class TraceLogFilter extends TurboFilter {

    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String s, Object[] objects, Throwable throwable) {
        MDC.put(CoreConstant.TRACE_ID_KEY, TraceContext.traceId());
        return FilterReply.NEUTRAL;
    }
}
