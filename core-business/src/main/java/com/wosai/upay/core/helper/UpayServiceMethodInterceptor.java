package com.wosai.upay.core.helper;


import com.wosai.core.crypto.exception.BizException;
import com.wosai.data.dao.DaoException;
import com.wosai.data.dao.DaoVersionMismatchException;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.log.*;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.common.util.SpringWebUtil;
import com.wosai.upay.core.common.CoreBusinessIgnoreTranslate;
import com.wosai.upay.core.common.CoreBusinessTranslate;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.service.CacheService;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.core.util.EncryptArgUtils;
import com.wosai.upay.exception.RateLimiterException;
import com.wosai.upay.exception.RequestRejectedException;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.jdbc.UncategorizedSQLException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.ConnectException;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class UpayServiceMethodInterceptor implements MethodInterceptor {

    private static final ThreadLocal<Boolean> coreBusinessIgnoreTranslateFlag = new ThreadLocal<Boolean>(){
      @Override
      protected Boolean initialValue(){
          return false;
      }
    };

    public UpayServiceMethodInterceptor() {
    }

    private static final Logger logger = LoggerFactory.getLogger(UpayServiceMethodInterceptor.class);

    private static final String CANNOT_SET_INCORRECT_VALUE_MATCHES = ".+Incorrect (.+) value: '(.+)' for column '(.+)'.+";
    private static final String CANNOT_SET_INCORRECT_VALUE = "$3属性不能被赋值为'$2'";

    private static final String DATA_OBJECT_NOT_EXISTS = "object id not exists.";

    private static final String UNKNOWN_COLUMN_MATCHES = ".+Unknown column '(.+)' in 'field list'.*";
    private static final String UNKNOWN_COLUMN = "未知属性'$1'";
    private ThreadLocal<Integer> entryCountTl = new ThreadLocal<>();

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        //通过entryCountTl 控制只有入口的service 方法调用才打印日志, 此方法调用其他service不再进行日志打印，减少日志打印量
        Integer entryCount = entryCountTl.get();
        if(entryCount == null){
            entryCount = 0;
        }
        entryCount = entryCount + 1;
        entryCountTl.set(entryCount);

        long before = System.currentTimeMillis();
        Method method = invocation.getMethod();
        String methodName = method.getName();
        Object[] arguments = invocation.getArguments();
        //手机号脱敏实现
        Object[] argumentsCopy = EncryptArgUtils.encrypt(arguments);
        //updateDbCryptoKeyLastPart 方法不记录请求参数，因为这个方法的参数为机密信息
        boolean notLogArgument = method.getName().contains("updateDbCryptoKeyLastPart");
        MethodStartLog startLog = new MethodStartLog(method, notLogArgument ? null : argumentsCopy, JsonRpcCallUtil.getCallHeaderMapFromRequest(SpringWebUtil.getCurrentRequest()));
        if(entryCount == 1){
            logger.trace(LogstashMarkerAppendFileds.append(startLog), "invoking method start");
        }
        boolean success = false;

        Throwable tex = null;
        try {
            // 对@CoreBusinessIgnoreTranslate 注解不进行翻译
            if (method.getAnnotation(CoreBusinessIgnoreTranslate.class) != null && entryCount == 1) {
                coreBusinessIgnoreTranslateFlag.set(true);
            }
            Object result = invocation.proceed();
            success = true;

            // 对get, find系列方法的特定属性进行翻译，层级信息补充
            // Note: @CoreBusinessTranslate 需要加在接口方法上，接口方法实现上获取不到
            if ((methodName.startsWith("get") || methodName.startsWith("find") || method.getAnnotation(CoreBusinessTranslate.class) != null)
                    && !coreBusinessIgnoreTranslateFlag.get()) {
                CacheService cacheService = SpringContextHolder.getBean(CacheService.class);
                if (result instanceof Map) {
                    cacheService.setTranslateInfo((Map) result);
                } else if (result instanceof ListResult) {
                    List<Map> list = ((ListResult) result).getRecords();
                    cacheService.setTranslateInfos(list);
                } else if (result instanceof List) {
                    List rsList = (List) result;
                    if (rsList != null && rsList.size() > 0 && rsList.get(0) instanceof Map) {
                        cacheService.setTranslateInfos(rsList);
                    }
                }
            }
            return result;
        } catch (ConstraintViolationException ex) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
                if (sb.length() > 0)
                    sb.append(";");
                sb.append(violation.getMessage());
            }
            tex = new CoreInvalidParameterException(sb.toString(), ex);
            throw tex;
        } catch (DaoVersionMismatchException ex) {
            tex = new CoreVersionMismatchException(CoreException.getCodeDesc(CoreException.CODE_DATA_VERSION_MISMATCH), ex);
            throw tex;
        } catch (CannotGetJdbcConnectionException ex) {
            tex = new CoreCannotGetJdbcConnectionException(CoreException.getCodeDesc(CoreException.CODE_CANNOT_GET_JDBC_CONNECTION), ex);
            throw tex;
        } catch (DuplicateKeyException ex) {
            tex = new CoreDatabaseDuplicateKeyException(CoreException.getCodeDesc(CoreException.CODE_DATABASE_DUPLICATE_KEY), ex);
            throw tex;
        } catch (UncategorizedSQLException ex) {
            if (ex.getMessage().matches(CANNOT_SET_INCORRECT_VALUE_MATCHES)) {
                tex = new CoreDatabaseCannotSetIncorrectValueException(ex.getMessage().replaceAll(CANNOT_SET_INCORRECT_VALUE_MATCHES, CANNOT_SET_INCORRECT_VALUE), ex);
            } else {
                tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            }
            throw tex;
        } catch (BadSqlGrammarException ex) {
            if (ex.getMessage().matches(UNKNOWN_COLUMN_MATCHES)) {
                tex = new CoreDatabaseUnknownColumnException(ex.getMessage().replaceAll(UNKNOWN_COLUMN_MATCHES, UNKNOWN_COLUMN), ex);
            } else {
                tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            }
            throw tex;
        } catch (DataAccessException ex) {
            tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            throw tex;
        } catch (UnknownHostException ex) {
            tex = new CoreUnknownException(CoreException.getCodeDesc(CoreException.CODE_UNKNOWN_HOST_EXCEPTION), ex);
            throw tex;
        } catch (ConnectException ex) {
            tex = new CoreNetConnectErrorException(CoreException.getCodeDesc(CoreException.CODE_NET_CONNECT_ERROR), ex);
            throw tex;
        }catch (CoreScenesException ex){
        	// 设置返回内容
    		ApolloConfigurationCenterUtil.getWosaiErrorDefinition(ex);
    		tex = ex;
    		throw ex;
        }catch (CoreException ex) {
            tex = ex;
            throw ex;
        } catch (IOException ex) {
            tex = new CoreIOException(CoreException.getCodeDesc(CoreException.CODE_IO_EXCEPTION), ex);
            throw tex;
        } catch (DaoException ex) {
            if (DATA_OBJECT_NOT_EXISTS.equals(ex.getMessage())) {
                tex = new CoreDataObjectNotExistsException(CoreException.getCodeDesc(CoreException.CODE_DATA_OBJECT_NOT_EXISTS), ex);
            } else {
                tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            }
            throw tex;
        } catch (NullPointerException ex) {
            tex = new CoreNullPointerException(CoreException.getCodeDesc(CoreException.CODE_NULL_POINTER_EXCEPTION), ex);
            throw tex;
        } catch (BizException ex) {
            tex = new CoreDbCryptoConfigAbnormalException(CoreException.getCodeDesc(CoreException.CODE_DB_CRYPTO_CONFIG_ABNORMAL), ex);
            throw tex;
        } catch (RequestRejectedException | RateLimiterException r){
            throw new CoreMerchantConfigAbnormalException(r.getMessage());
        } catch (Throwable ex) {
            tex = new CoreUnknownException(CoreException.getCodeDesc(CoreException.CODE_UNKNOWN_ERROR), ex);
            throw tex;
        } finally {
            long duration = System.currentTimeMillis() - before;
            if(entryCount == 1){
                if (Objects.nonNull(tex)) {
                    if (tex instanceof CoreMerchantConfigAbnormalException) {
                        //如果是CoreMerchantConfigAbnormalException异常，则不打印异常栈，避免影响性能
                        logger.error(LogstashMarkerAppendFileds.append(buildMethodEndLog(startLog, success, duration)), "invoking method end");
                    } else {
                        logger.error(LogstashMarkerAppendFileds.append(buildMethodEndLog(startLog, success, duration)), "invoking method end", tex);
                    }
                } else {
                    logger.trace(LogstashMarkerAppendFileds.append(buildMethodEndLog(startLog, success, duration)), "invoking method end");
                }
                entryCountTl.set(0);//reset
                coreBusinessIgnoreTranslateFlag.set(false);
            }
        }
    }

    private MethodEndLog buildMethodEndLog(MethodStartLog startLog, boolean success, long duration) {
        MethodEndLog endLog = new MethodEndLog(startLog);
        endLog.setDuration(duration);
        endLog.setSuccess(success);
        endLog.setReturnType(null);
        endLog.setException(null);
        return endLog;
    }

    /**
     * 改为使用MethodStartLog, MethodEndLog.
    class MethodInvocationFormatter {
        private final MethodInvocation invocation;
        public MethodInvocationFormatter(MethodInvocation invocation) {
            this.invocation = invocation;
        }
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("method ").append(invocation.getMethod()).append(" with arguments ");
            for(Object arg: invocation.getArguments()) {
                sb.append(" ").append(arg);
            }
            return sb.toString();
        }
    }
    */
}
