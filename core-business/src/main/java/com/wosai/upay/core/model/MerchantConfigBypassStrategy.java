package com.wosai.upay.core.model;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.wosai.pantheon.util.StringUtil;

public class MerchantConfigBypassStrategy{
    private String batchNo;
    private int weight;
    private Set<String> changeRules;
    private Map<String, Boolean> flags = new ConcurrentHashMap<String, Boolean>();

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public Set<String> getChangeRules() {
        return changeRules;
    }

    public void setChangeRules(Set<String> changeRules) {
        this.changeRules = changeRules;
    }

    public boolean useBypass(Integer provider, Integer payway, Integer subPayway) {
        String cacheKey = StringUtil.join("-", provider, payway, subPayway);
        Boolean cacheValue = flags.get(cacheKey);
        if (cacheValue == null) {
            List<String> useRules = Arrays.asList(StringUtil.join("-", provider, payway, subPayway),
                    StringUtil.join("-", provider, payway, ""),
                    StringUtil.join("-", provider, "", subPayway),
                    StringUtil.join("-", provider, "", ""),
                    "--"
                    );
            cacheValue = false;
            for (String useRule : useRules) {
                if (changeRules.contains(useRule)) {
                    cacheValue = true;
                    break;
                }
            }
            flags.put(cacheKey, cacheValue);
        }
        return cacheValue;
    }
}
