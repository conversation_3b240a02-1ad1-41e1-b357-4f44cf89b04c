package com.wosai.upay.core.repository;

import com.wosai.data.dao.Dao;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.concurrent.Callable;

public class DataRepository {
    private Dao<Map<String, Object>> systemConfigDao;

    private Dao<Map<String, Object>> vendorDao;
    private Dao<Map<String, Object>> vendorUserDao;
    private Dao<Map<String, Object>> vendorConfigDao;
    private Dao<Map<String, Object>> vendorDeveloperDao;
    private Dao<Map<String, Object>> vendorAppDao;

    private Dao<Map<String, Object>> agentDao;
    
    private Dao<Map<String, Object>> merchantDao;
    private Dao<Map<String, Object>> merchantConfigDao;
    private Dao<Map<String, Object>> merchantConfigCustomDao;
    private Dao<Map<String, Object>> merchantDeveloperDao;
    private Dao<Map<String, Object>> merchantUserDao;
    private Dao<Map<String, Object>> merchantUserStoreAuthDao;
    private Dao<Map<String, Object>> merchantBankAccountReadDao;
    private Dao<Map<String, Object>> merchantBankAccountWriteDao;
    private Dao<Map<String, Object>> merchantBizBankAccountReadDao;
    private Dao<Map<String, Object>> merchantBizBankAccountWriteDao;
    private Dao<Map<String, Object>> merchantBankAccountPreReadDao;
    private Dao<Map<String, Object>> merchantBankAccountPreWriteDao;
    private Dao<Map<String, Object>> merchantBankAccountChangeLogReadDao;
    private Dao<Map<String, Object>> merchantBankAccountChangeLogWriteDao;
    private Dao<Map<String, Object>> merchantBusinessLicenseReadDao;
    private Dao<Map<String, Object>> merchantBusinessLicenseWriteDao;
    private Dao<Map<String, Object>> storeBusinessLicenseReadDao;
    private Dao<Map<String, Object>> storeBusinessLicenseWriteDao;
    private Dao<Map<String, Object>> licenseDao;

    private Dao<Map<String, Object>> merchantGalleryDao;
    private Dao<Map<String, Object>> specialAuthWhitelistDao;


    private Dao<Map<String, Object>> storeDao;
    private Dao<Map<String, Object>> storeConfigDao;
    private Dao<Map<String, Object>> storeDeveloperDao;
    
    private Dao<Map<String, Object>> solicitorDao;
    private Dao<Map<String, Object>> solicitorConfigDao;
    private Dao<Map<String, Object>> solicitorDeveloperDao;
    private Dao<Map<String, Object>> solicitorBankAccountDao;
    private Dao<Map<String, Object>> solicitorUserDao;
    
    private Dao<Map<String, Object>> terminalDao;
    private Dao<Map<String, Object>> terminalConfigDao;
    private Dao<Map<String, Object>> terminalActivationCodeDao;
    private Dao<Map<String, Object>> changeShiftsDao;
    
    private Dao<Map<String, Object>> rsaKeyDao;

    private Dao<Map<String, Object>> withdrawDao;

    private Dao<Map<String, Object>> messageDao;

    private Dao<Map<String, Object>> importantChangeLogDao;
    private Dao<Map<String, Object>> opLogDao;
    private Dao<Map<String, Object>> taskApplyLogDao;

    private Dao<Map<String, Object>> permissionDao;
    private Dao<Map<String, Object>> userRoleDao;
    private Dao<Map<String, Object>> merchantRoleDao;
    private Dao<Map<String, Object>> ospRoleDao;
    private Dao<Map<String, Object>> rolePermissionDao;
    private Dao<Map<String, Object>> accountDao;
    private Dao<Map<String, Object>> ospUserDao;
    private Dao<Map<String, Object>> oauthTokenDao;
    private Dao<Map<String, Object>> industryDao;
    private Dao<Map<String, Object>> accountCommonDao;

    private Dao<Map<String, Object>> bankInfoDao;
    private Dao<Map<String, Object>> wftBankCodeDao;
    private Dao<Map<String, Object>> wftDistrictCodeDao;

    private Dao<Map<String, Object>> groupDao;
    private Dao<Map<String, Object>> groupUserDao;
    private Dao<Map<String, Object>> groupUserMerchantAuthDao;

    private Dao<Map<String, Object>> departmentDao;
    private Dao<Map<String, Object>> merchantUserDepartmentAuthDao;
    private Dao<Map<String, Object>> departmentTreeDao;
    private Dao<Map<String, Object>> departmentStoreDao;
    private Dao<Map<String, Object>> baseConfigDao;
    private Dao<Map<String, Object>> currencyFeerateMappingDao;
    private Dao<Map<String, Object>> signConfigDao;
    
    private Dao<Map<String, Object>> merchantAppConfigDao;
    private Dao<Map<String, Object>> storeAppConfigDao;

    private Dao<Map<String, Object>> tradeExtConfigDao;
    private Dao<Map<String, Object>> providerAbilityDao;

    private Dao<Map<String, Object>> cashDeskDao;

    private Dao<Map<String, Object>> cashDeskDeviceDao;

    private Dao<Map<String, Object>> cashDeskOpLogDao;

    private Dao<Map<String, Object>> metaPaywayDao;
    private Dao<Map<String, Object>> metaPaySourceDao;
    private Dao<Map<String, Object>> metaProductFlagDao;
    private Dao<Map<String, Object>> metaProviderDao;
    private Dao<Map<String, Object>> metaAcquirerDao;
    private Dao<Map<String, Object>> metaBizModelDao;
    private Dao<Map<String, Object>> metaPayPathDao;

    private Dao<Map<String, Object>> tradeAppConfigDao;
    private Dao<Map<String, Object>> externalExtraConfigDao;



    public void setCashDeskDao(Dao<Map<String, Object>> cashDeskDao) {
        this.cashDeskDao = cashDeskDao;
    }

    public Dao<Map<String, Object>> getCashDeskDao() {
        return cashDeskDao;
    }

    public void setCashDeskDeviceDao(Dao<Map<String, Object>> cashDeskDeviceDao) {
        this.cashDeskDeviceDao = cashDeskDeviceDao;
    }

    public Dao<Map<String, Object>> getCashDeskDeviceDao() {
        return cashDeskDeviceDao;
    }

    public void setCashDeskOpLogDao(Dao<Map<String, Object>> cashDeskOpLogDao) {
        this.cashDeskOpLogDao = cashDeskOpLogDao;
    }

    public Dao<Map<String, Object>> getCashDeskOpLogDao() {
        return cashDeskOpLogDao;
    }

    public Dao<Map<String, Object>> getStoreExtDao() {
        return storeExtDao;
    }

    public void setStoreExtDao(Dao<Map<String, Object>> storeExtDao) {
        this.storeExtDao = storeExtDao;
    }

    public Dao<Map<String, Object>> getPhotoInfoDao() {
        return photoInfoDao;
    }

    public void setPhotoInfoDao(Dao<Map<String, Object>> photoInfoDao) {
        this.photoInfoDao = photoInfoDao;
    }

    private Dao<Map<String, Object>> storeExtDao;
    private Dao<Map<String, Object>> photoInfoDao;

    public Dao<Map<String, Object>> getMcPreDao() {
        return mcPreDao;
    }

    public void setMcPreDao(Dao<Map<String, Object>> mcPreDao) {
        this.mcPreDao = mcPreDao;
    }

    private Dao<Map<String, Object>> mcPreDao;

    public Dao<Map<String, Object>> getMerchantBusinessLicenseReadDao() {
        return merchantBusinessLicenseReadDao;
    }

    public void setMerchantBusinessLicenseReadDao(Dao<Map<String, Object>> merchantBusinessLicenseReadDao) {
        this.merchantBusinessLicenseReadDao = merchantBusinessLicenseReadDao;
    }

    public Dao<Map<String, Object>> getMerchantBusinessLicenseWriteDao() {
        return merchantBusinessLicenseWriteDao;
    }

    public void setMerchantBusinessLicenseWriteDao(Dao<Map<String, Object>> merchantBusinessLicenseWriteDao) {
        this.merchantBusinessLicenseWriteDao = merchantBusinessLicenseWriteDao;
    }

    public Dao<Map<String, Object>> getStoreBusinessLicenseReadDao() {
        return storeBusinessLicenseReadDao;
    }

    public void setStoreBusinessLicenseReadDao(Dao<Map<String, Object>> storeBusinessLicenseReadDao) {
        this.storeBusinessLicenseReadDao = storeBusinessLicenseReadDao;
    }

    public Dao<Map<String, Object>> getStoreBusinessLicenseWriteDao() {
        return storeBusinessLicenseWriteDao;
    }

    public void setStoreBusinessLicenseWriteDao(Dao<Map<String, Object>> storeBusinessLicenseWriteDao) {
        this.storeBusinessLicenseWriteDao = storeBusinessLicenseWriteDao;
    }

    public Dao<Map<String, Object>> getLicenseDao() {
        return licenseDao;
    }

    public void setLicenseDao(Dao<Map<String, Object>> licenseDao) {
        this.licenseDao = licenseDao;
    }

    public Dao<Map<String, Object>> getDepartmentTreeDao() {
        return departmentTreeDao;
    }

    public void setDepartmentTreeDao(Dao<Map<String, Object>> departmentTreeDao) {
        this.departmentTreeDao = departmentTreeDao;
    }

    public Dao<Map<String, Object>> getOauthTokenDao() {
        return oauthTokenDao;
    }

    public void setOauthTokenDao(Dao<Map<String, Object>> oauthTokenDao) {
        this.oauthTokenDao = oauthTokenDao;
    }

    public Dao<Map<String, Object>> getVendorDao() {
        return vendorDao;
    }

    public void setVendorDao(Dao<Map<String, Object>> vendorDao) {
        this.vendorDao = vendorDao;
    }

    public Dao<Map<String, Object>> getVendorUserDao() {
        return vendorUserDao;
    }

    public void setVendorUserDao(Dao<Map<String, Object>> vendorUserDao) {
        this.vendorUserDao = vendorUserDao;
    }

    public Dao<Map<String, Object>> getVendorConfigDao() {
        return vendorConfigDao;
    }

    public void setVendorConfigDao(Dao<Map<String, Object>> vendorConfigDao) {
        this.vendorConfigDao = vendorConfigDao;
    }

    public Dao<Map<String, Object>> getVendorDeveloperDao() {
        return vendorDeveloperDao;
    }

    public void setVendorDeveloperDao(Dao<Map<String, Object>> vendorDeveloperDao) {
        this.vendorDeveloperDao = vendorDeveloperDao;
    }

    public Dao<Map<String, Object>> getVendorAppDao() {
        return vendorAppDao;
    }

    public void setVendorAppDao(Dao<Map<String, Object>> vendorAppDao) {
        this.vendorAppDao = vendorAppDao;
    }

    public Dao<Map<String, Object>> getMerchantDao() {
        return merchantDao;
    }

    public void setMerchantDao(Dao<Map<String, Object>> merchantDao) {
        this.merchantDao = merchantDao;
    }

    public Dao<Map<String, Object>> getMerchantConfigDao() {
        return merchantConfigDao;
    }

    public void setMerchantConfigDao(Dao<Map<String, Object>> merchantConfigDao) {
        this.merchantConfigDao = merchantConfigDao;
    }

    public Dao<Map<String, Object>> getMerchantDeveloperDao() {
        return merchantDeveloperDao;
    }

    public void setMerchantDeveloperDao(Dao<Map<String, Object>> merchantDeveloperDao) {
        this.merchantDeveloperDao = merchantDeveloperDao;
    }


    public Dao<Map<String, Object>> getMerchantUserDao() {
        return merchantUserDao;
    }

    public void setMerchantUserDao(Dao<Map<String, Object>> merchantUserDao) {
        this.merchantUserDao = merchantUserDao;
    }

    public Dao<Map<String, Object>> getMerchantUserStoreAuthDao() {
        return merchantUserStoreAuthDao;
    }

    public void setMerchantUserStoreAuthDao(Dao<Map<String, Object>> merchantUserStoreAuthDao) {
        this.merchantUserStoreAuthDao = merchantUserStoreAuthDao;
    }

    public void setMerchantBankAccountPreWriteDao(Dao<Map<String, Object>> merchantBankAccountPreWriteDao) {
        this.merchantBankAccountPreWriteDao = merchantBankAccountPreWriteDao;
    }


    public Dao<Map<String, Object>> getMerchantBankAccountPreWriteDao() {
        return merchantBankAccountPreWriteDao;
    }


    public void setMerchantBankAccountPreReadDao(Dao<Map<String, Object>> merchantBankAccountPreReadDao) {
        this.merchantBankAccountPreReadDao = merchantBankAccountPreReadDao;
    }


    public Dao<Map<String, Object>> getMerchantBankAccountPreReadDao() {
        return merchantBankAccountPreReadDao;
    }

    public Dao<Map<String, Object>> getMerchantBankAccountReadDao() {
        return merchantBankAccountReadDao;
    }

    public void setMerchantBankAccountReadDao(Dao<Map<String, Object>> merchantBankAccountReadDao) {
        this.merchantBankAccountReadDao = merchantBankAccountReadDao;
    }

    public Dao<Map<String, Object>> getMerchantBankAccountWriteDao() {
        return merchantBankAccountWriteDao;
    }

    public void setMerchantBankAccountWriteDao(Dao<Map<String, Object>> merchantBankAccountWriteDao) {
        this.merchantBankAccountWriteDao = merchantBankAccountWriteDao;
    }

    public Dao<Map<String, Object>> getmerchantBankAccountChangeLogReadDao() {
        return merchantBankAccountChangeLogReadDao;
    }

    public void setMerchantBankAccountChangeLogReadDao(Dao<Map<String, Object>> merchantBankAccountChangeLogReadDao) {
        this.merchantBankAccountChangeLogReadDao = merchantBankAccountChangeLogReadDao;
    }

    public Dao<Map<String, Object>> getmerchantBankAccountChangeLogWriteDao() {
        return merchantBankAccountChangeLogWriteDao;
    }

    public void setMerchantBankAccountChangeLogWriteDao(Dao<Map<String, Object>> merchantBankAccountChangeLogWriteDao) {
        this.merchantBankAccountChangeLogWriteDao = merchantBankAccountChangeLogWriteDao;
    }

    public Dao<Map<String, Object>> getMerchantGalleryDao() {
        return merchantGalleryDao;
    }

    public void setMerchantGalleryDao(Dao<Map<String, Object>> merchantGalleryDao) {
        this.merchantGalleryDao = merchantGalleryDao;
    }

    public Dao<Map<String, Object>> getStoreDao() {
        return storeDao;
    }

    public void setStoreDao(Dao<Map<String, Object>> storeDao) {
        this.storeDao = storeDao;
    }

    public Dao<Map<String, Object>> getStoreConfigDao() {
        return storeConfigDao;
    }

    public void setStoreConfigDao(Dao<Map<String, Object>> storeConfigDao) {
        this.storeConfigDao = storeConfigDao;
    }

    public Dao<Map<String, Object>> getStoreDeveloperDao() {
        return storeDeveloperDao;
    }

    public void setStoreDeveloperDao(Dao<Map<String, Object>> storeDeveloperDao) {
        this.storeDeveloperDao = storeDeveloperDao;
    }

    public Dao<Map<String, Object>> getSolicitorConfigDao() {
        return solicitorConfigDao;
    }

    public void setSolicitorConfigDao(Dao<Map<String, Object>> solicitorConfigDao) {
        this.solicitorConfigDao = solicitorConfigDao;
    }

    public Dao<Map<String, Object>> getSolicitorDao() {
        return solicitorDao;
    }

    public void setSolicitorDao(Dao<Map<String, Object>> solicitorDao) {
        this.solicitorDao = solicitorDao;
    }

    public Dao<Map<String, Object>> getSolicitorDeveloperDao() {
        return solicitorDeveloperDao;
    }

    public void setSolicitorDeveloperDao(Dao<Map<String, Object>> solicitorDeveloperDao) {
        this.solicitorDeveloperDao = solicitorDeveloperDao;
    }

    public Dao<Map<String, Object>> getSolicitorBankAccountDao() {
        return solicitorBankAccountDao;
    }

    public void setSolicitorBankAccountDao(Dao<Map<String, Object>> solicitorBankAccountDao) {
        this.solicitorBankAccountDao = solicitorBankAccountDao;
    }


    public Dao<Map<String, Object>> getSolicitorUserDao() {
        return solicitorUserDao;
    }

    public void setSolicitorUserDao(Dao<Map<String, Object>> solicitorUserDao) {
        this.solicitorUserDao = solicitorUserDao;
    }
    public Dao<Map<String, Object>> getTerminalDao() {
        return terminalDao;
    }

    public void setTerminalDao(Dao<Map<String, Object>> terminalDao) {
        this.terminalDao = terminalDao;
    }

    public Dao<Map<String, Object>> getTerminalConfigDao() {
        return terminalConfigDao;
    }

    public void setTerminalConfigDao(Dao<Map<String, Object>> terminalConfigDao) {
        this.terminalConfigDao = terminalConfigDao;
    }

    public Dao<Map<String, Object>> getTerminalActivationCodeDao() {
        return terminalActivationCodeDao;
    }

    public void setTerminalActivationCodeDao(Dao<Map<String, Object>> terminalActivationCodeDao) {
        this.terminalActivationCodeDao = terminalActivationCodeDao;
    }

    public Dao<Map<String, Object>> getRsaKeyDao() {
        return rsaKeyDao;
    }

    public void setRsaKeyDao(Dao<Map<String, Object>> rsaKeyDao) {
        this.rsaKeyDao = rsaKeyDao;
    }

    public Dao<Map<String, Object>> getWithdrawDao() {
        return withdrawDao;
    }

    public void setWithdrawDao(Dao<Map<String, Object>> withdrawDao) {
        this.withdrawDao = withdrawDao;
    }

    public Dao<Map<String, Object>> getMessageDao() {
        return messageDao;
    }

    public void setMessageDao(Dao<Map<String, Object>> messageDao) {
        this.messageDao = messageDao;
    }

    public Dao<Map<String, Object>> getOpLogDao() {
        return opLogDao;
    }

    public void setOpLogDao(Dao<Map<String, Object>> opLogDao) {
        this.opLogDao = opLogDao;
    }

    public Dao<Map<String, Object>> getTaskApplyLogDao() {
        return taskApplyLogDao;
    }

    public void setTaskApplyLogDao(Dao<Map<String, Object>> taskApplyLogDao) {
        this.taskApplyLogDao = taskApplyLogDao;
    }

    public Dao<Map<String, Object>> getImportantChangeLogDao() {
        return importantChangeLogDao;
    }

    public void setImportantChangeLogDao(Dao<Map<String, Object>> importantChangeLogDao) {
        this.importantChangeLogDao = importantChangeLogDao;
    }

    public void setPermissionDao(Dao<Map<String, Object>> permissionDao) {
        this.permissionDao = permissionDao;
    }

    public Dao<Map<String, Object>> getPermissionDao() {
        return permissionDao;
    }

    public Dao<Map<String, Object>> getUserRoleDao() {
        return userRoleDao;
    }

    public void setUserRoleDao(Dao<Map<String, Object>> userRoleDao) {
        this.userRoleDao = userRoleDao;
    }

    public Dao<Map<String, Object>> getOspRoleDao() {
        return ospRoleDao;
    }

    public void setOspRoleDao(Dao<Map<String, Object>> ospRoleDao) {
        this.ospRoleDao = ospRoleDao;
    }

    public Dao<Map<String, Object>> getMerchantRoleDao() {
        return merchantRoleDao;
    }

    public void setMerchantRoleDao(Dao<Map<String, Object>> merchantRoleDao) {
        this.merchantRoleDao = merchantRoleDao;
    }

    public Dao<Map<String, Object>> getRolePermissionDao() {
        return rolePermissionDao;
    }

    public void setRolePermissionDao(Dao<Map<String, Object>> rolePermissionDao) {
        this.rolePermissionDao = rolePermissionDao;
    }

    public Dao<Map<String, Object>> getAccountDao() {
        return accountDao;
    }

    public void setAccountDao(Dao<Map<String, Object>> accountDao) {
        this.accountDao = accountDao;
    }

    public void setOspUserDao(Dao<Map<String, Object>> ospUserDao) {
        this.ospUserDao = ospUserDao;
    }

    public Dao<Map<String, Object>> getOspUserDao() {
        return ospUserDao;
    }

    public Dao<Map<String, Object>> getMerchantConfigCustomDao() {
        return merchantConfigCustomDao;
    }

    public void setMerchantConfigCustomDao(Dao<Map<String, Object>> merchantConfigCustomDao) {
        this.merchantConfigCustomDao = merchantConfigCustomDao;
    }

    public Dao<Map<String, Object>> getSystemConfigDao() {
        return systemConfigDao;
    }

    public void setSystemConfigDao(Dao<Map<String, Object>> systemConfigDao) {
        this.systemConfigDao = systemConfigDao;
    }

    public Dao<Map<String, Object>> getBankInfoDao() {
        return bankInfoDao;
    }

    public void setBankInfoDao(Dao<Map<String, Object>> bankInfoDao) {
        this.bankInfoDao = bankInfoDao;
    }

    public Dao<Map<String, Object>> getWftBankCodeDao() {
        return wftBankCodeDao;
    }

    public void setWftBankCodeDao(Dao<Map<String, Object>> wftBankCodeDao) {
        this.wftBankCodeDao = wftBankCodeDao;
    }

    public Dao<Map<String, Object>> getWftDistrictCodeDao() {
        return wftDistrictCodeDao;
    }

    public void setWftDistrictCodeDao(Dao<Map<String, Object>> wftDistrictCodeDao) {
        this.wftDistrictCodeDao = wftDistrictCodeDao;
    }

    public Dao<Map<String, Object>> getIndustryDao() {
        return industryDao;
    }

    public void setIndustryDao(Dao<Map<String, Object>> industryDao) {
        this.industryDao = industryDao;
    }
    public Dao<Map<String, Object>> getAccountCommonDao() {
        return accountCommonDao;
    }

    public void setAccountCommonDao(Dao<Map<String, Object>> accountCommonDao) {
        this.accountCommonDao = accountCommonDao;
    }

    public Dao<Map<String, Object>> getAgentDao() {
        return agentDao;
    }

    public void setAgentDao(Dao<Map<String, Object>> agentDao) {
        this.agentDao = agentDao;
    }
    public Dao<Map<String, Object>> getGroupDao() {
        return groupDao;
    }

    public void setGroupDao(Dao<Map<String, Object>> groupDao) {
        this.groupDao = groupDao;
    }

    public Dao<Map<String, Object>> getGroupUserDao() {
        return groupUserDao;
    }

    public void setGroupUserDao(Dao<Map<String, Object>> groupUserDao) {
        this.groupUserDao = groupUserDao;
    }

    public Dao<Map<String, Object>> getGroupUserMerchantAuthDao() {
        return groupUserMerchantAuthDao;
    }

    public void setGroupUserMerchantAuthDao(Dao<Map<String, Object>> groupUserMerchantAuthDao) {
        this.groupUserMerchantAuthDao = groupUserMerchantAuthDao;
    }

    public Dao<Map<String, Object>> getSpecialAuthWhitelistDao() {
        return specialAuthWhitelistDao;
    }

    public void setSpecialAuthWhitelistDao(Dao<Map<String, Object>> specialAuthWhitelistDao) {
        this.specialAuthWhitelistDao = specialAuthWhitelistDao;
    }

	public Dao<Map<String, Object>> getChangeShiftsDao() {
		return changeShiftsDao;
	}

	public void setChangeShiftsDao(Dao<Map<String, Object>> changeShiftsDao) {
		this.changeShiftsDao = changeShiftsDao;
	}

    public Dao<Map<String, Object>> getDepartmentDao() {
        return departmentDao;
    }

    public void setDepartmentDao(Dao<Map<String, Object>> departmentDao) {
        this.departmentDao = departmentDao;
    }

    public Dao<Map<String, Object>> getMerchantUserDepartmentAuthDao() {
        return merchantUserDepartmentAuthDao;
    }

    public void setMerchantUserDepartmentAuthDao(Dao<Map<String, Object>> merchantUserDepartmentAuthDao) {
        this.merchantUserDepartmentAuthDao = merchantUserDepartmentAuthDao;
    }
    public Dao<Map<String, Object>> getDepartmentStoreDao() {
        return departmentStoreDao;
    }

    public void setDepartmentStoreDao(Dao<Map<String, Object>> departmentStoreDao) {
        this.departmentStoreDao = departmentStoreDao;
    }
    public void setBaseConfigDao(Dao<Map<String, Object>> baseConfigDao) {
        this.baseConfigDao = baseConfigDao;
    }

    public Dao<Map<String, Object>> getBaseConfigDao() {
        return baseConfigDao;
    }

    public Dao<Map<String, Object>> getCurrencyFeerateMappingDao(){ return currencyFeerateMappingDao; }

    public void setCurrencyFeerateMappingDao(Dao<Map<String, Object>> currencyFeerateMappingDao) {
        this.currencyFeerateMappingDao = currencyFeerateMappingDao;
    }

    public Dao<Map<String, Object>> getSignConfigDao() {
        return signConfigDao;
    }

    public void setSignConfigDao(Dao<Map<String, Object>> signConfigDao) {
        this.signConfigDao = signConfigDao;
    }

    public Dao<Map<String, Object>> getMerchantAppConfigDao() {
        return merchantAppConfigDao;
    }

    public void setMerchantAppConfigDao(Dao<Map<String, Object>> merchantAppConfigDao) {
        this.merchantAppConfigDao = merchantAppConfigDao;
    }

    public Dao<Map<String, Object>> getMerchantBizBankAccountReadDao() {
        return merchantBizBankAccountReadDao;
    }

    public void setMerchantBizBankAccountReadDao(Dao<Map<String, Object>> merchantBizBankAccountReadDao) {
        this.merchantBizBankAccountReadDao = merchantBizBankAccountReadDao;
    }

    public Dao<Map<String, Object>> getMerchantBizBankAccountWriteDao() {
        return merchantBizBankAccountWriteDao;
    }

    public void setMerchantBizBankAccountWriteDao(Dao<Map<String, Object>> merchantBizBankAccountWriteDao) {
        this.merchantBizBankAccountWriteDao = merchantBizBankAccountWriteDao;
    }

    public Dao<Map<String, Object>> getStoreAppConfigDao() {
        return storeAppConfigDao;
    }

    public void setStoreAppConfigDao(Dao<Map<String, Object>> storeAppConfigDao) {
        this.storeAppConfigDao = storeAppConfigDao;
    }

    public Dao<Map<String, Object>> getTradeExtConfigDao() {
        return tradeExtConfigDao;
    }

    public void setTradeExtConfigDao(Dao<Map<String, Object>> tradeExtConfigDao) {
        this.tradeExtConfigDao = tradeExtConfigDao;
    }

    public Dao<Map<String, Object>> getProviderAbilityDao() {
        return providerAbilityDao;
    }

    public void setProviderAbilityDao(Dao<Map<String, Object>> providerAbilityDao) {
        this.providerAbilityDao = providerAbilityDao;
    }

    public Dao<Map<String, Object>> getMetaPaywayDao() {
        return metaPaywayDao;
    }

    public void setMetaPaywayDao(Dao<Map<String, Object>> metaPaywayDao) {
        this.metaPaywayDao = metaPaywayDao;
    }

    public Dao<Map<String, Object>> getMetaProductFlagDao() {
        return metaProductFlagDao;
    }

    public void setMetaProductFlagDao(Dao<Map<String, Object>> metaProductFlagDao) {
        this.metaProductFlagDao = metaProductFlagDao;
    }

    public Dao<Map<String, Object>> getMetaProviderDao() {
        return metaProviderDao;
    }

    public void setMetaProviderDao(Dao<Map<String, Object>> metaProviderDao) {
        this.metaProviderDao = metaProviderDao;
    }

    public Dao<Map<String, Object>> getMetaBizModelDao() {
        return metaBizModelDao;
    }

    public void setMetaBizModelDao(Dao<Map<String, Object>> metaBizModelDao) {
        this.metaBizModelDao = metaBizModelDao;
    }

    public Dao<Map<String, Object>> getMetaPayPathDao() {
        return metaPayPathDao;
    }

    public void setMetaPayPathDao(Dao<Map<String, Object>> metaPayPathDao) {
        this.metaPayPathDao = metaPayPathDao;
    }

    public Dao<Map<String, Object>> getTradeAppConfigDao() {
        return tradeAppConfigDao;
    }

    public void setTradeAppConfigDao(Dao<Map<String, Object>> tradeAppConfigDao) {
        this.tradeAppConfigDao = tradeAppConfigDao;
    }

    public Dao<Map<String, Object>> getExternalExtraConfigDao() {
        return externalExtraConfigDao;
    }

    public void setExternalExtraConfigDao(Dao<Map<String, Object>> externalExtraConfigDao) {
        this.externalExtraConfigDao = externalExtraConfigDao;
    }

    public Dao<Map<String, Object>> getMetaAcquirerDao() {
        return metaAcquirerDao;
    }

    public void setMetaAcquirerDao(Dao<Map<String, Object>> metaAcquirerDao) {
        this.metaAcquirerDao = metaAcquirerDao;
    }

    public Dao<Map<String, Object>> getMetaPaySourceDao() {
        return metaPaySourceDao;
    }

    public void setMetaPaySourceDao(Dao<Map<String, Object>> metaPaySourceDao) {
        this.metaPaySourceDao = metaPaySourceDao;
    }

    @Transactional(value = "transactionManager", isolation= Isolation.REPEATABLE_READ, propagation = Propagation.REQUIRES_NEW)
    public Long doInTicketTransaction(Callable<Long> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @Transactional(value = "transactionManager", isolation = Isolation.REPEATABLE_READ, propagation = Propagation.REQUIRES_NEW)
    public void doMerchantRelatedTransaction(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional(value = "transactionManager")
    public void doInTransaction(Runnable runnable){
        runnable.run();
    }
}
