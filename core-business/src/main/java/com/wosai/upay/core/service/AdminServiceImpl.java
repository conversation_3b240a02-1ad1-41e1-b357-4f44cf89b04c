package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.exception.CoreDataAccessException;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.user.api.model.Account;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@AutoJsonRpcServiceImpl
public class AdminServiceImpl implements AdminService {
    public static final Logger logger = LoggerFactory.getLogger(AdminService.class);

    @Autowired
    MerchantService merchantService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    DataRepository dataRepository;

    @Override
    public void health() {
        //test db
        try{
            merchantService.getMerchant("test");
        }catch (Throwable e){
            throw  new CoreDataAccessException("数据库访问异常: " + CoreUtil.getErrorMessage(e));
        }


        //test redis
        try{
            redisTemplate.boundValueOps(UUID.randomUUID().toString()).get();
        }catch (Throwable e){
            throw  new CoreDataAccessException("redis访问异常: " + CoreUtil.getErrorMessage(e));
        }



    }


    @Override
    public void updateDbCryptoKeyLastPart(String lastPart) {
        //mock
        return;
    }

    @Override
    public void refreshConfig() {
        //mock
        return;
    }

    @Override
    public void reSaveMerchantForDbCrypto(String merchantId) {
        Map<String,Object> merchant = dataRepository.getMerchantDao().getPart(merchantId, Arrays.asList(
                DaoConstants.ID, Merchant.LEGAL_PERSON_ID_NUMBER, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO,
                Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO
                ));
        if(merchant != null){
            dataRepository.getMerchantDao().updatePart(merchant);
        }
    }

    @Override
    public void reSaveMerchantBankAccountForDbCrypto(String merchantId) {
        Criteria criteria = Criteria.where(MerchantBankAccount.MERCHANT_ID).is(merchantId);
        List<Map<String,Object>> merchantBankAccounts = CollectionUtil.iterator2list(
                dataRepository.getMerchantBankAccountReadDao().filter(criteria, Arrays.asList(
                        DaoConstants.ID,
                        MerchantBankAccount.IDENTITY, MerchantBankAccount.NUMBER,
                        MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, MerchantBankAccount.HOLDER_ID_BACK_PHOTO
                )).fetchAll()
        );
        for(Map<String,Object> merchantBankAccount: merchantBankAccounts){
            dataRepository.getMerchantBankAccountWriteDao().updatePart(merchantBankAccount);
        }
    }

    @Override
    public void reSaveAccountForDbCrypto(String accountId) {
        Map<String,Object> account = dataRepository.getAccountDao().getPart(accountId, Arrays.asList(Account.CELLPHONE));
        if(!Objects.isNull(account)){
            dataRepository.getAccountDao().updatePart(account);
        }
    }

}
