package com.wosai.upay.core.service;

import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.SetUtil;
import com.wosai.sp.business.logstash.dto.ValidList;
import com.wosai.sp.business.logstash.dto.req.BsOpLogCreateReqDto;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MetaPayway;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.repository.DataRepository;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 记录日志相关接口
 */
@Component
public class BizLogFacade implements InitializingBean {
    private static final Logger log = LoggerFactory.getLogger(BizLogFacade.class);
    private static final String BIZ_LOG_KEY_MERCHANT_CONFIG = "merchant_config#";

    private static final String KEY_BIZ_CODE_MERCHANT_CONFIG = "3R1S2CE63LEH";

    public static final String KEY_OP_USER_NAME_SYSTEM = "system";
    public static String DEFAULT_PLATFORM_CODE = "SPA";

    public static final String BIZ_UNLIMITED_DESC = "无限额";
    @Autowired
    BusinssCommonService businssCommonService;
    private static Map<String, String> PAYWAY_NAME_DESC = new HashMap<>();
    private static Map<String, String> SUB_PAYWAY_NAME_DESC = new HashMap<>();


    /**
     * 限额是map类型，并且key是payway的格式
     */
    private static final Set<String> TRADE_VALIDATE_ADAPT_PAYWAY_MAP_KEY = SetUtil.hashSet(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);

    @Autowired
    BusinessOpLogService businessOpLogService;

    private Dao<Map<String, Object>> merchantConfigDao;

    @Autowired
    public BizLogFacade(DataRepository repository) {
        this.merchantConfigDao = repository.getMerchantConfigDao();
    }

    public void safeSendMerchantConfigParamsUpdate(Map<String, Object> beforeParams, String merchantConfigId) {
        try {
            Map<String, Object> currentMerchantConfig = merchantConfigDao.get(merchantConfigId);
            if (currentMerchantConfig != null) {
                String merchantId = MapUtil.getString(currentMerchantConfig, MerchantConfig.MERCHANT_ID);
                safeSendMerchantConfigParamsUpdate(merchantId, beforeParams, MapUtil.getMap(currentMerchantConfig, MerchantConfig.PARAMS));
            }
        } catch (Exception e) {
            log.error("send trade validate error beforeParams:{} merchant_config_id:{}", JsonUtil.toJsonStr(beforeParams), merchantConfigId, e);
        }
    }

    /**
     * 发送merchant_config_params更新的字段业务属性
     *
     * @param merchantId
     * @param beforeParams
     * @param afterParams
     */
    public void safeSendMerchantConfigParamsUpdate(String merchantId, Map<String, Object> beforeParams, Map<String, Object> afterParams) {
        try {
            if (beforeParams == null) {
                beforeParams = new HashMap<>();
            }
            if (afterParams == null) {
                afterParams = new HashMap<>();
            }
            safeSendTradeValidateParams(merchantId, beforeParams, afterParams);
        } catch (Exception e) {
            log.error("send trade validate error merchant_id:{}, beforeParams:{} afterParams:{}", merchantId, JsonUtil.toJsonStr(beforeParams), JsonUtil.toJsonStr(afterParams), e);
        }
    }

    /**
     * 发送限额相关的日志
     *
     * @param merchantId
     * @param before
     * @param after
     */
    private void safeSendTradeValidateParams(String merchantId, Map<String, Object> before, Map<String, Object> after) {
        try {
            BsOpLogCreateReqDto createReqDto = new BsOpLogCreateReqDto();
            createReqDto.setLogTemplateCode(KEY_BIZ_CODE_MERCHANT_CONFIG);
            createReqDto.setOpObjectId(merchantId);
            createReqDto.setOpUserId(merchantId);
            createReqDto.setOpUserName(KEY_OP_USER_NAME_SYSTEM);
            ValidList<BsOpLogCreateReqDto.Diff> diffs = new ValidList<>();
            for (String tradeValidateParamKey : TradeConfigServiceImpl.tradeValidateParamKeys) {
                String beforeValue = "";
                String afterValue = "";
                try {
                    Object beforeValueObj = MapUtil.getObject(before, tradeValidateParamKey);
                    beforeValue = transitionTradeValidateObjToString(tradeValidateParamKey, beforeValueObj);
                    Object afterValueObj = MapUtil.getObject(after, tradeValidateParamKey);
                    afterValue = transitionTradeValidateObjToString(tradeValidateParamKey, afterValueObj);
                    if (!Objects.equals(beforeValue, afterValue)) {
                        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
                        diff.setColumnCode(BIZ_LOG_KEY_MERCHANT_CONFIG + tradeValidateParamKey);
                        diff.setValueBefore(beforeValue);
                        diff.setValueAfter(afterValue);
                        diffs.add(diff);
                    }
                } catch (Exception e) {
                    log.error("send trade validate error merchant_id:{}, key:{} beforeParams:{} afterParams:{}", merchantId, tradeValidateParamKey, beforeValue, afterValue, e);
                }
            }
            createReqDto.setDiffList(diffs);
            createReqDto.setPlatformCode(DEFAULT_PLATFORM_CODE);
            if (CollectionUtil.isNotEmpty(diffs)) {
                businessOpLogService.createBusinessLogForAsync(createReqDto);
            }
        } catch (Exception e) {
            log.error("send trade validate error merchant_id:{}, beforeParams:{} afterParams:{}", merchantId, JsonUtil.toJsonStr(before), JsonUtil.toJsonStr(after), e);
        }
    }

    /**
     * 通用的发送日志的方法
     *
     * @param opObjectId         对象id
     * @param logTemplateCode    日志模板code
     * @param tablePrefix        表前缀
     * @param opLogCreateRequest 日志请求
     * @param fixedKeyList       固定的字段列表
     * @param changeKeyList      变化的字段
     * @param keyNameMap         变化的字段的名称
     * @param before             改变之前的值
     * @param after              改变之后的值
     */
    public void safeSendTradeCommonParamsLog(OpLogCreateRequest opLogCreateRequest, String opObjectId, String rootObjectId, String logTemplateCode, String tablePrefix, List<String> fixedKeyList, List<String> changeKeyList, Map<String, Map<String, String>> keyNameMap, Map<String, Object> before, Map<String, Object> after) {
        try {
            BsOpLogCreateReqDto createReqDto = new BsOpLogCreateReqDto();
            createReqDto.setLogTemplateCode(logTemplateCode);
            createReqDto.setRemark(opLogCreateRequest.getRemark());
            createReqDto.setOpObjectId(opObjectId);
            createReqDto.setRootObjectId(rootObjectId);
            createReqDto.setOuterSceneTraceId(StringUtils.isEmpty(opLogCreateRequest.getOuterSceneTraceId()) ? TraceContext.traceId() : opLogCreateRequest.getOuterSceneTraceId());
            createReqDto.setOpUserId(StringUtils.isEmpty(opLogCreateRequest.getOpUserId()) ? opObjectId : opLogCreateRequest.getOpUserId());
            createReqDto.setOpUserName(StringUtils.isEmpty(opLogCreateRequest.getOpUserName()) ? opObjectId : opLogCreateRequest.getOpUserName());
            ValidList<BsOpLogCreateReqDto.Diff> diffs = new ValidList<>();
            List<String> allKeyList = new ArrayList<>();
            allKeyList.addAll(changeKeyList);
            allKeyList.addAll(fixedKeyList);
            for (String tradeValidateParamKey : allKeyList) {
                String beforeValue = "";
                String afterValue = "";
                String beforeValueDesc = "";
                String afterValueDesc = "";
                Map<String, String> valueDescMap = null;
                if (null != keyNameMap && keyNameMap.containsKey(tradeValidateParamKey)) {
                    valueDescMap = keyNameMap.get(tradeValidateParamKey);
                }
                try {

                    Object beforeValueObj = MapUtil.getObject(before, tradeValidateParamKey);
                    beforeValue = transitionTradeParamToString(beforeValueObj);
                    if (null != valueDescMap && valueDescMap.containsKey(beforeValue)) {
                        beforeValueDesc = valueDescMap.get(beforeValue);
                    } else if (MerchantConfig.PAYWAY.equals(tradeValidateParamKey) && PAYWAY_NAME_DESC.containsKey(beforeValue)) {
                        beforeValueDesc = PAYWAY_NAME_DESC.get(beforeValue);
                    }

                    Object afterValueObj = MapUtil.getObject(after, tradeValidateParamKey);
                    afterValue = transitionTradeParamToString(afterValueObj);
                    if (null != valueDescMap && valueDescMap.containsKey(afterValue)) {
                        afterValueDesc = valueDescMap.get(afterValue);
                    } else if (MerchantConfig.PAYWAY.equals(tradeValidateParamKey) && PAYWAY_NAME_DESC.containsKey(afterValue)) {
                        afterValueDesc = PAYWAY_NAME_DESC.get(afterValue);
                    }
                    if (!Objects.equals(beforeValue, afterValue) || fixedKeyList.contains(tradeValidateParamKey)) {
                        BsOpLogCreateReqDto.Diff diff = new BsOpLogCreateReqDto.Diff();
                        diff.setColumnCode(tablePrefix + tradeValidateParamKey);
                        diff.setValueBefore(StringUtils.isEmpty(beforeValueDesc) ? beforeValue : beforeValueDesc);
                        if (fixedKeyList.contains(tradeValidateParamKey)) {
                            diff.setValueAfter("--");
                        } else {
                            diff.setValueAfter(StringUtils.isEmpty(afterValueDesc) ? afterValue : afterValueDesc);
                        }
                        diffs.add(diff);
                    }
                } catch (Exception e) {
                    log.error("send trade common log error op_object_id:{}, key:{} beforeParams:{} afterParams:{}", opObjectId, changeKeyList, beforeValue, afterValue, e);
                }
            }
            createReqDto.setDiffList(diffs);
            createReqDto.setPlatformCode(StringUtils.isEmpty(opLogCreateRequest.getPlatformCode()) ? DEFAULT_PLATFORM_CODE : opLogCreateRequest.getPlatformCode());
            if (CollectionUtil.isNotEmpty(diffs)) {
                businessOpLogService.createBusinessLogForAsync(createReqDto);
            }
        } catch (Exception e) {
            log.error("send trade common log error op_object_id:{}, beforeParams:{} afterParams:{}", opObjectId, JsonUtil.toJsonStr(before), JsonUtil.toJsonStr(after), e);
        }
    }


    private String transitionTradeValidateObjToString(String key, Object tradeValidateObj) {
        if (tradeValidateObj == null) {
            return BIZ_UNLIMITED_DESC;
        } else {
            if (tradeValidateObj instanceof String) {
                return tradeValidateObj.toString();
            } else if (tradeValidateObj instanceof Number) {
                Number tradeValidateNum = (Number) tradeValidateObj;
                return tradeValidateNum.toString();
            } else if (tradeValidateObj instanceof Map) {
                return JsonUtil.toJsonStr(transitionTradeValidateForMap(key, (Map) tradeValidateObj));
            } else {
                return "";
            }
        }
    }


    private String transitionTradeParamToString(Object tradeValidateObj) {
        if (tradeValidateObj instanceof String) {
            return tradeValidateObj.toString();
        } else if (tradeValidateObj instanceof Number) {
            Number tradeValidateNum = (Number) tradeValidateObj;
            return tradeValidateNum.intValue() + "";
        } else if (tradeValidateObj instanceof Map) {
            return JsonUtil.toJsonStr((Map) tradeValidateObj);
        } else {
            return "";
        }
    }


    private Map transitionTradeValidateForMap(String key, Map tradeValidateMap) {
        if (TRADE_VALIDATE_ADAPT_PAYWAY_MAP_KEY.contains(key)) {
            //如果是payway层级的处理
            Map transitionNewMap = new HashMap();
            Map<String, Object> stringTradeValidateMap = tradeValidateMap;
            for (Map.Entry<String, Object> entry : stringTradeValidateMap.entrySet()) {
                String paywayKey = entry.getKey();
                String paywayName = PAYWAY_NAME_DESC.getOrDefault(paywayKey, paywayKey);
                Object value = entry.getValue();
                Object valueTransition = value;
                if (value == null) {
                    valueTransition = BIZ_UNLIMITED_DESC;
                } else if (value instanceof String) {
                    valueTransition = value.toString();
                } else if (value instanceof Number) {
                    Number tradeValidateNum = (Number) value;
                    if (tradeValidateNum.intValue() == Integer.MAX_VALUE) {
                        valueTransition = BIZ_UNLIMITED_DESC;
                    } else {
                        valueTransition = StringUtils.cents2yuan(tradeValidateNum.longValue());
                    }
                } else if (value instanceof Map) {
                    //subpayway结构
                    valueTransition = transitionTradeValidateSubPaywayForMap((Map) value);
                }
                transitionNewMap.put(paywayName, valueTransition);
            }
            return transitionNewMap;
        } else {
            return tradeValidateMap;
        }
    }

    private Map transitionTradeValidateSubPaywayForMap(Map<String, Object> subPaywayTradeValidate) {
        Map transitionNewMap = new HashMap();
        for (Map.Entry<String, Object> entry : subPaywayTradeValidate.entrySet()) {
            String subPayway = entry.getKey();
            String subPaywayName = SUB_PAYWAY_NAME_DESC.getOrDefault(subPayway + "", subPayway);
            Object value = entry.getValue();
            Object valueDesc = value;
            if (value == null) {
                valueDesc = BIZ_UNLIMITED_DESC;
            } else if (value instanceof String) {
                valueDesc = value.toString();
            } else if (value instanceof Number) {
                Number tradeValidateNum = (Number) value;
                if (tradeValidateNum.intValue() == Integer.MAX_VALUE) {
                    valueDesc = BIZ_UNLIMITED_DESC;
                } else {
                    valueDesc = value + "";
                }
            }
            transitionNewMap.put(subPaywayName, valueDesc);
        }
        return transitionNewMap;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        List<Map<String, Object>> allMetaPayways = businssCommonService.getAllMetaPayways();
        PAYWAY_NAME_DESC = allMetaPayways.stream().collect(Collectors.toMap(o -> MapUtil.getString(o, DaoConstants.ID), v -> MapUtil.getString(v, MetaPayway.NAME)));
        SUB_PAYWAY_NAME_DESC = SubPayway.getAll().stream().collect(Collectors.toMap(o -> o.getCode() + "", o -> o.getName()));
        SUB_PAYWAY_NAME_DESC.put("", "所有方式");
    }
}
