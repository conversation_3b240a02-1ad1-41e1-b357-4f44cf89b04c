package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.bean.model.MetaBizModel;
import com.wosai.upay.core.bean.model.MetaPayPath;
import com.wosai.upay.core.bean.model.ProviderAbility;
import com.wosai.upay.core.bean.response.GetAllMetaResponse;
import com.wosai.upay.core.bean.response.GetAllProviderAbilityResponse;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.meta.*;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.repository.DataRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class BusinssCommonServiceImpl implements BusinssCommonService {

    private DataRepository repository;
    private Dao<Map<String, Object>> vendorDao;
    private Dao<Map<String, Object>> vendorAppDao;
    private Dao<Map<String, Object>> solicitorDao;
    private Dao<Map<String, Object>> merchantDao;
    private Dao<Map<String, Object>> storeDao;
    private Dao<Map<String, Object>> terminalDao;
    private Dao<Map<String, Object>> providerAbilityDao;
    private Dao<Map<String, Object>> paywayDao;
    private Dao<Map<String, Object>> productFlagDao;
    private Dao<Map<String, Object>> providerDao;
    private Dao<Map<String, Object>> acquirerDao;

    private MetaService metaService;

    @Autowired
    public BusinssCommonServiceImpl(DataRepository repository, MetaService metaService) {
        this.repository = repository;
        this.vendorDao = repository.getVendorDao();
        this.vendorAppDao = repository.getVendorAppDao();
        this.solicitorDao = repository.getSolicitorDao();
        this.merchantDao = repository.getMerchantDao();
        this.storeDao = repository.getStoreDao();
        this.terminalDao = repository.getTerminalDao();
        this.providerAbilityDao = repository.getProviderAbilityDao();
        this.paywayDao = repository.getMetaPaywayDao();
        this.productFlagDao = repository.getMetaProductFlagDao();
        this.providerDao = repository.getMetaProviderDao();
        this.acquirerDao = repository.getMetaAcquirerDao();
        this.metaService = metaService;
    }

    @Override
    public Map<Integer, String> getExceptionCodesAndDesc() {
        return CoreException.CODES_DESC_MAP;
    }

    @Override
    public String getVendorIdBySn(String vendorSn) {
        return BeanUtil.getPropString(getVendorMinimalInfoBySn(vendorSn), DaoConstants.ID);
    }

    @Override
    public Map<String, Object> getVendorMinimalInfoBySn(String vendorSn) {
        return getVendorMinimalInfoBySn(vendorSn, true);
    }

    @Override
    public Map<String, Object> getVendorMinimalInfoBySn(String vendorSn, boolean throwException) {
        Map<String, Object> map = vendorDao.filter(Criteria.where(Vendor.SN).is(vendorSn), CollectionUtil.hashSet(DaoConstants.ID, Vendor.SN, Vendor.NAME, Vendor.STATUS)).fetchOne();
        if (map == null && throwException) {
            throw new CoreVendorNotExistsException("服务商不存在");
        }
        return map;
    }

    @Override
    public String getVendorSnById(String vendorId) {
        return BeanUtil.getPropString(getVendorMinimalInfoById(vendorId), Vendor.SN);
    }

    @Override
    public Map<String, Object> getVendorMinimalInfoById(String vendorId) {
        return getVendorMinimalInfoById(vendorId, true);
    }

    @Override
    public Map<String, Object> getVendorMinimalInfoById(String vendorId, boolean throwException) {
        Map<String, Object> map = vendorDao.filter(Criteria.where(DaoConstants.ID).is(vendorId), CollectionUtil.hashSet(DaoConstants.ID, Vendor.SN, Vendor.NAME, Vendor.STATUS)).fetchOne();
        if (map == null && throwException) {
            throw new CoreVendorNotExistsException("服务商不存在");
        }
        return map;
    }

    @Override
    public String getVendorAppIdByAppId(String vendorAppAppId) {
        return BeanUtil.getPropString(getVendorAppMinimalInfoByAppId(vendorAppAppId), DaoConstants.ID);
    }

    @Override
    public Map<String, Object> getVendorAppMinimalInfoByAppId(String vendorAppAppId) {
        return getVendorAppMinimalInfoByAppId(vendorAppAppId, true);
    }

    @Override
    public Map<String, Object> getVendorAppMinimalInfoByAppId(String vendorAppAppId, boolean throwException) {
        Map<String, Object> map = vendorAppDao.filter(Criteria.where(VendorApp.APPID).is(vendorAppAppId), CollectionUtil.hashSet(DaoConstants.ID, VendorApp.VENDOR_ID, VendorApp.APPID, VendorApp.NAME)).fetchOne();
        if (map == null && throwException) {
            throw new CoreVendorAppNotExistsException("服务商应用不存在");
        }
        return map;
    }

    @Override
    public String getVendorAppAppIdById(String vendorAppId) {
        return BeanUtil.getPropString(getVendorAppMinimalInfoById(vendorAppId), VendorApp.APPID);
    }

    @Override
    public Map<String, Object> getVendorAppMinimalInfoById(String vendorAppId) {
        return getVendorAppMinimalInfoById(vendorAppId, true);
    }

    @Override
    public Map<String, Object> getVendorAppMinimalInfoById(String vendorAppId, boolean throwException) {
        Map<String, Object> map = vendorAppDao.filter(Criteria.where(DaoConstants.ID).is(vendorAppId), CollectionUtil.hashSet(DaoConstants.ID, VendorApp.VENDOR_ID, VendorApp.APPID, VendorApp.NAME)).fetchOne();
        if (map == null && throwException) {
            throw new CoreVendorAppNotExistsException("服务商应用不存在");
        }
        return map;
    }

    @Override
    public String getSolicitorIdBySn(String solicitorSn) {
        return BeanUtil.getPropString(getSolicitorMinimalInfoBySn(solicitorSn), DaoConstants.ID);
    }

    @Override
    public Map<String, Object> getSolicitorMinimalInfoBySn(String solicitorSn) {
        return getSolicitorMinimalInfoBySn(solicitorSn, true);
    }

    @Override
    public Map<String, Object> getSolicitorMinimalInfoBySn(String solicitorSn, boolean throwException) {
        Map<String, Object> map = solicitorDao.filter(Criteria.where(Solicitor.SN).is(solicitorSn), CollectionUtil.hashSet(DaoConstants.ID, Solicitor.SN, Solicitor.NAME, Solicitor.STATUS)).fetchOne();
        if (map == null && throwException) {
            throw new CoreSolicitorNotExistsException("推广渠道不存在");
        }
        return map;
    }

    @Override
    public String getSolicitorSnById(String solicitorId) {
        return BeanUtil.getPropString(getSolicitorMinimalInfoById(solicitorId), Solicitor.SN);
    }

    @Override
    public Map<String, Object> getSolicitorMinimalInfoById(String solicitorId) {
        return getSolicitorMinimalInfoById(solicitorId, true);
    }

    @Override
    public Map<String, Object> getSolicitorMinimalInfoById(String solicitorId, boolean throwException) {
        Map<String, Object> map = solicitorDao.filter(Criteria.where(DaoConstants.ID).is(solicitorId), CollectionUtil.hashSet(DaoConstants.ID, Solicitor.SN, Solicitor.NAME, Solicitor.STATUS)).fetchOne();
        if (map == null && throwException) {
            throw new CoreSolicitorNotExistsException("推广渠道不存在");
        }
        return map;
    }

    @Override
    public String getMerchantIdBySn(String merchantSn) {
        return BeanUtil.getPropString(getMerchantMinimalInfoBySn(merchantSn), DaoConstants.ID);
    }

    @Override
    public Map<String, Object> getMerchantMinimalInfoBySn(String merchantSn) {
        return getMerchantMinimalInfoBySn(merchantSn, true);
    }

    @Override
    public Map<String, Object> getMerchantMinimalInfoBySn(String merchantSn, boolean throwException) {
        Map<String, Object> map = merchantDao.filter(Criteria.where(Merchant.SN).is(merchantSn), CollectionUtil.hashSet(DaoConstants.ID, Merchant.SN, Merchant.NAME, Merchant.BUSINESS_NAME, Merchant.STATUS, Merchant.VENDOR_ID, Merchant.SOLICITOR_ID, Merchant.CLIENT_SN, Merchant.CONTACT_NAME, Merchant.CONTACT_PHONE, Merchant.CONTACT_CELLPHONE)).fetchOne();
        if (map == null && throwException) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        return map;
    }

    @Override
    public String getMerchantSnById(String merchantId) {
        Map<String, Object> map = merchantDao.filter(Criteria.where(DaoConstants.ID).is(merchantId), CollectionUtil.hashSet(Merchant.SN)).fetchOne();
        if (map == null) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        return BeanUtil.getPropString(map, Merchant.SN);
    }

    @Override
    public Map<String, Object> getMerchantMinimalInfoById(String merchantId) {
        return getMerchantMinimalInfoById(merchantId, true);
    }

    @Override
    public Map<String, Object> getMerchantMinimalInfoById(String merchantId, boolean throwException) {
        Map<String, Object> map = merchantDao.filter(Criteria.where(DaoConstants.ID).is(merchantId), CollectionUtil.hashSet(DaoConstants.ID, Merchant.SN, Merchant.NAME, Merchant.BUSINESS_NAME, Merchant.STATUS, Merchant.VENDOR_ID, Merchant.SOLICITOR_ID, Merchant.CLIENT_SN, Merchant.CONTACT_NAME, Merchant.CONTACT_PHONE, Merchant.CONTACT_CELLPHONE)).fetchOne();
        if (map == null && throwException) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        return map;
    }

    @Override
    public String getStoreIdBySn(String storeSn) {
        return BeanUtil.getPropString(getStoreMinimalInfoBySn(storeSn), DaoConstants.ID);
    }

    @Override
    public Map<String, Object> getStoreMinimalInfoBySn(String storeSn) {
        return getStoreMinimalInfoBySn(storeSn, true);
    }

    @Override
    public Map<String, Object> getStoreMinimalInfoBySn(String storeSn, boolean throwException) {
        Map<String, Object> map = storeDao.filter(Criteria.where(Store.SN).is(storeSn), CollectionUtil.hashSet(DaoConstants.ID, Store.SN, Store.NAME, Store.STATUS, Store.MERCHANT_ID, Store.VENDOR_ID, Store.SOLICITOR_ID, Store.CLIENT_SN)).fetchOne();
        if (map == null && throwException) {
            throw new CoreStoreNotExistsException("门店不存在");
        }
        return map;
    }

    @Override
    public String getStoreSnById(String storeId) {
        return BeanUtil.getPropString(getStoreMinimalInfoById(storeId), Store.SN);
    }

    @Override
    public Map<String, Object> getStoreMinimalInfoById(String storeId) {
        return getStoreMinimalInfoById(storeId, true);
    }

    @Override
    public Map<String, Object> getStoreMinimalInfoById(String storeId, boolean throwException) {
        Map<String, Object> map = storeDao.filter(Criteria.where(DaoConstants.ID).is(storeId), CollectionUtil.hashSet(DaoConstants.ID, Store.SN, Store.NAME, Store.STATUS, Store.MERCHANT_ID, Store.VENDOR_ID, Store.SOLICITOR_ID, Store.CLIENT_SN)).fetchOne();
        if (map == null && throwException) {
            throw new CoreStoreNotExistsException("门店不存在");
        }
        return map;
    }

    @Override
    public String getTerminalIdBySn(String terminalSn) {
        return BeanUtil.getPropString(getTerminalMinimalInfoBySn(terminalSn), DaoConstants.ID);
    }

    @Override
    public Map<String, Object> getTerminalMinimalInfoBySn(String terminalSn) {
        return getTerminalMinimalInfoBySn(terminalSn, true);
    }

    @Override
    public Map<String, Object> getTerminalMinimalInfoBySn(String terminalSn, boolean throwException) {
        Map<String, Object> map = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn), CollectionUtil.hashSet(DaoConstants.ID, Terminal.SN, Terminal.NAME, Terminal.STATUS, Terminal.STORE_ID, Terminal.MERCHANT_ID, Terminal.VENDOR_ID, Terminal.SOLICITOR_ID, Terminal.CLIENT_SN, Terminal.DEVICE_FINGERPRINT)).fetchOne();
        if (map == null && throwException) {
            throw new CoreTerminalNotExistsException("终端不存在");
        }
        return map;
    }

    @Override
    public String getTerminalSnById(String terminalId) {
        return BeanUtil.getPropString(getTerminalMinimalInfoById(terminalId), Terminal.SN);
    }

    @Override
    public Map<String, Object> getTerminalMinimalInfoById(String terminalId) {
        return getTerminalMinimalInfoById(terminalId, true);
    }

    @Override
    public Map<String, Object> getTerminalMinimalInfoById(String terminalId, boolean throwException) {
        Map<String, Object> map = terminalDao.filter(Criteria.where(DaoConstants.ID).is(terminalId), CollectionUtil.hashSet(DaoConstants.ID, Terminal.SN, Terminal.NAME, Terminal.STATUS, Terminal.STORE_ID, Terminal.MERCHANT_ID, Terminal.VENDOR_ID, Terminal.SOLICITOR_ID, Terminal.CLIENT_SN, Terminal.DEVICE_FINGERPRINT)).fetchOne();
        if (map == null && throwException) {
            throw new CoreTerminalNotExistsException("终端不存在");
        }
        return map;
    }

    @Override
    public void checkVendorStatus(String vendorId, String vendorSn) {
        Map vendorMinInfo;
        if (vendorId != null) {
            vendorMinInfo = getVendorMinimalInfoById(vendorId);
        } else if (vendorSn != null) {
            vendorMinInfo = getVendorMinimalInfoBySn(vendorSn);
        } else {
            return;
        }
        Integer status = BeanUtil.getPropInt(vendorMinInfo, Vendor.STATUS, 0);
        if (status != Vendor.STATUS_ENABLED) {
            throw new CoreVendorStatusAbnormalException(CoreException.getCodeDesc(CoreException.CODE_VENDOR_STATUS_ABNORMAL));
        }
    }

    @Override
    public void checkSolicitorStatus(String solicitorId, String solicitorSn) {
        Map solicitorMinInfo;
        if (solicitorId != null) {
            solicitorMinInfo = getSolicitorMinimalInfoById(solicitorId);
        } else if (solicitorSn != null) {
            solicitorMinInfo = getSolicitorMinimalInfoBySn(solicitorSn);
        } else {
            return;
        }
        Integer status = BeanUtil.getPropInt(solicitorMinInfo, Solicitor.STATUS, 0);
        if (status != Solicitor.STATUS_ENABLED) {
            throw new CoreSolicitorStatusAbnormalException(CoreException.getCodeDesc(CoreException.CODE_SOLICITOR_STATUS_ABNORMAL));
        }
    }

    @Override
    public void checkMerchantStatus(String merchantId, String merchantSn) {
        Map merchantMinInfo;
        if (merchantId != null) {
            merchantMinInfo = getMerchantMinimalInfoById(merchantId);
        } else if (merchantSn != null) {
            merchantMinInfo = getMerchantMinimalInfoBySn(merchantSn);
        } else {
            return;
        }
        Integer status = BeanUtil.getPropInt(merchantMinInfo, Merchant.STATUS, 0);
        if (status != Merchant.STATUS_ENABLED) {
            throw new CoreMerchantStatusAbnormalException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_STATUS_ABNORMAL));
        }
    }

    @Override
    public void checkStoreStatus(String storeId, String storeSn) {
        Map storeMinInfo;
        if (storeId != null) {
            storeMinInfo = getStoreMinimalInfoById(storeId);
        } else if (storeSn != null) {
            storeMinInfo = getStoreMinimalInfoBySn(storeSn);
        } else {
            return;
        }
        Integer status = BeanUtil.getPropInt(storeMinInfo, Store.STATUS, 0);
        if (status != Store.STATUS_ENABLED) {
            throw new CoreStoreStatusAbnormalException(CoreException.getCodeDesc(CoreException.CODE_STORE_STATUS_ABNORMAL));
        }
    }

    @Override
    public void checkTerminalStatus(String terminalId, String terminalSn) {
        Map terminalMinInfo;
        if (terminalId != null) {
            terminalMinInfo = getTerminalMinimalInfoById(terminalId);
        } else if (terminalSn != null) {
            terminalMinInfo = getTerminalMinimalInfoBySn(terminalSn);
        } else {
            return;
        }
        Integer status = BeanUtil.getPropInt(terminalMinInfo, Terminal.STATUS, 0);
        if (status != Terminal.STATUS_ACTIVATED) {
            throw new CoreTerminalStatusAbnormalException(CoreException.getCodeDesc(CoreException.CODE_TERMINAL_STATUS_ABNORMAL));
        }
    }

    @Override
    @Cacheable("getAllMeta")
    public GetAllMetaResponse getAllMeta() {
        GetAllMetaResponse response = new GetAllMetaResponse();
        response.setSubPayways(SubPayway.getAll());
        //从数据库中获取payway product_flag provider信息
        response.setPayways(getAllMetaPayways().stream().map(r -> new Payway(MapUtil.getInteger(r, DaoConstants.ID), MapUtil.getString(r, MetaPayway.NAME))).collect(Collectors.toList()));
        response.setProductFlags(getAllMetaProductFlags().stream().map(r -> new ProductFlag(MapUtil.getString(r, DaoConstants.ID), MapUtil.getString(r, MetaProductFlag.NAME))).collect(Collectors.toList()));
        response.setProviders(getAllMetaProviders().stream().map(r -> new Provider(MapUtil.getInteger(r, DaoConstants.ID), MapUtil.getString(r, MetaProvider.NAME))).collect(Collectors.toList()));
        response.setClearanceProviders(getAllMetaAcquirers().stream().map(r -> new ClearanceProvider(MapUtil.getInteger(r, DaoConstants.ID), MapUtil.getString(r, MetaAcquirer.NAME))).collect(Collectors.toList()));
        return response;
    }

    @Override
    @Cacheable("getAllProviderAbilitys")
    public GetAllProviderAbilityResponse getAllProviderAbilitys() {
        List<ProviderAbility> providerAbilitys = new ArrayList<>();
        providerAbilityDao.filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll().forEachRemaining(pa ->{
            providerAbilitys.add(ProviderAbility.fromMap(pa));
        });
        GetAllProviderAbilityResponse response = new GetAllProviderAbilityResponse();
        response.setProviderAbilitys(providerAbilitys);
        return response;
    }

    @Override
    @Cacheable("getAllMetaPayways")
    public List<Map<String, Object>> getAllMetaPayways() {
        return CollectionUtil.iterator2list(paywayDao.filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
    }

    @Override
    @Cacheable("getAllMetaProductFlags")
    public List<Map<String, Object>> getAllMetaProductFlags() {
        return CollectionUtil.iterator2list(productFlagDao.filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
    }

    @Override
    @Cacheable("getAllMetaProviders")
    public List<Map<String, Object>> getAllMetaProviders() {
        return CollectionUtil.iterator2list(providerDao.filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
    }

    @Override
    @Cacheable("getAllMetaAcquirers")
    public List<Map<String, Object>> getAllMetaAcquirers() {
        return CollectionUtil.iterator2list(acquirerDao.filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
    }

    @Override
    @Cacheable("getMetaAcquirer")
    public Map<String, Object> getMetaAcquirerById(int acquirer) {
        return acquirerDao.filter(Criteria.where(DaoConstants.ID).is(acquirer)).fetchOne();
    }

    @Override
    @Cacheable("getMetaProvider")
    public Map<String, Object> getMetaProviderById(int provider) {
        return providerDao.filter(Criteria.where(DaoConstants.ID).is(provider)).fetchOne();

    }

    @Override
    @Cacheable("getAllMetaBizModel")
    public List<MetaBizModel> getAllMetaBizModel() {
        return metaService.getAllMetaBizModel();
    }

    @Override
    @Cacheable("getAllMetaPayPath")
    public List<MetaPayPath> getAllMetaPayPath() {
        return metaService.getAllMetaPayPath();
    }

    @Override
    @Cacheable("getTradeParamsKey")
    public String getTradeParamsKey(int provider) {
        return com.wosai.pantheon.util.MapUtil.getString(getMetaProviderById(provider), MetaProvider.TRADE_PARAMS_KEY);
    }
}
