package com.wosai.upay.core.service;

import com.wosai.upay.core.model.ProviderAbilityRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CacheService {

    /**
     * 设置层级的翻译信息，当对象是低层级时将高层级的信息补上.
     * <p/>
     * 层级一：服务商、推广渠道
     * 层级二：商户
     * 层级三：门店
     * 层级四：终端
     * <p/>
     * 商户属于服务商、推广渠道
     * 门店属于商户、服务商、推广渠道
     * 终端属于、门店、商户、服务商、推广渠道
     */
    void setTranslateInfo(Map map);

    /**
     * 设置层级的翻译信息，当对象是低层级时将高层级的信息补上.
     * <p/>
     * 层级一：服务商、推广渠道
     * 层级二：商户
     * 层级三：门店
     * 层级四：终端
     * <p/>
     * 商户属于服务商、推广渠道
     * 门店属于商户、服务商、推广渠道
     * 终端属于、门店、商户、服务商、推广渠道
     */
    void setTranslateInfos(List<Map> list);

    /**
     * 根据服务商ID或SN查找服务商最小化信息（名称、状态）.
     *
     * @param vendorId
     * @param vendorSn
     * @return
     */
    Map<String, Object> getVendorMinimalInfo(String vendorId, String vendorSn);

    /**
     * 根据服务商应用ID或AppId查找服务商应用最小化信息（服务商id、应用编号、名称）.
     *
     * @param vendorAppId
     * @param vendorAppAppId
     * @return
     */
    Map<String, Object> getVendorAppMinimalInfo(String vendorAppId, String vendorAppAppId);

    /**
     * 根据推广渠道ID或SN查找推广渠道最小化信息（名称、状态）.
     *
     * @param solicitorId
     * @param solicitorSn
     * @return
     */
    Map<String, Object> getSolicitorMinimalInfo(String solicitorId, String solicitorSn);

    /**
     * 根据商户ID或SN查找商户最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param merchantId
     * @param merchantSn
     * @return
     */
    Map<String, Object> getMerchantMinimalInfo(String merchantId, String merchantSn);

    /**
     * 根据门店ID或SN查找门店最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param storeId
     * @param storeSn
     * @return
     */
    Map<String, Object> getStoreMinimalInfo(String storeId, String storeSn);

    /**
     * 根据终端编号查找终端最小化信息（名称、状态、组织层级、外部编号）.
     *
     * @param terminalId
     * @param terminalSn
     * @return
     */
    Map<String, Object> getTerminalMinimalInfo(String terminalId, String terminalSn);

    /**
     * 删除缓存.
     *
     * @param type 1:服务商;2:推广渠道;3:商户;4:门店;5终端;
     * @param obj id, sn
     */
    void deleteCache(int type, Map obj);

    /**
     * 查询清算通道(带缓存)
     *
     * @param request
     * @return
     */
    Integer getClearanceProviderWithCache(ProviderAbilityRequest request);
}
