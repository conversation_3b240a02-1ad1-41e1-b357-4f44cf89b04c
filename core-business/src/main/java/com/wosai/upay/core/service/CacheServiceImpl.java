package com.wosai.upay.core.service;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.log.LogstashMarkerAppendFileds;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.constant.PublicConstants;
import com.wosai.upay.core.model.ProviderAbilityRequest;
import com.wosai.upay.core.model.VendorApp;
import com.wosai.upay.core.util.FakeRequestUtil;
import com.wosai.upay.core.util.JsonUtil;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.core.model.ProviderAbility.*;

/**
 * <AUTHOR>
 */
@Service
public class CacheServiceImpl implements CacheService {

    private static final Logger logger = LoggerFactory.getLogger(CacheServiceImpl.class);

    /**
     * 缓存过期时间，单位秒.
     */
    @Value("${redis.cacheService.expiredTime}")
    private long cacheExpiredTime;
    @Value("${redis.cacheService.keyPrefix}")
    private String cacheKeyPrefix;
    /**
     * 缓存Key前缀.
     */
    private String cacheKeyVendorPrefix;
    private String cacheKeySolicitorPrefix;
    private String cacheKeyMerchantPrefix;
    private String cacheKeyStorePrefix;
    private String cacheKeyTerminalPrefix;
    private String cacheKeyVendorAppPrefix;

    private static String SELECT_PREFIX = "select " + ConstantUtil.KEY_ID + ", " + ConstantUtil.KEY_SN + ", " + ConstantUtil.KEY_NAME + ", " + ConstantUtil.KEY_STATUS;
    private static String SELECT_CONDITIONS_IDS = "where id in (:ids)";
    private static String SELECT_CONDITIONS_SNS = "where sn in (:sns)";
    private static String SELECT_CONDITIONS_IDS_SNS = SELECT_CONDITIONS_IDS + " or sn in (:sns)";
    private static String SELECT_VENDOR = SELECT_PREFIX + " from vendor ";
    private static String SELECT_SOLICITOR = SELECT_PREFIX + " from solicitor ";
    private static String SELECT_CLIENT_SN_VENDOR_ID_SOLICITOR_ID = ", " + ConstantUtil.KEY_CLIENT_SN + ", " + ConstantUtil.KEY_SOLICITOR_ID + ", " + ConstantUtil.KEY_VENDOR_ID;
    private static String SELECT_MERCHANT = SELECT_PREFIX + SELECT_CLIENT_SN_VENDOR_ID_SOLICITOR_ID + " from merchant ";
    private static String SELECT_STORE = SELECT_PREFIX + ", " + ConstantUtil.KEY_MERCHANT_ID + SELECT_CLIENT_SN_VENDOR_ID_SOLICITOR_ID + " from store ";
    private static String SELECT_TERMINAL = SELECT_PREFIX + ", " + ConstantUtil.KEY_STORE_ID + ", " + ConstantUtil.KEY_MERCHANT_ID +
            SELECT_CLIENT_SN_VENDOR_ID_SOLICITOR_ID + " from terminal ";
    private static String SELECT_VENDOR_APP = "select id, vendor_id, appid, name from vendor_app ";
    private static String SELECT_CONDITIONS_APPID = "where appid in (:sns)";
    private static String SELECT_CONDITIONS_IDS_APPID = SELECT_CONDITIONS_IDS + " or appid in (:sns)";

    /**
     * 有些id对应的对象在数据库中并不存在，用个单元素的map记录cache.
     */
    private static Map<String, Object> CACHE_NOT_EXISTS_THIS_OBJECT = new HashMap<String, Object>() {{
        put("object", "object not exist in database");
    }};

    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    BusinssCommonService businssCommonService;
    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private Dao<Map<String, Object>> providerAbilityDao;

    private AsyncLoadingCache<ProviderAbilityRequest, Integer> PROVIDER_ABILITY_CACHE = Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(7, TimeUnit.DAYS)
            .refreshAfterWrite(6, TimeUnit.HOURS)
            .buildAsync(this::getClearanceProvider);

    @PostConstruct
    public void init() {
        this.cacheKeyVendorPrefix = cacheKeyPrefix + "_vendor:";
        this.cacheKeySolicitorPrefix = cacheKeyPrefix + "_solicitor:";
        this.cacheKeyMerchantPrefix = cacheKeyPrefix + "_merchant:";
        this.cacheKeyStorePrefix = cacheKeyPrefix + "_store:";
        this.cacheKeyTerminalPrefix = cacheKeyPrefix + "_terminal:";
        this.cacheKeyVendorAppPrefix = cacheKeyPrefix + "_vendor_app:";
    }

    @Override
    public void setTranslateInfo(Map map) {
        setTranslateInfo(map, null);
    }

    private void setTranslateInfo(Map map, Map<String, Map> transMap) {
        if (FakeRequestUtil.isFakeRequest()) {
            return;
        }
        if (map == null || map.size() == 0) {
            return;
        }
        setMerchantInfo(map, setStoreInfo(map, setTerminalInfo(map, transMap), transMap), transMap);
        setSolicitorInfo(map, transMap);
        setVendorAppInfo(map, transMap);
        setVendorInfo(map, transMap);
    }

    @Override
    public void setTranslateInfos(List<Map> list) {
        if (FakeRequestUtil.isFakeRequest()) {
            return;
        }
        if (list == null || list.size() == 0) {
            return;
        }
        try {
            Set<String> terminalIds = new HashSet<>();
            Set<String> terminalSns = new HashSet<>();
            Set<String> storeIds = new HashSet<>();
            Set<String> storeSns = new HashSet<>();
            Set<String> merchantIds = new HashSet<>();
            Set<String> merchantSns = new HashSet<>();
            Set<String> solicitorIds = new HashSet<>();
            Set<String> solicitorSns = new HashSet<>();
            Set<String> vendorAppIds = new HashSet<>();
            Set<String> vendorAppAppIds = new HashSet<>();
            Set<String> vendorIds = new HashSet<>();
            Set<String> vendorSns = new HashSet<>();
            // 用于存list翻译的Map，避免从redis重复读相同的值多次
            Map<String, Map> transMap = new HashMap<String, Map>();
            for (Map map : list) {
                addIfContainsAndNotCached(transMap, map, terminalIds, cacheKeyTerminalPrefix, ConstantUtil.KEY_TERMINAL_ID);
                addIfContainsAndNotCached(transMap, map, terminalSns, cacheKeyTerminalPrefix, ConstantUtil.KEY_TERMINAL_SN);
                addIfContainsAndNotCached(transMap, map, storeIds, cacheKeyStorePrefix, ConstantUtil.KEY_STORE_ID);
                addIfContainsAndNotCached(transMap, map, storeSns, cacheKeyStorePrefix, ConstantUtil.KEY_STORE_SN);
                addIfContainsAndNotCached(transMap, map, merchantIds, cacheKeyMerchantPrefix, ConstantUtil.KEY_MERCHANT_ID);
                addIfContainsAndNotCached(transMap, map, merchantSns, cacheKeyMerchantPrefix, ConstantUtil.KEY_MERCHANT_SN);
                addIfContainsAndNotCached(transMap, map, solicitorIds, cacheKeySolicitorPrefix, ConstantUtil.KEY_SOLICITOR_ID);
                addIfContainsAndNotCached(transMap, map, solicitorSns, cacheKeySolicitorPrefix, ConstantUtil.KEY_SOLICITOR_SN);
                addIfContainsAndNotCached(transMap, map, vendorAppIds, cacheKeyVendorAppPrefix, ConstantUtil.KEY_VENDOR_APP_ID);
                addIfContainsAndNotCached(transMap, map, vendorAppAppIds, cacheKeyVendorAppPrefix, ConstantUtil.KEY_VENDOR_APP_APPID);
                addIfContainsAndNotCached(transMap, map, vendorIds, cacheKeyVendorPrefix, ConstantUtil.KEY_VENDOR_ID);
                addIfContainsAndNotCached(transMap, map, vendorSns, cacheKeyVendorPrefix, ConstantUtil.KEY_VENDOR_SN);
            }
            // 取terminal进行缓存
            cacheAndAddSetKeys(transMap, SELECT_TERMINAL, cacheKeyTerminalPrefix, terminalIds, terminalSns, storeIds, merchantIds, solicitorIds, vendorIds);
            // 取store进行缓存
            cacheAndAddSetKeys(transMap, SELECT_STORE, cacheKeyStorePrefix, storeIds, storeSns, null, merchantIds, solicitorIds, vendorIds);
            // 取merchant进行缓存
            cacheAndAddSetKeys(transMap, SELECT_MERCHANT, cacheKeyMerchantPrefix, merchantIds, merchantSns, null, null, solicitorIds, vendorIds);
            // 取solicitor进行缓存
            cacheAndAddSetKeys(transMap, SELECT_SOLICITOR, cacheKeySolicitorPrefix, solicitorIds, solicitorSns, null, null, null, null);
            // 取vendor_app进行缓存
            cacheAndAddSetKeys(transMap, SELECT_VENDOR_APP, cacheKeyVendorAppPrefix, vendorAppIds, vendorAppAppIds, null, null, null, null);
            // 取vendor进行缓存
            cacheAndAddSetKeys(transMap, SELECT_VENDOR, cacheKeyVendorPrefix, vendorIds, vendorSns, null, null, null, null);

            for (Map map : list) {
                setTranslateInfo(map, transMap);
            }
            transMap = null;
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfos. " + e.getMessage());
        }
    }

    private void cacheAndAddSetKeys(Map<String, Map> transMap, String sql, String keyPrefix, Set<String> ids, Set<String> sns, Set<String> storeIds, Set<String> merchantIds, Set<String>
            solicitorIds, Set<String> vendorIds) {
        if (ids.size() == 0 && sns.size() == 0) {
            return;
        }
        List<Map<String, Object>> miniInfos = getMiniInfosByIdsSns(sql, ids, sns, keyPrefix);
        if (miniInfos == null) {
            return;
        }
        for (Map<String, Object> map : miniInfos) {
            String keyId = keyPrefix + BeanUtil.getPropString(map, ConstantUtil.KEY_ID);
            String keySn = keyPrefix + BeanUtil.getPropString(map, ConstantUtil.KEY_SN);
            cacheAndSetExpired(keyId, map);
            cacheAndSetExpired(keySn, map);
            transMap.put(keyId, map);
            transMap.put(keySn, map);
            if (storeIds != null) {
                addIfContainsAndNotCached(transMap, map, storeIds, cacheKeyStorePrefix, ConstantUtil.KEY_STORE_ID);
            }
            if (merchantIds != null) {
                addIfContainsAndNotCached(transMap, map, merchantIds, cacheKeyMerchantPrefix, ConstantUtil.KEY_MERCHANT_ID);
            }
            if (solicitorIds != null) {
                addIfContainsAndNotCached(transMap, map, solicitorIds, cacheKeySolicitorPrefix, ConstantUtil.KEY_SOLICITOR_ID);
            }
            if (vendorIds != null) {
                addIfContainsAndNotCached(transMap, map, vendorIds, cacheKeyVendorPrefix, ConstantUtil.KEY_VENDOR_ID);
            }
        }
    }

    private List<Map<String, Object>> getMiniInfosByIdsSns(String sql, Set<String> ids, Set<String> sns, String keyPrefix) {
        if (ids.size() > 1000 || sns.size() > 1000) {
            return null; // TODO ids 或者sns超过1000暂忽略翻译
        }
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        if (ids.size() != 0 && sns.size() != 0) {
            sql = sql + (cacheKeyVendorAppPrefix.equals(keyPrefix) ? SELECT_CONDITIONS_IDS_APPID : SELECT_CONDITIONS_IDS_SNS);
            parameters.addValue("ids", ids);
            parameters.addValue("sns", sns);
        } else if (ids.size() != 0) {
            sql = sql + SELECT_CONDITIONS_IDS;
            parameters.addValue("ids", ids);
        } else if (sns.size() != 0) {
            sql = sql + (cacheKeyVendorAppPrefix.equals(keyPrefix) ? SELECT_CONDITIONS_APPID : SELECT_CONDITIONS_SNS);
            parameters.addValue("sns", sns);
        }
        return namedParameterJdbcTemplate.queryForList(sql, parameters);
    }

    /**
     * 没有进行过缓存的才加入keys中.
     *
     * @param transMap
     * @param map
     * @param keys
     * @param keyPrefix
     * @param key
     */
    private void addIfContainsAndNotCached(Map<String, Map> transMap, Map map, Set<String> keys, String keyPrefix, String key) {
        String value = BeanUtil.getPropString(map, key, null);
        if (value == null) {
            return;
        }
        String tranKey = keyPrefix + value;
        if (map.containsKey(key) && !keys.contains(value) && !transMap.containsKey(tranKey)) {
            Map tranMap = getCachedValue(tranKey);
            if (isEmptyMap(tranMap)) {
                keys.add(value);
            } else {
                transMap.put(tranKey, tranMap);
            }
        }
    }

    @Override
    public Map<String, Object> getVendorMinimalInfo(String vendorId, String vendorSn) {
        return getVendorMinimalInfo(vendorId, vendorSn, null);
    }

    private Map getFromTransMapOrCached(String key, Map<String, Map> transMap) {
        return transMap == null ? getCachedValue(key) : transMap.get(key);
    }

    private Map getFromTransMapOrCachedFromEhcache(String name, String key, Map<String, Map> transMap) {
        return transMap == null ? getEhCachedValue(name, key) : transMap.get(key);
    }

    private Map<String, Object> getVendorMinimalInfo(String vendorId, String vendorSn, Map<String, Map> transMap) {
        if (vendorId != null) {
            String key = cacheKeyVendorPrefix + vendorId;
            Map<String, Object> info = getFromTransMapOrCachedFromEhcache(PublicConstants.VENDOR_INFO_BY_ID, key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getVendorMinimalInfoById(vendorId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                ehCacheAndSetExpired(PublicConstants.VENDOR_INFO_BY_ID, key, info);
            }
            return info;
        } else if (vendorSn != null) {
            String key = cacheKeyVendorPrefix + vendorSn;
            Map<String, Object> info = getFromTransMapOrCachedFromEhcache(PublicConstants.VENDOR_INFO_BY_SN, key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getVendorMinimalInfoBySn(vendorSn, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                ehCacheAndSetExpired(PublicConstants.VENDOR_INFO_BY_SN, key, info);
            }
            return info;
        }
        return null;
    }

    @Override
    public Map<String, Object> getVendorAppMinimalInfo(String vendorAppId, String vendorAppAppId) {
        return getVendorAppMinimalInfo(vendorAppId, vendorAppAppId, null);
    }

    private Map<String, Object> getVendorAppMinimalInfo(String vendorAppId, String vendorAppAppId, Map<String, Map> transMap) {
        if (vendorAppId != null) {
            String key = cacheKeyVendorAppPrefix + vendorAppId;
            Map<String, Object> info = getFromTransMapOrCachedFromEhcache(PublicConstants.VENDOR_APP_INFO_BY_ID, key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getVendorAppMinimalInfoById(vendorAppId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                ehCacheAndSetExpired(PublicConstants.VENDOR_APP_INFO_BY_ID, key, info);
            }
            return info;
        } else if (vendorAppAppId != null) {
            String key = cacheKeyVendorAppPrefix + vendorAppAppId;
            Map<String, Object> info = getFromTransMapOrCachedFromEhcache(PublicConstants.VENDOR_APP_INFO_BY_APP_ID, key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getVendorAppMinimalInfoByAppId(vendorAppAppId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                ehCacheAndSetExpired(PublicConstants.VENDOR_APP_INFO_BY_APP_ID, key, info);
            }
            return info;
        }
        return null;
    }

    @Override
    public Map<String, Object> getSolicitorMinimalInfo(String solicitorId, String solicitorSn) {
        return getSolicitorMinimalInfo(solicitorId, solicitorSn, null);
    }

    private Map<String, Object> getSolicitorMinimalInfo(String solicitorId, String solicitorSn, Map<String, Map> transMap) {
        if (solicitorId != null) {
            String key = cacheKeySolicitorPrefix + solicitorId;
            Map<String, Object> info = getFromTransMapOrCachedFromEhcache(PublicConstants.SOLICITOR_INFO_BY_ID, key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getSolicitorMinimalInfoById(solicitorId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                ehCacheAndSetExpired(PublicConstants.SOLICITOR_INFO_BY_ID, key, info);
            }
            return info;
        } else if (solicitorSn != null) {
            String key = cacheKeySolicitorPrefix + solicitorSn;
            Map<String, Object> info = getFromTransMapOrCachedFromEhcache(PublicConstants.SOLICITOR_INFO_BY_SN, key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getSolicitorMinimalInfoBySn(solicitorSn, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                ehCacheAndSetExpired(PublicConstants.SOLICITOR_INFO_BY_SN, key, info);
            }
            return info;
        }
        return null;
    }

    @Override
    public Map<String, Object> getMerchantMinimalInfo(String merchantId, String merchantSn) {
        return getMerchantMinimalInfo(merchantId, merchantSn, null);
    }

    private Map<String, Object> getMerchantMinimalInfo(String merchantId, String merchantSn, Map<String, Map> transMap) {
        if (merchantId != null) {
            String key = cacheKeyMerchantPrefix + merchantId;
            Map<String, Object> info = getFromTransMapOrCached(key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getMerchantMinimalInfoById(merchantId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                cacheAndSetExpired(key, info);
            }
            return info;
        } else if (merchantSn != null) {
            String key = cacheKeyMerchantPrefix + merchantSn;
            Map<String, Object> info = getFromTransMapOrCached(key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getMerchantMinimalInfoBySn(merchantSn, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                cacheAndSetExpired(key, info);
            }
            return info;
        }
        return null;
    }

    @Override
    public Map<String, Object> getStoreMinimalInfo(String storeId, String storeSn) {
        return getStoreMinimalInfo(storeId, storeSn, null);
    }

    private Map<String, Object> getStoreMinimalInfo(String storeId, String storeSn, Map<String, Map> transMap) {
        if (storeId != null) {
            String key = cacheKeyStorePrefix + storeId;
            Map<String, Object> info = getFromTransMapOrCached(key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getStoreMinimalInfoById(storeId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                cacheAndSetExpired(key, info);
            }
            return info;
        } else if (storeSn != null) {
            String key = cacheKeyStorePrefix + storeSn;
            Map<String, Object> info = getFromTransMapOrCached(key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getStoreMinimalInfoBySn(storeSn, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                cacheAndSetExpired(key, info);
            }
            return info;
        }
        return null;
    }

    @Override
    public Map<String, Object> getTerminalMinimalInfo(String terminalId, String terminalSn) {
        return getTerminalMinimalInfo(terminalId, terminalSn, null);
    }

    private Map<String, Object> getTerminalMinimalInfo(String terminalId, String terminalSn, Map<String, Map> transMap) {
        if (terminalId != null) {
            String key = cacheKeyTerminalPrefix + terminalId;
            Map<String, Object> info = getFromTransMapOrCached(key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getTerminalMinimalInfoById(terminalId, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                cacheAndSetExpired(key, info);
            }
            return info;
        } else if (terminalSn != null) {
            String key = cacheKeyTerminalPrefix + terminalSn;
            Map<String, Object> info = getFromTransMapOrCached(key, transMap);
            if (isEmptyMap(info)) {
                info = businssCommonService.getTerminalMinimalInfoBySn(terminalSn, false);
                info = objectExistAndPutTransMap(key, info, transMap);
                cacheAndSetExpired(key, info);
            }
            return info;
        }
        return null;
    }

    private Map<String, Object> objectExistAndPutTransMap(String key, Map<String, Object> info, Map<String, Map> transMap) {
        info = (info == null) ? CACHE_NOT_EXISTS_THIS_OBJECT : info;
        if (transMap != null && !transMap.containsKey(key)) {
            transMap.put(key, info);
        }
        return info;
    }

    private Map getCachedValue(String key) {
        return redisTemplate.boundHashOps(key).entries();
    }

    private Map getEhCachedValue(String name, String key) {
        Cache cache = cacheManager.getCache(name);
        Element element = cache.get(key);
        if (!Objects.isNull(element)) {
            return (Map) element.getObjectValue();
        }
        return null;
    }

    private void cacheAndSetExpired(String key, Map<String, Object> info) {
        if (info != null) {
            if (info.containsKey(ConstantUtil.KEY_STATUS)) {
                info.put(ConstantUtil.KEY_STATUS, BeanUtil.getPropString(info, ConstantUtil.KEY_STATUS));
            }
            Map<String, Object> infoCopy = new HashMap<String, Object>();
            for (String iKey : info.keySet()) {
                if (info.get(iKey) != null) { // 需要排除null，否则putAll是会写不进redis
                    infoCopy.put(iKey, info.get(iKey));
                }
            }
            // 改为一次putAll，减少对redis的写入次数
            BoundHashOperations operations = redisTemplate.boundHashOps(key);
            operations.putAll(infoCopy);
            operations.expire(cacheExpiredTime, TimeUnit.SECONDS);
        }
    }

    private void ehCacheAndSetExpired(String name, String key, Map<String, Object> info) {
        if (info != null) {
            if (info.containsKey(ConstantUtil.KEY_STATUS)) {
                info.put(ConstantUtil.KEY_STATUS, BeanUtil.getPropString(info, ConstantUtil.KEY_STATUS));
            }
            Map<String, Object> infoCopy = new HashMap<String, Object>();
            for (String iKey : info.keySet()) {
                if (info.get(iKey) != null) { // 需要排除null，否则putAll是会写不进redis
                    infoCopy.put(iKey, info.get(iKey));
                }
            }
            Cache cache = cacheManager.getCache(name);
            if (cache != null) {
                cache.put(new Element(key, info));
            }
        }
    }

    /**
     * 10个线程的池子，用于删除缓存.
     */
    private ExecutorService deleteCachePool = Executors.newFixedThreadPool(10);

    /**
     * 删除缓存.
     *
     * @param type 1:服务商;11:服务商应用;2:推广渠道;3:商户;4:门店;5终端;
     * @param obj  id, sn
     */
    public void deleteCache(final int type, final Map obj) {
        deleteCachePool.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    deleteCache(type, BeanUtil.getPropString(obj, ConstantUtil.KEY_ID), BeanUtil.getPropString(obj, ConstantUtil.KEY_SN));
                } catch (Exception e) {
                    logger.error(LogstashMarkerAppendFileds.append(e), "Redis error. " + e.getMessage());
                }
            }
        });
    }

    /**
     * 删除缓存.
     *
     * @param type 1:服务商;11:服务商应用;2:推广渠道;3:商户;4:门店;5终端;
     * @param id
     * @param sn
     */
    private void deleteCache(int type, String id, String sn) {
        String idKey = null;
        String snKey = null;
        switch (type) {
            case ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR:
                idKey = id == null ? null : (cacheKeyVendorPrefix + id);
                snKey = sn == null ? null : (cacheKeyVendorPrefix + sn);
                deleteEhcaheCache(PublicConstants.VENDOR_INFO_BY_ID, idKey, PublicConstants.VENDOR_INFO_BY_SN, snKey);
                break;
            case ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR_APP:
                idKey = id == null ? null : (cacheKeyVendorAppPrefix + id);
                snKey = sn == null ? null : (cacheKeyVendorAppPrefix + sn);
                deleteEhcaheCache(PublicConstants.VENDOR_APP_INFO_BY_ID, idKey, PublicConstants.VENDOR_APP_INFO_BY_APP_ID, snKey);
                break;
            case ConstantUtil.BUSINESS_OBJECT_TYPE_SOLICITOR:
                idKey = id == null ? null : (cacheKeySolicitorPrefix + id);
                snKey = sn == null ? null : (cacheKeySolicitorPrefix + sn);
                deleteEhcaheCache(PublicConstants.SOLICITOR_INFO_BY_ID, idKey, PublicConstants.SOLICITOR_INFO_BY_SN, snKey);
                break;
            case ConstantUtil.BUSINESS_OBJECT_TYPE_MERCHANT:
                idKey = id == null ? null : (cacheKeyMerchantPrefix + id);
                snKey = sn == null ? null : (cacheKeyMerchantPrefix + sn);
                deleteRedisCache(idKey, snKey);
                break;
            case ConstantUtil.BUSINESS_OBJECT_TYPE_STORE:
                idKey = id == null ? null : (cacheKeyStorePrefix + id);
                snKey = sn == null ? null : (cacheKeyStorePrefix + sn);
                deleteRedisCache(idKey, snKey);
                break;
            case ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL:
                idKey = id == null ? null : (cacheKeyTerminalPrefix + id);
                snKey = sn == null ? null : (cacheKeyTerminalPrefix + sn);
                deleteRedisCache(idKey, snKey);
                break;
        }
    }

    private void deleteRedisCache(String idKey, String snKey) {
        if (idKey != null) {
            redisTemplate.delete(idKey);
        }
        if (snKey != null) {
            redisTemplate.delete(snKey);
        }
    }

    private void deleteEhcaheCache(String idName, String idKey, String snName, String snKey) {
        if (idKey != null && idName != null) {
            Cache cache = cacheManager.getCache(idName);
            if (cache != null) {
                cache.remove(idKey);
            }
        }
        if (snName != null && snKey != null) {
            Cache cache = cacheManager.getCache(snName);
            if (cache != null) {
                cache.remove(snKey);
            }
        }
    }

    /**
     * 设置终端ID、SN、名称及门店ID、商户ID、推广渠道ID、服务商ID.
     *
     * @param map
     * @param transMap
     * @return
     */
    private boolean setTerminalInfo(Map map, Map<String, Map> transMap) {
        try {
            Map<String, Object> minimalInfo = getTerminalMinimalInfo(BeanUtil.getPropString(map, ConstantUtil.KEY_TERMINAL_ID, null), BeanUtil.getPropString(map,
                    ConstantUtil.KEY_TERMINAL_SN, null), transMap);
            if (minimalInfo != null) {
                putIfNotContains(map, ConstantUtil.KEY_TERMINAL_ID, minimalInfo, ConstantUtil.KEY_ID);
                putIfNotContains(map, ConstantUtil.KEY_TERMINAL_SN, minimalInfo, ConstantUtil.KEY_SN);
                putIfNotContains(map, ConstantUtil.KEY_TERMINAL_NAME, minimalInfo, ConstantUtil.KEY_NAME);
                putIfNotContains(map, ConstantUtil.KEY_STORE_ID, minimalInfo);
                putIfNotContains(map, ConstantUtil.KEY_MERCHANT_ID, minimalInfo);
                putIfNotContains(map, ConstantUtil.KEY_SOLICITOR_ID, minimalInfo);
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_ID, minimalInfo);
                return true;
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfo > setTerminalInfo. " + e.getMessage());
        }
        return false;
    }

    /**
     * 设置门店ID、SN、名称及商户ID、推广渠道ID、服务商ID.
     *
     * @param map
     * @param idsSet   是否已设置商户ID、推广渠道ID、服务商ID
     * @param transMap
     * @return
     */
    private boolean setStoreInfo(Map map, boolean idsSet, Map<String, Map> transMap) {
        try {
            Map<String, Object> minimalInfo = getStoreMinimalInfo(BeanUtil.getPropString(map, ConstantUtil.KEY_STORE_ID, null), BeanUtil.getPropString(map,
                    ConstantUtil.KEY_STORE_SN, null), transMap);
            if (minimalInfo != null) {
                putIfNotContains(map, ConstantUtil.KEY_STORE_ID, minimalInfo, ConstantUtil.KEY_ID);
                putIfNotContains(map, ConstantUtil.KEY_STORE_SN, minimalInfo, ConstantUtil.KEY_SN);
                putIfNotContains(map, ConstantUtil.KEY_STORE_NAME, minimalInfo, ConstantUtil.KEY_NAME);
                if (!idsSet) {
                    putIfNotContains(map, ConstantUtil.KEY_MERCHANT_ID, minimalInfo);
                    putIfNotContains(map, ConstantUtil.KEY_SOLICITOR_ID, minimalInfo);
                    putIfNotContains(map, ConstantUtil.KEY_VENDOR_ID, minimalInfo);
                }
                return true;
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfo > setStoreInfo. " + e.getMessage());
        }
        return false;
    }

    /**
     * 设置商户ID、SN、名称及推广渠道ID、服务商ID.
     *
     * @param map
     * @param idsSet   是否已设置推广渠道ID、服务商ID
     * @param transMap
     * @return
     */
    private boolean setMerchantInfo(Map map, boolean idsSet, Map<String, Map> transMap) {
        try {
            Map<String, Object> minimalInfo = getMerchantMinimalInfo(BeanUtil.getPropString(map, ConstantUtil.KEY_MERCHANT_ID, null), BeanUtil.getPropString(map,
                    ConstantUtil.KEY_MERCHANT_SN, null), transMap);
            if (minimalInfo != null) {
                putIfNotContains(map, ConstantUtil.KEY_MERCHANT_ID, minimalInfo, ConstantUtil.KEY_ID);
                putIfNotContains(map, ConstantUtil.KEY_MERCHANT_SN, minimalInfo, ConstantUtil.KEY_SN);
                putIfNotContains(map, ConstantUtil.KEY_MERCHANT_NAME, minimalInfo, ConstantUtil.KEY_NAME);
                if (!idsSet) {
                    putIfNotContains(map, ConstantUtil.KEY_SOLICITOR_ID, minimalInfo);
                    putIfNotContains(map, ConstantUtil.KEY_VENDOR_ID, minimalInfo);
                }
                return true;
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfo > setMerchantInfo. " + e.getMessage());
        }
        return false;
    }

    /**
     * 设置推广渠道ID、SN、名称.
     *
     * @param map
     * @param transMap
     * @return
     */
    private void setSolicitorInfo(Map map, Map<String, Map> transMap) {
        try {
            Map<String, Object> minimalInfo = getSolicitorMinimalInfo(BeanUtil.getPropString(map, ConstantUtil.KEY_SOLICITOR_ID, null), BeanUtil.getPropString(map,
                    ConstantUtil.KEY_SOLICITOR_SN, null), transMap);
            if (minimalInfo != null) {
                putIfNotContains(map, ConstantUtil.KEY_SOLICITOR_ID, minimalInfo, ConstantUtil.KEY_ID);
                putIfNotContains(map, ConstantUtil.KEY_SOLICITOR_SN, minimalInfo, ConstantUtil.KEY_SN);
                putIfNotContains(map, ConstantUtil.KEY_SOLICITOR_NAME, minimalInfo, ConstantUtil.KEY_NAME);
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfo > setSolicitorInfo. " + e.getMessage());
        }
    }

    /**
     * 设置服务商ID、SN、名称.
     *
     * @param map
     * @param transMap
     * @return
     */
    private void setVendorInfo(Map map, Map<String, Map> transMap) {
        try {
            Map<String, Object> minimalInfo = getVendorMinimalInfo(BeanUtil.getPropString(map, ConstantUtil.KEY_VENDOR_ID, null), BeanUtil.getPropString(map,
                    ConstantUtil.KEY_VENDOR_SN, null), transMap);
            if (minimalInfo != null) {
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_ID, minimalInfo, ConstantUtil.KEY_ID);
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_SN, minimalInfo, ConstantUtil.KEY_SN);
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_NAME, minimalInfo, ConstantUtil.KEY_NAME);
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfo > setVendorInfo. " + e.getMessage());
        }
    }

    /**
     * 设置服务商应用ID、服务商Id、AppId、名称、类型.
     *
     * @param map
     * @param transMap
     * @return
     */
    private void setVendorAppInfo(Map map, Map<String, Map> transMap) {
        try {
            Map<String, Object> minimalInfo = getVendorAppMinimalInfo(BeanUtil.getPropString(map, ConstantUtil.KEY_VENDOR_APP_ID, null), BeanUtil.getPropString(map,
                    ConstantUtil.KEY_VENDOR_APP_APPID, null), transMap);
            if (minimalInfo != null) {
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_APP_ID, minimalInfo, ConstantUtil.KEY_ID);
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_APP_APPID, minimalInfo, VendorApp.APPID);
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_ID, minimalInfo, VendorApp.VENDOR_ID);
                putIfNotContains(map, ConstantUtil.KEY_VENDOR_APP_NAME, minimalInfo, VendorApp.NAME);
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when setTranslateInfo > setVendorAppInfo. " + e.getMessage());
        }
    }

    private void putIfNotContains(Map map, String key, Map minimalInfo) {
        putIfNotContains(map, key, minimalInfo, key);
    }

    private void putIfNotContains(Map map, String key, Map minimalInfo, String minimalKey) {
        if (!map.containsKey(key)) {
            map.put(key, BeanUtil.getPropString(minimalInfo, minimalKey));
        }
    }

    private boolean isEmptyMap(Map map) {
        return map == null || map.size() == 0;
    }


    /**
     * 查询清算通道(带缓存)
     *
     * @param request
     * @return
     */
    public Integer getClearanceProviderWithCache(ProviderAbilityRequest request) {
        if(null == request.getProvider()) {
            return null;
        }

        try {
            return PROVIDER_ABILITY_CACHE.get(request).get();
        } catch (ExecutionException e) {
            logger.error("getClearanceProviderWithCache exception: request={}, error={}",
                    JsonUtil.toJsonStr(request), e.getMessage());
            return null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("getClearanceProviderWithCache interrupt: request={}, error={}",
                    JsonUtil.toJsonStr(request), e.getMessage());
            return null;
        }
    }

    /**
     * 查询清算通道
     *
     * @param request
     * @return
     */
    private Integer getClearanceProvider(ProviderAbilityRequest request) {
        Criteria criteria = Criteria.where(DaoConstants.DELETED).is(0)
                .with(PROVIDER).is(request.getProvider())
                .with(PAYWAY).is(request.getPayway());
        Map<String, Object> providerAbilityMap = providerAbilityDao.filter(criteria).fetchOne();
        if(null == providerAbilityMap) {
            return null;
        }
        return MapUtil.getInteger(providerAbilityMap, CLEARANCE_PROVIDER);
    }
}
