package com.wosai.upay.core.service;

import com.google.common.collect.Lists;
import com.google.errorprone.annotations.concurrent.LazyInit;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckInRequest;
import com.wosai.upay.core.model.CashDesk;
import com.wosai.upay.core.model.CashDeskDevice;
import com.wosai.upay.core.model.CashDeskOpLog;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.CoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class CashDeskServiceImpl implements CashDeskService {

    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private DataRepository repository;

    @Autowired
    private BusinssCommonService businssCommonService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private SnGenerator snGenerator;

    private Dao<Map<String, Object>> cashDeskDao;

    private Dao<Map<String, Object>> cashDeskDeviceDao;

    private Dao<Map<String, Object>> cashDeskOpLogDao;

    private Dao<Map<String, Object>> terminalDao;

    private final ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);

    @PostConstruct
    private void init() {
        cashDeskDao = repository.getCashDeskDao();
        cashDeskDeviceDao = repository.getCashDeskDeviceDao();
        cashDeskOpLogDao = repository.getCashDeskOpLogDao();
        terminalDao = repository.getTerminalDao();
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map createCashDesk(Map cashDesk) {
        CrudUtil.ignoreForCreate(cashDesk);
        String merchantId = WosaiMapUtils.getString(cashDesk, CashDesk.MERCHANT_ID);
        List<String> terminals = (List<String>) cashDesk.remove("terminals");
        List<String> ucUserIds = Optional.ofNullable((List<String>)cashDesk.remove("uc_user_ids")).orElse(Lists.newArrayList());
        // 先将绑定了终端和收银员的收银台解绑
        List<String> deviceIds = new ArrayList<>();
        deviceIds.addAll(terminals);
        deviceIds.addAll(ucUserIds);
        Criteria criteria = Criteria.where(CashDeskDevice.DEVICE_ID).in(deviceIds).with(CashDeskDevice.MERCHANT_ID).is(merchantId);
        Iterator<Map<String, Object>> mapIterator = cashDeskDeviceDao.filter(criteria).fetchAll();
        while (mapIterator != null && mapIterator.hasNext()) {
            cashDeskDeviceDao.delete(WosaiMapUtils.getString(mapIterator.next(), DaoConstants.ID));
        }
        // 保存收银台信息
        String operator = (String) cashDesk.remove(CashDeskOpLog.OPERATOR);
        String deskId = uuidGenerator.nextUuid();
        cashDesk.put(DaoConstants.ID, deskId);
        cashDesk.put(CashDesk.SN, snGenerator.nextCashDeskSn());
        cashDeskDao.save(cashDesk);
        // 保存收银台绑定的终端和收银员信息
        for (String terminal : terminals) {
            Map terminalSave = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid(), CashDeskDevice.MERCHANT_ID, merchantId, CashDeskDevice.CASH_DESK_ID, deskId, CashDeskDevice.DEVICE_ID, terminal, CashDeskDevice.DEVICE_TYPE, CashDeskDevice.DEVICE_TYPE_TERMINAL);
            cashDeskDeviceDao.save(terminalSave);
        }
        for (String ucUserId : ucUserIds) {
            Map ucUserSave = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid(), CashDeskDevice.MERCHANT_ID, merchantId, CashDeskDevice.CASH_DESK_ID, deskId, CashDeskDevice.DEVICE_ID, ucUserId, CashDeskDevice.DEVICE_TYPE, CashDeskDevice.DEVICE_TYPE_USER);
            cashDeskDeviceDao.save(ucUserSave);
        }
        // 自动签到
        ChangeShiftsCheckInRequest checkInRequest = new ChangeShiftsCheckInRequest();
        checkInRequest.setCashDeskId(deskId);
        applicationContext.getBean(ChangeShiftsService.class).changeShiftsCheckIn(checkInRequest);
        tradeConfigService.configCommonSwitch(CollectionUtil.hashMap("merchant_id", merchantId, "type", 8, "status", 1));
        String merchantSn = businssCommonService.getMerchantSnById(merchantId);
        applicationContext.getBean(SupportService.class).removeCachedParams(merchantSn);
        cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, deskId, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, "创建", DaoConstants.CTIME, System.currentTimeMillis()));
        return getSimpleCashDeskById(deskId);
    }

    @Override
    public Map updateCashDesk(Map cashDesk) {
        CrudUtil.ignoreForUpdate(cashDesk, new String[]{CashDesk.SN});
        String id = WosaiMapUtils.getString(cashDesk, DaoConstants.ID);
        String operator = (String) cashDesk.remove(CashDeskOpLog.OPERATOR);
        Map cashDeskBefore = getSimpleCashDeskById(id);
        String merchantId = WosaiMapUtils.getString(cashDeskBefore, CashDesk.MERCHANT_ID);
        List<Map<String, Object>> oldDevices = getDevicesByCashDeskId(id);
        List<String> oldTerminals = oldDevices.stream().filter(r -> WosaiMapUtils.getIntValue(r, CashDeskDevice.DEVICE_TYPE) == CashDeskDevice.DEVICE_TYPE_TERMINAL).map(r -> WosaiMapUtils.getString(r, CashDeskDevice.DEVICE_ID)).collect(Collectors.toList());
        List<String> oldUcUserIds = oldDevices.stream().filter(r -> WosaiMapUtils.getIntValue(r, CashDeskDevice.DEVICE_TYPE) == CashDeskDevice.DEVICE_TYPE_USER).map(r -> WosaiMapUtils.getString(r, CashDeskDevice.DEVICE_ID)).collect(Collectors.toList());
        List<String> newTerminals = (List<String>) cashDesk.remove("terminals");
        List<String> newUcUserIds = Optional.ofNullable((List<String>)cashDesk.remove("uc_user_ids")).orElse(Lists.newArrayList());
        // 求交集，查询出旧的有新的没有的数据去删除  查询出新的有旧的没有的数据去新增
        List<String> waitToDeleteTerminals = oldTerminals.stream().filter(r -> !newTerminals.contains(r)).collect(Collectors.toList());
        List<String> waitToDeleteUcUserIds = oldUcUserIds.stream().filter(r -> !newUcUserIds.contains(r)).collect(Collectors.toList());
        List<String> waitToAddTerminals = newTerminals.stream().filter(r -> !oldTerminals.contains(r)).collect(Collectors.toList());
        List<String> waitToAddUcUserIds = newUcUserIds.stream().filter(r -> !oldUcUserIds.contains(r)).collect(Collectors.toList());
        List<String> deviceIds = new ArrayList<>();
        deviceIds.addAll(waitToDeleteUcUserIds);
        deviceIds.addAll(waitToDeleteTerminals);
        deviceIds.addAll(waitToAddTerminals);
        deviceIds.addAll(waitToAddUcUserIds);
        if (WosaiCollectionUtils.isNotEmpty(deviceIds)) {
            Criteria criteria = Criteria.where(CashDeskDevice.DEVICE_ID).in(deviceIds).with(CashDeskDevice.MERCHANT_ID).is(merchantId);
            Iterator<Map<String, Object>> mapIterator = cashDeskDeviceDao.filter(criteria).fetchAll();
            while (mapIterator != null && mapIterator.hasNext()) {
                cashDeskDeviceDao.delete(WosaiMapUtils.getString(mapIterator.next(), DaoConstants.ID));
            }
        }

        // 保存收银台绑定的终端和收银员信息
        for (String terminal : waitToAddTerminals) {
            Map terminalSave = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid(), CashDeskDevice.MERCHANT_ID, merchantId, CashDeskDevice.CASH_DESK_ID, id, CashDeskDevice.DEVICE_ID, terminal, CashDeskDevice.DEVICE_TYPE, CashDeskDevice.DEVICE_TYPE_TERMINAL);
            cashDeskDeviceDao.save(terminalSave);
        }
        for (String ucUserId : waitToAddUcUserIds) {
            Map ucUserSave = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid(), CashDeskDevice.MERCHANT_ID, merchantId, CashDeskDevice.CASH_DESK_ID, id, CashDeskDevice.DEVICE_ID, ucUserId, CashDeskDevice.DEVICE_TYPE, CashDeskDevice.DEVICE_TYPE_USER);
            cashDeskDeviceDao.save(ucUserSave);
        }
        cashDeskDao.updatePart(cashDesk);
        recordUpdateLog(id, operator, cashDeskBefore, cashDesk, waitToDeleteTerminals, waitToDeleteUcUserIds, waitToAddTerminals, waitToAddUcUserIds);
        return getSimpleCashDeskById(id);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int deleteCashDesk(String id) {
        Criteria criteria = Criteria.where(CashDeskDevice.CASH_DESK_ID).in(id);
        Iterator<Map<String, Object>> cashDeskDevices = cashDeskDeviceDao.filter(criteria).fetchAll();
        List<Map<String, Object>> maps = CollectionUtil.iterator2list(cashDeskDevices);
        // 先把该收银台下的终端和收银员解绑掉 再删除收银台信息
        for (Map<String, Object> map : maps) {
            String cashDeskDeviceId = WosaiMapUtils.getString(map, DaoConstants.ID);
            cashDeskDeviceDao.delete(cashDeskDeviceId);
        }
        Map update = CollectionUtil.hashMap(DaoConstants.ID, id, DaoConstants.DELETED, true);
        cashDeskDao.updatePart(update);
        return 1;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int deleteCashDeskFromOperator(Map cashDesk) {
        String cashDeskId = MapUtils.getString(cashDesk, DaoConstants.ID);
        String operator = MapUtils.getString(cashDesk, CashDeskOpLog.OPERATOR);
        deleteCashDesk(cashDeskId);
        cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, cashDeskId, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, "删除收银台", DaoConstants.CTIME, System.currentTimeMillis()));
        return 1;
    }

    @Override
    public int deleteCashDeskDevice(String id) {
        cashDeskDeviceDao.delete(id);
        return 1;
    }

    @Override
    public Map getSimpleCashDeskById(String id) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id);
        return cashDeskDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getCashDeskWithDevicesById(String id) {
        Map cashDesk = getSimpleCashDeskById(id);
        if (WosaiMapUtils.isEmpty(cashDesk)) {
            return cashDesk;
        }
        List<Map<String, Object>> devices = getDevicesByCashDeskId(id);
        cashDesk.put("devices", devices);
        return cashDesk;
    }

    @Override
    public Map getSimpleCashDeskByMerchantIdAndName(String merchantId, String name) {
        Criteria criteria = Criteria.where(CashDesk.MERCHANT_ID).is(merchantId).with(CashDesk.NAME).is(name).with(DaoConstants.DELETED).is(false);
        return cashDeskDao.filter(criteria).fetchOne();
    }

    @Override
    public List<Map> getCashDeskDevicesByMerchantIdAndDeviceIds(String merchantId, List<String> deviceIds) {
        Criteria criteria = Criteria.where(CashDeskDevice.MERCHANT_ID).is(merchantId).with(CashDeskDevice.DEVICE_ID).in(deviceIds);
        Filter filter = cashDeskDeviceDao.filter(criteria);
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    @Override
    public Map getCashDeskDeviceByMerchantIdAndDeviceId(String merchantId, String deviceId) {
        Criteria criteria = Criteria.where(CashDeskDevice.DEVICE_ID).is(deviceId).with(CashDeskDevice.MERCHANT_ID).is(merchantId);
        return cashDeskDeviceDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findCashDesks(PageInfo pageInfo, Map queryFilter) {
        ListResult listResult = findSimpleCashDesk(pageInfo, queryFilter);
        // 填充绑定的设备信息
        if (WosaiCollectionUtils.isNotEmpty(listResult.getRecords())) {
            listResult.getRecords().forEach(r -> r.put("devices", getDevicesByCashDeskId(WosaiMapUtils.getString(r, DaoConstants.ID))));
        }
        return listResult;
    }

    @Override
    public ListResult findSimpleCashDesk(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        String merchantId = WosaiMapUtils.getString(queryFilter, CashDesk.MERCHANT_ID);
        String sn = WosaiMapUtils.getString(queryFilter, CashDesk.SN);
        String name = WosaiMapUtils.getString(queryFilter, CashDesk.NAME);
        Criteria criteria = Criteria.where(DaoConstants.ID).ne(null);
        if (queryFilter.get("ids") != null && queryFilter.get("ids") instanceof List && WosaiCollectionUtils.isNotEmpty((Collection) queryFilter.get("ids"))) {
            criteria.with(DaoConstants.ID).in((List) queryFilter.get("ids"));
        }
        if (WosaiStringUtils.isNotEmpty(merchantId)) {
            criteria.with(CashDesk.MERCHANT_ID).is(merchantId);
        }
        if (WosaiStringUtils.isNotEmpty(sn)) {
            criteria.with(CashDesk.SN).is(sn);
        }
        if (WosaiStringUtils.isNotEmpty(name)) {
            criteria.with(CashDesk.NAME).like("%" + name + "%");
        }
        if (queryFilter.get("store_ids") != null && queryFilter.get("store_ids") instanceof List && WosaiCollectionUtils.isNotEmpty((Collection) queryFilter.get("store_ids"))) {
            criteria.with(CashDesk.STORE_ID).in((List) queryFilter.get("store_ids"));
        }
        if (WosaiMapUtils.getInteger(queryFilter, DaoConstants.DELETED) != null) {
            criteria.with(DaoConstants.DELETED).is(WosaiMapUtils.getIntValue(queryFilter, DaoConstants.DELETED));
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = cashDeskDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = cashDeskDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public Map getSimpleCashDesksByMerchantIdAndDeviceIds(String merchantId, List<String> deviceIds) {
        // 查询出device_id对应的收银台设备绑定信息
        Criteria criteria = Criteria.where(CashDeskDevice.DEVICE_ID).in(deviceIds).with(CashDeskDevice.MERCHANT_ID).is(merchantId);
        Iterator<Map<String, Object>> devices = cashDeskDeviceDao.filter(criteria).fetchAll();
        List<Map<String, Object>> cashDeskDevices = CollectionUtil.iterator2list(devices);
        if (WosaiCollectionUtils.isEmpty(cashDeskDevices)) {
            return null;
        }
        List<String> cashDeskIds = cashDeskDevices.stream().map(r -> WosaiMapUtils.getString(r, CashDeskDevice.CASH_DESK_ID)).collect(Collectors.toList());
        Iterator<Map<String, Object>> mapIterator = cashDeskDao.filter(Criteria.where(DaoConstants.ID).in(cashDeskIds)).fetchAll();
        List<Map<String, Object>> cashDesks = CollectionUtil.iterator2list(mapIterator);
        // key是id，value是收银台信息
        Map<String, Map<String, Object>> cashDeskMap = cashDesks.stream().collect(Collectors.toMap(r -> WosaiMapUtils.getString(r, DaoConstants.ID), r -> r));
        Map result = new HashMap(cashDesks.size());
        for (Map<String, Object> cashDeskDevice : cashDeskDevices) {
            result.put(WosaiMapUtils.getString(cashDeskDevice, CashDeskDevice.DEVICE_ID), cashDeskMap.get(WosaiMapUtils.getString(cashDeskDevice, CashDeskDevice.CASH_DESK_ID)));

        }
        return result;
    }

    @Override
    public Map getSimpleCashDeskByMerchantIdAndDeviceId(String merchantId, String deviceId) {
        Criteria criteria = Criteria.where(CashDeskDevice.DEVICE_ID).is(deviceId).with(CashDeskDevice.MERCHANT_ID).is(merchantId);
        Map<String, Object> cashDeskDevice = cashDeskDeviceDao.filter(criteria).fetchOne();
        if (WosaiMapUtils.isEmpty(cashDeskDevice)) {
            return null;
        }
        Criteria criteria1 = Criteria.where(DaoConstants.ID).is(WosaiMapUtils.getString(cashDeskDevice, CashDeskDevice.CASH_DESK_ID)).with(CashDesk.MERCHANT_ID).is(merchantId);
        return cashDeskDao.filter(criteria1).fetchOne();
    }

    @Override
    public ListResult findCashDeskOpLogs(PageInfo pageInfo, String cashDeskId) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = Criteria.where(CashDeskOpLog.CASH_DESK_ID).is(cashDeskId);
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = cashDeskOpLogDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = cashDeskOpLogDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }


    private List<Map<String, Object>> getDevicesByCashDeskId(String id) {
        Criteria criteria = Criteria.where(CashDeskDevice.CASH_DESK_ID).is(id);
        Iterator<Map<String, Object>> devices = cashDeskDeviceDao.filter(criteria).fetchAll();
        return CollectionUtil.iterator2list(devices);
    }

    private void recordUpdateLog(String id, String operator, Map cashDeskBefore, Map cashDeskAfter, List<String> waitToDeleteTerminals, List<String> waitToDeleteUcUserIds, List<String> waitToAddTerminals, List<String> waitToAddUcUserIds) {
        long ctime = System.currentTimeMillis();
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                String merchantId = MapUtils.getString(cashDeskBefore, CashDesk.MERCHANT_ID);
                String beforeName = MapUtils.getString(cashDeskBefore, CashDesk.NAME);
                String afterName = MapUtils.getString(cashDeskAfter, CashDesk.NAME);
                if (!Objects.equals(beforeName, afterName)) {
                    cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, id, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, String.format("修改收银台名称:由%s修改为%s", beforeName, afterName), DaoConstants.CTIME, ctime));
                }
                if (WosaiCollectionUtils.isNotEmpty(waitToDeleteTerminals)) {
                    List<Map> deleteTerminals = getTerminalsByIds(waitToDeleteTerminals);
                    StringJoiner changeContent = new StringJoiner("、", "删除绑定终端:", "");
                    for (Map deleteTerminal : deleteTerminals) {
                        changeContent.add(MapUtils.getString(deleteTerminal, Terminal.SN));
                    }
                    cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, id, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, substring(changeContent.toString(), 500), DaoConstants.CTIME, ctime));
                }
                if (WosaiCollectionUtils.isNotEmpty(waitToDeleteUcUserIds)) {
                    StringJoiner changeContent = new StringJoiner("、", "删除绑定收银员:", "");
                    for (String waitToDeleteUcUserId : waitToDeleteUcUserIds) {
                        List<UcMerchantUserInfo> ucMerchantUserInfos = merchantUserServiceV2.getMerchantUserByUcUserId(waitToDeleteUcUserId);
                        Optional<UcMerchantUserInfo> first = ucMerchantUserInfos.stream().filter(r -> r.getMerchant_id().equals(merchantId)).findFirst();
                        first.ifPresent(ucMerchantUserInfo -> changeContent.add(ucMerchantUserInfo.getName()));
                    }
                    cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, id, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, substring(changeContent.toString(), 500), DaoConstants.CTIME, ctime));
                }
                if (WosaiCollectionUtils.isNotEmpty(waitToAddTerminals)) {
                    List<Map> addTerminals = getTerminalsByIds(waitToAddTerminals);
                    StringJoiner changeContent = new StringJoiner("、", "增加绑定终端:", "");
                    for (Map deleteTerminal : addTerminals) {
                        changeContent.add(MapUtils.getString(deleteTerminal, Terminal.SN));
                    }
                    cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, id, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, substring(changeContent.toString(), 500), DaoConstants.CTIME, ctime));
                }
                if (WosaiCollectionUtils.isNotEmpty(waitToAddUcUserIds)) {
                    StringJoiner changeContent = new StringJoiner("、", "增加绑定收银员:", "");
                    for (String waitToAddUcUserId : waitToAddUcUserIds) {
                        List<UcMerchantUserInfo> ucMerchantUserInfos = merchantUserServiceV2.getMerchantUserByUcUserId(waitToAddUcUserId);
                        Optional<UcMerchantUserInfo> first = ucMerchantUserInfos.stream().filter(r -> r.getMerchant_id().equals(merchantId)).findFirst();
                        first.ifPresent(ucMerchantUserInfo -> changeContent.add(ucMerchantUserInfo.getName()));
                    }
                    cashDeskOpLogDao.save(CollectionUtil.hashMap(CashDeskOpLog.CASH_DESK_ID, id, CashDeskOpLog.OPERATOR, operator, CashDeskOpLog.CHANGE_CONTENT, substring(changeContent.toString(), 500), DaoConstants.CTIME, ctime));
                }
            }
        });
    }

    private List<Map> getTerminalsByIds(List<String> ids) {
        Criteria criteria = Criteria.where(DaoConstants.ID).in(ids);
        Filter filter = terminalDao.filter(criteria, CollectionUtil.hashSet(Terminal.SN));
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    private static String substring(String str, int maxLength) {
        if (str != null && str.length() > maxLength) {
            return str.substring(0, maxLength);
        }
        return str;
    }
}
