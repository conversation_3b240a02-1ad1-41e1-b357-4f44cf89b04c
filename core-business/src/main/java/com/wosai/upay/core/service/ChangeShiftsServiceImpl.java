package com.wosai.upay.core.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.dao.jdbc.IndexHintContextHolder;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.OrderBy.OrderType;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.request.ChangeShiftsBatchQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryInfo;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckInRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckOutRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsQueryRequest;
import com.wosai.upay.core.bean.request.HasCashDeskChangeShiftsRequest;
import com.wosai.upay.core.bean.request.UpdateChangeShiftsExtraRequest;
import com.wosai.upay.core.bean.response.ChangeShiftsBatchQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCashierQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckInResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckOutResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsInfo;
import com.wosai.upay.core.bean.response.ChangeShiftsQueryResponse;
import com.wosai.upay.core.exception.CoreBizException;
import com.wosai.upay.core.exception.CoreChangeShiftsException;
import com.wosai.upay.core.exception.CoreScenesException;
import com.wosai.upay.core.exception.CoreTerminalActivationException;
import com.wosai.upay.core.model.CashDesk;
import com.wosai.upay.core.model.ChangeShifts;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.core.util.CoreErrorScenesConstant;
import com.wosai.upay.user.api.model.MerchantUser;
import com.wosai.upay.util.DateUtil;

import lombok.NoArgsConstructor;

@AutoJsonRpcServiceImpl
@Service
@NoArgsConstructor
public class ChangeShiftsServiceImpl implements ChangeShiftsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChangeShiftsService.class);

    @Autowired
    CashDeskService cashDeskService;
    @Autowired
    SimpleRedisLock simpleRedisLock;
    @Autowired
    MerchantUserServiceV2 merchantUserServiceV2;

    private Dao<Map<String, Object>> changeShiftsDao;
    private Dao<Map<String, Object>> terminalDao;

    @Autowired
    public ChangeShiftsServiceImpl(DataRepository repository) {
        changeShiftsDao = repository.getChangeShiftsDao();
        terminalDao = repository.getTerminalDao();
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public ChangeShiftsCheckInResponse changeShiftsCheckIn(ChangeShiftsCheckInRequest request) {
        ChangeShiftsCheckInRequest.check(request);
        ImmutableTriple<Map<String, Object>, Integer, String> serviceInfo;
        if (StringUtil.isNotEmpty(request.getCashDeskId())) {
            Map<String, Object> cashDeskInfo = cashDeskService.getSimpleCashDeskById(request.getCashDeskId());
            if (cashDeskInfo == null) {
                throw new CoreChangeShiftsException("收银台不存在");
            }
            serviceInfo = ImmutableTriple.of(cashDeskInfo, ChangeShifts.TYPE_CASHDESK, request.getCashDeskId());
        } else {
            serviceInfo = getServiceIdByTerminalSn(request.getTerminalSn(), request.isAccessCashDesk());
        }
        Map<String, Object> changeShiftsInfo;
        try {
            if (serviceInfo.getMiddle() == ChangeShifts.TYPE_TERMINAL) {
                changeShiftsInfo = terminalChangeShiftsCheckIn(serviceInfo.getLeft());
            } else {
                changeShiftsInfo = cashDeskChangeShiftsCheckIn(serviceInfo.getLeft());
            }
        } catch (DataIntegrityViolationException e) {
            LOGGER.warn("changeShifts checkin fail", e);
            throw new CoreChangeShiftsException("正在处理中，请稍后再试");
        }
        return new ChangeShiftsCheckInResponse(MapUtil.getString(changeShiftsInfo, ChangeShifts.BATCH_SN), MapUtil.getLongValue(changeShiftsInfo, ChangeShifts.START_DATE));
    }

    /**
     * 收银台签到
     * 
     * @param cashDeskInfo
     * @return Map<String, Object>
     */
    private Map<String, Object> cashDeskChangeShiftsCheckIn(Map<String, Object> cashDeskInfo) {
        String cashDeskId = MapUtil.getString(cashDeskInfo, DaoConstants.ID);
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(cashDeskId).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK);
        Filter<Map<String, Object>> filter = changeShiftsDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map<String, Object> changeShiftsInfo = filter.fetchOne();
        // 批次未签到或最后一批次已签退时，重新生成批次号
        if (changeShiftsInfo == null || MapUtil.getLong(changeShiftsInfo, ChangeShifts.END_DATE) != null) {
            String nextBatchNo = getNextBatchNo(MapUtil.getString(changeShiftsInfo, ChangeShifts.BATCH_SN));
            changeShiftsInfo = lockCashDeskChangeShifts(MapUtil.getString(changeShiftsInfo, Terminal.MERCHANT_ID), cashDeskId, () -> {
                Map<String, Object> nextChangeShifts = CollectionUtil.hashMap(
                        ChangeShifts.MERCHANT_ID, MapUtil.getString(cashDeskInfo, CashDesk.MERCHANT_ID),
                        ChangeShifts.STORE_ID, MapUtil.getString(cashDeskInfo, CashDesk.STORE_ID),
                        ChangeShifts.TYPE, ChangeShifts.TYPE_CASHDESK,
                        ChangeShifts.SERVICE_ID, cashDeskId,
                        ChangeShifts.BATCH_SN, nextBatchNo,
                        ChangeShifts.START_DATE, System.currentTimeMillis());
                changeShiftsDao.save(nextChangeShifts);
                return nextChangeShifts;
            });
        }
        return changeShiftsInfo;
    }

    /**
     * 终端签到
     * 
     * @param terminalInfo
     * @return Map<String, Object>
     */
    private Map<String, Object> terminalChangeShiftsCheckIn(Map<String, Object> terminalInfo) {
        String serviceId = MapUtil.getString(terminalInfo, DaoConstants.ID);
        String nextBatchSn = "";
        Map<String, Object> lastChangeShifts = getLastOneChangeShifts(serviceId, ChangeShifts.TYPE_TERMINAL, null);
        // 终端处于签到状态，且存在上一笔签到记录，且未进行签退时，返回上一笔班次信息
        if (Terminal.CURRENT_CHECKIN_STATUS_CHECKIN == MapUtil.getIntValue(terminalInfo, Terminal.CURRENT_CHECKIN_STATUS)
                && lastChangeShifts != null && MapUtil.getLong(lastChangeShifts, ChangeShifts.END_DATE) == null) {
            return lastChangeShifts;
        }
        String lastBatchNo = MapUtil.getString(lastChangeShifts, ChangeShifts.BATCH_SN);
        nextBatchSn = getNextBatchNo(lastBatchNo);
        terminalDao.updatePart(CollectionUtil.hashMap(DaoConstants.ID, MapUtil.getString(terminalInfo, DaoConstants.ID),
                Terminal.CURRENT_CHECKIN_STATUS, Terminal.CURRENT_CHECKIN_STATUS_CHECKIN));

        Map<String, Object> terminalChangeShiftsInfo = CollectionUtil.hashMap(
                ChangeShifts.MERCHANT_ID, MapUtil.getString(terminalInfo, Terminal.MERCHANT_ID),
                ChangeShifts.STORE_ID, MapUtil.getString(terminalInfo, Terminal.STORE_ID),
                ChangeShifts.TYPE, ChangeShifts.TYPE_TERMINAL,
                ChangeShifts.SERVICE_ID, serviceId,
                ChangeShifts.BATCH_SN, nextBatchSn,
                ChangeShifts.START_DATE, System.currentTimeMillis());
        changeShiftsDao.save(terminalChangeShiftsInfo);
        return terminalChangeShiftsInfo;
    }

    /**
     * 设备签退
     */
    @Transactional(value = "transactionManager")
    public ChangeShiftsCheckOutResponse changeShiftsCheckOut(ChangeShiftsCheckOutRequest request) {
        ChangeShiftsCheckOutRequest.check(request);
        ImmutableTriple<Map<String, Object>, Integer, String> serviceInfo;
        if (StringUtil.isNotEmpty(request.getCashDeskId())) {
            Map<String, Object> cashDeskInfo = cashDeskService.getSimpleCashDeskById(request.getCashDeskId());
            if (cashDeskInfo == null) {
                throw new CoreChangeShiftsException("收银台不存在");
            }
            serviceInfo = ImmutableTriple.of(cashDeskInfo, ChangeShifts.TYPE_CASHDESK, request.getCashDeskId());
        } else {
            serviceInfo = getServiceIdByTerminalSn(request.getTerminalSn(), request.isAccessCashDesk());
        }
        Map<String, Object> changeShiftsInfo;
        if (serviceInfo.getMiddle() == ChangeShifts.TYPE_TERMINAL) {
            changeShiftsInfo = terminalChangeShiftsCheckOut(serviceInfo.getLeft(), request.getBatchSn(), request.getCashierId(), request.getCashierNo());
        } else {
            changeShiftsInfo = cashDeskChangeShiftsCheckOut(serviceInfo.getLeft(), request.getCashierId(), request.getCashierNo());
        }
        return new ChangeShiftsCheckOutResponse(MapUtil.getString(changeShiftsInfo, ChangeShifts.BATCH_SN), 
                MapUtil.getLongValue(changeShiftsInfo, ChangeShifts.END_DATE),
                MapUtil.getString(changeShiftsInfo, ChangeShifts.NEXT_BATCH_SN));
    }

    /**
     * 
     * 收银台签退
     * 
     * @param cashDeskInfo
     * @param cashierNo
     * @return
     */
    private Map<String, Object> cashDeskChangeShiftsCheckOut(Map<String, Object> cashDeskInfo, String cashierId, String cashierNo) {
        String cashDeskId = MapUtil.getString(cashDeskInfo, DaoConstants.ID);
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(cashDeskId).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK);
        Filter<Map<String, Object>> filter = changeShiftsDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(2);
        List<Map<String, Object>> changeShiftsInfos = CollectionUtil.iterator2list(filter.fetchAll());
        if (changeShiftsInfos == null || changeShiftsInfos.isEmpty()) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_CHECKIN_NOT_FOUND, CoreBizException.CODE_CHANGE_SHITFS_EROR, CoreChangeShiftsException.class.getName());
        }
        Map<String, Object> changeShiftsInfo = changeShiftsInfos.get(0);
        if (MapUtil.getLong(changeShiftsInfo, ChangeShifts.END_DATE) != null) {
            // 最后一个批次的结束时间在间隔范围之内，可以认为当前请求是重复签退，返回当前批次信息
            if (System.currentTimeMillis() - MapUtil.getLong(changeShiftsInfo, ChangeShifts.END_DATE) < ApolloConfigurationCenterUtil.getChangeshiftsCheckoutInterval() 
                    && Objects.equals(cashierId, MapUtil.getString(changeShiftsInfo, ChangeShifts.CASHIER_ID))) {
                return changeShiftsInfo;
            } else {
                throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_ALERDY_CHECK_OUT, CoreBizException.CODE_CHANGE_SHITFS_EROR, CoreChangeShiftsException.class.getName());
            }
        } else if (changeShiftsInfos.size() == 2){
            Map<String, Object> lastChangeShiftsInfo = changeShiftsInfos.get(1);
            if (Objects.equals(cashierId, MapUtil.getString(lastChangeShiftsInfo, ChangeShifts.CASHIER_ID))
                    && System.currentTimeMillis() - MapUtil.getLongValue(lastChangeShiftsInfo, ChangeShifts.END_DATE) < ApolloConfigurationCenterUtil.getChangeshiftsCheckoutInterval()) {
                // 倒数第二个批次的结束时间在间隔范围之内，可以认为当前请求是重复签退，返回上个批次和当前批次信息
                lastChangeShiftsInfo.put(ChangeShifts.NEXT_BATCH_SN, MapUtil.getString(changeShiftsInfo, ChangeShifts.BATCH_SN));
                return lastChangeShiftsInfo;
            }
        }
        String merchantId = MapUtil.getString(changeShiftsInfos.get(0), ChangeShifts.MERCHANT_ID);
        Map<String, Object> nextChangeShiftsInfo = lockCashDeskChangeShifts(merchantId, cashDeskId, () -> {
            long now = System.currentTimeMillis();
            Map<String, Object> updateInfo = CollectionUtil.hashMap(DaoConstants.ID, MapUtil.getLong(changeShiftsInfo, DaoConstants.ID),
                    ChangeShifts.END_DATE, now,
                    ChangeShifts.CASHIER_NO, cashierNo
            );
            setCashierUpdateInfo(merchantId, cashierId, updateInfo);
            changeShiftsDao.updatePart(updateInfo);
            changeShiftsInfo.putAll(updateInfo);

            // 收银台签退后自动生成下个批次
            Map<String, Object> nextChangeShifts = CollectionUtil.hashMap(
                    ChangeShifts.MERCHANT_ID, MapUtil.getString(cashDeskInfo, Terminal.MERCHANT_ID),
                    ChangeShifts.STORE_ID, MapUtil.getString(cashDeskInfo, Terminal.STORE_ID),
                    ChangeShifts.TYPE, ChangeShifts.TYPE_CASHDESK,
                    ChangeShifts.SERVICE_ID, cashDeskId,
                    ChangeShifts.BATCH_SN, getNextBatchNo(MapUtil.getString(changeShiftsInfo, ChangeShifts.BATCH_SN)),
                    ChangeShifts.START_DATE, now + 1);
            changeShiftsDao.save(nextChangeShifts);
            return nextChangeShifts;
        });
        
        changeShiftsInfo.put(ChangeShifts.NEXT_BATCH_SN, MapUtil.getString(nextChangeShiftsInfo, ChangeShifts.BATCH_SN));
        return changeShiftsInfo;
    }

    // 设置收银员信息（由于收银员绑定关系会移除，所以将收银员名称和手机号保留在扩展信息中）
    public void setCashierUpdateInfo(String merchantId, String cashierId, Map<String, Object> updateInfo) {
        if (StringUtil.isNotBlank(cashierId)) {
            QueryMerchantUserReq req = new QueryMerchantUserReq();
            req.setMerchant_id(merchantId);
            req.setUc_user_id(cashierId);
            List<UcMerchantUserInfo> ucMerchantUserInfos = merchantUserServiceV2.getMerchantUser(req);
            if (ucMerchantUserInfos != null && !ucMerchantUserInfos.isEmpty()) {
                UcMerchantUserInfo userInfo = ucMerchantUserInfos.get(0);
                Map<String, Object> cashierInfo = MapUtil.hashMap(ChangeShifts.CASHIER_NAME, userInfo.getName());
                if (userInfo.getUcUserInfo() != null) {
                    // 老板签退班次优先使用别名
                    if (MerchantUser.ROLE_SUPER_ADMIN.equals(userInfo.getRole()) 
                            && StringUtil.isNotEmpty(userInfo.getUcUserInfo().getNickname())) {
                        cashierInfo.put(ChangeShifts.CASHIER_NAME, userInfo.getUcUserInfo().getNickname());
                    }
                    cashierInfo.put(ChangeShifts.CASHIER_PHONE, userInfo.getUcUserInfo().getCellphone());
                }
                updateInfo.put(ChangeShifts.EXTRA, MapUtil.hashMap(ChangeShifts.EXTRA_CASHIER_INFO, cashierInfo));
                updateInfo.put(ChangeShifts.CASHIER_ID, cashierId);
            } else {
                throw new CoreChangeShiftsException("收银员信息异常");
            }
        }
    }

    /**
     * 
     * 终端签退
     * 
     * @param terminalInfo
     * @param batchSn
     * @param cashierNo
     * @return Map<String, Object>
     */
    private Map<String, Object> terminalChangeShiftsCheckOut(Map<String, Object> terminalInfo, String batchSn, String cashierId, String cashierNo) {
        Map<String, Object> terminalChangeShiftsInfo = getLastOneChangeShifts(MapUtil.getString(terminalInfo, DaoConstants.ID), ChangeShifts.TYPE_TERMINAL, batchSn);
        if (null == terminalChangeShiftsInfo) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_CHECKIN_NOT_FOUND, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        }

        if (Terminal.CURRENT_CHECKIN_STATUS_CHECKOUT == MapUtil.getIntValue(terminalInfo, Terminal.CURRENT_CHECKIN_STATUS)) {
            if (!Objects.equals(cashierNo, MapUtil.getString(terminalChangeShiftsInfo, ChangeShifts.CASHIER_NO))) {
                throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_ALEADY_CHECKOUT, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
            }
        } else if (Terminal.CURRENT_CHECKIN_STATUS_CHECKIN == MapUtil.getIntValue(terminalInfo, Terminal.CURRENT_CHECKIN_STATUS)) {
            long endDate = System.currentTimeMillis();
            terminalDao.updatePart(CollectionUtil.hashMap(DaoConstants.ID, MapUtil.getString(terminalInfo, DaoConstants.ID),
                    Terminal.CURRENT_CHECKIN_STATUS, Terminal.CURRENT_CHECKIN_STATUS_CHECKOUT
            ));
            Map<String, Object> updateInfo = CollectionUtil.hashMap(DaoConstants.ID, MapUtil.getLong(terminalChangeShiftsInfo, DaoConstants.ID),
                    ChangeShifts.END_DATE, endDate,
                    ChangeShifts.CASHIER_NO, cashierNo
            );
            setCashierUpdateInfo(MapUtil.getString(terminalChangeShiftsInfo, ChangeShifts.MERCHANT_ID), cashierId, updateInfo);
            changeShiftsDao.updatePart(updateInfo);
            terminalChangeShiftsInfo.putAll(updateInfo);
        } else {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_CHECKIN_NOT_FOUND, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        }
        return terminalChangeShiftsInfo;
    }

    /**
     * 获取设备班次信息
     */
    public ChangeShiftsQueryResponse getChangeShiftsInfo(ChangeShiftsQueryRequest request) {
        ChangeShiftsQueryRequest.check(request);
        Integer useType = ChangeShifts.TYPE_CASHDESK;
        Criteria criteria;
        if (!StringUtil.isEmpty(request.getCsStoreId())) {
            criteria = Criteria.where(ChangeShifts.STORE_ID).is(request.getCsStoreId()).with(ChangeShifts.TYPE).is(useType);
            if (!StringUtil.isEmpty(request.getCashDeskId())) {
                criteria = criteria.with(ChangeShifts.SERVICE_ID).is(request.getCashDeskId());
            }
        } else {
            String serviceId = request.getCashDeskId();
            if (StringUtil.isEmpty(serviceId)) {
                ImmutableTriple<Map<String, Object>, Integer, String> serviceInfo = getServiceIdByTerminalSn(request.getTerminalSn(), request.isAccessCashDesk());
                useType = serviceInfo.getMiddle();
                serviceId = serviceInfo.getRight();
            }
            criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(serviceId).with(ChangeShifts.TYPE).is(useType);
        }

        String batchSn = request.getBatchSn();
        if (StringUtil.isNotEmpty(batchSn) && (useType == ChangeShifts.TYPE_TERMINAL || (request.getUseBatchSn() != null && request.getUseBatchSn()))) {
            criteria = criteria.with(ChangeShifts.BATCH_SN).is(batchSn);
        }
        Filter<Map<String, Object>> filter = changeShiftsDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map<String, Object> info = filter.fetchOne();
        ChangeShiftsQueryResponse response = ChangeShiftsQueryResponse.toChangeShiftsQueryResponse(info);
        if (response != null && response.getType() == ChangeShifts.TYPE_CASHDESK) {
            response.setCashDeskName(MapUtil.getString(cashDeskService.getSimpleCashDeskById(response.getServiceId()), CashDesk.NAME));
        }
        return response;
    }

    /**
     * 获取设备班次列表
     */
    @Override
    public ChangeShiftsBatchQueryResponse getChangeShiftsList(ChangeShiftsBatchQueryRequest request) {
        ChangeShiftsBatchQueryRequest.check(request);
        Integer useType = ChangeShifts.TYPE_CASHDESK;
        Criteria criteria;
        if (!StringUtil.isEmpty(request.getCsMerchantId())) {
            criteria = Criteria.where(ChangeShifts.MERCHANT_ID).is(request.getCsMerchantId());
            if (request.getType() != null) {
                criteria = criteria.with(ChangeShifts.TYPE).is(request.getType());
            }
            if (com.wosai.pantheon.util.CollectionUtil.isNotEmpty(request.getCsStoreIds())) {
                criteria = criteria.with(ChangeShifts.STORE_ID).in(request.getCsStoreIds());
            }
        } else if (!StringUtil.isEmpty(request.getCsStoreId())) {
            criteria = Criteria.where(ChangeShifts.STORE_ID).is(request.getCsStoreId()).with(ChangeShifts.TYPE).is(useType);
            if (!StringUtil.isEmpty(request.getCashDeskId())) {
                criteria = criteria.with(ChangeShifts.SERVICE_ID).is(request.getCashDeskId());
            }
        } else {
            String serviceId = request.getCashDeskId();
            if (StringUtil.isNotBlank(request.getTerminalSn())) {
                ImmutableTriple<Map<String, Object>, Integer, String> serviceInfo = getServiceIdByTerminalSn(request.getTerminalSn(), request.isAccessCashDesk());
                serviceId = serviceInfo.getRight();
                useType = serviceInfo.getMiddle();
            }
            criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(serviceId).with(ChangeShifts.TYPE).is(useType);
        }
        boolean isTradeCashierQuery = request.getTradeCashierQuery() != null && request.getTradeCashierQuery();
        if (isTradeCashierQuery || (request.getTimeRange() != null && request.getTimeRange() == ChangeShiftsBatchQueryRequest.TIMERANGE_TRADE_TIME)){
            // 收银员查询按照签到和签退时间范围查询
            long start = request.getStartDate();
            long end = request.getEndDate() != null ? request.getEndDate() : System.currentTimeMillis();
            Criteria betweenMatch = Criteria.and(Criteria.where(ChangeShifts.START_DATE).ge(start), Criteria.where(ChangeShifts.END_DATE).le(end));
            Criteria startMatch = Criteria.and(Criteria.where(ChangeShifts.START_DATE).le(start), Criteria.where(ChangeShifts.END_DATE).ge(start));
            Criteria endMatch = Criteria.and(Criteria.where(ChangeShifts.START_DATE).ge(start), Criteria.where(ChangeShifts.START_DATE).le(end));
            criteria = criteria.withOr(betweenMatch, startMatch, endMatch);
            criteria = criteria.with(ChangeShifts.CASHIER_ID).ne(null);
        } else {
            String searchDateKey = ChangeShifts.START_DATE;
            if (request.getTimeRange() != null && request.getTimeRange() == ChangeShiftsBatchQueryRequest.TIMERANGE_END_TIME) {
                searchDateKey = ChangeShifts.END_DATE;
            }
            // 批次查询按照签到开始时间范围查询
            if (request.getStartDate() != null) {
                criteria.with(searchDateKey).ge(request.getStartDate());
            }
            if (request.getEndDate() != null) {
                criteria.with(searchDateKey).le(request.getEndDate());
            }
        }
        if (request.getReturnUnCheckout() == null || !request.getReturnUnCheckout()) {
            criteria.with(ChangeShifts.END_DATE).ne(null);
        }
        PageInfo pageInfo = new PageInfo(request.getPage(), request.getPageSize());
        if (request.getOrderBy() == null || request.getOrderBy() == ChangeShiftsBatchQueryRequest.ORDER_BY_CTIME_DESC) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderType.DESC)));
        } else if (request.getOrderBy() == ChangeShiftsBatchQueryRequest.ORDER_BY_END_DATE_DESC) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy(ChangeShifts.END_DATE, OrderType.DESC)));
        } else if (request.getOrderBy() == ChangeShiftsBatchQueryRequest.ORDER_BY_END_DATE_ASC) {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy(ChangeShifts.END_DATE, OrderType.ASC)));
        } else {
            pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderType.ASC)));
        }
        List<ChangeShiftsInfo> changeShiftsInfo = null;
        long count = 0L;
        Filter<Map<String, Object>> filter;
        try {
            if (!isTradeCashierQuery) {
                count = changeShiftsDao.filter(criteria).count();
                filter = changeShiftsDao.filter(criteria);
            } else {
                count = 1024L;
                IndexHintContextHolder.setIndexHint("force index(idx_merc_start_end)");
                // 收银员查询时，由于返回的数据条数过多，只查询需要使用的字段
                filter = changeShiftsDao.filter(criteria, Arrays.asList(DaoConstants.ID, ChangeShifts.BATCH_SN, ChangeShifts.TYPE, ChangeShifts.START_DATE, ChangeShifts.END_DATE, ChangeShifts.SERVICE_ID, ChangeShifts.CASHIER_ID, ChangeShifts.EXTRA));
            }
            PageInfoUtil.pagination(pageInfo, filter);
            changeShiftsInfo = CollectionUtil.iterator2list(filter.fetchAll()).stream().map(ChangeShiftsInfo::toChangeShiftsInfo).collect(Collectors.toList());
        }finally {
            IndexHintContextHolder.clear();
        }
        if (changeShiftsInfo != null &&  changeShiftsInfo.size() > 0) {
            // 非收银员查询时才设置收银台名称
            if (!isTradeCashierQuery) {
                // 设置收银台名称
                List<String> cashDeskIds = changeShiftsInfo
                        .stream()
                        .filter(csi -> csi.getType() == ChangeShifts.TYPE_CASHDESK)
                        .map(csi -> csi.getServiceId())
                        .distinct()
                        .collect(Collectors.toList());
                if (!cashDeskIds.isEmpty()) {
                    Map<String, String> cashDeskNameMap = cashDeskIds
                            .stream()
                            .map(id -> cashDeskService.getSimpleCashDeskById(id))
                            .filter(cashDesk -> cashDesk != null)
                            .collect(Collectors.toMap(cashDesk -> MapUtil.getString(cashDesk, DaoConstants.ID), cashDesk -> MapUtil.getString(cashDesk, CashDesk.NAME)));
                    changeShiftsInfo
                            .stream()
                            .forEach(csi -> csi.setCashDeskName(cashDeskNameMap.get(csi.getServiceId())));
                }
            } else {
                // 查询收银员信息时，移除扩展字段
                changeShiftsInfo.stream().forEach(c -> c.setExtra(null));
            }
        }
        return new ChangeShiftsBatchQueryResponse(count, changeShiftsInfo);
    }

    /**
     * 
     * 收银台种包含多个设备，不支持并发处理，添加redis锁
     * 
     * @param merchantId
     * @param cashDeskId
     * @param sp
     * @return
     */
    private Map<String, Object> lockCashDeskChangeShifts(String merchantId, String cashDeskId, Supplier<Map<String, Object>> sp){
        String lockKey = SimpleRedisLock.LOCK_KEY_CASH_DESK_PREFIX + merchantId + "-" + cashDeskId;
        String lockValue = UUID.randomUUID().toString();
        if (simpleRedisLock.tryLock(lockKey, lockValue, 30, TimeUnit.SECONDS)) {
            try {
                return sp.get();
            } finally {
                simpleRedisLock.unlock(lockKey, lockValue);
            }
        } else {
            throw new CoreScenesException(CoreErrorScenesConstant.CASH_DESK_CHECK_IN_PROG, CoreBizException.CODE_CHANGE_SHITFS_EROR, CoreChangeShiftsException.class.getName());
        }
    }
    
    private ImmutableTriple<Map<String, Object>, Integer, String> getServiceIdByTerminalSn(String terminalSn, boolean accessCashDesk) {
        TerminalService terminalService = SpringContextHolder.getBean(TerminalService.class);
        Map terminalInfo = terminalService.getTerminalBySn(terminalSn);
        if (terminalInfo == null) {
            throw new CoreTerminalActivationException(String.format("终端号 %s 不存在", terminalSn));
        }
        Map<String, Object> terminalOrCashDeskInfo = terminalInfo;
        int type = ChangeShifts.TYPE_TERMINAL;
        String deviceId = MapUtil.getString(terminalInfo, DaoConstants.ID);
        if (accessCashDesk) {
            Map<String, Object> simpleCashDesk = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(MapUtil.getString(terminalInfo, Terminal.MERCHANT_ID), deviceId);
            if (simpleCashDesk != null) {
                type = ChangeShifts.TYPE_CASHDESK;
                deviceId = MapUtil.getString(simpleCashDesk, DaoConstants.ID);
                terminalOrCashDeskInfo = simpleCashDesk;
            }
        }
        return ImmutableTriple.of(terminalOrCashDeskInfo, type, deviceId);
    }

    private Map<String, Object> getLastOneChangeShifts(String serviceId, Integer type, String batchNo){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(serviceId).with(ChangeShifts.TYPE).is(type);
        if (StringUtil.isNotEmpty(batchNo)) {
            criteria = criteria.with(ChangeShifts.BATCH_SN).is(batchNo);
        }
        Filter<Map<String, Object>> filter = changeShiftsDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        return filter.fetchOne();
    }

    private String getNextBatchNo(String lastBatchNo) {
        String todayStr = DateUtil.formatDate(new Date(), "yyyyMMdd");
        int nextIdx;
        if (lastBatchNo == null) {
            nextIdx = 1;
        } else {
            String date = lastBatchNo.substring(0, 8);
            String index = lastBatchNo.substring(8);
            nextIdx = (!todayStr.equals(date) ? 1 : (Integer.valueOf(index) + 1));
            if (nextIdx > 9999) {
                throw new CoreScenesException(CoreErrorScenesConstant.TERMIAL_CHANGE_SHIFTS_CHECKIN_FAIL, CoreChangeShiftsException.CODE_CHANGE_SHITFS_EROR, CoreChangeShiftsException.class.getName());
            }
        }
        return todayStr + StringUtils.leftPad(nextIdx + "", 4, "0");
    }

    @Override
    public void updateChangeShiftsExtra(UpdateChangeShiftsExtraRequest request) {
        UpdateChangeShiftsExtraRequest.check(request);
        changeShiftsDao.updatePart(MapUtil.hashMap(DaoConstants.ID, request.getId(),
                    ChangeShifts.EXTRA, request.getExtra()
                ));
        
    }

    @Override
    public boolean hasCashDeskChangeShifts(HasCashDeskChangeShiftsRequest request) {
        HasCashDeskChangeShiftsRequest.check(request);
        for (String terminalSn : request.getTerminalSns()) {
            ImmutableTriple<Map<String, Object>, Integer, String> serviceInfo = getServiceIdByTerminalSn(terminalSn, Boolean.TRUE);
            if (serviceInfo.getMiddle() == ChangeShifts.TYPE_CASHDESK) {
                Map<String, Object>  changeShifts = getLastOneChangeShifts(serviceInfo.getRight(), ChangeShifts.TYPE_CASHDESK, null);
                if (changeShifts != null) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public ChangeShiftsCashierQueryResponse getChangeShiftsCashier(ChangeShiftsCashierQueryRequest request) {
        ChangeShiftsCashierQueryRequest.check(request);
        List<ChangeShiftsInfo> responseInfos = new ArrayList<>(request.getCashierQueryInfos().size());
        List<String> queryColumns = Arrays.asList(DaoConstants.ID, ChangeShifts.CASHIER_ID, ChangeShifts.EXTRA);
        for (ChangeShiftsCashierQueryInfo queryInfo : request.getCashierQueryInfos()) {
            Map<String, Object> result = null;
            // 优先使用收银台
            if (queryInfo.getCashDeskId() != null) {
                result = changeShiftsDao.filter(Criteria.where(ChangeShifts.START_DATE).le(queryInfo.getCtime())
                        .with(ChangeShifts.END_DATE).ge(queryInfo.getCtime())
                        .with(ChangeShifts.SERVICE_ID).is(queryInfo.getCashDeskId())
                        .with(ChangeShifts.MERCHANT_ID).is(queryInfo.getMerchantId())
                        .with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK)
                        .with(ChangeShifts.CASHIER_ID).ne(null), queryColumns).fetchOne();
            }
            // 收银台批次不存在时，查询终端批次
            if (result == null && queryInfo.getTerminalId() != null) {
                result = changeShiftsDao.filter(Criteria.where(ChangeShifts.START_DATE).le(queryInfo.getCtime())
                        .with(ChangeShifts.END_DATE).ge(queryInfo.getCtime())
                        .with(ChangeShifts.SERVICE_ID).is(queryInfo.getTerminalId())
                        .with(ChangeShifts.MERCHANT_ID).is(queryInfo.getMerchantId())
                        .with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL)
                        .with(ChangeShifts.CASHIER_ID).ne(null), queryColumns).fetchOne();
            }
            ChangeShiftsInfo changeShiftsInfo = null;
            if (result != null && !result.isEmpty()) {
                changeShiftsInfo = new ChangeShiftsInfo();
                changeShiftsInfo.setId(MapUtil.getLongValue(result, DaoConstants.ID));
                changeShiftsInfo.setCashierId(MapUtil.getString(result, ChangeShifts.CASHIER_ID));
                Object extra;
                if ((extra = result.get(ChangeShifts.EXTRA)) != null && extra instanceof Map) {
                    Map<String, Object> cashierInfo = MapUtil.getMap((Map)extra, ChangeShifts.EXTRA_CASHIER_INFO);
                    if (cashierInfo != null) {
                        changeShiftsInfo.setCashierName(MapUtil.getString(cashierInfo, ChangeShifts.CASHIER_NAME));
                        changeShiftsInfo.setCashierPhone(MapUtil.getString(cashierInfo, ChangeShifts.CASHIER_PHONE));
                    }
                }
            }
            responseInfos.add(changeShiftsInfo);
        }
        return new ChangeShiftsCashierQueryResponse(responseInfos);
    }
}
