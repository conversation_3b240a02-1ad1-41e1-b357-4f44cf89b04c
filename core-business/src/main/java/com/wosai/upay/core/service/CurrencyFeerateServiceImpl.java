package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.exception.CoreCurrencyFeerateMappingException;
import com.wosai.upay.core.model.CurrencyFeerate;
import com.wosai.upay.core.model.OpLog;
import com.wosai.upay.core.repository.DataRepository;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;

/**
 * Created by maoyu on 2018/3/12.
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class CurrencyFeerateServiceImpl implements CurrencyFeerateService {
    @Autowired
    private UuidGenerator uuidGenerator;

    @Autowired
    private BizLogFacade bizLogFacade;
    private Dao<Map<String, Object>> currencyFeerateMappingDao;

    @Autowired
    public CurrencyFeerateServiceImpl(DataRepository repository) {
        this.currencyFeerateMappingDao = repository.getCurrencyFeerateMappingDao();
    }

    @Override
    public Map createCurrencyFeerateMappingRules(Map request) {
        if (request.get(DaoConstants.ID) == null) {
            request.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        currencyFeerateMappingDao.save(request);
        String id = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        return getCurrencyFeeRateById(id);
    }

    @Override
    public Map createCurrencyFeerateMappingRulesAndLog(Map request, OpLogCreateRequest opLogCreateRequest) {
        Map after = createCurrencyFeerateMappingRules(request);
        String merchantId = BeanUtil.getPropString(request, CurrencyFeerate.MERCHANT_ID);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.CURRENCY_FEE_RATE_TEMPLATE_CODE, OpLog.CURRENCY_FEE_RATE_TABLE_NAME, new ArrayList<>(), OpLog.CURRENCY_FEE_RATE_CHANGE_KEY_LIST, OpLog.CURRENCY_FEE_RATE_DESC_MAP, new HashMap<>(), after);
        return after;
    }

    @Override
    public ListResult getCurrencyFeerate(String merchantId, PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 100, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).ne(null);

        if (!StringUtil.empty(merchantId)) {
            criteria.with(CurrencyFeerate.MERCHANT_ID).is(merchantId);
        }
        long count = currencyFeerateMappingDao.filter(criteria).count();
        Filter filter = currencyFeerateMappingDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> currencyFeerateResult = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, currencyFeerateResult);
    }

    @Override
    public Map updateCurrencyFeerate(Map request) {
        String id = BeanUtil.getPropString(request, ConstantUtil.KEY_ID);
        String feeRate = BeanUtil.getPropString(request, CurrencyFeerate.FEE_RATE);

        Criteria criteria = Criteria.where(DaoConstants.ID).is(id);
        Map oriInfo = currencyFeerateMappingDao.filter(criteria).fetchOne();
        if (oriInfo == null || oriInfo.isEmpty()) {
            throw new CoreCurrencyFeerateMappingException("找不到相关记录");
        }
        Map param = CollectionUtil.hashMap(DaoConstants.ID, id, CurrencyFeerate.FEE_RATE, feeRate);
        int status = BeanUtil.getPropInt(request, CurrencyFeerate.STATUS, -1);
        if (status != -1) {
            param.put(CurrencyFeerate.STATUS, status);
        }

        currencyFeerateMappingDao.updatePart(param);
        return getCurrencyFeeRateById(id);
    }

    @Override
    public Map updateCurrencyFeerateAndLog(Map request, OpLogCreateRequest opLogCreateRequest) {
        String id = MapUtils.getString(request, ConstantUtil.KEY_ID);
        String merchantId = MapUtils.getString(request, CurrencyFeerate.MERCHANT_ID);
        Map before = getCurrencyFeeRateById(id);
        Map after = updateCurrencyFeerate(request);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.CURRENCY_FEE_RATE_TEMPLATE_CODE, OpLog.CURRENCY_FEE_RATE_TABLE_NAME, new ArrayList<>(), OpLog.CURRENCY_FEE_RATE_CHANGE_KEY_LIST, OpLog.CURRENCY_FEE_RATE_DESC_MAP, before, after);
        return after;
    }


    @Override
    public Map getCurrencyFeeRateById(String id) {
        if (StringUtil.empty(id)) {
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id);
        return currencyFeerateMappingDao.filter(criteria).fetchOne();
    }
}
