package com.wosai.upay.core.service;

import com.ctrip.framework.apollo.core.dto.ApolloConfig;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.exception.CoreExternalStateException;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.ExternalExtraConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ExternalExtraFacade {

    @Autowired
    DataRepository dataRepository;

   private static final Map<Integer, String> SN_TYPE_CLOSE_THROW_MSG = MapUtil.hashMap(
            ExternalExtraConfig.SN_TYPE_PROVIDER_MCH, CoreExternalStateException.PROVIDER_MCH_CLOSE_MSG,
            ExternalExtraConfig.SN_TYPE_SUB_MCH_ID, CoreExternalStateException.SUB_MCH_ID_CLOSE_MSG,
            ExternalExtraConfig.SN_TYPE_PROVIDER_TERMINAL, CoreExternalStateException.PROVIDER_TERMINAL_CLOSE_MSG
    );



    public void checkExternalConfig(Integer payway, Map<String, Object> config) {
        if (ApolloConfigurationCenterUtil.checkExternalFlag(false)) {
            if (config != null) {
                Map<Integer, String> externalTradeParams = getExternalTradeParams(payway, config);
                checkExternalTradeParams(payway, config, externalTradeParams);
            }
        }
    }

    /**
     * 获取需要校验的 通道子商户号 & 收单机构子商户号 & 收单机构终端号
     *
     * @param payway
     * @param config
     * @return
     */
    private Map<Integer, String> getExternalTradeParams(Integer payway, Map<String, Object> config) {
        Map<Integer, String> checkExternalSnAndTypeMap = new HashMap<>();
        Integer provider = MapUtil.getInteger(config, TransactionParam.PROVIDER);
        Map<String, Object> tradeConfig = null;
        for (String key : config.keySet()) {
            if (key != null && key.endsWith(CoreCommonConstants.SUFFIX_TRADE_PARAMS)) {
                tradeConfig = com.wosai.pantheon.util.MapUtil.getMap(config, key);
            }
        }
        if (tradeConfig != null) {
            //解析收单机构商户号
            Integer providerKey = provider == null ? payway : provider;
            String providerMchIdKey = MapUtil.getString(ApolloConfigurationCenterUtil.getOverrideProviderMchIdKey(), providerKey, TransactionParam.PROVIDER_MCH_ID);
            String providerMchId = MapUtil.getString(tradeConfig, providerMchIdKey);
            if (StringUtil.isNotEmpty(providerMchId)) {
                checkExternalSnAndTypeMap.put(ExternalExtraConfig.SN_TYPE_PROVIDER_MCH, providerMchId);
            }


            if (provider != null) {
                //解析通道子商户号
                String subMchIdKey = null;
                if (Objects.equals(Payway.WEIXIN.getCode(), payway)) {
                    subMchIdKey = TransactionParam.WEIXIN_SUB_MCH_ID;
                } else if (Objects.equals(Payway.ALIPAY2.getCode(), payway)) {
                    subMchIdKey = TransactionParam.ALIPAY_SUB_MCH_ID;
                }
                if (StringUtil.isNotEmpty(subMchIdKey)) {
                    String subMchId = MapUtil.getString(tradeConfig, subMchIdKey);
                    if (StringUtil.isNotEmpty(subMchId)) {
                        checkExternalSnAndTypeMap.put(ExternalExtraConfig.SN_TYPE_SUB_MCH_ID, subMchId);
                    }
                }
            }

            //解析收单终端号
            if (config.containsKey(TransactionParam.TRADE_EXT_TERM_INFO_TERM_ID)) {
                String termId = MapUtil.getString(config, TransactionParam.TRADE_EXT_TERM_INFO_TERM_ID);
                checkExternalSnAndTypeMap.put(ExternalExtraConfig.SN_TYPE_PROVIDER_TERMINAL, termId);
            }
        }
        return checkExternalSnAndTypeMap;
    }

    /**
     * 对需要校验的收单参数进行校验
     *
     * @param payway
     * @param config
     * @param externalTradeParams
     */
    private void checkExternalTradeParams(Integer payway, Map<String, Object> config, Map<Integer, String> externalTradeParams) {
        Dao<Map<String, Object>> externalExtraConfigDao = dataRepository.getExternalExtraConfigDao();

        if (MapUtil.isNotEmpty(externalTradeParams)) {
            Integer provider = MapUtil.getInteger(config, TransactionParam.PROVIDER);
            if (provider != null) {
                //拉卡拉需要转成1032
                provider = TradeConfigServiceImpl.LAKALA_V3_COMPATIBILITY_PROVIDER.contains(provider) ? TradeConfigServiceImpl.LAKALA_V3_PROVIDER : provider;
            }
            //同时限制bizType为1 交易状态
            Criteria baseCriteria = Criteria.where(ExternalExtraConfig.PROVIDER).is(provider).with(ExternalExtraConfig.BIZ_TYPE).is(ExternalExtraConfig.BIZ_TYPE_TRADE);
            List<Criteria> criteriaList = new ArrayList<>();
            for (Map.Entry<Integer, String> checkExternalSnAndTypeEntry : externalTradeParams.entrySet()) {
                Integer snType = checkExternalSnAndTypeEntry.getKey();
                Criteria subCriteria =  Criteria.and(Criteria.where(ExternalExtraConfig.SN).is(checkExternalSnAndTypeEntry.getValue()),
                        Criteria.where(ExternalExtraConfig.SN_TYPE).is(snType));
                criteriaList.add(subCriteria);
            }

            baseCriteria = baseCriteria.withOr(criteriaList);
            List<Map<String, Object>> externalConfigs = com.wosai.data.util.CollectionUtil.iterator2list(externalExtraConfigDao.filter(baseCriteria).fetchAll());

            if (CollectionUtil.isNotEmpty(externalConfigs)) {
                for (Map<String, Object> externalConfig : externalConfigs) {
                    Integer type = MapUtil.getInteger(externalConfig, ExternalExtraConfig.SN_TYPE);
                    Integer status = MapUtil.getInteger(externalConfig, ExternalExtraConfig.STATUS);
                    if (Objects.equals(ExternalExtraConfig.STATUS_CLOSED, status)) {
                        String msg = SN_TYPE_CLOSE_THROW_MSG.get(type);
                        if (StringUtil.isNotEmpty(msg)) {
                            throw new CoreExternalStateException(msg);
                        }
                    }
                }
            }
        }
    }

}
