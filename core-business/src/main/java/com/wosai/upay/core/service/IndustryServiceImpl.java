package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.core.model.Industry;
import com.wosai.upay.core.repository.DataRepository;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Deprecated
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class IndustryServiceImpl implements IndustryService {

    private Dao<Map<String, Object>> industryDao;

    @Autowired
    public IndustryServiceImpl(DataRepository repository) {
        this.industryDao = repository.getIndustryDao();
    }

    @Override
    public Map getIndustry(String industryId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(industryId);
        return industryDao.filter(criteria).fetchOne();
    }

    @Override
    @Cacheable("Industries")
    public List findIndustrys(Map queryFilter) {
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(Industry.LEVEL1, Industry.LEVEL1);
            put(Industry.LEVEL2, Industry.LEVEL2);
            put(Industry.DEPTH, Industry.DEPTH);
        }});
        Filter filter = industryDao.filter(criteria);
        filter.orderBy(new ArrayList<Filter.OrderByField>() {{
            add(new Filter.OrderByField(Industry.LEVEL1, Filter.DESC));
            add(new Filter.OrderByField(Industry.LEVEL2, Filter.ASC));
        }});
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return list;
    }

    @Override
    public List findIndustries(Map queryFilter) {
        return findIndustrys(queryFilter);
    }


    private Map<String, Map> getLevel1IndustriesMap() {
        List industries = findIndustries(null);
        Map<String, Map> level1IndustriesMap = new LinkedHashMap<String, Map>();
        if (industries != null) {
            for (int i = 0; i < industries.size(); i++) {
                Map levelMap = (Map) industries.get(i);
                String level1 = BeanUtil.getPropString(levelMap, Industry.LEVEL1);
                Map map = level1IndustriesMap.get(level1);
                if (map == null) {
                    level1IndustriesMap.put(level1, CollectionUtil.hashMap(DaoConstants.ID, level1, Industry.LEVEL1, level1, Industry.DEPTH, 1));
                }
            }
        }
        return level1IndustriesMap;
    }

    @Override
    @Cacheable("LevelsIndustries")
    public List getLevelsIndustries() {
        Map<String, Map> level1IndustriesMap = getLevel1IndustriesMap();
        List industries = findIndustries(null);
        if (industries != null) {
            for (int i = 0; i < industries.size(); i++) {
                Map levelMap = (Map) industries.get(i);
                String level1 = BeanUtil.getPropString(levelMap, Industry.LEVEL1);
                Map map = level1IndustriesMap.get(level1);
                if (map.get("level2s") == null) {
                    map.put("level2s", new ArrayList<>());
                }
                List level2s = (List) map.get("level2s");
                level2s.add(levelMap);
            }
        }
        List levelsIndustries = new ArrayList();
        for (String key : level1IndustriesMap.keySet()) {
            levelsIndustries.add(level1IndustriesMap.get(key));
        }
        return levelsIndustries;
    }


}
