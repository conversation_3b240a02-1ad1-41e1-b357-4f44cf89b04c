package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.core.exception.CoreLicenseNotExistsException;
import com.wosai.upay.core.model.License;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.EffectiveTimeUtil;
import com.wosai.upay.core.util.RemoveSpaceCharsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class LicenseServiceImpl implements LicenseService {

    private Dao<Map<String, Object>> licenseDao;
    @Autowired
    private DataRepository repository;

    @PostConstruct
    private void init() {
        licenseDao = repository.getLicenseDao();
    }

    private static final String BUSINESS_LICENSE_ID = "business_license_id";

    private static final List<String> fields = Arrays.asList(License.LICENSE_NAME, License.LICENSE_NUMBER);
    @Autowired
    private McPreService mcPreService;

    public static final int LICENSE_ERROR_CODE = 1033;


    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    @Override
    public int saveLicense(Map license) {
        this.replaceXAndCheckIdValidity(license);
        RemoveSpaceCharsUtil.removeSpaceChars(license, fields);
        if (WosaiStringUtils.isBlank(MapUtils.getString(license, ConstantUtil.KEY_ID))) {
            license.put(ConstantUtil.KEY_ID, UUID.randomUUID().toString());
        }

        licenseDao.save(license);
        return 1;
    }

    @Override
    public List getLicenseByBusinessLicenseId(String storeBusinessLicenseId) {
        Criteria criteria = Criteria.where(BUSINESS_LICENSE_ID).is(storeBusinessLicenseId).with(ConstantUtil.KEY_DELETED).is(false);
        Iterator<Map<String, Object>> all = licenseDao.filter(criteria).fetchAll();
        return CollectionUtil.iterator2list(all);
    }

    @Override
    public Map<String, Object> getLicenseById(String id) {
        Criteria criteria = Criteria.where(ConstantUtil.KEY_ID).is(id).with(ConstantUtil.KEY_DELETED).is(false);
        return licenseDao.filter(criteria).fetchOne();
    }

    @Override
    public int updateLicense(Map license) {
        this.replaceXAndCheckIdValidity(license);
        RemoveSpaceCharsUtil.removeSpaceChars(license, fields);
        String id = BeanUtil.getPropString(license, ConstantUtil.KEY_ID);
        Criteria criteria = Criteria.where(ConstantUtil.KEY_ID).is(id);
        Map<String, Object> storeLicense = licenseDao.filter(criteria).fetchOne();
        if (storeLicense == null || storeLicense.size() == 0) {
            throw new CoreLicenseNotExistsException("无此许可证信息");
        }
        CrudUtil.ignoreForUpdate(license, new String[]{License.BUSINESS_LICENSE_ID});
        licenseDao.updatePart(license);
        return 1;
    }


    @Override
    public int updateLicenseComplete(List<Map> licenses) {
        if (WosaiCollectionUtils.isEmpty(licenses)) {
            return 0;
        }
        //有 business_license_id 才可以进表
        List<Map> date = licenses.stream().filter(map -> WosaiStringUtils.isNotBlank(MapUtils.getString(map, License.BUSINESS_LICENSE_ID))).collect(Collectors.toList());

        if (WosaiCollectionUtils.isEmpty(date)) {
            return 0;
        }
        for (Map licens : date) {

            try {
                saveLicense(licens);
            } catch (Exception e) {
                log.error("保存许可证数据异常,data --> {}", licens, e);
            }
        }
        return 1;
    }

    @Override
    public int deleteLicenseById(String id) {
        licenseDao.delete(id);
        return 1;
    }


    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    @Override
    public int deleteAllLicenseByBusinessLicenseId(String businessLicenseId) {
        Criteria criteria = Criteria.where("business_license_id").is(businessLicenseId);
        Iterator<Map<String, Object>> all = licenseDao.filter(criteria).fetchAll();
        if (all != null) {
            List<Map<String, Object>> maps = CollectionUtil.iterator2list(all);
            for (Map<String, Object> map : maps) {
                String id = (String) map.get("id");
                deleteLicenseById(id);
            }

        }
        return 1;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public List<Map<String, Object>> deleteAllLicenseByBusinessLicenseIdTruly(String businessLicenseId) {
        Criteria criteria = Criteria.where("business_license_id").is(businessLicenseId);
        Iterator<Map<String, Object>> all = licenseDao.filter(criteria).fetchAll();
        mcPreService.deleteExcessData("license", businessLicenseId);
        if (all != null) {
            List<Map<String, Object>> maps = CollectionUtil.iterator2list(all);
            if (WosaiCollectionUtils.isEmpty(maps)) {
                return null;
            }

            for (Map<String, Object> map : maps) {
                String id = (String) map.get("id");
                licenseDao.delete(id);
            }
            return maps;
        }
        return null;
    }

    private void replaceXAndCheckIdValidity(Map license) {
        if (WosaiMapUtils.isNotEmpty(license)) {
            String storeLicenseValidity = WosaiMapUtils.getString(license, License.LICENSE_VALIDITY);
            if (WosaiStringUtils.isNotEmpty(storeLicenseValidity)) {
                EffectiveTimeUtil.checkoutEffectiveTime(storeLicenseValidity);
            }
        }
    }

}
