package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.log.ImportantChangeLog;
import com.wosai.upay.core.model.log.OpLog;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;

import com.wosai.upay.core.util.CoreUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@AutoJsonRpcServiceImpl
public class LogServiceImpl implements LogService {

    @Autowired
    private UuidGenerator uuidGenerator;

    private DataRepository repository;
    private Dao<Map<String, Object>> opLogDao;
    private Dao<Map<String, Object>> importantChangeLogDao;
    private Dao<Map<String, Object>> taskApplyLogDao;

    @Autowired
    public LogServiceImpl(DataRepository repository) {
        this.repository = repository;
        this.opLogDao = repository.getOpLogDao();
        this.importantChangeLogDao = repository.getImportantChangeLogDao();
        this.taskApplyLogDao = repository.getTaskApplyLogDao();
    }
    @Override
    public Map createOpLog(Map opLog) {
        if (opLog.get(DaoConstants.ID) == null) {
            opLog.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        opLogDao.save(opLog);
        return opLog;
    }

    @Override
    public void deleteOpLog(String opLogId) {
        opLogDao.delete(opLogId);
    }

    @Override
    public Map updateOpLog(Map opLog) {
        opLogDao.updatePart(opLog);
        return getOpLog(BeanUtil.getPropString(opLog, DaoConstants.ID));
    }

    @Override
    public Map getOpLog(String opLogId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(opLogId);
        return opLogDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findOpLogs(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put("request_system", OpLog.REQUEST_SYSTEM);
            put("operator_id", OpLog.OPERATOR_ID);
            put("operator_name", OpLog.OPERATOR_NAME);
            put("operator_login", OpLog.OPERATOR_LOGIN);
            put("action_class", OpLog.ACTION_CLASS);
            put("action_method", OpLog.ACTION_METHOD);
            put("duration", OpLog.DURATION);
            put("result", OpLog.RESULT);
            put("action_type", OpLog.ACTION_TYPE);
            put("action_date", OpLog.ACTION_DATE);
            put("client_ip", OpLog.CLIENT_IP);
            put("deleted", DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = opLogDao.filter(criteria).count();
        Filter filter = opLogDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public Map createImportantChangeLog(Map importantChangeLog) {
        if (importantChangeLog.get(DaoConstants.ID) == null) {
            importantChangeLog.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        importantChangeLogDao.save(importantChangeLog);
        return importantChangeLog;
    }

    @Override
    public void deleteImportantChangeLog(String importantChangeLogId) {
        importantChangeLogDao.delete(importantChangeLogId);
    }

    @Override
    public Map updateImportantChangeLog(Map importantChangeLog) {
        importantChangeLogDao.updatePart(importantChangeLog);
        return getImportantChangeLog(BeanUtil.getPropString(importantChangeLog, DaoConstants.ID));
    }

    @Override
    public Map getImportantChangeLog(String importantChangeLogId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(importantChangeLogId);
        return importantChangeLogDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findImportantChangeLogs(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put("merchant_id", ImportantChangeLog.MERCHANT_ID);
            put("merchant_sn", ImportantChangeLog.MERCHANT_SN);
            put("object_type", ImportantChangeLog.OBJECT_TYPE);
            put("object_id", ImportantChangeLog.OBJECT_ID);
            put("type", ImportantChangeLog.TYPE);
            put("post_change_status", ImportantChangeLog.POST_CHANGE_STATUS);
            put("subject_type", ImportantChangeLog.SUBJECT_TYPE);
            put("subject_id", ImportantChangeLog.SUBJECT_ID);
            put("subject_name", ImportantChangeLog.SUBJECT_NAME);
            put("subject_login", ImportantChangeLog.SUBJECT_LOGIN);
            put("change_time", ImportantChangeLog.CHANGE_TIME);
            put("deleted", DaoConstants.DELETED);
        }});
        if (queryFilter.get("types") != null && queryFilter.get("types") instanceof List) {
            criteria.with(ImportantChangeLog.TYPE).in((ArrayList)queryFilter.get("types"));
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = importantChangeLogDao.filter(criteria).count();
        Filter filter = importantChangeLogDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public Map createTaskApplyLog(Map taskApplyLog) {
        if (taskApplyLog.get(DaoConstants.ID) == null) {
            taskApplyLog.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        taskApplyLogDao.save(taskApplyLog);
        return taskApplyLog;
    }

    @Override
    public void deleteTaskApplyLog(String taskApplyLogId) {
        taskApplyLogDao.delete(taskApplyLogId);
    }

    @Override
    public Map updateTaskApplyLog(Map taskApplyLog) {
        taskApplyLogDao.updatePart(taskApplyLog);
        return getTaskApplyLog(BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
    }

    @Override
    public Map getTaskApplyLog(String taskApplyLogId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(taskApplyLogId);
        return taskApplyLogDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findTaskApplyLogs(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(TaskApplyLog.TYPE, TaskApplyLog.TYPE);
            put(TaskApplyLog.APPLY_SYSTEM, TaskApplyLog.APPLY_SYSTEM);
            put(TaskApplyLog.USER_ID, TaskApplyLog.USER_ID);
            put(TaskApplyLog.APPLY_STATUS, TaskApplyLog.APPLY_STATUS);
            put(TaskApplyLog.APPLY_DATE, TaskApplyLog.APPLY_DATE);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
//        long count = taskApplyLogDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = taskApplyLogDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(1024, list);
    }

    @Override
    public ListResult findTaskApplyLogsByTypeList(PageInfo pageInfo, Map<String, Object> queryFilter, List<Integer> typeList) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(TaskApplyLog.APPLY_SYSTEM, TaskApplyLog.APPLY_SYSTEM);
            put(TaskApplyLog.USER_ID, TaskApplyLog.USER_ID);
            put(TaskApplyLog.APPLY_STATUS, TaskApplyLog.APPLY_STATUS);
            put(TaskApplyLog.APPLY_DATE, TaskApplyLog.APPLY_DATE);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        if(typeList !=null ){
            criteria.with(TaskApplyLog.TYPE).in(typeList);
        }
//        long count = taskApplyLogDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = taskApplyLogDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(1024, list);
    }

}
