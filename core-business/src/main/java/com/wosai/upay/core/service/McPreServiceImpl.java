package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-26
 * @Description:
 */

@Service
@AutoJsonRpcServiceImpl
public class McPreServiceImpl implements McPreService {
    @Autowired
    Dao<Map<String, Object>> mcPreDao;

    @Autowired
    JdbcTemplate jdbcTemplate;


    @Override
    public void saveMcPre(Map mcPre) {
        mcPreDao.save(mcPre);
    }

    @Override
    public void updateMcPre(Map mcPre) {
        mcPreDao.updatePart(mcPre);
    }

    @Override
    public void deleteMcPre(int id) {
        jdbcTemplate.update("DELETE FROM mc_pre WHERE id= ?", id);
    }

    @Override
    public Map findMcPre(String devCode, String tableName, String bizId) {
        Criteria criteria = Criteria.where("dev_code").is(devCode).with("table_name").is(tableName)
                .with("biz_id").is(bizId);
        return mcPreDao.filter(criteria).fetchOne();
    }

    @Override
    public Map findMcPre(String tableName, String bizId) {
        Criteria criteria = Criteria.where("biz_id").is(bizId).with("table_name").is(tableName);
        Filter<Map<String, Object>> filter = mcPreDao.filter(criteria);
        PageInfo pageInfo = new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.MTIME, OrderBy.OrderType.DESC)));
        PageInfoUtil.pagination(pageInfo, filter);
        List resultList = CollectionUtil.iterator2list(filter.fetchAll());
        if (CollectionUtils.isNotEmpty(resultList)) {
            return (Map) resultList.get(0);
        }
        return null;
    }

    @Override
    public void deleteExcessData(String tableName, String bizId) {
        Criteria criteria = Criteria.where("biz_id").is(bizId).with("table_name").is(tableName);
        Filter<Map<String, Object>> filter = mcPreDao.filter(criteria);

        List all = CollectionUtil.iterator2list(filter.fetchAll());
        List<Long> willDelIds = new ArrayList(all.size());
        Map data = null;
        for (int i = 0; i < all.size(); i++) {
            data = (Map) all.get(i);
            BigInteger id = (BigInteger) data.get("id");
            willDelIds.add(id.longValue());
        }
        delBatch(willDelIds);
    }

    /**
     * 批量删除
     *
     * @param list
     */
    private void delBatch(List<Long> list) {
        jdbcTemplate.batchUpdate("DELETE FROM mc_pre WHERE id= ?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                preparedStatement.setLong(1, list.get(i));
            }

            @Override
            public int getBatchSize() {
                return list.size();
            }
        });
    }

}
