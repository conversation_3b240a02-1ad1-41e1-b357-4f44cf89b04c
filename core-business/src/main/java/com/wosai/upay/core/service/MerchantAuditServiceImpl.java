package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by xuchmao on 16/11/10.
 */
@Service
@AutoJsonRpcServiceImpl
@Deprecated
@NoArgsConstructor
public class MerchantAuditServiceImpl implements MerchantAuditService {

    @Autowired
    private com.wosai.upay.merchant.audit.api.service.MerchantAuditService delegate;

    @Override
    public Map getAuditById(String id){
        return delegate.getAuditById(id);
    }

    @Override
    public Map getAuditByMerchantId(String merchantId){
        return delegate.getAuditByMerchantId(merchantId);
    }

    @Override
    public Map getAuditByMerchantSn(String merchantSn){
        return delegate.getAuditByMerchantSn(merchantSn);
    }
}
