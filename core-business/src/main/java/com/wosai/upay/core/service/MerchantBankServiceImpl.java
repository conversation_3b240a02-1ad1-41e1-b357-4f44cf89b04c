package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.exception.CoreMerchantNotBindBankAccountException;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.bank.MerchantClearingBankInfo;
import com.wosai.upay.core.model.bank.MerchantClearingBankRequest;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.biz.BankInfoBiz;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.core.util.IdCardExtendUtil;
import com.wosai.upay.core.util.JsonUtil;
import com.wosai.upay.core.util.RemoveSpaceCharsUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lihebin on 2018/12/12.
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class MerchantBankServiceImpl implements MerchantBankService {
    private static final Logger logger = LoggerFactory.getLogger(MerchantBankServiceImpl.class);

    private Dao<Map<String, Object>> merchantBankAccountPreReadDao;
    private Dao<Map<String, Object>> merchantBankAccountPreWriteDao;
    private Dao<Map<String, Object>> merchantBankAccountReadDao;
    private Dao<Map<String, Object>> merchantBankAccountWriteDao;
    private Dao<Map<String, Object>> merchantBankAccountChangeLogWriteDao;


    @Autowired
    private UuidGenerator uuidGenerator;

    @Autowired
    private RMQService rmqService;

    @Autowired
    private BankInfoBiz bankInfoBiz;

    @Autowired
    public MerchantBankServiceImpl(DataRepository repository) {
        merchantBankAccountPreReadDao = repository.getMerchantBankAccountPreReadDao();
        merchantBankAccountPreWriteDao = repository.getMerchantBankAccountPreWriteDao();
        merchantBankAccountReadDao = repository.getMerchantBankAccountReadDao();
        merchantBankAccountWriteDao = repository.getMerchantBankAccountWriteDao();
        merchantBankAccountChangeLogWriteDao = repository.getmerchantBankAccountChangeLogWriteDao();
    }

    private static final List<String> fields = Arrays.asList(MerchantBankAccount.HOLDER, MerchantBankAccount.IDENTITY, MerchantBankAccount.NUMBER);

    @Override
    public Map saveMerchantBankAccountPre(Map merchantBankAccountPre) {
        bankInfoBiz.appendOpeningClearingNum(merchantBankAccountPre);
        this.replaceXAndCheckIdValidity(merchantBankAccountPre);
        RemoveSpaceCharsUtil.removeSpaceChars(merchantBankAccountPre, fields);

        String merchantId = BeanUtil.getPropString(merchantBankAccountPre, MerchantBankAccountPre.MERCHANT_ID);
        Criteria criteria = Criteria.where(MerchantBankAccountPre.MERCHANT_ID).is(merchantId)
                .with(DaoConstants.DELETED).is(false);
        long count = merchantBankAccountPreReadDao.filter(criteria).count();
        if (count >= 10) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHNAT_BANKACCOUNT_NO_INSERT));
        }
        String number = BeanUtil.getPropString(merchantBankAccountPre, MerchantBankAccountPre.NUMBER);
        Map merchantBankAccountPreOld = getMerchantBankAccountPreByMerchantIdAndNumberCheckDelete(merchantId, number, false);
        String id;
        if (!MapUtils.isEmpty(merchantBankAccountPreOld)) {
            id = BeanUtil.getPropString(merchantBankAccountPreOld, DaoConstants.ID);
            merchantBankAccountPre.put(DaoConstants.ID, id);
            merchantBankAccountPre.put(DaoConstants.DELETED, false);
            IdCardExtendUtil.getExtend(merchantBankAccountPre, merchantBankAccountPreOld);
            updateMerchantBankAccountPre(merchantBankAccountPre);
        } else {
            id = uuidGenerator.nextUuid();
            merchantBankAccountPre.put(DaoConstants.ID, id);
            IdCardExtendUtil.getExtend(merchantBankAccountPre);
            merchantBankAccountPreWriteDao.save(merchantBankAccountPre);
        }
        return getMerchantBankAccountPre(id);
    }

    @Override
    public Map updateMerchantBankAccountPre(Map merchantBankAccountPre) {
        bankInfoBiz.appendOpeningClearingNum(merchantBankAccountPre);
        this.replaceXAndCheckIdValidity(merchantBankAccountPre);
        RemoveSpaceCharsUtil.removeSpaceChars(merchantBankAccountPre, fields);

        String id = BeanUtil.getPropString(merchantBankAccountPre, DaoConstants.ID);
        merchantBankAccountPre.remove(MerchantBankAccountPre.MERCHANT_ID);
        merchantBankAccountPreWriteDao.updatePart(merchantBankAccountPre);
        return getMerchantBankAccountPre(id);
    }

    @Override
    public void updateMerchantBankAccountPhotosAndIdValidity(Map request) {
        String merchantId = BeanUtil.getPropString(request, MerchantBankAccountPre.MERCHANT_ID);
        String identity = BeanUtil.getPropString(request, MerchantBankAccountPre.IDENTITY);
        Map updateRequest = CollectionUtil.hashMap(
                MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, BeanUtil.getPropString(request, MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO),
                MerchantBankAccountPre.ID_VALIDITY, BeanUtil.getPropString(request, MerchantBankAccountPre.ID_VALIDITY)
        );
        if (WosaiStringUtils.isNotEmpty(BeanUtil.getPropString(request, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO))) {
            updateRequest.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, BeanUtil.getPropString(request, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO));
        }
        Criteria criteria1 = Criteria.where(MerchantBankAccountPre.MERCHANT_ID).is(merchantId).with(MerchantBankAccountPre.IDENTITY).is(identity)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantBankAccount = merchantBankAccountReadDao.filter(criteria1).fetchOne();
        if (MapUtils.isNotEmpty(merchantBankAccount)) {
            updateRequest.put(DaoConstants.ID, BeanUtil.getPropString(merchantBankAccount, DaoConstants.ID));
            merchantBankAccountWriteDao.updatePart(updateRequest);
        }

        Criteria criteria2 = Criteria.where(MerchantBankAccountPre.MERCHANT_ID).is(merchantId).with(MerchantBankAccountPre.IDENTITY).is(identity)
                .with(DaoConstants.DELETED).is(false);
        List<Map<String, Object>> preList = CollectionUtil.iterator2list(merchantBankAccountPreReadDao.filter(criteria2).fetchAll());
        if (WosaiCollectionUtils.isEmpty(preList)) {
            return;
        }
        for (Map<String, Object> pre : preList) {
            updateRequest.put(DaoConstants.ID, BeanUtil.getPropString(pre, DaoConstants.ID));
            merchantBankAccountPreWriteDao.updatePart(updateRequest);
        }
    }

    @Override
    public Map getMerchantBankAccountPre(String id) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id)
                .with(DaoConstants.DELETED).is(false);
        Map merchantBankAccountPre = merchantBankAccountPreReadDao.filter(criteria).fetchOne();
        bankInfoBiz.appendBankBranchName(merchantBankAccountPre);
        return merchantBankAccountPre;
    }

    @Override
    public Map getMerchantBankAccountPreByMerchantIdAndNumber(String merchantId, String number) {

        return getMerchantBankAccountPreByMerchantIdAndNumberCheckDelete(merchantId, number, true);
    }

    private Map getMerchantBankAccountPreByMerchantIdAndNumberCheckDelete(String merchantId, String number, boolean checkDelete) {
        Criteria criteria = Criteria.where(MerchantBankAccountPre.MERCHANT_ID).is(merchantId)
                .with(MerchantBankAccountPre.NUMBER).is(number);

        if (checkDelete) {
            criteria.with(DaoConstants.DELETED).is(false);
        }
        Map merchantBankAccountPre = merchantBankAccountPreReadDao.filter(criteria).fetchOne();
        bankInfoBiz.appendBankBranchName(merchantBankAccountPre);
        return merchantBankAccountPre;
    }


    @Override
    public ListResult findMerchantBankAccountPres(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(MerchantBankAccountPre.MERCHANT_ID, MerchantBankAccountPre.MERCHANT_ID);
            put(MerchantBankAccountPre.NUMBER, MerchantBankAccountPre.NUMBER);
            put(MerchantBankAccountPre.TYPE, MerchantBankAccountPre.TYPE);
            put(MerchantBankAccountPre.HOLDER, MerchantBankAccountPre.HOLDER);
            put(MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS);
            put(MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS);
            put(MerchantBankAccountPre.IDENTITY, MerchantBankAccountPre.IDENTITY);
            put(MerchantBankAccountPre.CELLPHONE, MerchantBankAccountPre.CELLPHONE);
            put(MerchantBankAccountPre.ID_TYPE, MerchantBankAccountPre.ID_TYPE);
            put(MerchantBankAccountPre.HOLDER_ID_CARD_ADDRESS, MerchantBankAccountPre.HOLDER_ID_CARD_ADDRESS);
            put(MerchantBankAccountPre.HOLDER_ID_CARD_ISSUING_AUTHORITY, MerchantBankAccountPre.HOLDER_ID_CARD_ISSUING_AUTHORITY);
        }});
        criteria.with(DaoConstants.DELETED).is(false);
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = merchantBankAccountPreReadDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = merchantBankAccountPreReadDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        for (Map map : list) {
            bankInfoBiz.appendBankBranchName(map);
        }
        return new ListResult(count, list);
    }

    @Override
    public void updateMerchantBankAccount(Map merchantBankAccount) {
        logger.info("传入的变更默认卡参数：{}", JSON.toJSONString(merchantBankAccount));
        bankInfoBiz.appendOpeningClearingNum(merchantBankAccount);
        this.replaceXAndCheckIdValidity(merchantBankAccount);
        RemoveSpaceCharsUtil.removeSpaceChars(merchantBankAccount, fields);

        String merchantId = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID);
        Criteria criteria = Criteria.where(MerchantBankAccount.MERCHANT_ID).is(merchantId);
        Map oldMerchantBankAccount = merchantBankAccountReadDao.filter(criteria).fetchOne();
        if (MapUtils.isEmpty(oldMerchantBankAccount)) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT));
        }
        String id = BeanUtil.getPropString(oldMerchantBankAccount, DaoConstants.ID);
        merchantBankAccount.put(DaoConstants.ID, id);
        IdCardExtendUtil.getExtend(merchantBankAccount, oldMerchantBankAccount);

        merchantBankAccountWriteDao.updatePart(merchantBankAccount);
        Map newMerchantBankAccount = merchantBankAccountWriteDao.get(id);
        Map merchantBankAccountChangeLog = bankInfoBiz.getMerchantBankAccountChangeLog(oldMerchantBankAccount, newMerchantBankAccount);
        if (WosaiMapUtils.isNotEmpty(merchantBankAccountChangeLog)) {
            merchantBankAccountChangeLogWriteDao.save(merchantBankAccountChangeLog);
        }
        rmqService.writeMarketingDTS(getMerchantMarketingDTSMessage(id, "update", oldMerchantBankAccount, newMerchantBankAccount, null, null), 2);
    }


    private Map<String, Object> getMerchantMarketingDTSMessage(String id, String operationType, Map merchantBankAccountOld, Map merchantBankAccountNew, String changeWay, String operator) {
        Map<String, Object> message = new HashMap<String, Object>();
        message.put("source", "gw2");
        message.put("table_name", "merchant_bank_account");
        message.put("id", id);
        message.put("operation_type", operationType);
        message.put("record_old", merchantBankAccountOld);
        message.put("record_new", merchantBankAccountNew);
        message.put("change_way", changeWay);
        message.put("operator", operator);
        return message;
    }

    @Override
    public void deletedMerchantBankAccountPre(String id) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id);
        Map oldMerchantBankAccountPre = merchantBankAccountPreReadDao.filter(criteria).fetchOne();
        if (MapUtils.isEmpty(oldMerchantBankAccountPre)) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT));
        }
        int defaultStatus = BeanUtil.getPropInt(oldMerchantBankAccountPre, MerchantBankAccountPre.DEFAULT_STATUS);
        int verifyStatus = BeanUtil.getPropInt(oldMerchantBankAccountPre, MerchantBankAccountPre.VERIFY_STATUS);
        if (defaultStatus == MerchantBankAccountPre.DEFAULT_STATUS_TRUE && verifyStatus == MerchantBankAccountPre.VERIFY_STATUS_SUCC) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_DELETED_BANK_ACCOUNT_PRE));
        }
        if (verifyStatus == MerchantBankAccountPre.VERIFY_STATUS_INPROGRESS) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_DELETED_BANK_ACCOUNT_PRE));
        }
        String numberOld = BeanUtil.getPropString(oldMerchantBankAccountPre, MerchantBankAccountPre.NUMBER);
        Map merchantBankAccountPre;
        if (numberOld != null && numberOld.contains("bak")) {
            merchantBankAccountPre = CollectionUtil.hashMap(
                    DaoConstants.ID, id,
                    DaoConstants.DELETED, true);
        } else {
            merchantBankAccountPre = CollectionUtil.hashMap(
                    DaoConstants.ID, id,
                    DaoConstants.DELETED, true,
                    MerchantBankAccountPre.NUMBER, String.format("%s_bak_%d", numberOld, System.currentTimeMillis())
            );
        }
        merchantBankAccountPreWriteDao.updatePart(merchantBankAccountPre);
    }

    @Override
    public void deletedMerchantBankAccount(String merchantBankAccountId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantBankAccountId);
        Map old = merchantBankAccountReadDao.filter(criteria).fetchOne();
        merchantBankAccountWriteDao.delete(merchantBankAccountId);
        Map bankAccountChangeLog = bankInfoBiz.getMerchantBankAccountChangeLog(old, null);
        if (WosaiMapUtils.isNotEmpty(bankAccountChangeLog)) {
            bankAccountChangeLog.put("deleted", 1);
            merchantBankAccountChangeLogWriteDao.save(bankAccountChangeLog);
        }
    }

    @Override
    public List<String> getMerchantBankAccountPreMerchantIds(Map merchantBankAccount) {
        String number = (String) merchantBankAccount.get("number");
        String identity = (String) merchantBankAccount.get("identity");
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(number)) {
            criteria.with("number").is(number);
        }
        if (StringUtils.isNotEmpty(identity)) {
            criteria.with("identity").is(identity);
        }
        List<Map<String, Object>> maps = CollectionUtil.iterator2list(merchantBankAccountPreReadDao.filter(criteria).fetchAll());
        return maps.stream().map(map -> (String) map.get("merchant_id")).collect(Collectors.toList());
    }

    private void replaceXAndCheckIdValidity(Map bankAccount) {
        if (WosaiMapUtils.isNotEmpty(bankAccount)) {
            Integer idType = WosaiMapUtils.getInteger(bankAccount, MerchantBankAccount.ID_TYPE);
            if (Objects.isNull(idType) || idType != 1) {
                return;
            }
            String idNumber = WosaiMapUtils.getString(bankAccount, MerchantBankAccount.IDENTITY);
            if (WosaiStringUtils.isNotEmpty(idNumber) && idNumber.contains("x")) {
                idNumber = idNumber.replace("x", "X");
                bankAccount.put(MerchantBankAccount.IDENTITY, idNumber);
            }
            if (WosaiStringUtils.isNotEmpty(idNumber)) {
                if (idNumber.startsWith("810000") || idNumber.startsWith("820000") || idNumber.startsWith("830000")) {
                    throw new CoreInvalidParameterException("证件类型与证件号不匹配");
                }
            }
        }
    }

    @Override
    public List<MerchantClearingBankInfo> getMerchantBankInfosByClearingNumber(MerchantClearingBankRequest request) {
        logger.info("查询清算行下的商户信息, request={}", JsonUtil.toJsonStr(request));
        if (StringUtils.isEmpty(request.getClearingNumber())) {
            throw new CoreInvalidParameterException("清算行号clearingNumber不能为空");
        }
        if (Objects.isNull(request.getBeginTime())) {
            throw new CoreInvalidParameterException("请求参数beginTime不能为空");
        }
        Criteria criteria = Criteria
                .where(MerchantBankAccount.CLEARING_NUMBER).is(request.getClearingNumber())
                .with(DaoConstants.CTIME).gt(request.getBeginTime());
        Filter<Map<String, Object>> filter = merchantBankAccountReadDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.ASC);
        filter.limit(request.getLimit());
        Iterator<Map<String, Object>> iterator = filter.fetchAll();

        if (null == iterator) {
            return Collections.emptyList();
        }

        List<MerchantClearingBankInfo> result = new ArrayList<>(request.getLimit());
        while (iterator.hasNext()) {
            MerchantClearingBankInfo clearingBankInfo = new MerchantClearingBankInfo();
            Map<String, Object> bankAccountMap = iterator.next();
            clearingBankInfo.setMerchantId(MapUtils.getString(bankAccountMap, MerchantBankAccount.MERCHANT_ID));
            clearingBankInfo.setBankName(MapUtils.getString(bankAccountMap, MerchantBankAccount.BANK_NAME));
            clearingBankInfo.setCtime(MapUtils.getLong(bankAccountMap, DaoConstants.CTIME));
            result.add(clearingBankInfo);
        }
        logger.info("查询清算行下的商户信息, count={}", result.size());
        return result;
    }
}
