package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.core.exception.CoreMerchantNotBindBankAccountException;
import com.wosai.upay.core.model.MerchantBizBankAccount;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.biz.BankInfoBiz;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.core.util.IdCardExtendUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-09-22
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class MerchantBizBankAccountServiceImpl implements MerchantBizBankAccountService {
    private Dao<Map<String, Object>> merchantBizBankAccountReadDao;
    private Dao<Map<String, Object>> merchantBizBankAccountWriteDao;

    @Autowired
    private UuidGenerator uuidGenerator;

    @Autowired
    private BankInfoBiz bankInfoBiz;

    @Autowired
    public MerchantBizBankAccountServiceImpl(DataRepository repository) {
        merchantBizBankAccountReadDao = repository.getMerchantBizBankAccountReadDao();
        merchantBizBankAccountWriteDao = repository.getMerchantBizBankAccountWriteDao();
    }


    @Override
    public Map saveMerchantBizBankAccount(Map merchantBizBankAccount) {
        bankInfoBiz.appendOpeningClearingNum(merchantBizBankAccount);
        String biz = BeanUtil.getPropString(merchantBizBankAccount, MerchantBizBankAccount.BIZ);

        String merchantId = BeanUtil.getPropString(merchantBizBankAccount, MerchantBizBankAccount.MERCHANT_ID);
        Criteria criteria = Criteria.where(MerchantBizBankAccount.MERCHANT_ID).is(merchantId)
                .with(MerchantBizBankAccount.BIZ).is(BeanUtil.getPropString(merchantBizBankAccount, MerchantBizBankAccount.BIZ))
                .with(DaoConstants.DELETED).is(false);
        long count = merchantBizBankAccountReadDao.filter(criteria).count();
        if (count >= 10) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHNAT_BANKACCOUNT_NO_INSERT));
        }
        String number = BeanUtil.getPropString(merchantBizBankAccount, MerchantBizBankAccount.NUMBER);
        Map merchantBizBankAccountOld = getMerchantBizBankAccountByMerchantIdAndNumber(merchantId, biz, number);
        String id;
        if (!MapUtils.isEmpty(merchantBizBankAccountOld)) {
            id = BeanUtil.getPropString(merchantBizBankAccountOld, DaoConstants.ID);
            merchantBizBankAccount.put(DaoConstants.ID, id);
            merchantBizBankAccount.put(DaoConstants.DELETED, false);
            IdCardExtendUtil.getExtend(merchantBizBankAccount, merchantBizBankAccountOld);
            updateMerchantBizBankAccount(merchantBizBankAccount);
        } else {
            id = uuidGenerator.nextUuid();
            merchantBizBankAccount.put(DaoConstants.ID, id);
            IdCardExtendUtil.getExtend(merchantBizBankAccount);
            merchantBizBankAccountWriteDao.save(merchantBizBankAccount);
        }
        return getMerchantBizBankAccount(id);
    }

    @Override
    public Map updateMerchantBizBankAccount(Map merchantBizBankAccount) {
        bankInfoBiz.appendOpeningClearingNum(merchantBizBankAccount);

        String id = BeanUtil.getPropString(merchantBizBankAccount, DaoConstants.ID);
        merchantBizBankAccount.remove(MerchantBizBankAccount.MERCHANT_ID);
        merchantBizBankAccountWriteDao.updatePart(merchantBizBankAccount);
        return getMerchantBizBankAccount(id);
    }

    @Override
    public Map getMerchantBizBankAccount(String id) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id)
                .with(DaoConstants.DELETED).is(false);
        Map bankAccount = merchantBizBankAccountReadDao.filter(criteria).fetchOne();
        bankInfoBiz.appendBankBranchName(bankAccount);
        return bankAccount;
    }

    @Override
    public Map getMerchantBizBankAccountByMerchantIdAndNumber(String merchantId, String biz, String number) {
        Criteria criteria = Criteria.where(MerchantBizBankAccount.MERCHANT_ID).is(merchantId)
                .with(MerchantBizBankAccount.NUMBER).is(number)
                .with(MerchantBizBankAccount.BIZ).is(biz)
                .with(DaoConstants.DELETED).is(false);
        Map bankAccount = merchantBizBankAccountReadDao.filter(criteria).fetchOne();
        bankInfoBiz.appendBankBranchName(bankAccount);
        return bankAccount;
    }

    @Override
    public ListResult findMerchantBizBankAccounts(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(MerchantBizBankAccount.MERCHANT_ID, MerchantBizBankAccount.MERCHANT_ID);
            put(MerchantBizBankAccount.NUMBER, MerchantBizBankAccount.NUMBER);
            put(MerchantBizBankAccount.BIZ, MerchantBizBankAccount.BIZ);
            put(MerchantBizBankAccount.TYPE, MerchantBizBankAccount.TYPE);
            put(MerchantBizBankAccount.HOLDER, MerchantBizBankAccount.HOLDER);
            put(MerchantBizBankAccount.DEFAULT_STATUS, MerchantBizBankAccount.DEFAULT_STATUS);
            put(MerchantBizBankAccount.VERIFY_STATUS, MerchantBizBankAccount.VERIFY_STATUS);
            put(MerchantBizBankAccount.IDENTITY, MerchantBizBankAccount.IDENTITY);
            put(MerchantBizBankAccount.CELLPHONE, MerchantBizBankAccount.CELLPHONE);
            put(MerchantBizBankAccount.ID_TYPE, MerchantBizBankAccount.ID_TYPE);
            put(MerchantBizBankAccount.HOLDER_ID_CARD_ADDRESS, MerchantBizBankAccount.HOLDER_ID_CARD_ADDRESS);
            put(MerchantBizBankAccount.HOLDER_ID_CARD_ISSUING_AUTHORITY, MerchantBizBankAccount.HOLDER_ID_CARD_ISSUING_AUTHORITY);
        }});
        criteria.with(DaoConstants.DELETED).is(false);
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = merchantBizBankAccountReadDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = merchantBizBankAccountReadDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        for (Map map : list) {
            bankInfoBiz.appendBankBranchName(map);
        }
        return new ListResult(count, list);
    }

    @Override
    public void deletedMerchantBizBankAccount(String id) {
        Map oldMerchantBankAccount = merchantBizBankAccountReadDao.filter(Criteria.where(DaoConstants.ID).is(id)).fetchOne();
        if (MapUtils.isEmpty(oldMerchantBankAccount)) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT));
        }
//        int defaultStatus = BeanUtil.getPropInt(oldMerchantBankAccount, MerchantBizBankAccount.DEFAULT_STATUS);
//        int verifyStatus = BeanUtil.getPropInt(oldMerchantBankAccount, MerchantBizBankAccount.VERIFY_STATUS);
//        if (defaultStatus == MerchantBizBankAccount.DEFAULT_STATUS_TRUE && verifyStatus == MerchantBizBankAccount.VERIFY_STATUS_SUCC) {
//            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_DELETED_BANK_ACCOUNT_PRE));
//        }
//        if (verifyStatus == MerchantBizBankAccount.VERIFY_STATUS_INPROGRESS) {
//            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_DELETED_BANK_ACCOUNT_PRE));
//        }
        String numberOld = BeanUtil.getPropString(oldMerchantBankAccount, MerchantBizBankAccount.NUMBER);
        Map merchantBankAccount;
        if (numberOld != null && numberOld.contains("bak")) {
            merchantBankAccount = CollectionUtil.hashMap(
                    DaoConstants.ID, id,
                    DaoConstants.DELETED, true);
        } else {
            merchantBankAccount = CollectionUtil.hashMap(
                    DaoConstants.ID, id,
                    DaoConstants.DELETED, true,
                    MerchantBizBankAccount.NUMBER, String.format("%s_bak_%d", numberOld, System.currentTimeMillis())
            );
        }
        merchantBizBankAccountWriteDao.updatePart(merchantBankAccount);
    }
}
