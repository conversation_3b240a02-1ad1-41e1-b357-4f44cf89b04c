package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.model.License;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.core.util.EffectiveTimeUtil;
import com.wosai.upay.core.util.RemoveSpaceCharsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by hzq on 19/4/3.
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class MerchantBusinessLicenseServiceImpl implements MerchantBusinessLicenseService {

    private Dao<Map<String, Object>> merchantBusinessLicenseReadDao;
    private Dao<Map<String, Object>> merchantBusinessLicenseWriteDao;


    @Autowired
    private DataRepository repository;
    @Autowired
    MerchantService merchantService;
    private Dao<Map<String, Object>> merchantDao;
    @Autowired
    private RMQService rmqService;
    @Autowired
    private LicenseService licenseService;
    //xxx.jpg为第一个捕获组
    private static final Pattern pattern = Pattern.compile("(.*?\\.(jpg|jpeg|png))");

    @PostConstruct
    private void init() {
        merchantBusinessLicenseReadDao = repository.getMerchantBusinessLicenseReadDao();
        merchantBusinessLicenseWriteDao = repository.getMerchantBusinessLicenseWriteDao();
        this.merchantDao = repository.getMerchantDao();
    }

    private static final List<String> fields = Arrays.asList(MerchantBusinessLicence.NUMBER, MerchantBusinessLicence.NAME, MerchantBusinessLicence.LEGAL_PERSON_NAME, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER);

    private static final String MERCHANT_ID = "merchant_id";
    /* 商户类型：个人0、个体1、组织2；
     营业执照类型  0=无营业执照、1=个体工商户营业执照、2=企业营业执照、3=事业单位法人证书、4=民办非企业单位登记证书、5=社会团体法人登记证书、6=基金会法人登记证书、7=律师事务所执业许可证、8=宗教活动场所法人登记证书、9=农民专业合作社
 */
    public final static Map MERCHANT_TYPE = new HashMap() {{
        put(0, 0);
        put(1, 1);
    }};

    //营业执照变更字段
    private static final List<String> updateFields = Arrays.asList("type", "photo", "number", "name", "business_scope",
            "validity", "address", "registered_legal_person_name", "letter_of_authorization",
            "trade_license", "legal_person_id_type", "legal_person_id_card_front_photo",
            "legal_person_id_card_back_photo", "legal_person_name", "legal_person_id_number",
            "id_validity");

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int save(Map merchantBusinessLicense) {
        this.replaceXAndCheckIdValidity(merchantBusinessLicense);
        merchantBusinessLicense.remove(MerchantBusinessLicence.TRADE_LICENSE);
        RemoveSpaceCharsUtil.removeSpaceChars(merchantBusinessLicense, fields);
        merchantBusinessLicense.put(MerchantBusinessLicence.BUSINESS_SCOPE, StringUtils.substring(WosaiMapUtils.getString(merchantBusinessLicense, MerchantBusinessLicence.BUSINESS_SCOPE), 0, 1024));
        merchantBusinessLicenseWriteDao.save(merchantBusinessLicense);
        updateMerchantType(merchantBusinessLicense);
        //许可证字段不为空,入新表
        checkAndSaveSingleLicense(merchantBusinessLicense);
        return 1;
    }

    private void updateMerchantType(Map merchantBusinessLicense) {
        String merchant_id = (String) merchantBusinessLicense.get(MERCHANT_ID);
        Map<String, Object> businessLicenseByMerchantId = this.getBusinessLicenseByMerchantId(merchant_id);
        Long type = (Long) businessLicenseByMerchantId.get("type");
        int merchant_type = (int) Optional.ofNullable(MERCHANT_TYPE.get(type.intValue())).orElse(2);
        merchantDao.updatePart(CollectionUtil.hashMap("id", merchant_id, "merchant_type", merchant_type));
    }


    @Override
    public Map<String, Object> getBusinessLicenseByMerchantId(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        Criteria criteria = Criteria.where(MERCHANT_ID).is(merchantId);
        Map<String, Object> businessLicense = merchantBusinessLicenseReadDao.filter(criteria).fetchOne();
        return getBusinessLicenseTradeLicenseByBusinessLicenseId(businessLicense);
    }

    @Override
    public Map<String, Object> getBusinessLicenseById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id);
        Map<String, Object> businessLicense = merchantBusinessLicenseReadDao.filter(criteria).fetchOne();
        return getBusinessLicenseTradeLicenseByBusinessLicenseId(businessLicense);
    }

    /**
     * 老字段的保存更新操作已弃用, 但展示时拼一个图片
     *
     * @param businessLicense 商户维度营业执照
     * @return
     */
    private Map<String, Object> getBusinessLicenseTradeLicenseByBusinessLicenseId(Map<String, Object> businessLicense) {
        if (WosaiMapUtils.isEmpty(businessLicense) || WosaiStringUtils.isEmpty(MapUtils.getString(businessLicense, "id"))) {
            return businessLicense;
        }
        List<Map> license = licenseService.getLicenseByBusinessLicenseId(MapUtils.getString(businessLicense, "id"));
        if (WosaiCollectionUtils.isNotEmpty(license)) {
            businessLicense.put(MerchantBusinessLicence.TRADE_LICENSE, license.get(0).get(License.LICENSE_PHOTO));
            businessLicense.put(MerchantBusinessLicence.TRADE_LICENSE_LIST, license);
        }

        return businessLicense;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int updateMerchantBusinessLicense(Map businessLicense) {
        this.replaceXAndCheckIdValidity(businessLicense);

        businessLicense.remove(MerchantBusinessLicence.TRADE_LICENSE);
        RemoveSpaceCharsUtil.removeSpaceChars(businessLicense, fields);
        businessLicense.put(MerchantBusinessLicence.BUSINESS_SCOPE, StringUtils.substring(WosaiMapUtils.getString(businessLicense, MerchantBusinessLicence.BUSINESS_SCOPE), 0, 1024));
        handleMerchantBusinessLicensePhoto(businessLicense, MerchantBusinessLicence.PHOTOS);
        String merchantId = BeanUtil.getPropString(businessLicense, MERCHANT_ID);
        Criteria criteria = Criteria.where(MERCHANT_ID).is(merchantId);
        Map<String, Object> license = merchantBusinessLicenseReadDao.filter(criteria).fetchOne();
        if (license == null || license.size() == 0) {
            this.save(businessLicense);
            return 1;
        }
        long id = BeanUtil.getPropLong(license, ConstantUtil.KEY_ID);
        businessLicense.put(ConstantUtil.KEY_ID, id);
        merchantBusinessLicenseWriteDao.updatePart(businessLicense);
        updateMerchantType(businessLicense);
        //发送消息
        if (checkSendMQ(businessLicense, license)) {
            rmqService.writeUpdateBusinessLicense(updateLicense(merchantId));
        }
        checkAndSaveSingleLicense(businessLicense);
        return 1;
    }

    //简化图片,只保存 xxx.jpg ,  后续字符均不保存
    private void handleMerchantBusinessLicensePhoto(Map businessLicense, List<String> keys) {
        for (String key : keys) {
            Matcher matcher = pattern.matcher(MapUtils.getString(businessLicense, key, ""));
            if (matcher.find()) {
                businessLicense.put(key, matcher.group(1));
            }
        }

    }

    @Override
    public int deleteMerchantBusinessLicenseById(String id) {
        merchantBusinessLicenseWriteDao.delete(id);
        return 1;
    }

    @Override
    public List<String> getBusinessLicenseMerchantIds(Map merchantBusinessLicense) {
        String number = (String) merchantBusinessLicense.get("number");
        String legalPersonIdNumber = (String) merchantBusinessLicense.get("legal_person_id_number");
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(number)) {
            criteria.with("number").is(number);
        }
        if (StringUtils.isNotEmpty(legalPersonIdNumber)) {
            criteria.with("legal_person_id_number").is(legalPersonIdNumber);
        }
        List<Map<String, Object>> ts = CollectionUtil.iterator2list(merchantBusinessLicenseReadDao.filter(criteria).fetchAll());
        return ts.stream().map(map -> (String) map.get("merchant_id")).collect(Collectors.toList());
    }


    @Override
    public ListResult getBusinessLicenseByLegalPersonIdNumberOrNumber(PageInfo pageInfo, Map merchantBusinessLicense) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        String number = (String) merchantBusinessLicense.get("number");
        String legalPersonIdNumber = (String) merchantBusinessLicense.get("legal_person_id_number");
        Criteria criteria = new Criteria();
        if (StringUtils.isNotEmpty(number)) {
            criteria.with("number").is(number);
        }
        if (StringUtils.isNotEmpty(legalPersonIdNumber)) {
            criteria.with("legal_person_id_number").is(legalPersonIdNumber);
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = merchantBusinessLicenseReadDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = merchantBusinessLicenseReadDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        List<Map> result = new ArrayList<>();
        for (Map map : list) {
            result.add(getBusinessLicenseTradeLicenseByBusinessLicenseId(map));
        }
        return new ListResult(count, result);
    }


    private void replaceXAndCheckIdValidity(Map license) {
        if (WosaiMapUtils.isNotEmpty(license)) {
            String validity = WosaiMapUtils.getString(license, MerchantBusinessLicence.VALIDITY);
            if (WosaiStringUtils.isNotEmpty(validity)) {
                EffectiveTimeUtil.checkoutEffectiveTime(validity);
            }
            Integer idType = WosaiMapUtils.getInteger(license, MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE);
            if (Objects.isNull(idType) || idType != 1) {
                return;
            }
            String idNumber = WosaiMapUtils.getString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER);
            if (WosaiStringUtils.isNotEmpty(idNumber) && idNumber.contains("x")) {
                idNumber = idNumber.replace("x", "X");
                license.put(MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER, idNumber);
            }
            if (WosaiStringUtils.isNotEmpty(idNumber)) {
                if (idNumber.startsWith("810000") || idNumber.startsWith("820000") || idNumber.startsWith("830000")) {
                    throw new CoreInvalidParameterException("证件类型与证件号不匹配");
                }
            }
            String idValidity = WosaiMapUtils.getString(license, MerchantBusinessLicence.ID_VALIDITY);
            if (WosaiStringUtils.isNotEmpty(idValidity) && WosaiMapUtils.getInteger(license, MerchantBusinessLicence.LEGAL_PERSON_ID_TYPE) == 1) {
                EffectiveTimeUtil.checkoutEffectiveTime(idValidity);
            }
        }
    }


    /**
     * 检查是否发送消息
     * true 发送消息 false 不发消息
     *
     * @param updateLicense 接口传入 更新的营业执照信息
     * @param license       数据库保存的营业执照信息
     * @return
     */
    private boolean checkSendMQ(Map updateLicense, Map license) {
        //以下字段更新 发送消息
        for (String field : updateFields) {
            String update = WosaiMapUtils.getString(updateLicense, field);
            if (StringUtils.isNotBlank(update)) {
                String original = WosaiMapUtils.getString(license, field);
                if (StringUtils.isBlank(original) || !update.equals(original)) {
                    return true;
                }
            }
        }
        return false;
    }


    private Map updateLicense(String merchantId) {
        Criteria merInfo = Criteria.where(DaoConstants.ID).is(merchantId);
        Map<String, Object> merchant = merchantDao.filter(merInfo).fetchOne();
        log.info("商户{}修改营业执照", merchantId);
        Map<String, String> map = new HashMap<>();
        map.put("id", merchantId);
        map.put("sn", BeanUtil.getPropString(merchant, "sn"));
        map.put("type", BeanUtil.getPropString(merchant, "merchant_type", "0"));
        log.info("商户{}修改营业执照{}", merchantId, JSONObject.toJSONString(map));
        return map;
    }

    /**
     * 检查商户营业执照许可证字段,
     * 有就存入单独的许可证表(新表无数据), 或者随机更新一条新表数据(新表有数据)
     * 老字段设为空字符就删除一个新表数据(如新表有数据)
     */
    private void checkAndSaveSingleLicense(Map merchantBusinessLicense) {

//        if (MapUtils.getString(merchantBusinessLicense, MerchantBusinessLicence.TRADE_LICENSE) == null) {
//            return;
//        }
//        String business_license_id = null;
//
//        if (Objects.isNull(merchantBusinessLicense.get(ConstantUtil.KEY_ID))) {
//            //营业执照id没有值, 只能反查一次再获取到了
//            Map<String, Object> saved = getBusinessLicenseByMerchantId((String) merchantBusinessLicense.get(ConstantUtil.KEY_MERCHANT_ID));
//            business_license_id = Long.toString(MapUtils.getLongValue(saved, "id"));
//
//        } else {
//            business_license_id = Long.toString(MapUtils.getLongValue(merchantBusinessLicense, "id"));
//        }
//
//        List<Map> licenses = licenseService.getLicenseByBusinessLicenseId(business_license_id);
//
//        //老字段有值
//        if (WosaiStringUtils.isNotBlank(MapUtils.getString(merchantBusinessLicense, MerchantBusinessLicence.TRADE_LICENSE))) {
//            //许可证新表不为空,就随便更新一个
//            if (CollectionUtils.isNotEmpty(licenses)) {
//                String willUpdateId = MapUtils.getString(licenses.get(0), ConstantUtil.KEY_ID);
//                licenseService.updateLicense(CollectionUtil.hashMap(ConstantUtil.KEY_ID, willUpdateId, License.LICENSE_PHOTO, merchantBusinessLicense.get("trade_license")));
//            } else {
//                Map license = new HashMap();
//                license.put(ConstantUtil.KEY_ID, UUID.randomUUID().toString());
//                license.put(License.BUSINESS_LICENSE_ID, business_license_id);
//                license.put(License.LICENSE_PHOTO, merchantBusinessLicense.get("trade_license"));
//                license.put("ctime", System.currentTimeMillis());
//                license.put("mtime", System.currentTimeMillis());
//                licenseService.saveLicense(license);
//            }
//        } else {
//            //老字段为空字符,删除一条原表数据(新表如有数据)
//            if (CollectionUtils.isNotEmpty(licenses)) {
//                String willDeleteId = MapUtils.getString(licenses.get(0), ConstantUtil.KEY_ID);
//                licenseService.deleteLicenseById(willDeleteId);
//
//            }
//        }

    }

}
