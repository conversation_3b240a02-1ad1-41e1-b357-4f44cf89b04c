package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class MerchantMaterialPhotoServiceImpl implements MerchantMaterialPhotoService {

    @Autowired
    private Dao<Map<String, Object>> merchantMaterialPhotoDao;

    @Override
    public int createMerchantMaterialPhoto(Map<String, Object> merchantMaterialPhoto) {
        merchantMaterialPhotoDao.save(merchantMaterialPhoto);
        return 1;
    }

    @Override
    public int updateMerchantMaterialPhoto(Map<String, Object> merchantMaterialPhoto) {
        merchantMaterialPhotoDao.updatePart(merchantMaterialPhoto);
        return 1;
    }

    @Override
    public Map<String, Object> findMerchantMaterialPhotoById(Long merchantMaterialPhotoId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantMaterialPhotoId);
        return merchantMaterialPhotoDao.filter(criteria).fetchOne();
    }

    @Override
    public Map<String, Object> findMerchantMaterialPhotoByMerchantId(String merchantId) {
        Criteria criteria = Criteria.where("merchant_id").is(merchantId);
        return merchantMaterialPhotoDao.filter(criteria).fetchOne();
    }

    @Override
    public int saveOrUpdateMerchantMaterialPhoto(Map<String, Object> merchantMaterialPhoto) {
        MapUtil.removeNullValues(merchantMaterialPhoto);
        String merchantId = MapUtils.getString(merchantMaterialPhoto, "merchant_id", "");
        Map<String, Object> merchantMaterialPhotoByMerchantId = findMerchantMaterialPhotoByMerchantId(merchantId);
        if (MapUtils.isEmpty(merchantMaterialPhotoByMerchantId)) {
            createMerchantMaterialPhoto(merchantMaterialPhoto);
        } else {
            merchantMaterialPhoto.put(DaoConstants.ID, merchantMaterialPhotoByMerchantId.get(DaoConstants.ID));
            updateMerchantMaterialPhoto(merchantMaterialPhoto);
        }

        return 1;
    }
}
