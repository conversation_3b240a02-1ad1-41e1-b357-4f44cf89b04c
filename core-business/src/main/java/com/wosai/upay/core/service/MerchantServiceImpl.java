package com.wosai.upay.core.service;

import avro.shaded.com.google.common.base.Predicate;
import avro.shaded.com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.bank.info.api.service.IndustryService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.request.BaseMerchantRequest;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.bean.request.WapMiniChangeVerifyRequest;
import com.wosai.upay.core.bean.response.MerchantBankHolderSimpleIdentityInfoResponse;
import com.wosai.upay.core.bean.response.WapMiniChangeVerifyResponse;
import com.wosai.upay.core.databus.MerchantDataBusBiz;
import com.wosai.upay.core.datasource.DataSourceConstant;
import com.wosai.upay.core.datasource.DataSourceType;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.application.ApplicationBase;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.biz.BankInfoBiz;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.core.util.EsUtil;
import com.wosai.upay.core.util.IdCardExtendUtil;
import com.wosai.upay.core.util.MapValueUtil;
import com.wosai.upay.merchant.audit.api.service.ApplicationService;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import com.wosai.upay.user.api.service.UserService;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.data.util.StringUtil.empty;

/**
 * Created by jianfree on 21/1/16.
 */

@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class MerchantServiceImpl implements MerchantService {
    private static final Logger logger = LoggerFactory.getLogger(MerchantServiceImpl.class);

    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private SnGenerator snGenerator;

    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private SolicitorService solicitorService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private UserService userService;
    @Autowired
    private RMQService rmqService;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private MerchantAuditService merchantAuditService;
    @Autowired
    private IndustryService industryService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private MerchantDataBusBiz merchantDataBusBiz;

    @Autowired
    private BankInfoBiz bankInfoBiz;

    @Autowired
    private BizLogFacade bizLogFacade;

    @Autowired
    private DataRepository dataRepository;
    private Dao<Map<String, Object>> merchantDao;
    private Dao<Map<String, Object>> merchantBankAccountReadDao;
    private Dao<Map<String, Object>> merchantBankAccountWriteDao;
    private Dao<Map<String, Object>> merchantBankAccountPreReadDao;
    private Dao<Map<String, Object>> merchantBankAccountPreWriteDao;
    private Dao<Map<String, Object>> merchantBankAccountChangeLogWriteDao;
    private Dao<Map<String, Object>> merchantDeveloperDao;
    private Dao<Map<String, Object>> merchantConfigDao;


    public static ThreadPoolExecutor threadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);

    @Autowired
    public MerchantServiceImpl(DataRepository repository) {
        this.merchantDao = repository.getMerchantDao();
        this.merchantBankAccountReadDao = repository.getMerchantBankAccountReadDao();
        this.merchantBankAccountWriteDao = repository.getMerchantBankAccountWriteDao();
        this.merchantBankAccountPreReadDao = repository.getMerchantBankAccountPreReadDao();
        this.merchantBankAccountPreWriteDao = repository.getMerchantBankAccountPreWriteDao();
        this.merchantBankAccountChangeLogWriteDao = repository.getmerchantBankAccountChangeLogWriteDao();
        this.merchantDeveloperDao = repository.getMerchantDeveloperDao();
        this.merchantConfigDao = repository.getMerchantConfigDao();

    }

    @Override
    @Transactional(value = "transactionManager")
    public Map createMerchant(Map merchant) {
        CrudUtil.ignoreForCreate(merchant);
        return createMerchantForMerchantCenter(merchant);
    }

    @Override
    public Map createMerchantForMerchantCenter(Map merchant) {
        MapUtil.removeKeys(merchant, new String[]{Merchant.STATUS, Merchant.SN});
        if (merchant.get(DaoConstants.ID) == null) {
            merchant.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        merchant.put(Merchant.SN, snGenerator.nextMerchantSn());
        merchant.put(Merchant.STATUS, Merchant.STATUS_ENABLED); // 默认开启
        // 默认是小微，新增或者更新营业执照时，根据营业执照类型更新
        merchant.put(Merchant.MERCHANT_TYPE, 0);
        merchant.put(Merchant.WITHDRAW_MODE, Merchant.WITHDRAW_MODE_AUTO);
        bankInfoBiz.appendDistrictCode(merchant, true);
        merchantDao.save(merchant);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        closeAlipay(merchantId);
        Map rs = getMerchant(merchantId);
        rmqService.writeNewMerchantCreated(rs);
        merchantDataBusBiz.insert(rs);
        return rs;
    }

    private Map<String, Object> getMerchantMarketingDTSMessage(String id, String operationType, Map merchantBankAccountOld, Map merchantBankAccountNew, String changeWay, String operator) {
        Map<String, Object> message = new HashMap<String, Object>();
        message.put("source", "gw2");
        message.put("table_name", "merchant_bank_account");
        message.put("id", id);
        message.put("operation_type", operationType);
        message.put("record_old", merchantBankAccountOld);
        message.put("record_new", merchantBankAccountNew);
        message.put("change_way", changeWay);
        message.put("operator", operator);
        return message;
    }

    @Override
    @Transactional(value = "transactionManager")
    /*
     * 在request中添加新的不属于merchant对象的属性，注意在createMerchant接口调用前取出之后remove掉
     * */
    public Map createMerchantComplete(Map request) {
        // 商户费率配置
        List<Map> merchantConfig = null;
        if (request.get("merchant_config") != null) {
            merchantConfig = (List<Map>) request.get("merchant_config");
            request.remove("merchant_config");
        }

        // 银行卡信息
        Map merchantBankAccount = null;
        if (request.get("merchant_bank_account") != null) {
            merchantBankAccount = (Map) request.get("merchant_bank_account");
            request.remove("merchant_bank_account");
        }

        // 门店信息
        Map store = null;
        if (request.get("store") != null) {
            store = (Map) request.get("store");
            request.remove("store");
        }

        // 关联account
        String accountId = null;
        if (request.get("account_id") != null) {
            accountId = (String) request.get("account_id");
            request.remove("account_id");
        }

        // 资料提交人
        String submitter = BeanUtil.getPropString(request, MerchantAudit.SUBMITTER);
        if (submitter != null) {
            request.remove(MerchantAudit.SUBMITTER);
        }

        String submitPlatform = BeanUtil.getPropString(request, MerchantAudit.SUBMIT_PLATFORM);
        if (submitPlatform != null) {
            request.remove(MerchantAudit.SUBMIT_PLATFORM);
        }

        String submitOrganizationPath = BeanUtil.getPropString(request, MerchantAudit.SUBMIT_ORGANIZATION_PATH);
        if (submitOrganizationPath != null) {
            request.remove(MerchantAudit.SUBMIT_ORGANIZATION_PATH);
        }

        // 真实性审核验证
        Map merchantAudit = null;
        if (request.get("merchant_audit") != null) {
            merchantAudit = (Map) request.get("merchant_audit");
            request.remove("merchant_audit");
        }

        MerchantService merchantService = SpringContextHolder.getBean(MerchantService.class);
        Map merchant = merchantService.createMerchant(request);
        final String merchantId = BeanUtil.getPropString(merchant, ConstantUtil.KEY_ID);
        final String solicitorId = BeanUtil.getPropString(merchant, Merchant.SOLICITOR_ID);

        if (merchantConfig != null && merchantConfig.size() != 0) { // 新增费率配置
            TradeConfigService tradeConfigService = SpringContextHolder.getBean(TradeConfigService.class);

            Map defaultSolicitorTradeConfig = tradeConfigService.getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, null);
            Map defaultMerchantTradeConfig = new HashMap();
            defaultMerchantTradeConfig.put(MerchantConfig.MERCHANT_ID, merchantId);
            if (defaultSolicitorTradeConfig != null) {
                defaultMerchantTradeConfig.put(MerchantConfig.PARAMS, defaultSolicitorTradeConfig.get(SolicitorConfig.PARAMS));
                defaultMerchantTradeConfig.put(MerchantConfig.PROVIDER, defaultSolicitorTradeConfig.get(SolicitorConfig.PROVIDER));
            }

            for (Map config : merchantConfig) {
                if (config.get(MerchantConfig.PAYWAY) == null) { // payway为null的配置时取params信息
                    defaultMerchantTradeConfig.put(MerchantConfig.PARAMS, config.get(MerchantConfig.PARAMS));
                    defaultMerchantTradeConfig.put(MerchantConfig.PROVIDER, config.get(MerchantConfig.PROVIDER));
                    //todo 设置agentName 按照规则设置还是根据上送参数来设置
                    defaultMerchantTradeConfig.put(MerchantConfig.B2C_AGENT_NAME, config.get(TradeConfigService.AGENT_NAME));
                    defaultMerchantTradeConfig.put(MerchantConfig.C2B_AGENT_NAME, config.get(TradeConfigService.AGENT_NAME));
                    defaultMerchantTradeConfig.put(MerchantConfig.WAP_AGENT_NAME, config.get(TradeConfigService.AGENT_NAME));
                    continue;
                }

                config.put("new", "yes"); // 标识是新增的
                tradeConfigService.updateMerchantConfigStatusAndFeeRateObeySolicitor(merchantId, config);
            }

            // 设置payway为null时候的配置
            if (defaultMerchantTradeConfig.get(MerchantConfig.PARAMS) != null) {
                tradeConfigService.createMerchantConfig(defaultMerchantTradeConfig);
            }

            merchant.put("merchant_config", tradeConfigService.getAnalyzedMerchantConfigs(merchantId));
        }

        if (merchantBankAccount != null) { // 新增银行卡信息
            merchantBankAccount.put(ConstantUtil.KEY_MERCHANT_ID, merchantId);
            merchant.put("merchant_bank_account", merchantService.bindMerchantBankAccount(merchantBankAccount));
        }

        // 创建applicationBase
        Map applicationBase = transMerchantToApplicationBase(merchant, merchantBankAccount);
        merchant.put("applicationBase", applicationService.createApplicationBase(applicationBase));

        // 真实性审核验证
        merchantAudit = transMerchantToMerchantAudit(merchant, submitter, submitPlatform, submitOrganizationPath, merchantAudit);
        merchant.put("merchantAudit", merchantAuditService.create(merchantAudit));

        if (store != null) { // 新增门店
            store.put(ConstantUtil.KEY_MERCHANT_ID, merchantId);
            merchant.put("store", storeService.createStore(store));
        }

        if (accountId != null) {
            Map merchantUser = new HashMap();
            merchantUser.put(MerchantUser.ACCOUNT_ID, accountId);
            merchantUser.put(MerchantUser.MERCHANT_ID, merchantId);
            merchantUser.put(MerchantUser.ROLE, MerchantUser.ROLE_SUPER_ADMIN); // 创建时候的账号是商户的超级管理员
            merchantUser.put(MerchantUser.STORE_AUTH, 1); // 管理所有门店
            merchant.put("account", userService.createMerchantUser(merchantUser));
        }

        return merchant;
    }

    /**
     * 将 商户、商户银行账号信息 转换到ApplicationBase.
     *
     * @param merchant
     * @param merchantBankAccount
     * @return
     */
    private Map transMerchantToApplicationBase(Map merchant, Map merchantBankAccount) {
        Map applicationBase = new HashMap();
        applicationBase.put(ApplicationBase.MERCHANT_ID, BeanUtil.getPropString(merchant, DaoConstants.ID));
        applicationBase.put(ApplicationBase.MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
        applicationBase.put(ApplicationBase.STATUS, ApplicationBase.STATUS_CREATED);
        applicationBase.put(ApplicationBase.ACCOUNT_TYPE, BeanUtil.getPropString(merchant, Merchant.LEGAL_PERSON_TYPE));
        applicationBase.put(ApplicationBase.ADDRESS, BeanUtil.getPropString(merchant, Merchant.PROVINCE) + BeanUtil.getPropString(merchant, Merchant.CITY)
                + BeanUtil.getPropString(merchant, Merchant.DISTRICT) + BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS));
        applicationBase.put(ApplicationBase.LEGAL_PERSON, BeanUtil.getPropString(merchant, Merchant.LEGAL_PERSON_NAME));
        applicationBase.put(ApplicationBase.LEGAL_PERSON_ID_TYPE, BeanUtil.getPropString(merchant, Merchant.LEGAL_PERSON_ID_TYPE));
        applicationBase.put(ApplicationBase.LEGAL_PERSON_ID_NUMBER, BeanUtil.getPropString(merchant, Merchant.LEGAL_PERSON_ID_NUMBER));
        applicationBase.put(ApplicationBase.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, BeanUtil.getPropString(merchant, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
        applicationBase.put(ApplicationBase.LEGAL_PERSON_ID_CARD_BACK_PHOTO, BeanUtil.getPropString(merchant, Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        applicationBase.put(ApplicationBase.BUSINESS_LICENSE_PHOTO, BeanUtil.getPropString(merchant, Merchant.BUSINESS_LICENSE_PHOTO));

        applicationBase.put(ApplicationBase.BANK_NAME, BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.BANK_NAME));
        applicationBase.put(ApplicationBase.BANK_ACCOUNT_NUMBER, BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.NUMBER));
        applicationBase.put(ApplicationBase.BANK_ACCOUNT_HOLDER_NAME, BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER));
        applicationBase.put(ApplicationBase.BANK_BRANCH_PROVINCE, BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.CITY));
        return applicationBase;
    }

    /**
     * 将 商户、商户银行账号信息 转换到MerchantAudit
     *
     * @param merchant
     * @return
     */
    private Map transMerchantToMerchantAudit(Map merchant, String submitter, String submitPlatform, String submitOrganizationPath, Map merchantAuditInfo) {
        Map merchantAudit = new HashMap();
        merchantAudit.put(MerchantAudit.MERCHANT_ID, BeanUtil.getPropString(merchant, DaoConstants.ID));
        merchantAudit.put(MerchantAudit.MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
        merchantAudit.put(MerchantAudit.MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
        merchantAudit.put(MerchantAudit.MERCHANT_PROVINCE, BeanUtil.getPropString(merchant, Merchant.PROVINCE));
        merchantAudit.put(MerchantAudit.MERCHANT_CITY, BeanUtil.getPropString(merchant, Merchant.CITY));
        merchantAudit.put(MerchantAudit.MERCHANT_DISTRICT, BeanUtil.getPropString(merchant, Merchant.DISTRICT));
        merchantAudit.put(MerchantAudit.MERCHANT_STREET_ADDRESS, BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS));
        merchantAudit.put(MerchantAudit.MERCHANT_OWNER_NAME, BeanUtil.getPropString(merchant, Merchant.OWNER_NAME));
        merchantAudit.put(MerchantAudit.MERCHANT_OWNER_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.OWNER_CELLPHONE));
        merchantAudit.put(MerchantAudit.MERCHANT_CONTACT_NAME, BeanUtil.getPropString(merchant, Merchant.CONTACT_NAME));
        merchantAudit.put(MerchantAudit.MERCHANT_CONTACT_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
        merchantAudit.put(MerchantAudit.MERCHANT_CTIME, BeanUtil.getPropLong(merchant, DaoConstants.CTIME));

        //merchantAuditInfo 中的submitter信息覆盖
        submitter = BeanUtil.getPropString(merchantAuditInfo, MerchantAudit.SUBMITTER, submitter);
        submitPlatform = BeanUtil.getPropString(merchantAuditInfo, MerchantAudit.SUBMIT_PLATFORM, submitPlatform);
        submitOrganizationPath = BeanUtil.getPropString(merchantAuditInfo, MerchantAudit.SUBMIT_ORGANIZATION_PATH, submitOrganizationPath);
        merchantAudit.put(MerchantAudit.SUBMITTER, submitter);
        merchantAudit.put(MerchantAudit.SUBMIT_PLATFORM, submitPlatform);
        merchantAudit.put(MerchantAudit.SUBMIT_ORGANIZATION_PATH, submitOrganizationPath);
        merchantAudit.put(MerchantAudit.STATUS, MerchantAudit.STATUS_MATERIAL_NOT_SUBMIT);
        merchantAudit.put(MerchantAudit.STORE_PHOTO_STATUS, MerchantAudit.STATUS_MATERIAL_NOT_SUBMIT);
        merchantAudit.put(MerchantAudit.BASE_INFO_STATUS, MerchantAudit.STATUS_PENDING);
        String businessLicensePhoto = BeanUtil.getPropString(merchant, Merchant.BUSINESS_LICENSE_PHOTO);
        merchantAudit.put(MerchantAudit.BUSINESS_LICENSE_STATUS, StringUtil.empty(businessLicensePhoto) ? MerchantAudit.STATUS_MATERIAL_NOT_SUBMIT : MerchantAudit.STATUS_PENDING);
        String solicitorId = BeanUtil.getPropString(merchant, Merchant.SOLICITOR_ID);
        Map solicitor = solicitorService.getSolicitor(solicitorId);
        merchantAudit.put(MerchantAudit.MERCHANT_SOLICITOR_ID, solicitorId);
        merchantAudit.put(MerchantAudit.MERCHANT_SOLICITOR_NAME, BeanUtil.getPropString(solicitor, Solicitor.NAME));

        merchantAuditInfo = BeanUtil.getPart(merchantAuditInfo, Arrays.asList(
                MerchantAudit.BRAND_PHOTO,
                MerchantAudit.INDOOR_MATERIAL_PHOTO,
                MerchantAudit.OUTDOOR_MATERIAL_PHOTO,
                MerchantAudit.OTHER_PHOTO,
                MerchantAudit.BRAND,
                MerchantAudit.INDOOR_MATERIAL,
                MerchantAudit.OUTDOOR_MATERIAL,
                MerchantAudit.OTHER,
                MerchantAudit.REMARK,
                MerchantAudit.AUDIT_PHOTO
        ));
        merchantAudit.putAll(merchantAuditInfo);
        return merchantAudit;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void enableMerchant(String merchantId) {
        Map merchantMininfo = businssCommonService.getMerchantMinimalInfoById(merchantId);
        int preStatus = BeanUtil.getPropInt(merchantMininfo, ConstantUtil.KEY_STATUS, -1);
        if (preStatus != Merchant.STATUS_DISABLED) {
            throw new CoreOnlyStatusDisabledCouldEnableException(CoreException.getCodeDesc(CoreException.CODE_ONLY_STATUS_DISABLED_COULD_ENABLE));
        }
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, merchantId,
                Merchant.STATUS, Merchant.STATUS_ENABLED
        );
        merchantDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_MERCHANT, merchantMininfo);
        merchantDataBusBiz.statusChange(merchantMininfo, preStatus, Merchant.STATUS_ENABLED);
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(BeanUtil.getPropString(merchantMininfo, Merchant.SN));
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void disableMerchant(String merchantId) {
        Map merchantMininfo = businssCommonService.getMerchantMinimalInfoById(merchantId);
        int preStatus = BeanUtil.getPropInt(merchantMininfo, ConstantUtil.KEY_STATUS, -1);
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(merchantMininfo, DaoConstants.ID),
                Merchant.STATUS, Merchant.STATUS_DISABLED
        );
        merchantDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_MERCHANT, merchantMininfo);
        merchantDataBusBiz.statusChange(merchantMininfo, preStatus, Merchant.STATUS_DISABLED);
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(BeanUtil.getPropString(merchantMininfo, Merchant.SN));
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void closeMerchant(String merchantId) {
        Map merchantMininfo = businssCommonService.getMerchantMinimalInfoById(merchantId);
        int preStatus = BeanUtil.getPropInt(merchantMininfo, ConstantUtil.KEY_STATUS, -1);
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(merchantMininfo, DaoConstants.ID),
                Merchant.STATUS, Merchant.STATUS_CLOSED
        );
        merchantDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_MERCHANT, merchantMininfo);
        merchantDataBusBiz.statusChange(merchantMininfo, preStatus, Merchant.STATUS_CLOSED);
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(BeanUtil.getPropString(merchantMininfo, Merchant.SN));
    }

    @Override
    public void closeMerchantAndLog(String merchantId, OpLogCreateRequest opLogCreateRequest) {
        Map merchantMininfoBefore = businssCommonService.getMerchantMinimalInfoById(merchantId);
        int preStatus = BeanUtil.getPropInt(merchantMininfoBefore, ConstantUtil.KEY_STATUS, -1);
        Map<String, Object> beforeMap = new HashMap() {
            {
                put(Merchant.STATUS, preStatus);
                put(Merchant.SN, BeanUtil.getPropString(merchantMininfoBefore, Merchant.SN));
                put(com.wosai.upay.common.dao.DaoConstants.ID, BeanUtil.getPropString(merchantMininfoBefore, com.wosai.upay.common.dao.DaoConstants.ID));
            }
        };
        dataRepository.doMerchantRelatedTransaction(() -> {
            closeMerchant(merchantId);
        });
        Map merchantMininfoAfter = businssCommonService.getMerchantMinimalInfoById(merchantId);
        int afterStatus = BeanUtil.getPropInt(merchantMininfoAfter, ConstantUtil.KEY_STATUS, -1);
        Map<String, Object> AfterMap = new HashMap() {
            {
                put(Merchant.STATUS, afterStatus);
                put(Merchant.SN, BeanUtil.getPropString(merchantMininfoBefore, Merchant.SN));
                put(com.wosai.upay.common.dao.DaoConstants.ID, BeanUtil.getPropString(merchantMininfoBefore, com.wosai.upay.common.dao.DaoConstants.ID));
            }
        };
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.MERCHANT_TEMPLATE_CODE, OpLog.MERCHANT_TABLE_NAME, OpLog.FIXED_MERCHANT_KEY_LIST, OpLog.MERCHANT_CHANGE_KEY_LIST, OpLog.MERCHANT_DESC_MAP, beforeMap, AfterMap);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map updateMerchant(Map merchant) {
        merchant = MapValueUtil.filterNullValues(merchant);
        Map preValue;
        String id = BeanUtil.getPropString(merchant, DaoConstants.ID);
        String sn = BeanUtil.getPropString(merchant, Merchant.SN);

        if (WosaiStringUtils.isNotEmpty(id)) {
            preValue = getMerchant(id);
        } else if (WosaiStringUtils.isNotEmpty(sn)) {
            preValue = getMerchantByMerchantSn(sn);
            merchant.put(DaoConstants.ID, BeanUtil.getPropString(preValue, DaoConstants.ID));
        } else {
            throw new CoreInvalidParameterException("id、sn不能同时为空");
        }

        if (WosaiMapUtils.isEmpty(preValue)) {
            return preValue;
        }

        if (merchant.containsKey(Merchant.EXTRA)) {
            Map beforeExtra = WosaiMapUtils.getMap(preValue, Merchant.EXTRA, new HashMap());
            Map afterExtra = WosaiMapUtils.getMap(merchant, Merchant.EXTRA, new HashMap());
            beforeExtra.putAll(afterExtra);
            merchant.put(Merchant.EXTRA, beforeExtra);
        }

        CrudUtil.ignoreForUpdate(merchant, new String[]{Merchant.STATUS, Merchant.SN, Merchant.VENDOR_ID, Merchant.SOLICITOR_ID});
        bankInfoBiz.appendDistrictCode(merchant, false);
        merchantDao.updatePart(merchant);
        Map updateRs = getMerchantByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_MERCHANT, updateRs);
        updateMerchantAudit(merchant);
        merchantDataBusBiz.update(preValue, updateRs);
        return updateRs;
    }

    @Override
    public Map updateMerchantAndLog(Map merchant, OpLogCreateRequest opLogCreateRequest) {
        Map preValue;
        Map afterValue;
        String id = BeanUtil.getPropString(merchant, DaoConstants.ID);
        String sn = BeanUtil.getPropString(merchant, Merchant.SN);
        if (WosaiStringUtils.isNotEmpty(id)) {
            preValue = getMerchant(id);
        } else if (WosaiStringUtils.isNotEmpty(sn)) {
            preValue = getMerchantByMerchantSn(sn);
            id = BeanUtil.getPropString(preValue, DaoConstants.ID);
        } else {
            throw new CoreInvalidParameterException("id、sn不能同时为空");
        }
        dataRepository.doMerchantRelatedTransaction(()->{
            updateMerchant(merchant);
        });
        afterValue = getMerchant(id);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, id, null, OpLog.MERCHANT_TEMPLATE_CODE, OpLog.MERCHANT_TABLE_NAME, OpLog.FIXED_MERCHANT_KEY_LIST, OpLog.MERCHANT_CHANGE_KEY_LIST, OpLog.MERCHANT_DESC_MAP, preValue, afterValue);
        return afterValue;
    }

    @Override
    public Map getMerchantByMerchantSn(String merchantSn) {
        if (StringUtil.empty(merchantSn)) {
            return null;
        }
        Criteria criteria = Criteria.where(Merchant.SN).is(merchantSn);
        Map<String, Object> merchant = merchantDao.filter(criteria).fetchOne();
        bankInfoBiz.resolveDistrict(merchant);
        return merchant;

    }

    @Override
    public Map getMerchantByMerchantId(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map<String, Object> merchant = merchantDao.filter(criteria).fetchOne();
        bankInfoBiz.resolveDistrict(merchant);
        return merchant;
    }

    @Override
    public Map getMerchantAndBankAccount(String merchantId) {
        Map merchant = getMerchantByMerchantId(merchantId);
        if (merchant == null) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        String industry = SpringContextHolder.getBean(MerchantService.class).getIndustriesNameMapFromBankInfoService().get(BeanUtil.getPropString(merchant, Merchant.INDUSTRY));
        if (industry != null) {
            String[] levels = industry.split("\t");
            merchant.put(Merchant.INDUSTRY + "_" + Industry.LEVEL1, levels[0]);
            merchant.put(Merchant.INDUSTRY + "_" + Industry.LEVEL2, levels[1]);
        }
        merchant.put("bank_account", getMerchantBankAccountByMerchantId(merchantId));
        return merchant;
    }

    @Override
    @Cacheable("industriesNameMapFromBankInfoService")
    public Map<String, String> getIndustriesNameMapFromBankInfoService() {
        return industryService.getIndustriesNameMap();
    }

    @Override
    public Map getMerchant(String merchantId) {
        return getMerchantByMerchantId(merchantId);
    }

    @Override
    public Map getMerchantBySn(String merchantSn) {
        return getMerchantByMerchantSn(merchantSn);
    }

    @Override
    public ListResult findMerchants(PageInfo pageInfo, Map queryFilter) {
        return applyCriteria(pageInfo, queryFilter, null);
    }

    @Override
    public ListResult findSimpleMerchants(PageInfo pageInfo, Map queryFilter) {
        Collection<String> projection = Arrays.asList(DaoConstants.ID, Merchant.SN, Merchant.NAME, Merchant.STATUS);
        return applyCriteria(pageInfo, queryFilter, projection);
    }

    public ListResult applyCriteria(PageInfo pageInfo, Map queryFilter, Collection<String> projection) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);

        String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
        String merchantSn = BeanUtil.getPropString(queryFilter, "merchant_sn");
        String merchantName = BeanUtil.getPropString(queryFilter, "merchant_name");
        String solicitorId = BeanUtil.getPropString(queryFilter, "solicitor_id");
        String solicitorName = BeanUtil.getPropString(queryFilter, "solicitor_name");
        String merchantAlias = BeanUtil.getPropString(queryFilter, "merchant_alias");
        String contactPhone = BeanUtil.getPropString(queryFilter, "contact_phone");
        String contactCellphone = BeanUtil.getPropString(queryFilter, "contact_cellphone");
        String ownerCellphone = BeanUtil.getPropString(queryFilter, "owner_cellphone");
        String cellphone = BeanUtil.getPropString(queryFilter, "cellphone");
        String vendorId = BeanUtil.getPropString(queryFilter, "vendor_id");
        String merchantType = BeanUtil.getPropString(queryFilter, "merchant_type");
        String merchantBusinessName = BeanUtil.getPropString(queryFilter, "merchant_business_name");
        List<String> merchantSns = null;
        if (queryFilter != null && queryFilter.containsKey("merchant_sns")) {
            try {
                merchantSns = (List<String>) queryFilter.get("merchant_sns");
            } catch (Exception e) {

            }
        }
        List<String> merchantIds = null;
        if (queryFilter != null && queryFilter.containsKey("merchant_ids")) {
            try {
                merchantIds = (List<String>) queryFilter.get("merchant_ids");
            } catch (Exception e) {

            }
        }
        Integer status = null;
        if (queryFilter != null && queryFilter.containsKey("status")) {
            status = BeanUtil.getPropInt(queryFilter, "status");
        }

        Criteria criteria = Criteria.where(DaoConstants.ID).ne(null);

        if (!StringUtil.empty(merchantId)) {
            criteria.with(DaoConstants.ID).is(merchantId);
        }
        if (!empty(merchantSn)) {
            criteria.with(Merchant.SN).is(merchantSn);
        }
        if (merchantSns != null && merchantSns.size() > 0) {
            criteria.with(Merchant.SN).in(merchantSns);
        }
        if (merchantIds != null && merchantIds.size() > 0) {
            criteria.with(DaoConstants.ID).in(merchantIds);
        }
        if (!empty(merchantName)) {
            try {
                Criteria esCriteria = EsUtil.allLikeConvertCriteria(new EsUtil.QueryInfo("merchant", "name"), merchantName, "id");
                if (esCriteria != null) {
                    logger.info("ES拼接前查询条件:" + criteria.toString());
                    criteria.withAnd(esCriteria);
                    logger.info("ES拼接后查询条件:" + criteria.toString());
                } else {
                    return new ListResult(0, new ArrayList<Map>());
                }
            } catch (Exception ex) {
                logger.error("visit es error:" + ex.getMessage(), ex);
                criteria.with(Merchant.NAME).like("%" + merchantName + "%");
            }
        }

        if (!empty(merchantBusinessName)) {
            try {
                Criteria esCriteria = EsUtil.allLikeConvertCriteria(new EsUtil.QueryInfo("merchant", "business_name"), merchantBusinessName, "id");
                if (esCriteria != null) {
                    logger.info("ES拼接前查询条件:" + criteria.toString());
                    criteria.withAnd(esCriteria);
                    logger.info("ES拼接后查询条件:" + criteria.toString());
                } else {
                    return new ListResult(0, new ArrayList<Map>());
                }
            } catch (Exception ex) {
                logger.error("visit es error:" + ex.getMessage(), ex);
                criteria.with(Merchant.BUSINESS_NAME).like("%" + merchantBusinessName + "%");
            }
        }

        if (!empty(vendorId)) {
            criteria.with(Merchant.VENDOR_ID).is(vendorId);
        }
        if (!empty(merchantType)) {
            criteria.with(Merchant.MERCHANT_TYPE).is(merchantType);
        }
        if (!empty(merchantAlias)) {
            try {
                Criteria esCriteria = EsUtil.allLikeConvertCriteria(new EsUtil.QueryInfo("merchant", "alias"), merchantAlias, "id");
                if (esCriteria != null) {
                    logger.info("ES拼接alias前查询条件:" + criteria.toString());
                    criteria.withAnd(esCriteria);
                    logger.info("ES拼接alis后查询条件:" + criteria.toString());
                } else {
                    return new ListResult(0, new ArrayList<Map>());
                }
            } catch (Exception ex) {
                logger.error("visit es error:" + ex.getMessage(), ex);
                criteria.with(Merchant.ALIAS).like("%" + merchantAlias + "%");
            }
        }
        if (!empty(contactPhone)) {
            criteria.with(Merchant.CONTACT_PHONE).is(contactPhone);
        }
        if (!empty(contactCellphone)) {
            criteria.with(Merchant.CONTACT_CELLPHONE).is(contactCellphone);
        }
        if (!empty(solicitorId)) {
            criteria.with(Merchant.SOLICITOR_ID).is(solicitorId);
        }
        if (!empty(ownerCellphone)) {
            criteria.with(Merchant.OWNER_CELLPHONE).is(ownerCellphone);
        }
        if (!empty(cellphone)) {
            criteria.withAnd(Criteria.or(
                    Criteria.where(Merchant.OWNER_CELLPHONE).is(cellphone),
                    Criteria.where(Merchant.CONTACT_CELLPHONE).is(cellphone)
            ));
        }
        if (!empty(solicitorName)) {
            Map request = new HashMap();
            request.put("name", solicitorName);
            ListResult result = solicitorService.findSolicitors(new PageInfo(1, 300), request);
            List<String> solicitorIds = new ArrayList<>();
            for (Map solicitor : result.getRecords()) {
                solicitorIds.add(BeanUtil.getPropString(solicitor, DaoConstants.ID));
            }
            if (solicitorIds.size() == 0) {
                return ListResult.emptyListResult();
            } else {
                criteria.with(Merchant.SOLICITOR_ID).in(solicitorIds);
            }
        }
        if (status != null) {
            criteria.with(Merchant.STATUS).is(status);
        }
        if (pageInfo.getDateStart() != null) {
            criteria.with(DaoConstants.CTIME).ge(pageInfo.getDateStart());
        }
        if (pageInfo.getDateEnd() != null) {
            criteria.with(DaoConstants.CTIME).lt(pageInfo.getDateEnd());
        }

//        long count = merchantDao.filter(criteria).count();
        if (pageInfo.getOrderBy() == null || pageInfo.getOrderBy().size() == 0) {
            pageInfo.setOrderBy(Arrays.asList(
                    new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)
            ));
        }

        Filter filter = CollectionUtils.isEmpty(projection) ? merchantDao.filter(criteria) : merchantDao.filter(criteria, projection);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> merchantsResult = CollectionUtil.iterator2list(filter.fetchAll());
        merchantsResult.forEach(item -> bankInfoBiz.resolveDistrict(item));
        return new ListResult(100000, merchantsResult);
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public ListResult findMerchantsFromSlaveDb(PageInfo pageInfo, Map queryFilter) {
        return findMerchants(pageInfo, queryFilter);
    }

    @Override
    @Transactional(value = "transactionManager")
    public Map bindMerchantBankAccount(Map merchantBankAccount) {
        replaceX(merchantBankAccount);
        bankInfoBiz.appendOpeningClearingNum(merchantBankAccount);

        String change_way = BeanUtil.getPropString(merchantBankAccount, "change_way");
        String operator = BeanUtil.getPropString(merchantBankAccount, "operator");
        String id = uuidGenerator.nextUuid();
        if (StringUtil.empty(BeanUtil.getPropString(merchantBankAccount, DaoConstants.ID))) {
            merchantBankAccount.put(DaoConstants.ID, id);
        }
        if (StringUtil.empty(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.VERIFY_STATUS))) {
            merchantBankAccount.put(MerchantBankAccount.VERIFY_STATUS, MerchantBankAccount.VERIFY_STATUS_NOT);
        }

        int defaultNullValue = -2;
        //设置身份证前面默认值
        int holderIdFrontOcrStatus = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, defaultNullValue);
        if (holderIdFrontOcrStatus == defaultNullValue) {
            String holderIdFrontPhoto = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER_ID_FRONT_PHOTO);
            holderIdFrontOcrStatus = StringUtil.empty(holderIdFrontPhoto) ? MerchantBankAccount.HOLDER_ID_STATUS_NOT_SUBMIT : MerchantBankAccount.HOLDER_ID_STATUS_WAIT;
            merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, holderIdFrontOcrStatus);
        }
        //设置身份证后面默认值
        int holderIdBackOcrStatus = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, defaultNullValue);
        if (holderIdBackOcrStatus == defaultNullValue) {
            String holderIdBackPhoto = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER_ID_BACK_PHOTO);
            holderIdBackOcrStatus = StringUtil.empty(holderIdBackPhoto) ? MerchantBankAccount.HOLDER_ID_STATUS_NOT_SUBMIT : MerchantBankAccount.HOLDER_ID_STATUS_WAIT;
            merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS, holderIdBackOcrStatus);
        }
        //设置身份证状态默认值
        int holderIdStatus = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.HOLDER_ID_STATUS, defaultNullValue);
        if (holderIdStatus == defaultNullValue) {
            //身份证类型
            if (BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.ID_TYPE) == 1) {
                if (holderIdFrontOcrStatus == MerchantBankAccount.HOLDER_ID_STATUS_NOT_SUBMIT || holderIdBackOcrStatus == MerchantBankAccount.HOLDER_ID_STATUS_NOT_SUBMIT) {
                    holderIdStatus = MerchantBankAccount.HOLDER_ID_STATUS_NOT_SUBMIT;
                } else if (holderIdFrontOcrStatus == MerchantBankAccount.HOLDER_ID_STATUS_FAIL || holderIdBackOcrStatus == MerchantBankAccount.HOLDER_ID_STATUS_FAIL) {
                    holderIdStatus = MerchantBankAccount.HOLDER_ID_STATUS_FAIL;
                } else if (holderIdFrontOcrStatus == MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS && holderIdBackOcrStatus == MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS) {
                    holderIdStatus = MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS;
                } else {
                    holderIdStatus = MerchantBankAccount.HOLDER_ID_STATUS_WAIT;
                }
            } else {
                holderIdStatus = MerchantBankAccount.HOLDER_ID_STATUS_WAIT;
            }
            merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_STATUS, holderIdStatus);
        }

        //设置银行卡状态默认值
        int bankCardStatus = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.BANK_CARD_STATUS, defaultNullValue);
        if (bankCardStatus == defaultNullValue) {
            String bankCardImage = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.BANK_CARD_IMAGE);
            bankCardStatus = StringUtil.empty(bankCardImage) ? MerchantBankAccount.BANK_CARD_STATUS_NOT_SUBMIT : MerchantBankAccount.BANK_CARD_STATUS_WAIT;
            merchantBankAccount.put(MerchantBankAccount.BANK_CARD_STATUS, bankCardStatus);
        }

        //设置身份证信息extend
        IdCardExtendUtil.getExtend(merchantBankAccount);
        merchantBankAccountWriteDao.save(merchantBankAccount);
        Criteria criteria = Criteria.where(MerchantBankAccount.MERCHANT_ID).is(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID));
        Map newMerchantBankAccount = merchantBankAccountWriteDao.filter(criteria).fetchOne();
        saveMerchantBankAccountPre(newMerchantBankAccount);
        Map merchantBankAccountLog = bankInfoBiz.getMerchantBankAccountChangeLog(null, newMerchantBankAccount);
        if (WosaiMapUtils.isNotEmpty(merchantBankAccountLog)) {
            merchantBankAccountChangeLogWriteDao.save(merchantBankAccountLog);
        }
        rmqService.writeMarketingDTS(getMerchantMarketingDTSMessage(id, "insert", null, newMerchantBankAccount, change_way, operator), 2);
        return merchantBankAccount;
    }

    private void replaceX(Map bankAccount) {
        if (WosaiMapUtils.isNotEmpty(bankAccount)) {
            Integer idType = WosaiMapUtils.getInteger(bankAccount, MerchantBankAccount.ID_TYPE);
            if (Objects.isNull(idType) || idType != 1) {
                return;
            }
            String idNumber = WosaiMapUtils.getString(bankAccount, MerchantBankAccount.IDENTITY);
            if (WosaiStringUtils.isNotEmpty(idNumber) && idNumber.contains("x")) {
                idNumber = idNumber.replace("x", "X");
                bankAccount.put(MerchantBankAccount.IDENTITY, idNumber);
            }
            if (WosaiStringUtils.isNotEmpty(idNumber)) {
                if (idNumber.startsWith("810000") || idNumber.startsWith("820000") || idNumber.startsWith("830000")) {
                    throw new CoreInvalidParameterException("证件类型与证件号不匹配");
                }
            }
        }
    }


    private void saveMerchantBankAccountPre(Map merchantBankAccount) {
        if (MapUtils.isEmpty(merchantBankAccount)) {
            return;
        }
        //1,将商户默认卡改为非默认(默认卡只有一张)
        String merchantId = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccountPre.MERCHANT_ID);
        String number = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccountPre.NUMBER);
        Criteria criteria = Criteria.where(MerchantBankAccountPre.MERCHANT_ID).is(merchantId)
                .with(MerchantBankAccountPre.DEFAULT_STATUS).is(MerchantBankAccountPre.DEFAULT_STATUS_TRUE);
        Map merchantBankAccountPre = merchantBankAccountPreReadDao.filter(criteria).fetchOne();
        if (!MapUtils.isEmpty(merchantBankAccountPre)) {
            merchantBankAccountPreWriteDao.updatePart(CollectionUtil.hashMap(
                    DaoConstants.ID, BeanUtil.getPropString(merchantBankAccountPre, DaoConstants.ID),
                    MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_FALSE
            ));
        }
        //写入银行卡 或 更新相同卡号的卡  同时把这张卡置为默认卡
        Criteria criteriaPre = Criteria.where(MerchantBankAccountPre.MERCHANT_ID).is(merchantId)
                .with(MerchantBankAccountPre.NUMBER).is(number);
        Map merchantBankAccountPreNew = merchantBankAccountPreReadDao.filter(criteriaPre).fetchOne();
        merchantBankAccount.remove(DaoConstants.ID);
        merchantBankAccount.put(MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_TRUE);
        if (MapUtils.isEmpty(merchantBankAccountPreNew)) {
            String id = uuidGenerator.nextUuid();
            merchantBankAccount.put(DaoConstants.ID, id);
            merchantBankAccount = filterParams(merchantBankAccount, MerchantBankAccountPre.merchantBankAccountPreParams);
            merchantBankAccountPreWriteDao.save(merchantBankAccount);
            return;
        }
        merchantBankAccountPreNew.putAll(merchantBankAccount);
        merchantBankAccountPreNew = filterParams(merchantBankAccountPreNew, MerchantBankAccountPre.merchantBankAccountPreParams);
        merchantBankAccountPreWriteDao.updatePart(merchantBankAccountPreNew);
    }

    private Map filterParams(Map requestMap, List<String> filterList) {
        return Maps.filterKeys(requestMap, new Predicate<String>() {
            @Override
            public boolean apply(String s) {
                return filterList.contains(s);
            }
        });
//        return Maps.filterKeys(requestMap, o -> filterList.contains(o));
    }

    @Override
    public void updateMerchantBankAccountInfo(Map merchantBankAccount) {
        String change_way = BeanUtil.getPropString(merchantBankAccount, "change_way", "").toUpperCase();
        if (merchantBankAccount != null && merchantBankAccount.containsKey("change_way")) {
            merchantBankAccount.remove("change_way");
        }
        if (merchantBankAccount != null && merchantBankAccount.containsKey("operator")) {
            merchantBankAccount.remove("operator");
        }
        Map oldMerchantBankAccount = getMerchantBankAccountByMerchantId(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID));
        if (oldMerchantBankAccount == null) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT));
        } else {
            //非app调用无论有没有字段变更去除48小时限制
            if (StringUtil.empty(change_way) || !change_way.equals("APP")) {
                removeRecordAppRebindBank(BeanUtil.getPropString(oldMerchantBankAccount, MerchantBankAccount.MERCHANT_ID));
            }
            merchantBankAccount.put(DaoConstants.ID, BeanUtil.getPropString(oldMerchantBankAccount, DaoConstants.ID));
            boolean needChangeBankAccountVerifyStatus = needChangeBankAccountVerifyStatus(oldMerchantBankAccount, merchantBankAccount);
            if (needChangeBankAccountVerifyStatus) {
                merchantBankAccount.put(MerchantBankAccount.VERIFY_STATUS, MerchantBankAccount.VERIFY_STATUS_INPROGRESS);
                //CRM修改，不修改change_time
                if (StringUtil.empty(change_way) || !change_way.equals("CRM")) {
                    merchantBankAccount.put(MerchantBankAccount.CHANGE_TIME, System.currentTimeMillis());
                }
            }
            updateBankAccountInnerMethod(merchantBankAccount);
            //app调用有字段变更增加48小时限制
            if (!StringUtil.empty(change_way) && change_way.equals("APP") && needChangeBankAccountVerifyStatus) {
                recordAppRebindBank(BeanUtil.getPropString(oldMerchantBankAccount, MerchantBankAccount.MERCHANT_ID));
            }
        }
    }

    public void syncMerchantBankAccountInfoAfterVerifiedSuccess(Map merchantBankAccount) {
        Map oldMerchantBankAccount = getMerchantBankAccountByMerchantId(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID));
        if (oldMerchantBankAccount == null) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT));
        } else {
            merchantBankAccount.put(DaoConstants.ID, BeanUtil.getPropString(oldMerchantBankAccount, DaoConstants.ID));
            //支持blob字段的增量传入
            Map inputExtra = (Map) BeanUtil.getProperty(merchantBankAccount, MerchantBankAccount.EXTRA);
            inputExtra = inputExtra == null ? CollectionUtil.hashMap() : inputExtra;
            Map extra = (Map) BeanUtil.getProperty(merchantBankAccount, MerchantBankAccount.EXTRA);
            extra = extra == null ? CollectionUtil.hashMap() : extra;
            extra.putAll(inputExtra);
            merchantBankAccount.put(MerchantBankAccount.EXTRA, extra);

            updateBankAccountInnerMethod(merchantBankAccount);
            Map newMerchantBankAccount = getMerchantBankAccountByMerchantId(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID));
            //通知身份证状态变更
            noticeAuditHolderIdStatusChange(oldMerchantBankAccount, newMerchantBankAccount, "未知", "未知", "同步商户结算信息");
        }
    }

    private boolean needChangeBankAccountVerifyStatus(Map oldBankAccountInfo, Map newBankAccountInfo) {
        for (String item : MerchantBankAccount.KEY_INFORMATION_COLUMNS) {
            // 对公账户更换法人信息不变更银行卡的状态
            if (item.equals(MerchantBankAccount.IDENTITY)) {
                if (BeanUtil.getPropInt(oldBankAccountInfo, MerchantBankAccount.TYPE) == 2
                        && BeanUtil.getPropInt(newBankAccountInfo, MerchantBankAccount.TYPE) == 2) {
                    continue;
                }
            }
            String oldVal = BeanUtil.getPropString(oldBankAccountInfo, item, "");
            String newVal = BeanUtil.getPropString(newBankAccountInfo, item);
            if ((newVal != null) && !oldVal.equals(newVal)) {
                return true;
            }
        }
        return false;
    }


    public void updateBankAccountEdgeInfo(Map merchantBankAccount) {
        String change_way = BeanUtil.getPropString(merchantBankAccount, "change_way", "").toUpperCase();
        String operator = BeanUtil.getPropString(merchantBankAccount, "operator", "");
        String remark = BeanUtil.getPropString(merchantBankAccount, "remark", "");
        if (merchantBankAccount != null && merchantBankAccount.containsKey("change_way")) {
            merchantBankAccount.remove("change_way");
        }
        if (merchantBankAccount != null && merchantBankAccount.containsKey("operator")) {
            merchantBankAccount.remove("operator");
        }
        if (merchantBankAccount != null && merchantBankAccount.containsKey("remark")) {
            merchantBankAccount.remove("remark");
        }

        String merchantId = BeanUtil.getPropString(merchantBankAccount, ConstantUtil.KEY_MERCHANT_ID);
        Map oldMerchantBankAccount = getMerchantBankAccountByMerchantId(merchantId);
        if (oldMerchantBankAccount == null) {
            throw new CoreMerchantNotBindBankAccountException(CoreException.getCodeDesc(CoreException.CODE_MERCHANT_NOT_BIND_BANK_ACCOUNT));
        } else {
            HashMap map = new HashMap();
            String id = BeanUtil.getPropString(oldMerchantBankAccount, DaoConstants.ID);
            map.put(DaoConstants.ID, id);
            for (String item : MerchantBankAccount.KEY_NOT_INFORMATION_COLUMNS) {
                if (merchantBankAccount.containsKey(item)) {
                    map.put(item, merchantBankAccount.get(item));
                }
            }
            updateBankAccountInnerMethod(map);

            //身份证状态增加真实性通知
            noticeAuditHolderIdStatusChange(oldMerchantBankAccount, getMerchantBankAccountByMerchantId(merchantId), change_way, operator, remark);
        }
    }

    /**
     * 通知真实性身份证状态变化
     *
     * @param merchantBankAccountOld
     * @param merchantBankAccountNew
     * @param platform
     * @param operator
     * @param remark
     */
    private void noticeAuditHolderIdStatusChange(final Map merchantBankAccountOld, final Map merchantBankAccountNew, final String platform, final String operator, final String remark) {
        if (platform.equals("core-business")) {
            return;
        }
        int holderIdStatusOld = BeanUtil.getPropInt(merchantBankAccountOld, MerchantBankAccount.HOLDER_ID_STATUS, -2);
        final int holderIdStatusNew = BeanUtil.getPropInt(merchantBankAccountNew, MerchantBankAccount.HOLDER_ID_STATUS, -2);
        if (holderIdStatusNew == holderIdStatusOld || holderIdStatusNew == -2) {
            return;
        }
        threadPoolExecutor.submit(new Runnable() {
            @Override
            public void run() {
                String merchantId = BeanUtil.getPropString(merchantBankAccountNew, ConstantUtil.KEY_MERCHANT_ID);
                try {
                    int status = -1;
                    if (holderIdStatusNew == MerchantBankAccount.HOLDER_ID_STATUS_SUCCESS) {
                        status = MerchantAudit.STATUS_PASSED;
                    }
                    if (holderIdStatusNew == MerchantBankAccount.HOLDER_ID_STATUS_WAIT) {
                        status = MerchantAudit.STATUS_PENDING;
                    }
                    if (holderIdStatusNew == MerchantBankAccount.HOLDER_ID_STATUS_FAIL) {
                        status = MerchantAudit.STATUS_REJECTED;
                    }
                    if (status == -1) {
                        return;
                    }
                    String mark = StringUtil.empty(remark) ? "ocr自动识别" : remark;
                    merchantAuditService.noticeStatusChange(CollectionUtil.hashMap(
                            ConstantUtil.KEY_MERCHANT_ID, merchantId,
                            MerchantAudit.SUBMITTER, operator,
                            MerchantAudit.SUBMIT_PLATFORM, platform,
                            MerchantAudit.REMARK, mark,
                            MerchantBankAccount.HOLDER_ID_STATUS, status
                    ));
                } catch (Exception e) {
                    logger.error("异步通知真实性审核身份证照片变更异常merchantId[{}],holderIdStatus[{}]", merchantId, holderIdStatusNew, e);
                }
            }
        });
    }


    public void recordAppRebindBank(String merchant_id) {
        if (!StringUtil.empty(merchant_id)) {
            try {
                redisTemplate.boundValueOps("APP_REBIND_BANK_PRE_FLAG_" + merchant_id).set("live48hour", 2, TimeUnit.DAYS);
            } catch (Exception e) {
                logger.error("recordAppRebindBank error mercahnt_id[{}] ", merchant_id, e);
            }
        }
    }

    public void removeRecordAppRebindBank(String merchant_id) {
        if (!StringUtil.empty(merchant_id)) {
            try {
                if (redisTemplate.hasKey("APP_REBIND_BANK_PRE_FLAG_" + merchant_id)) {
                    redisTemplate.delete("APP_REBIND_BANK_PRE_FLAG_" + merchant_id);
                }
            } catch (Exception e) {
                logger.error("removeRecordAppRebindBank error mercahnt_id[{}] ", merchant_id, e);
            }
        }
    }

    public boolean isAppRebindBankInTwoDay(String merchant_id) {
        if (!StringUtil.empty(merchant_id)) {
            try {
                return redisTemplate.hasKey("APP_REBIND_BANK_PRE_FLAG_" + merchant_id);
            } catch (Exception e) {
                logger.error("isAppRebindBankInTwoDay error mercahnt_id[{}] ", merchant_id, e);
            }
        }
        return false;
    }


    @Override
    public Map getMerchantBankAccountByMerchantId(String merchantId) {
        Criteria criteria = Criteria.where(MerchantBankAccount.MERCHANT_ID).is(merchantId);
        Map merchantBankAccount = merchantBankAccountReadDao.filter(criteria).fetchOne();
        if (merchantBankAccount != null) {
            merchantBankAccount.put("app_change_in_twoday", isAppRebindBankInTwoDay(merchantId));
            bankInfoBiz.appendBankBranchName(merchantBankAccount);
        }
        return merchantBankAccount;
    }

    @Override
    public MerchantBankHolderSimpleIdentityInfoResponse getMerchantBankAccountHolderIdentityInfo(BaseMerchantRequest request) {
        Criteria criteria = Criteria.where(MerchantBankAccount.MERCHANT_ID).is(request.getMerchant_id());
        Map merchantBankAccount = merchantBankAccountReadDao.filter(criteria).fetchOne();
        if (MapUtils.isEmpty(merchantBankAccount)) {
            return null;
        }
        MerchantBankHolderSimpleIdentityInfoResponse response = new MerchantBankHolderSimpleIdentityInfoResponse();
        response.setName(WosaiMapUtils.getString(merchantBankAccount, "holder"));
        response.setIdentity_type(WosaiMapUtils.getInteger(merchantBankAccount, "id_type"));
        response.setIdentity_no(WosaiMapUtils.getString(merchantBankAccount, "identity"));
        response.setIdentity_front_photo(WosaiMapUtils.getString(merchantBankAccount, "holder_id_front_photo"));
        return response;
    }

    @Override
    public Map getMerchantBankAccount(String merchantBankAccountId) {
        Map merchantBankAccount = merchantBankAccountReadDao.get(merchantBankAccountId);
        bankInfoBiz.appendBankBranchName(merchantBankAccount);
        return merchantBankAccount;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getAutoWithdrawMerchantRollListByCtime(Long beginTime, int pageSize) {
        if (beginTime == null) {
            throw new CoreInvalidParameterException("时间不可为空");
        }
        Criteria criteria = Criteria.where(Merchant.WITHDRAW_MODE).is(Merchant.WITHDRAW_MODE_AUTO).with(Merchant.STATUS).is(Merchant.STATUS_ENABLED).with(DaoConstants.CTIME).ge(beginTime);
        Filter filter = merchantDao.filter(criteria, Arrays.asList(DaoConstants.ID, DaoConstants.CTIME));
        PageInfo pageInfo = new PageInfo(0, pageSize);
        pageInfo.setOrderBy(Collections.singletonList(
                new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)
        ));
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map<String, Object>> result = CollectionUtil.iterator2list(filter.fetchAll());
        result.forEach(item -> bankInfoBiz.resolveDistrict(item));
        return result;
    }


    @Override
    public List<Map<String, Object>> getMerchantRollListByCtime(Long beginTime, Long endTime, int pageSize) {
        if (beginTime == null || endTime == null) {
            throw new CoreInvalidParameterException("时间不可为空");
        }
        PageInfo pageInfo = new PageInfo(0, pageSize);
        pageInfo.setOrderBy(Collections.singletonList(
                new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)
        ));
        Criteria criteria = Criteria.where(DaoConstants.CTIME).ge(beginTime).with(DaoConstants.CTIME).le(endTime);
        Filter filter = merchantDao.filter(criteria, Arrays.asList(DaoConstants.ID, DaoConstants.CTIME));
        List<Map<String, Object>> merchantsResult = CollectionUtil.iterator2list(filter.fetchAll());
        merchantsResult.forEach(item -> bankInfoBiz.resolveDistrict(item));
        return merchantsResult;
    }


    @Override
    public Map getMerchantDeveloperByMerchantId(String merchantId) {
        Criteria criteria = Criteria.where(MerchantDeveloper.MERCHANT_ID).is(merchantId);
        return merchantDeveloperDao.filter(criteria).fetchOne();
    }

    /**
     * 关闭支付宝v1交易,默认走支付宝v2交易
     *
     * @param merchantId
     */
    private void closeAlipay(String merchantId) {
        HashMap basic = new HashMap();
        basic.put(MerchantConfig.MERCHANT_ID, merchantId);
        basic.put(MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_CLOSED);
        basic.put(MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_CLOSED);
        basic.put(MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_CLOSED);
        basic.put(MerchantConfig.PARAMS, new HashMap<>());
        HashMap alipayV1MerchantConfig = (HashMap) basic.clone();
        alipayV1MerchantConfig.put(MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY);
        alipayV1MerchantConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        Criteria criteria = Criteria.where("merchant_id").is(merchantId);
        criteria.with(MerchantConfig.PAYWAY).is(TradeConfigService.PAYWAY_ALIPAY);
        Map<String, Object> stringObjectMap = merchantConfigDao.filter(criteria).fetchOne();
        if (CollectionUtils.isEmpty(stringObjectMap)) {
            this.merchantConfigDao.save(alipayV1MerchantConfig);
        }

    }


    @Override
    public void updateBankAccountInnerMethod(Map bankAccount) {
        replaceX(bankAccount);
        bankInfoBiz.appendOpeningClearingNum(bankAccount);

        String id = BeanUtil.getPropString(bankAccount, DaoConstants.ID);
        Map oldBankAccount = merchantBankAccountReadDao.get(id);
        //设置身份证信息extend
        IdCardExtendUtil.getExtend(bankAccount, oldBankAccount);
        merchantBankAccountWriteDao.updatePart(bankAccount);
        Map newMerchantBankAccount = merchantBankAccountWriteDao.get(id);
        saveMerchantBankAccountPre(newMerchantBankAccount);
        Map newBankAccount = merchantBankAccountReadDao.get(id);
        Map merchantBankAccountChangeLog = bankInfoBiz.getMerchantBankAccountChangeLog(oldBankAccount, newBankAccount);
        if (WosaiMapUtils.isNotEmpty(merchantBankAccountChangeLog)) {
            merchantBankAccountChangeLogWriteDao.save(merchantBankAccountChangeLog);
        }
        rmqService.writeMarketingDTS(getMerchantMarketingDTSMessage(id, "update", oldBankAccount, newBankAccount, null, null), 2);
    }

    /**
     * getBankAccountVerifyStatus接口返回值说明
     * 返回值 {"code":"字符串 | ","status":"整型 | ","opt_type":"整型 | 1 新增 2 数据变更","time":"长整型|时间戳，毫秒","message":"字符串|审核状态不通过时返回，审核不通过时拉卡拉返回的原因"}
     * code = 00 状态明确(通过或未通过), status = 2 or status = 3
     * code = 10 提交信息失败，需人工介入的特殊审核中状态 status = 1
     * code = 11 提交信息成功，审核中，查询之后仍为审核中 status = 1
     */
    @Override
    public Map getBankAccountVerifyStatus(String merchantSn) {
        if (StringUtil.empty(merchantSn)) {
            throw new CoreUnknownException("商户号不能为空");
        }
        Map merchant = getMerchantByMerchantSn(merchantSn);
        if (merchant == null) {
            throw new CoreUnknownException("商户" + merchantSn + "不存在");
        }
        Map bankAccount = getMerchantBankAccountByMerchantId(BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID));
        if (bankAccount == null) {
            return CollectionUtil.hashMap("code", "00", "status", MerchantBankAccount.VERIFY_STATUS_NOT);
        }
        int verifyStatus = BeanUtil.getPropInt(bankAccount, MerchantBankAccount.VERIFY_STATUS);
        return CollectionUtil.hashMap("code", "00", "status", verifyStatus);
    }

    private void updateMerchantAudit(Map merchant) {
        Map merchantAudit = merchantAuditService.getAuditByMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
        if (merchantAudit != null) {
            HashMap map = new HashMap();
            map.put(MerchantAudit.MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
            map.put(MerchantAudit.MERCHANT_PROVINCE, BeanUtil.getPropString(merchant, Merchant.PROVINCE));
            map.put(MerchantAudit.MERCHANT_CITY, BeanUtil.getPropString(merchant, Merchant.CITY));
            map.put(MerchantAudit.MERCHANT_DISTRICT, BeanUtil.getPropString(merchant, Merchant.DISTRICT));
            map.put(MerchantAudit.MERCHANT_STREET_ADDRESS, BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS));
            map.put(MerchantAudit.MERCHANT_OWNER_NAME, BeanUtil.getPropString(merchant, Merchant.OWNER_NAME));
            map.put(MerchantAudit.MERCHANT_OWNER_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.OWNER_CELLPHONE));
            map.put(MerchantAudit.MERCHANT_CONTACT_NAME, BeanUtil.getPropString(merchant, Merchant.CONTACT_NAME));
            map.put(MerchantAudit.MERCHANT_CONTACT_CELLPHONE, BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
            for (Object mapKey : map.keySet().toArray()) {
                if (map.get(mapKey) == null) {
                    map.remove(mapKey);
                }
            }
            if (map.size() != 0) {
                map.put(DaoConstants.ID, BeanUtil.getPropString(merchantAudit, DaoConstants.ID));
                merchantAuditService.updateMerchantAuditById(map);
            }
        }
    }

    @Override
    public ListResult findMerchantBankAccounts(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
        String type = BeanUtil.getPropString(queryFilter, "type");
        String holder = BeanUtil.getPropString(queryFilter, "holder");
        String id_type = BeanUtil.getPropString(queryFilter, "id_type");
        String identity = BeanUtil.getPropString(queryFilter, "identity");
        String tax_payer_id = BeanUtil.getPropString(queryFilter, "tax_payer_id");
        String number = BeanUtil.getPropString(queryFilter, "number");
        String verify_status = BeanUtil.getPropString(queryFilter, "verify_status");
        String bank_name = BeanUtil.getPropString(queryFilter, "bank_name");
        String branch_name = BeanUtil.getPropString(queryFilter, "branch_name");
        String city = BeanUtil.getPropString(queryFilter, "city");
        String cellphone = BeanUtil.getPropString(queryFilter, "cellphone");
        String holder_id_card_address = BeanUtil.getPropString(queryFilter, "holder_id_card_address");
        String holder_id_card_issuing_authority = BeanUtil.getPropString(queryFilter, "holder_id_card_issuing_authority");
        List<String> merchant_ids = null;
        if (queryFilter != null && queryFilter.containsKey("merchant_ids")) {
            try {
                merchant_ids = (List<String>) queryFilter.get("merchant_ids");
            } catch (Exception e) {

            }
        }

        Criteria criteria = Criteria.where(DaoConstants.ID).ne(null);

        if (!StringUtil.empty(merchantId)) {
            criteria.with(MerchantBankAccount.MERCHANT_ID).is(merchantId);
        }
        if (merchant_ids != null && merchant_ids.size() > 0) {
            criteria.with(MerchantBankAccount.MERCHANT_ID).in(merchant_ids);
        }
        if (!StringUtil.empty(type)) {
            criteria.with(MerchantBankAccount.TYPE).is(type);
        }
        if (!StringUtil.empty(holder)) {
            criteria.with(MerchantBankAccount.HOLDER).is(holder);
        }
        if (!StringUtil.empty(id_type)) {
            criteria.with(MerchantBankAccount.ID_TYPE).is(id_type);
        }
        if (!StringUtil.empty(identity)) {
            criteria.with(MerchantBankAccount.IDENTITY).is(identity);
        }
        if (!StringUtil.empty(tax_payer_id)) {
            criteria.with(MerchantBankAccount.TAX_PAYER_ID).like("%" + tax_payer_id + "%");
        }
        if (!StringUtil.empty(number)) {
            criteria.with(MerchantBankAccount.NUMBER).is(number);
        }
        if (!StringUtil.empty(verify_status)) {
            criteria.with(MerchantBankAccount.VERIFY_STATUS).is(verify_status);
        }
        if (!StringUtil.empty(bank_name)) {
            criteria.with(MerchantBankAccount.BANK_NAME).like("%" + bank_name + "%");
        }
        if (!StringUtil.empty(branch_name)) {
            criteria.with(MerchantBankAccount.BRANCH_NAME).like("%" + branch_name + "%");
        }
        if (!StringUtil.empty(city)) {
            criteria.with(MerchantBankAccount.CITY).like("%" + city + "%");
        }
        if (!StringUtil.empty(cellphone)) {
            criteria.with(MerchantBankAccount.CELLPHONE).is(cellphone);
        }
        if (!StringUtil.empty(holder_id_card_address)) {
            criteria.with(MerchantBankAccount.HOLDER_ID_CARD_ADDRESS).is(holder_id_card_address);
        }
        if (!StringUtil.empty(holder_id_card_issuing_authority)) {
            criteria.with(MerchantBankAccount.HOLDER_ID_CARD_ISSUING_AUTHORITY).is(holder_id_card_issuing_authority);
        }
        if (pageInfo.getDateStart() != null) {
            criteria.with(DaoConstants.CTIME).ge(pageInfo.getDateStart());
        }
        if (pageInfo.getDateEnd() != null) {
            criteria.with(DaoConstants.CTIME).lt(pageInfo.getDateEnd());
        }
        long count = merchantBankAccountReadDao.filter(criteria).count();
        if (pageInfo.getOrderBy() == null || pageInfo.getOrderBy().size() == 0) {
            pageInfo.setOrderBy(Arrays.asList(
                    new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)
            ));
        }
        Filter filter = merchantBankAccountReadDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> merchantsResult = CollectionUtil.iterator2list(filter.fetchAll());
        for (Map map : merchantsResult) {
            bankInfoBiz.appendBankBranchName(map);
        }
        return new ListResult(count, merchantsResult);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> updateMerchantWithdrawModeById(Map<String, Object> update, Map<String, Object> operatorInfo) {
        Map<String, Object> merchantOld = merchantDao.filter(Criteria.where(DaoConstants.ID).is(BeanUtil.getPropString(update, DaoConstants.ID))).fetchOne();
        if (BeanUtil.getPropString(merchantOld, Merchant.WITHDRAW_MODE).equals(BeanUtil.getPropString(update, Merchant.WITHDRAW_MODE))) {
            return merchantOld;
        }
        int withdrawModel = BeanUtil.getPropInt(update, Merchant.WITHDRAW_MODE);
        Map<String, Object> merchantNew = updateMerchant(new HashMap() {{
            put(DaoConstants.ID, BeanUtil.getPropString(update, DaoConstants.ID));
            put(Merchant.WITHDRAW_MODE, withdrawModel);
        }});
        rmqService.writeWithdrawModeChange(merchantNew, operatorInfo);
        return merchantNew;
    }

    @Override
    public Map<String, Object> updateMerchantWithdrawModeByIdAndLog(Map<String, Object> update, Map<String, Object> operatorInfo, OpLogCreateRequest opLogCreateRequest) {
        Map<String, Object> before = merchantDao.filter(Criteria.where(DaoConstants.ID).is(BeanUtil.getPropString(update, DaoConstants.ID))).fetchOne();
        Map<String, Object> result = updateMerchantWithdrawModeById(update, operatorInfo);
        Map<String, Object> after = merchantDao.filter(Criteria.where(DaoConstants.ID).is(BeanUtil.getPropString(update, DaoConstants.ID))).fetchOne();
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(after, DaoConstants.ID), null, OpLog.MERCHANT_TEMPLATE_CODE, OpLog.MERCHANT_TABLE_NAME, OpLog.FIXED_MERCHANT_KEY_LIST, OpLog.MERCHANT_CHANGE_KEY_LIST, OpLog.MERCHANT_DESC_MAP, before, after);
        return result;
    }

    @Override
    public void tmpUpdateMerchantWithdrawModeById(Map<String, Object> update, Map<String, Object> operatorInfo) {
        Map<String, Object> merchantOld = merchantDao.filter(Criteria.where(DaoConstants.ID).is(BeanUtil.getPropString(update, DaoConstants.ID)), Arrays.asList(Merchant.WITHDRAW_MODE)).fetchOne();
        if (BeanUtil.getPropString(merchantOld, Merchant.WITHDRAW_MODE).equals(BeanUtil.getPropString(update, Merchant.WITHDRAW_MODE))) {
            return;
        }
        int withdrawModel = BeanUtil.getPropInt(update, Merchant.WITHDRAW_MODE);
        merchantDao.updatePart(com.wosai.pantheon.util.MapUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(update, DaoConstants.ID),
                Merchant.WITHDRAW_MODE, withdrawModel
        ));
    }

    @Override
    public List<Map<String, Object>> getChangeMerchant(long beginMtime, long endMtime) {
        Filter filter = merchantDao.filter(Criteria.where(ConstantUtil.KEY_MTIME).ge(beginMtime)
                .with(ConstantUtil.KEY_MTIME).le(endMtime));
        List<Map<String, Object>> result = CollectionUtil.iterator2list(filter.fetchAll());
        result.forEach(item -> bankInfoBiz.resolveDistrict(item));
        return result;
    }

    @Override
    public int deleteMerchantByMerchantId(String merchantId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map<String, Object> merchant = merchantDao.filter(criteria).fetchOne();
        merchantDao.delete(merchantId);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_MERCHANT, merchant);
        return 1;
    }

    @Override
    public List<String> getAutoWithdrawMerchantsByMerchantIds(List<String> merchantIds) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            throw new CoreInvalidParameterException("商户id列表不可为空");
        }
        Criteria criteria = Criteria.where(Merchant.WITHDRAW_MODE).is(Merchant.WITHDRAW_MODE_AUTO).with(DaoConstants.ID).in(merchantIds);
        Filter filter = merchantDao.filter(criteria, Arrays.asList(DaoConstants.ID));
        List<Map<String, Object>> list = CollectionUtil.iterator2list(filter.fetchAll());
        return list.stream().map(item -> BeanUtil.getPropString(item, DaoConstants.ID)).distinct().collect(Collectors.toList());
    }

    @Override
    public void clearRedisDistrict(String code) {
        redisTemplate.delete("bank-info-districts_" + code);
    }

    @Override
    public WapMiniChangeVerifyResponse changeToMiniVerify(WapMiniChangeVerifyRequest request) {
        boolean pass = true;
        StringBuffer message = new StringBuffer();
        Map<String, Object> merchantConfig = merchantConfigDao.filter(Criteria.where(MerchantConfig.MERCHANT_ID).is(request.getMerchantId()).with(MerchantConfig.PAYWAY).is(TradeConfigService.PAYWAY_WEIXIN)).fetchOne();
        int provider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER);
        if (provider == TradeConfigService.PROVIDER_CIBSHBANK || provider == TradeConfigService.PROVIDER_CIBHZBANK) {
            message.append("商户属于兴业支付通道 ");
            pass = false;
        }
        if (TradeConfigService.PROVIDER_CMB == provider) {
            message.append("商户属于招行支付通道 ");
            pass = false;
        }
        int miniStatus = BeanUtil.getPropInt(merchantConfig, MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED);
        if (miniStatus != MerchantConfig.STATUS_OPENED) {
            message.append("商户未开启小程序支付权限 ");
            pass = false;
        }
        String miniFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.MINI_FEE_RATE);
        if (StringUtil.empty(miniFeeRate)) {
            message.append("商户未配置小程序费率 ");
            pass = false;
        }
        String wapFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.WAP_FEE_RATE);
        if (!Objects.equals(miniFeeRate, wapFeeRate)) {
            message.append("商户小程序与门店码费率不相同 ");
            pass = false;
        }
        boolean miniFormal = BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.MINI_FORMAL);
        if (miniFormal) {
            message.append("商户小程序为直连 ");
            pass = false;
        }
        boolean wapFormal = BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.WAP_FORMAL);
        if (wapFormal && !miniFormal) {
            message.append("商户门店码为直连, 小程序为间连 ");
            pass = false;
        }
        return new WapMiniChangeVerifyResponse(pass, message.toString());
    }

    @Override
    public WapMiniChangeVerifyResponse changeToWapVerify(WapMiniChangeVerifyRequest request) {
        //任何情况都可切回wap
        return new WapMiniChangeVerifyResponse(true);
    }
}
