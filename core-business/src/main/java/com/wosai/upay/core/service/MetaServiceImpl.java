package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.bean.model.MetaBizModel;
import com.wosai.upay.core.bean.model.MetaPayPath;
import com.wosai.upay.core.bean.model.MetaPaySource;
import com.wosai.upay.core.bean.request.MetaBizModelCreateRequest;
import com.wosai.upay.core.bean.request.MetaPayPathCreateRequest;
import com.wosai.upay.core.bean.request.MetaPaySourceCreateRequest;
import com.wosai.upay.core.repository.DataRepository;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by wujianwei on 2023/12/14.
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class MetaServiceImpl implements MetaService{
    private static final Logger logger = LoggerFactory.getLogger(MetaService.class);

    private DataRepository dataRepository;

    @Autowired
    public MetaServiceImpl(@Autowired DataRepository dataRepository) {
        this.dataRepository = dataRepository;
    }

    @Override
    public void createMetaBizModel(MetaBizModelCreateRequest request) {
        Map<String,Object> model = new LinkedHashMap<>();
        model.put(DaoConstants.ID, request.getId());
        model.put(MetaBizModel.KEY_NAME, request.getName());
        model.put(MetaBizModel.KEY_REMARK, request.getRemark());
        model.put(DaoConstants.CTIME, System.currentTimeMillis());
        dataRepository.getMetaBizModelDao().save(model);
    }

    @Override
    public void createMetaPayPath(MetaPayPathCreateRequest request) {
        Map<String,Object> model = new LinkedHashMap<>();
        model.put(DaoConstants.ID, request.getId());
        model.put(MetaPayPath.KEY_NAME, request.getName());
        model.put(MetaPayPath.KEY_REMARK, request.getRemark());
        model.put(DaoConstants.CTIME, System.currentTimeMillis());
        dataRepository.getMetaPayPathDao().save(model);
    }

    @Override
    public void createMetaPaySource(MetaPaySourceCreateRequest request) {
        Map<String, Object> model = new LinkedHashMap<>();
        model.put(DaoConstants.ID, request.getId());
        model.put(MetaPaySource.KEY_NAME, request.getName());
        model.put(MetaPaySource.KEY_PAYWAY, request.getPayway());
        model.put(MetaPaySource.KEY_REMARK, request.getRemark());
        model.put(DaoConstants.CTIME, System.currentTimeMillis());
        dataRepository.getMetaPaySourceDao().save(model);
    }

    @Override
    public List<MetaBizModel> getAllMetaBizModel() {
        List<Map<String, Object>> records = CollectionUtil.iterator2list(dataRepository.getMetaBizModelDao().filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
        return records.stream().map(record -> {
            MetaBizModel bizModel = new MetaBizModel();
            bizModel.setId(MapUtil.getString(record, DaoConstants.ID));
            bizModel.setName(MapUtil.getString(record, MetaBizModel.KEY_NAME));
            bizModel.setRemark(MapUtil.getString(record, MetaBizModel.KEY_REMARK));
            return bizModel;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MetaPayPath> getAllMetaPayPath() {
        List<Map<String, Object>> records = CollectionUtil.iterator2list(dataRepository.getMetaPayPathDao().filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
        return records.stream().map(record -> {
            MetaPayPath payPath = new MetaPayPath();
            payPath.setId(MapUtil.getString(record, DaoConstants.ID));
            payPath.setName(MapUtil.getString(record, MetaPayPath.KEY_NAME));
            payPath.setRemark(MapUtil.getString(record, MetaPayPath.KEY_REMARK));
            return payPath;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MetaPaySource> getAllMetaPaySource() {
        List<Map<String, Object>> records = CollectionUtil.iterator2list(dataRepository.getMetaPaySourceDao().filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
        return records.stream().map(record -> {
            MetaPaySource paySource = new MetaPaySource();
            paySource.setId(MapUtil.getString(record, DaoConstants.ID));
            paySource.setName(MapUtil.getString(record, MetaPaySource.KEY_NAME));
            paySource.setPayway(MapUtil.getInteger(record, MetaPaySource.KEY_PAYWAY));
            paySource.setRemark(MapUtil.getString(record, MetaPaySource.KEY_REMARK));
            return paySource;
        }).collect(Collectors.toList());
    }

}
