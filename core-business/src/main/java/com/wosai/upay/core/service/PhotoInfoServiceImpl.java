package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;


import java.util.*;

/**
 * @Author: lishuangqiang
 * @Date: 2020-08-24
 * @Description:
 */

@Service
@AutoJsonRpcServiceImpl
public class PhotoInfoServiceImpl implements PhotoInfoService {
    @Autowired
    Dao<Map<String, Object>> photoInfoDao;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public int createPhotoinfo(Map photoinfo) {
        photoInfoDao.save(photoinfo);
        return 1;
    }

    @Override
    public int updatePhotoinfo(Map photoinfo) {
        photoInfoDao.updatePart(photoinfo);
        return 1;
    }

    @Override
    public int deletePhotoinfo(String photoinfoId) {
        photoInfoDao.delete(photoinfoId);
        return 1;
    }

    @Override
    public int deletePhotoInfos(List<String> photoInfoIds) {
        if (photoInfoIds.size() > 100) {
            throw new CoreInvalidParameterException("最多支持删除100张照片");
        }
        StringJoiner joiner = new StringJoiner(",");
        for (String id : photoInfoIds) {
            if (WosaiStringUtils.isNotEmpty(id)) {
                joiner.add("'" + id + "'");
            }
        }
        return jdbcTemplate.update("delete from photo_info where id in (" + joiner.toString() + ")");
    }


    @Override
    public Map findPhotoinfo(String photoinfoId, String devCode) {
        Criteria criteria = Criteria.where("id").is(photoinfoId);
        if (WosaiStringUtils.isNotEmpty(devCode)) {
            criteria.with("dev_code").is(devCode);
        }
        return photoInfoDao.filter(criteria).fetchOne();
    }

    @Override
    public Map<String, Map> findPhotoinfoBatch(List<String> photoInfoIds) {
        if (WosaiCollectionUtils.isEmpty(photoInfoIds)) {
            return Collections.emptyMap();
        }

        Criteria criteria = Criteria.where(DaoConstants.ID).in(photoInfoIds);
        List<Map<String, Object>> photoList = CollectionUtil.iterator2list(photoInfoDao.filter(criteria).fetchAll());

        if (WosaiCollectionUtils.isEmpty(photoList)) {
            return Collections.emptyMap();
        }
        Map<String, Map> result = new HashMap<>();
        for (Map<String, Object> photoInfo : photoList) {
            String id = (String) photoInfo.get(DaoConstants.ID);
            result.put(id, photoInfo);
        }
        return result;
    }

}
