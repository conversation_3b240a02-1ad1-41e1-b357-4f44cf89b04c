package com.wosai.upay.core.service;

import java.io.IOException;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.codec.Charsets;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;

@Service
@AutoJsonRpcServiceImpl
public class PreRiskServiceImpl implements PreRiskService{
    private static final Logger logger = LoggerFactory.getLogger(PreRiskServiceImpl.class);

    private static String RISK_TERMINAL_CHECKIN_FORMAT = "{\"jsonrpc\":\"2.0\",\"id\":0,\"method\":\"checkOrder\",\"params\":{\"merchant\":{\"id\":\"%s\"},\"position\":{\"ip\":\"%s\"},\"platform\":\"terminal_checkin\",\"order\":{\"clientSn\":\"%s\"}}}";
    private static String RISK_TRADE_FORMAT = "{\"jsonrpc\":\"2.0\",\"id\":0,\"method\":\"checkOrder\",\"params\":{\"merchant\":{\"id\":\"%s\"},\"position\":{\"ip\":\"%s\"},\"platform\":\"%s\",\"order\":{\"clientSn\":\"%s\",\"payway\":%s,\"subPayway\":%s}}}";
    
    @Autowired
    SupportService supportService;
    @Autowired
    CacheService cacheService;
    @Autowired
    DataRepository dataRepository;
    
    @Value("${wosai-preorder-risk.service}")
    String preOrderRisk;
    static CloseableHttpClient httpClient;
    
    static {
        ConnectionConfig connConfig = ConnectionConfig.custom()
                .setCharset(Charsets.UTF_8)
                .build();
        RequestConfig reqConfig = RequestConfig.custom()
                .setConnectTimeout(500)
                .setSocketTimeout(500)
                .setConnectionRequestTimeout(500)
                .build();

        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        cm.setDefaultConnectionConfig(connConfig);
        cm.setDefaultMaxPerRoute(8);

        httpClient = HttpClients.custom().setConnectionManager(cm).setDefaultRequestConfig(reqConfig).build();
    }
    
    @Override
    public boolean checkinCheck(Map info) {
        String storeSn = MapUtil.getString(info, WOSAI_STORE_ID);
        String terminalSn = MapUtil.getString(info, TERMINAL_SN);
        String merchantId = null;
        String merchantSn = null;

        if(!StringUtil.isEmpty(terminalSn)) {
            Map terminal = cacheService.getTerminalMinimalInfo(null, terminalSn);
            merchantId = MapUtil.getString(terminal, Terminal.MERCHANT_ID);
            merchantSn = MapUtil.getString(terminal, TransactionParam.MERCHANT_SN);
        }else {
            Map store = cacheService.getStoreMinimalInfo(null, storeSn);
            merchantId = MapUtil.getString(store, Terminal.MERCHANT_ID);
            merchantSn = MapUtil.getString(store, TransactionParam.MERCHANT_SN);
        }
        if(StringUtil.isEmpty(merchantId)) {
            logger.warn("get merchant fail, terminal_sn: {}, store_sn: {}", terminalSn, storeSn);
            return false;
        }
        if(ApolloConfigurationCenterUtil.inWhitelist(merchantSn)) {
            return false;
        }
        // 非直连商户校验客户端ip
        return remotePreOrderRisk(merchantId,  MapUtil.getString(info, IP), System.currentTimeMillis() + "", null, null, null);
    }
    
    @Override
    public boolean tradeCheck(Map info) {
        String storeSn = MapUtil.getString(info, WOSAI_STORE_ID);
        String terminalSn = MapUtil.getString(info, TERMINAL_SN);
        String service = MapUtil.getString(info, SERVICE);
        String merchantId = null;
        String merchantSn = null;
        
        if(!StringUtil.isEmpty(terminalSn)) {
            Map terminal = cacheService.getTerminalMinimalInfo(null, terminalSn);
            merchantId = MapUtil.getString(terminal, Terminal.MERCHANT_ID);
            merchantSn = MapUtil.getString(terminal, TransactionParam.MERCHANT_SN);
        }else {
            Map store = cacheService.getStoreMinimalInfo(null, storeSn);
            merchantId = MapUtil.getString(store, Terminal.MERCHANT_ID);
            merchantSn = MapUtil.getString(store, TransactionParam.MERCHANT_SN);
        }
        if(StringUtil.isEmpty(merchantId)) {
            logger.warn("get merchant fail, terminal_sn: {}, store_sn: {}", terminalSn, storeSn);
            return false;
        }
        if(ApolloConfigurationCenterUtil.inWhitelist(merchantSn)) {
            return false;
        }
        String dynamicId = MapUtil.getString(info, DYNAMIC_ID);
        Integer payway =  MapUtil.getIntValue(info, PAYWAY);
        int subPayway = MapUtil.getIntValue(info, SUB_PAYWAY, 1);
        // 根据条码反查支付模式
        if(!StringUtil.isEmpty(dynamicId)) {
            payway = UpayUtil.guessPayway(dynamicId);
            if(payway == null) {
                return false;
            }
        }
        String ip = MapUtil.getString(info, IP);
        String clientSn = MapUtil.getString(info, CLIENT_SN, System.currentTimeMillis() + "");
        return remotePreOrderRisk(merchantId, ip, clientSn, service, payway, subPayway);
    }
    
    
    private boolean remotePreOrderRisk(String merchantId, String ip, String clientSn, String service, Integer payway, Integer subpayway) {
        String rpcRequest = null;
        if(null == payway) {
            rpcRequest = String.format(RISK_TERMINAL_CHECKIN_FORMAT, merchantId, ip, clientSn);
        }else {
            rpcRequest = String.format(RISK_TRADE_FORMAT, merchantId, ip, service, clientSn, payway, subpayway);
        }
        HttpPost httpPost = new HttpPost(preOrderRisk + "rpc/risk");
        httpPost.setEntity(new StringEntity(rpcRequest, ContentType.APPLICATION_JSON));
        try {
            logger.debug("risk request: {}, body: {}", httpPost.getURI().toString(), rpcRequest);
            HttpResponse response = httpClient.execute(httpPost);
            Map<String, Object> res = new ObjectMapper().readValue(response.getEntity().getContent(), Map.class);
            logger.debug("risk response {}", res);
            Map<String, Object> jsonResult = MapUtil.getMap(res, "result");
            return MapUtil.getBooleanValue(jsonResult, "hasRisk", false);
        } catch (IOException e) {
            logger.debug("get ioex for risk: {}", e.getMessage());
        }
        return false;
    }
    
    static class UpayUtil{
        private static final Pattern ALIPAY_PATTERN = Pattern.compile("(2[5-9]|30)\\d{14,22}"); //25-30开头, 总共16-24位
        private static final Pattern WEIXIN_PATTERN = Pattern.compile("1[0-5]\\d{14,18}");
        private static final Pattern WEIXIN_HK_PATTERN = Pattern.compile("160\\d{15}");
        private static final Pattern LKLWALLET_PATTERN = Pattern.compile("(1|3)6\\d{16}");//拉卡拉钱包,条码规则: 16、36打头， 18位数字
        private static final Pattern LKL_UNIONPAY_PATTERN = Pattern.compile("62\\d{16,20}");//云闪付银联二维码, 官方条码规则: 19位十进制数组,62开头，第3位区分借贷记计价，其中1-4位借记，5-8为贷记, 为了兼容以后的升级,我们定义规则为：62开头，18-22位十进制数字
        private static final Pattern BESTPAY_PATTERN = Pattern.compile("51\\d{16}");//翼支付,条码规则:51开头，共18位
        private static final Pattern CMCCPAY_PATTERN = Pattern.compile("81\\d{16}");//和支付,条码规则:81开头，共18位
        private static final Pattern GIFTCARD_PATTERN = Pattern.compile("4378\\d{16}"); //礼品卡,条码规则:4378开头,共20位
        private static final Pattern SODEXO_PATTERN = Pattern.compile("77\\w{16}");//索迪斯,条码规则:77开头，共18位

        public static final int PAYWAY_ALIPAY = 1;
        public static final int PAYWAY_ALIPAY2 = 2;
        public static final int PAYWAY_WEIXIN = 3;
        public static final int PAYWAY_BAIFUBAO = 4;
        public static final int PAYWAY_JDWALLET = 5;
        public static final int PAYWAY_QQWALLET = 6;
        public static final int PAYWAY_APPLEPAY = 7;
        public static final int PAYWAY_LKLWALLET = 8;//拉卡拉钱包
        public static final int PAYWAY_CMCC = 9;// 和支付
        public static final int PAYWAY_UNIONPAY = 17;//银联云闪付
        public static final int PAYWAY_BESTPAY = 18;// 翼支付
        public static final int PAYWAY_WEIXIN_HK = 19;// 微信香港本地支付
        public static final int PAYWAY_ALIPAY_INTL = 20;// 支付宝国际版支付
        public static final int PAYWAY_BANKCARD = 21;// 银行卡
        public static final int PAYWAY_SODEXO = 22;// 索迪斯
        public static final int PAYWAY_GIFT_CARD = 101;//礼品卡支付方式

        private static Integer guessPayway(String dynamicId) {
            Integer payWay = null;

            if(ALIPAY_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_ALIPAY;
            }else if(WEIXIN_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_WEIXIN;
            }else if(WEIXIN_HK_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_WEIXIN_HK;
            }else if(LKLWALLET_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_LKLWALLET;
            }else if(LKL_UNIONPAY_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_UNIONPAY;
            }else if(BESTPAY_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_BESTPAY;
            }else if(CMCCPAY_PATTERN.matcher(dynamicId).matches()) {
                payWay = PAYWAY_CMCC;
            }else if(GIFTCARD_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_GIFT_CARD;
            }else if(SODEXO_PATTERN.matcher(dynamicId).matches()){
                payWay = PAYWAY_SODEXO;
            }

            return payWay;
        }

        
        /**
         * 查看是否正式, 默认是二清
         * @param existsTransaction
         * @return
         */
        public static  boolean isFormal(Map<String, Object> allParams) {
            for(String key: allParams.keySet()){
                Object value = allParams.get(key);
                if(value instanceof  Map && key.contains("_trade_params")){
                    return  !BeanUtil.getPropBoolean(value, TransactionParam.LIQUIDATION_NEXT_DAY, true);
                }
            }
            return false;
        }
    }
}
