package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> Date: 2019-07-05 Time: 16:22
 */
@Service
@AutoJsonRpcServiceImpl
public class PublicTradeConfigServiceImpl implements PublicTradeConfigService {

    @Autowired
    private TradeConfigService tradeConfigService;


    @Override
    public void updateTerminalConfigStatus(Map terminalConfig) {
        tradeConfigService.updateTerminalConfigStatus(terminalConfig);
    }
}
