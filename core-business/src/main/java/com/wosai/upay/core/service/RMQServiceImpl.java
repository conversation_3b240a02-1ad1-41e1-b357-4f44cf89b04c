package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.merchant.config.MerchantBankAccountChangeEvent;
import com.wosai.databus.event.merchant.config.MerchantBankAccountVerifyEvent;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.redis.constant.CommonConstant;
import com.wosai.redis.model.kafka.KafkaRedisChange;
import com.wosai.upay.core.constant.BusinessLogConstant;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantAudit;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.kafka.*;
import com.wosai.upay.core.model.kafka.storeExt.Digital;
import com.wosai.upay.core.model.kafka.storeExt.PhotoAvro;
import com.wosai.upay.core.model.kafka.storeExt.StoreExtAvro;
import com.wosai.upay.core.util.Jackson2PersistenceHelperUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.wosai.data.dao.DaoConstants.CTIME;
import static com.wosai.data.dao.DaoConstants.ID;

/**
 * <AUTHOR>
 */
@Service
@AutoJsonRpcServiceImpl
public class RMQServiceImpl implements RMQService {

    @Autowired
    @Qualifier("kafkaProducer")
    KafkaProducer kafkaProducer;
    @Autowired
    @Qualifier("aLiKafkaProducer")
    KafkaProducer aLiKafkaProducer;
    @Autowired
    private PhotoInfoService photoInfoService;

    private static final String KAFKA_MERCHANT_AUDIT_STATUS_CHANGE = "events.core-business.merchant.audit.status.change";
    private static final String KAFKA_TERMINAL_STATUS_ACTIVE = "events.core-business.terminal.status.active";
    private static final String KAFKA_NEW_MERCHANT_CREATED = "events.core-business.merchant.created";
    private static final String KAFKA_NEW_STORE_CREATED = "events.core-business.store.created";
    private static final String KAFKA_WITHDRAW_MODE_CHANGE = "events.core-business.withdrawMode.change";
    private static final String KAFKA_UPDATE_BUSINESS_LICENSE = "events.core-business.businessLicense.update";
    private static final String KAFKA_MERCHANT_BANK_ACCOUNT_CHANGE = "events_PAY_core-business_merchant_bank_account_change";
    private static final String KAFKA_MERCHANT_BANK_ACCOUNT_VERIFY = "events_PAY_core-business_merchant_bank_account_verify";
    private static final String KAFKA_STORE_EXT_CHANGE = "events_CUA_store_ext_change";


    private static Logger logger = LoggerFactory.getLogger(RMQServiceImpl.class);


    /**
     * 20个线程的池子，用于延迟发送队列消息，消息发送成功与否不确定.
     */
    private ExecutorService delaySendPool = Executors.newFixedThreadPool(20);
    private ExecutorService kafkaSendPool = Executors.newFixedThreadPool(15);


    @Override
    public void writeMarketingDTS(Map message) {
        try {
            AvroEventEntry event = new AvroEventEntry(System.currentTimeMillis(), System.currentTimeMillis(), ByteBuffer.wrap(Jackson2PersistenceHelperUtils.toJsonBytes(new MerchantBankAccountChangeEvent(message))));
            this.sendToKafka(KAFKA_MERCHANT_BANK_ACCOUNT_CHANGE, event);
        } catch (Throwable e) {
            logger.error("send kafka error writeMarketingDTS, {}", message, e);
        }
    }

    @Override
    public void writeMarketingDTS(final Map message, final int delay) {
        delaySendPool.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(delay * 1000l);
                    writeMarketingDTS(message);
                } catch (InterruptedException e) {
                    logger.error("Thread Delay WriteMarketing DTS send error.", e);
                }
            }
        });
    }

    @Override
    public void writeMerchantBankAccountVerify(Map message) {
        try {
            AvroEventEntry event = new AvroEventEntry(System.currentTimeMillis(), System.currentTimeMillis(), ByteBuffer.wrap(Jackson2PersistenceHelperUtils.toJsonBytes(new MerchantBankAccountVerifyEvent(message))));
            this.sendToKafka(KAFKA_MERCHANT_BANK_ACCOUNT_VERIFY, event);
        } catch (Throwable e) {
            logger.error("send kafka error writeMerchantBankAccountVerify, {}", message, e);
        }

    }

    @Override
    public void writeMerchantAuditStatusChange(final Map message) {
        delaySendPool.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(10 * 1000l);
                    writeMerchantAuditStatus(message);
                } catch (InterruptedException e) {
                    logger.error("msg merchant.audit.status.change send error.", e);
                }
            }
        });
    }

    private void writeMerchantAuditStatus(Map message) {

        try {
            this.sendToKafka(KAFKA_MERCHANT_AUDIT_STATUS_CHANGE, this.buildKafkaMerchantAudit(message));
        } catch (Exception ex) {
            logger.error("sendToKafka error:{}", KAFKA_MERCHANT_AUDIT_STATUS_CHANGE, ex);
        }
    }

    @Override
    public void writeTerminalStatusChange(Map message) {

    }

    @Override
    public void writeNewMerchantCreated(Map message) {
        try {
            this.sendToKafka(KAFKA_NEW_MERCHANT_CREATED, this.buildKafkaMerchant(message));
        } catch (Exception ex) {
            logger.error("sendToKafka error:{}", KAFKA_NEW_MERCHANT_CREATED, ex);
        }
    }

    @Override
    public void writeNewStoreCreated(Map message) {

        try {
            this.sendToKafka(KAFKA_NEW_STORE_CREATED, this.buildKafkaStore(message));
        } catch (Exception ex) {
            logger.error("sendToKafka error:{}", KAFKA_NEW_STORE_CREATED, ex);
        }
    }

    @Override
    public void writeTerminalActive(Map message) {
        try {
            this.sendToKafka(KAFKA_TERMINAL_STATUS_ACTIVE, this.buildKafkaTerminal(message));
        } catch (Exception ex) {
            logger.error("sendToKafka error:{}", KAFKA_TERMINAL_STATUS_ACTIVE, ex);
        }
    }

    @Override
    public void writeWithdrawModeChange(Map message, Map<String, Object> operatorInfo) {
        try {
            this.sendToKafka(KAFKA_WITHDRAW_MODE_CHANGE, this.buildKafkaWithdrawModeChange(message, operatorInfo));
        } catch (Exception ex) {
            logger.error("sendToKafka writeWithdrawModeChange message:{},operatorInfo:{}", message, operatorInfo);
            logger.error("sendToKafka error:{},ex:{}", KAFKA_WITHDRAW_MODE_CHANGE, ex);
        }
    }

    @Override
    public void writeRedisChange(Map message) {
        String region = getRegion();
        //只有杭州机房才写入
        if (!CommonConstant.REGION_HANG_ZHOU.equals(region)) {
            return;
        }
        try {
            this.sendToKafka(CommonConstant.KAFKA_REDIS_CHANGE_TOPIC, buildKafkaRedisChange(message));
        } catch (Exception ex) {
            logger.error("sendToKafka writeRedisChange message:{}", message);
            logger.error("sendToKafka error:{},ex:{}", CommonConstant.KAFKA_REDIS_CHANGE_TOPIC, ex);
        }
    }

    /**
     * 更新营业执照
     *
     * @param message
     */
    @Override
    public void writeUpdateBusinessLicense(Map message) {
        try {
            this.sendToKafka(KAFKA_UPDATE_BUSINESS_LICENSE, buildKafkaMerchantUpdateBusinessLicense(message));
        } catch (Exception ex) {
            logger.error("sendToKafka writeRedisChange message:{}", message);
            logger.error("sendToKafka error:{},ex:{}", CommonConstant.KAFKA_REDIS_CHANGE_TOPIC, ex);
        }
    }

    @Override
    public void writeUpdateStoreExt(Map origin, Map updated) {
        try {
            this.sendToAliKafka(KAFKA_STORE_EXT_CHANGE, buildUpdateStoreExt(origin, updated));
        } catch (Exception e) {
            logger.error("sendToKafka writeUpdateStoreExt error origin:{} ,updeat: {}", origin, updated, e);
        }
    }

    private void sendToKafka(final String topic, final Object message) {
        kafkaSendPool.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    ProducerRecord record = new ProducerRecord<>(topic, null, message);
                    kafkaProducer.send(record, new Callback() {
                        @Override
                        public void onCompletion(RecordMetadata recordMetadata, Exception e) {
                            if (e != null) {
                                logger.error("sendToKafka:{} error:{}", topic, message, e);
                            } else {
                                logger.info("sendToKafka success:{} {}", topic, message);
                            }
                        }
                    });
                } catch (Exception ex) {
                    logger.error("sendToKafka:{} error:{}", topic, message, ex);
                }
            }
        });
    }

    private void sendToAliKafka(final String topic, final Object message) {
        kafkaSendPool.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    ProducerRecord record = new ProducerRecord<>(topic, null, message);
                    aLiKafkaProducer.send(record, new Callback() {
                        @Override
                        public void onCompletion(RecordMetadata recordMetadata, Exception e) {
                            if (e != null) {
                                logger.error("sendToAliKafka:{} error:{}", topic, message, e);
                            } else {
                                logger.info("sendToAliKafka success:{} {}", topic, message);
                            }
                        }
                    });
                } catch (Exception ex) {
                    logger.error("sendToAliKafka:{} error:{}", topic, message, ex);
                }
            }
        });
    }

    /**
     * 创建kafka store model
     */
    private KafkaStore buildKafkaStore(Map store) {
        return KafkaStore.newBuilder()
                .setId(MapUtils.getString(store, ID))
                .setName(MapUtils.getString(store, Store.NAME))
                .setSn(MapUtils.getString(store, Store.SN))
                .setMerchantId(MapUtils.getString(store, Store.MERCHANT_ID))
                .setCtime(MapUtils.getLongValue(store, CTIME))
                .build();
    }

    /**
     * 创建kafka merchant model
     */
    private KafkaMerchant buildKafkaMerchant(Map merchant) {
        return KafkaMerchant.newBuilder()
                .setId(MapUtils.getString(merchant, ID))
                .setSn(MapUtils.getString(merchant, Merchant.SN))
                .setName(MapUtils.getString(merchant, Merchant.NAME))
                .setCtime(MapUtils.getLongValue(merchant, CTIME))
                .build();
    }

    /**
     * 创建kafka terminal model
     */
    private KafkaTerminal buildKafkaTerminal(Map terminal) {
        return KafkaTerminal.newBuilder()
                .setId(MapUtils.getString(terminal, ID))
                .setSn(MapUtils.getString(terminal, Terminal.SN))
                .setDeviceFingerprint(MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT))
                .setType(MapUtils.getIntValue(terminal, Terminal.TYPE))
                .setStatus(MapUtils.getIntValue(terminal, Terminal.STATUS))
                .setCode(MapUtils.getString(terminal, "code"))  //激活码
                .setStoreId(MapUtils.getString(terminal, Terminal.STORE_ID))
                .setMerchantId(MapUtils.getString(terminal, Terminal.MERCHANT_ID))
                .setVendorId(MapUtils.getString(terminal, Terminal.VENDOR_ID))
                .setVendorAppId(MapUtils.getString(terminal, Terminal.VENDOR_APP_ID))
                .setVendorAppAppid(MapUtils.getString(terminal, Terminal.VENDOR_APP_APPID))
                .setCtime(MapUtils.getLongValue(terminal, CTIME))
                .build();
    }

    /**
     * 创建kafka merchantAudit model
     */
    private KafkaMerchantAudit buildKafkaMerchantAudit(Map merchantAudit) {
        return KafkaMerchantAudit.newBuilder()
                .setMerchantId(MapUtils.getString(merchantAudit, MerchantAudit.MERCHANT_ID))
                .setStatus(MapUtils.getIntValue(merchantAudit, MerchantAudit.STATUS))
                .setSubmitPlatform(MapUtils.getString(merchantAudit, MerchantAudit.SUBMIT_PLATFORM))
                .build();
    }

    /**
     * 创建kafka MerchantUpdateBusinessLicense
     */
    private MerchantUpdateBusinessLicense buildKafkaMerchantUpdateBusinessLicense(Map update) {
        return MerchantUpdateBusinessLicense.newBuilder()
                .setMerchantId(MapUtils.getString(update, "id"))
                .setMerchantSn(MapUtils.getString(update, "sn"))
                .setMerchantType(MapUtils.getString(update, "type"))
                .setMerchantBusinessLicenseType(MapUtils.getString(update, "type"))
                .build();
    }


    /**
     * 创建kafka merchantAudit model
     */
    private KafkaWithdrawModeChange buildKafkaWithdrawModeChange(Map merchant, Map<String, Object> operatotInfo) {
        KafkaWithdrawModeChange modeChange = new KafkaWithdrawModeChange();
        modeChange.setMerchantId(MapUtils.getString(merchant, DaoConstants.ID));
        modeChange.setMerchantSn(MapUtils.getString(merchant, Merchant.SN));
        modeChange.setMerchantName(MapUtils.getString(merchant, Merchant.NAME));
        modeChange.setAlias(MapUtils.getString(merchant, Merchant.ALIAS));
        modeChange.setIndustry(MapUtils.getString(merchant, Merchant.INDUSTRY));
        modeChange.setStatus(MapUtils.getIntValue(merchant, Merchant.STATUS));
        modeChange.setWithdrawMode(MapUtils.getIntValue(merchant, Merchant.WITHDRAW_MODE));
        modeChange.setLongitude(MapUtils.getString(merchant, Merchant.LONGITUDE));
        modeChange.setLatitude(MapUtils.getString(merchant, Merchant.LATITUDE));
        modeChange.setCountry(MapUtils.getString(merchant, Merchant.COUNTRY));
        modeChange.setProvince(MapUtils.getString(merchant, Merchant.PROVINCE));
        modeChange.setCity(MapUtils.getString(merchant, Merchant.CITY));
        modeChange.setDistrict(MapUtils.getString(merchant, Merchant.DISTRICT));
        modeChange.setCtime(MapUtils.getLongValue(merchant, DaoConstants.CTIME));
        modeChange.setCtime(MapUtils.getLongValue(merchant, DaoConstants.MTIME));
        modeChange.setPlatform(MapUtils.getString(operatotInfo, BusinessLogConstant.PLATFORM));
        modeChange.setOperator(MapUtils.getString(operatotInfo, BusinessLogConstant.OPERATOR));
        modeChange.setOperatorId(MapUtils.getString(operatotInfo, BusinessLogConstant.OPERATOR_ID));
        return modeChange;
    }


    private KafkaRedisChange buildKafkaRedisChange(Map<String, Object> redisChange) {
        return new KafkaRedisChange(MapUtil.getString(redisChange, CommonConstant.KAFKA_REDIS_CHANGE_DATA_NAME),
                getRegion(),
                MapUtil.getString(redisChange, CommonConstant.KAFKA_REDIS_CHANGE_KEY),
                MapUtil.getString(redisChange, CommonConstant.KAFKA_REDIS_CHANGE_TYPE)
        );
    }

    /**
     * 获取所在机房
     *
     * @return
     */
    private String getRegion() {
        String region = System.getProperty("shouqianba.region");
        return region;
    }

    private StoreExtAvro buildUpdateStoreExt(Map origin, Map updated) {
        StoreExtAvro storeExtAvro = new StoreExtAvro();
        storeExtAvro.setId(MapUtils.getLongValue(updated, ID, 0L));

        //创建操作
        if (WosaiMapUtils.isEmpty(origin)) {
            storeExtAvro.setBefore(null);
            storeExtAvro.setStoreId(MapUtils.getString(updated, "store_id"));

        } else {
            //更新操作
            storeExtAvro.setStoreId(MapUtils.getString(origin, "store_id"));
            storeExtAvro.setBefore(buildDigital(origin));
        }

        storeExtAvro.setAfter(buildDigital(updated));

        return storeExtAvro;
    }

    private Digital buildDigital(Map data) {
        if (WosaiMapUtils.isEmpty(data)) {
            return null;
        }

        Digital.Builder builder = Digital.newBuilder();
        Digital digital = builder.setVideo(MapUtils.getString(data, "video"))
                .setBusinessHour(MapUtils.getString(data, "business_hour"))
                .setStoreArea(MapUtils.getString(data, "store_area"))
                .setRoomCount(MapUtils.getInteger(data, "room_count"))
                .setTableCount(MapUtils.getInteger(data, "table_count"))
                .setAverageConsumptionTime(MapUtils.getString(data, "average_consumption_time"))
                .setAroundType(MapUtils.getString(data, "around_type"))
                .setExtra(MapUtils.getString(data, "extra"))
                .build();

        String allPhotoIds = String.join(",",
                MapUtils.getString(data, "brand_photo_id", ""),
                MapUtils.getString(data, "brand_only_scene_photo_id", ""),
                MapUtils.getString(data, "indoor_material_photo_id", ""),
                MapUtils.getString(data, "indoor_only_scene_photo_id", ""),
                MapUtils.getString(data, "outdoor_material_photo_id", ""),
                MapUtils.getString(data, "outdoor_only_scene_photo_id", ""),
                MapUtils.getString(data, "other_photo_id", ""),
                MapUtils.getString(data, "order_price_photo_id", ""),
                MapUtils.getString(data, "product_price_id", ""),
                MapUtils.getString(data, "audit_picture_id", "")
        );

        // 批量查询photoInfo
        Map<String, Map> photoInfoMap = photoInfoService.findPhotoinfoBatch(Arrays.asList(allPhotoIds.split(",")));

        // 设置图片属性,门头照
        digital.setBrandPhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "brand_photo_id", "")));
        digital.setBrandOnlyScenePhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "brand_only_scene_photo_id", "")));
        //内景物料照
        digital.setIndoorMaterialPhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "indoor_material_photo_id", "")));
        digital.setIndoorOnlyScenePhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "indoor_only_scene_photo_id", "")));
        //外景物料照
        digital.setOutdoorMaterialPhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "outdoor_material_photo_id", "")));
        digital.setOutdoorOnlyScenePhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "outdoor_only_scene_photo_id", "")));
        //其它照片
        digital.setOtherPhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "other_photo_id", "")));
        digital.setOrderPricePhoto(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "order_price_photo_id", "")));
        digital.setProductPrice(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "product_price_id", "")));
        digital.setAuditPicture(buildPhotoAvro(photoInfoMap, MapUtils.getString(data, "audit_picture_id", "")));
        return digital;
    }

    private List<PhotoAvro> buildPhotoAvro(Map<String, Map> photoInfoMap, String photoId) {
        if (WosaiStringUtils.isEmpty(photoId) || WosaiMapUtils.isEmpty(photoInfoMap)) {
            return null;
        }

        List<PhotoAvro> result = new ArrayList<>();

        for (String id : photoId.split(",")) {
            Map photoInfo = photoInfoMap.get(id);
            if (WosaiMapUtils.isEmpty(photoInfo)) {
                continue;
            }
            PhotoAvro photoAvro = new PhotoAvro();
            photoAvro.setId(id);
            photoAvro.setUrl(MapUtils.getString(photoInfo, "url"));
            result.add(photoAvro);
        }

        return result;
    }
}
