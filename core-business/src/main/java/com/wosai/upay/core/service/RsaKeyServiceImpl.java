package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.RsaKey;
import com.wosai.upay.core.repository.DataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Created by jianfree on 31/12/15.
 */
@Service
@AutoJsonRpcServiceImpl
public class RsaKeyServiceImpl implements RsaKeyService {

    @Autowired
    private UuidGenerator uuidGenerator;
    private DataRepository repository;
    private Dao<Map<String, Object>> rsaKeyDao;

    @Autowired
    public RsaKeyServiceImpl(DataRepository repository) {
        this.repository = repository;
        this.rsaKeyDao = repository.getRsaKeyDao();
    }
    @Override
    public Map create(Map rsaKey) {
        String data = BeanUtil.getPropString(rsaKey, RsaKey.DATA);
        String digest = StringUtil.md5(data);
        if(rsaKey.get(DaoConstants.ID) == null){
            rsaKey.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        rsaKey.put(RsaKey.DIGEST, digest);
        rsaKeyDao.save(rsaKey);
        return rsaKey;
    }

    @Override
    public Map update(Map rsaKey) {
        rsaKeyDao.updatePart(rsaKey);
        return getRsaKey(BeanUtil.getPropString(rsaKey, DaoConstants.ID));
    }

    @Override
    public Map getRsaKey(String rsaKeyId) {
        Map rsaKey = rsaKeyDao.get(rsaKeyId);
        if(rsaKey != null){
            rsaKey.put(RsaKey.DATA, parseData(rsaKey.get(RsaKey.DATA)));
        }
        return rsaKey;
    }

    @Override
    public void delete(String rsaKeyId) {
        rsaKeyDao.delete(rsaKeyId);
    }

    @Override
    public Map getRsaKeyByDigest(String digest) {
        Criteria criteria = Criteria.where(RsaKey.DIGEST).is(digest);
        Map rsaKey = rsaKeyDao.filter(criteria).fetchOne();
        if(rsaKey != null){
            rsaKey.put(RsaKey.DATA, parseData(rsaKey.get(RsaKey.DATA)));
        }
        return rsaKey;
    }

    @Override
    public Map getRsaKeyByName(String name) {
        Criteria criteria = Criteria.where(RsaKey.NAME).is(name);
        Map rsaKey = rsaKeyDao.filter(criteria).fetchOne();
        if(rsaKey != null){
            rsaKey.put(RsaKey.DATA, parseData(rsaKey.get(RsaKey.DATA)));
        }
        return rsaKey;
    }

    @Override
    @SuppressWarnings("unchecked")
    public String storeRsaKey(String data) {
        String digest = StringUtil.md5(data);
        Map<String, Object> rsaMap = getRsaKeyByDigest(digest);
        if (rsaMap != null && !rsaMap.isEmpty()) {
            return BeanUtil.getPropString(rsaMap, DaoConstants.ID);
        } else {
            Map<String,Object> storeData = new HashMap<>();
            storeData.put(RsaKey.NAME,UUID.randomUUID().toString());
            storeData.put(RsaKey.DATA,data);
            rsaMap = create(storeData);
            return BeanUtil.getPropString(rsaMap, DaoConstants.ID);
        }
    }

    private String parseData(Object data){
        if(data instanceof String){
            return (String)data;
        }else if(data instanceof byte[]){
            return new String((byte [])data);
        }else{
            return null;
        }
    }

}
