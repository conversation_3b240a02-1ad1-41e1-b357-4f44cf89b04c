package com.wosai.upay.core.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.weixin.WeixinConstants;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.WeixinSignature;
import com.wosai.upay.common.exception.CommonException;
import com.wosai.upay.core.exception.CoreBizException;
import com.wosai.upay.core.exception.CoreDataAccessException;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.model.RsaKey;
import com.wosai.upay.core.model.SignConfig;
import com.wosai.upay.core.repository.DataRepository;

@Service
@AutoJsonRpcServiceImpl
public class SignServiceImpl implements SignService {
    private static Logger logger =  LoggerFactory.getLogger(SignServiceImpl.class);
    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private RsaKeyService rsaKeyService;
    @Autowired
    private SupportService supportService;
    private Dao<Map<String, Object>> signConfigDao;

    @Autowired
    public SignServiceImpl(DataRepository repository) {
        this.signConfigDao = repository.getSignConfigDao();
    }
    
    @Override
    public Map<String, Object> createSignConfig(String channelId, String signType, String key, Map signData) {
        if(!StringUtil.empty(signType)){
            signType = signType.toUpperCase();
        }
        if(!("RSA".equals(signType) || "RSA2".equals(signType) || "MD5".equals(signType))) {
            throw new CoreInvalidParameterException("不支持的签名类型，不为RSA或RSA2或MD5");
        }
        Map info = signConfigDao.filter(Criteria.where(SignConfig.CHANNEL_ID).is(channelId)).fetchOne();
        if(info != null) {
            String dbKey = "RSA".equals(signType) || "RSA2".equals(signType) ? supportService.getRsaKeyDataById(BeanUtil.getPropString(info, SignConfig.KEY)) :BeanUtil.getPropString(info, SignConfig.KEY); 
            if (!signType.equals(BeanUtil.getPropString(info, SignConfig.SIGN_TYPE)) || !key.equals(dbKey)) {
                throw new CoreInvalidParameterException("渠道已配置，创建失败");
            }
            
        }
        
        if(null == info) {
            info = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid(),
                        SignConfig.CHANNEL_ID, channelId,
                        SignConfig.SIGN_TYPE, signType,
                        SignConfig.KEY, key,
                        SignConfig.DATA, signData
                    );
            if("RSA".equals(signType) || "RSA2".equals(signType)) {
               Map rsaInfo = rsaKeyService.getRsaKeyByDigest(StringUtil.md5(key));
               if(null == rsaInfo) {
                   rsaInfo = rsaKeyService.create(
                           CollectionUtil.hashMap(RsaKey.DATA, key,
                                   RsaKey.REMARK, "渠道"+ channelId + "秘钥"
                           ));
               }
               info.put(SignConfig.KEY, BeanUtil.getPropString(rsaInfo, DaoConstants.ID));
            }
            signConfigDao.save(info);
        }
        
        info.put(SignConfig.KEY, key);
        return info;
    }

    @Override
    public String signWithData(String channelId, String data) {
        Map config = signConfigDao.filter(Criteria.where(SignConfig.CHANNEL_ID).is(channelId)).fetchOne();
        return singnWithConfig(config, data);
    }
    
    private String singnWithConfig(Map config, String data) {
        if(null == config) {
            throw new CoreDataAccessException("配置不存在，请先配置秘钥信息");
        }
        String signType = BeanUtil.getPropString(config, SignConfig.SIGN_TYPE);
        String key = BeanUtil.getPropString(config, SignConfig.KEY, "");
        if("RSA".equals(signType) || "RSA2".equals(signType)) {
            key = supportService.getRsaKeyDataById(key);
        }
        
        String sign = "";
        try {
            switch (signType.toUpperCase()) {
                case "RSA":
                    sign = RsaSignature.sign(data, "SHA1WithRSA", key);
                    break;
                case "RSA2":
                    sign = RsaSignature.sign(data, "SHA256WithRSA", key);
                    break;
                case "MD5":
                    sign = WeixinSignature.getMd5Sign(data, key, WeixinConstants.CHARSET_UTF8);
                    break;
                default:
                    break;
            }
        }catch (Throwable e) {
            logger.warn("签名失败", e);
            throw new CoreBizException(e.getMessage()) {
                public int getCode() {
                    return CommonException.CODE_BIZ_EXCEPTION;
                }
            };
        }
        
        return sign;
    }

    @Override
    public Map<String,String> wechatFoodSign(String channelId, String data) {
        Map result = signConfigDao.filter(Criteria.where(SignConfig.CHANNEL_ID).is(channelId)).fetchOne();
        String sign = singnWithConfig(result, data);
        String[] requestParams = data.split("\\n");
        String requestTime = requestParams != null && requestParams.length > 3 ? requestParams[2] : "";
        String requestNostr = requestParams != null && requestParams.length > 4 ? requestParams[3] : "";
        
        Map dbDataConfig = (Map) result.get(SignConfig.DATA);
        StringBuilder authorizatio = new StringBuilder();
        authorizatio.append("WECHATPAY2-SHA256-RSA2048 ");
        authorizatio.append("mchid=\"").append(channelId).append("\",");
        authorizatio.append("nonce_str=\"").append(requestNostr).append("\",");
        authorizatio.append("timestamp=\"").append(requestTime).append("\",");
        authorizatio.append("serial_no=\"").append(BeanUtil.getPropString(dbDataConfig, SignConfig.DATA_SERIAL_NO)).append("\",");
        authorizatio.append("signature=\"").append(sign).append("\"");
        return CollectionUtil.hashMap("sign", sign, "authorization", authorizatio.toString(), "sign_type", BeanUtil.getPropString(result, SignConfig.SIGN_TYPE));
    }
}
