package com.wosai.upay.core.service;

import java.util.Random;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.wosai.data.util.StringUtil;

@Service
public class SimpleKeyGenerator implements KeyGenerator {

    @Override
    public String nextKey(String base) {
        String key = base + generateString(12) + UUID.randomUUID().toString();
        return StringUtil.md5(key);
    }
    
    private static String generateString(int length) {
        String allChar = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuffer sb = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(allChar.charAt(random.nextInt(allChar.length())));
        }
        return sb.toString();
    }

}
