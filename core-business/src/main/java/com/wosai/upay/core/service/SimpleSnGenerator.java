package com.wosai.upay.core.service;

/**
 * Created by jianfree on 14/3/16.
 */

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.exception.CoreSnGeneratorFailException;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.SafeSimpleDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.concurrent.GuardedBy;
import java.util.Date;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.locks.ReentrantLock;

/**
 *
 * 计数器表建表语句：
 * CREATE TABLE `table_name_sn_prefix` (
 *     `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 *     `stub` varchar(10) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
 *     PRIMARY KEY (`id`),
 *     UNIQUE KEY `stub` (`stub`)
 * ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
 *
 */
@AutoJsonRpcServiceImpl
@Service
public class SimpleSnGenerator implements SnGenerator{

    private static final Logger logger = LoggerFactory.getLogger(SimpleSnGenerator.class);

    private static final Random random = new Random();
    private static final SafeSimpleDateFormat sdf = new SafeSimpleDateFormat("yyyyMMdd");

    @Autowired
    private DataRepository dataRepository;
    @Autowired
    @Qualifier("jdbcTemplate")
    JdbcTemplate jdbcTemplate;
    private int retries = 3;

    @Value("${sn.vendorSnPrefix:918}")
    private String vendorSnPrefix;

    @Value("${sn.solicitorSnPrefix:518}")
    private String solicitorSnPrefix;

    @Value("${sn.groupSnPrefix:188}")
    private String groupSnPrefix;

    @Value("${sn.merchantSnPrefix:168}")
    private String merchantSnPrefix;

    @Value("${sn.storeSnPrefix:158}")
    private String storeSnPrefix;

    @Value("${sn.terminalSnPrefix:100}")
    private String terminalSnPrefix;

    @Value("${sn.departmentSnPrefix:370}")
    private String departmentSnPrefix;

    @Value("${sn.cashDeskSnPrefix:101}")
    private String cashDeskSnPrefix;

    @Value("${sn.brandSnPrefix:233}")
    private String brandSnPrefix;

    private SnTicket vendorSnTicket;
    private SnTicket solicitorSnTicket;
    private SnTicket groupSnTicket;
    private SnTicket merchantSnTicket;
    private SnTicket storeSnTicket;
    private SnTicket terminalSnTicket;
    private SnTicket departmentSnTicket;
    private SnTicket activationCodeTicket;
    private SnTicket vendorAppidTicket;
    private SnTicket providerTerminalIdTicket;
    private SnTicket cashDeskTicket;
    private SnTicket brandSnTicket;

    private static String[] permTables = {
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
            "**********",
    };

    @PostConstruct
    public void init(){
        vendorSnTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "vendor_sn_ticket_" + vendorSnPrefix);
        solicitorSnTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "solicitor_sn_ticket_" + solicitorSnPrefix);
        groupSnTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "group_sn_ticket_" + groupSnPrefix);
        merchantSnTicket = new SnTicket(dataRepository, jdbcTemplate, 4, "merchant_sn_ticket_" + merchantSnPrefix);
        storeSnTicket = new SnTicket(dataRepository, jdbcTemplate, 4, "store_sn_ticket_" + storeSnPrefix);
        terminalSnTicket = new SnTicket(dataRepository, jdbcTemplate, 4, "terminal_sn_ticket_" + terminalSnPrefix);
        departmentSnTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "department_sn_ticket_" + departmentSnPrefix);
        activationCodeTicket = new SnTicket(dataRepository, jdbcTemplate, 10, "activation_code_");
        vendorAppidTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "vendor_appid_");
        providerTerminalIdTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "provider_terminal_id_");
        cashDeskTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "cash_desk_ticket_" + cashDeskSnPrefix);
        brandSnTicket = new SnTicket(dataRepository, jdbcTemplate, 1, "brand_sn_ticket_" + brandSnPrefix);
    }

    @Override
    /**
     * 前缀918 + 5位数字
     */
    public String nextVendorSn() {
        return vendorSnPrefix + nextSn(vendorSnTicket, 5);
    }

    @Override
    /**
     * 前缀518 + 7位数字
     */
    public String nextSolicitorSn() {
        return solicitorSnPrefix + nextSn(solicitorSnTicket, 7);
    }

    @Override
    /**
     * 前缀188 + 8位数字
     */
    public String nextGroupSn() {
        return groupSnPrefix + nextSn(groupSnTicket, 8);
    }

    @Override
    /**
     * 前缀168 + 10位数字
     */
    public String nextMerchantSn() {
        return merchantSnPrefix + nextSn(merchantSnTicket, 10);
    }

    @Override
    /**
     * 前缀158 + 13位数字
     */
    public String nextStoreSn() {
        return storeSnPrefix + nextSn(storeSnTicket, 13);
    }

    @Override
    /**
     * 前缀100 + 5位服务商编号 + (最多56位数字)
     */
    public String nextTerminalSn(String vendorSn) {
        String vendorNo = "";
        if(vendorSn != null){
            int length = vendorSn.length();
            if(length>=5){
                vendorNo = vendorSn.substring(length-5);
            }else{
                vendorNo = leftPadding(vendorNo, '0', 5);
            }
        }
        return terminalSnPrefix + vendorNo + nextSn(terminalSnTicket, 10);
    }


    /**
     * 8位数字
     * @return
     */
    public String nextActivationCode() {
        return nextSn(activationCodeTicket, 8, true);
    }

    @Override
    /**
     * yyyyMMdd + 8位数字
     */
    public String nextVendorAppid() {

        return this.sdf.format(new Date()) + nextSn(vendorAppidTicket, 8);
    }

    @Override
    public String nextDepartmentSn() {
        return departmentSnPrefix + nextSn(departmentSnTicket, 11);
    }

    @Override
    public String nextProviderTerminalId() {
        return nextSn(providerTerminalIdTicket, 11);
    }

    @Override
    public String nextCashDeskSn() {
        return cashDeskSnPrefix + nextSn(cashDeskTicket, 7);
    }

    @Override
    public String nextBrandSn() {
        return brandSnPrefix + nextSn(brandSnTicket,10);
    }

    private String nextSn(SnTicket snTicket, int length, boolean needShuffle){
        for (int i=0; i< retries; ++i) {
            try{
                long sn  = snTicket.nextSn();
                if (needShuffle) {
                    return shuffle(leftPadding(sn + "", '0', length));
                } else {
                    return leftPadding(sn + "", '0', length);
                }
            }catch (TransientDataAccessException ex){
                // continue to retry
                logger.warn("failed to generate sn due to transient jdbc error", ex);
                sleepRandom(50);
            }catch (Exception ex){
                throw new CoreSnGeneratorFailException("序列号产生器异常:" + ex.getMessage());
            }
        }
        throw new CoreSnGeneratorFailException(String.format("序列号产生器失败（已经试了%d次）", retries));
    }

    private String nextSn(SnTicket snTicket, int length) {
        return nextSn(snTicket, length, false);
    }

    private static void sleepRandom(int upTo) {
        long time = random.nextInt(upTo);
        try {
            Thread.sleep(time);
        }
        catch (InterruptedException e) {
            logger.info("SimpleSnGenerator sleepRandom InterruptedException");
        }
    }

    public static String shuffle(String code) {
        StringBuilder sb = new StringBuilder();
        for(int i=0; i<code.length(); ++i) {
            int digit = Integer.parseInt(code.substring(i, i+1));
            sb.append(permTables[i%11].charAt(digit));
        }
        return sb.toString();
    }


    /**
     * 添加前置字符串，左填充。
     * @param origin
     * @param padding
     * @param length
     * @return
     */
    public  static String leftPadding(String origin, char padding, int length){
        if(origin == null){
            return null;
        }
        if(origin.length() < length){
            int time = length - origin.length();
            StringBuilder originBuilder = new StringBuilder(origin);
            for (int i = 0; i< time; i++){
                originBuilder.insert(0, padding);
            }
            origin = originBuilder.toString();
        }
        return origin;
    }

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void setRetries(int retries) {
        this.retries = retries;
    }

    public String getVendorSnPrefix() {
        return vendorSnPrefix;
    }

    public void setVendorSnPrefix(String vendorSnPrefix) {
        this.vendorSnPrefix = vendorSnPrefix;
    }

    public String getSolicitorSnPrefix() {
        return solicitorSnPrefix;
    }

    public void setSolicitorSnPrefix(String solicitorSnPrefix) {
        this.solicitorSnPrefix = solicitorSnPrefix;
    }

    public String getGroupSnPrefix() {
        return groupSnPrefix;
    }

    public void setGroupSnPrefix(String groupSnPrefix) {
        this.groupSnPrefix = groupSnPrefix;
    }

    public String getMerchantSnPrefix() {
        return merchantSnPrefix;
    }

    public void setMerchantSnPrefix(String merchantSnPrefix) {
        this.merchantSnPrefix = merchantSnPrefix;
    }

    public String getStoreSnPrefix() {
        return storeSnPrefix;
    }

    public void setStoreSnPrefix(String storeSnPrefix) {
        this.storeSnPrefix = storeSnPrefix;
    }

    public String getTerminalSnPrefix() {
        return terminalSnPrefix;
    }

    public void setTerminalSnPrefix(String terminalSnPrefix) {
        this.terminalSnPrefix = terminalSnPrefix;
    }

    public String getDepartmentSnPrefix() {
        return departmentSnPrefix;
    }

    public void setDepartmentSnPrefix(String departmentSnPrefix) {
        this.departmentSnPrefix = departmentSnPrefix;
    }

    public String getCashDeskSnPrefix() {
        return cashDeskSnPrefix;
    }

    public void setCashDeskSnPrefix(String cashDeskSnPrefix) {
        this.cashDeskSnPrefix = cashDeskSnPrefix;
    }

    public String getBrandSnPrefix() {
        return brandSnPrefix;
    }

    public void setBrandSnPrefix(String brandSnPrefix) {
        this.brandSnPrefix = brandSnPrefix;
    }

    class SnTicket{
        private final ReentrantLock lock = new ReentrantLock();

        private DataRepository dataRepository;
        private JdbcTemplate jdbcTemplate;

        @GuardedBy("lock,this")
        private long current; //当前订单序号(已使用)
        @GuardedBy("lock,this")
        private long max; //可使用的最大的订单序号(未使用)
        @GuardedBy("lock,this")
        private long next; //下一批次的订单序号 可使用
        private String snNamePrefix; //snName
        private long batchCount;

        public SnTicket(DataRepository dataRepository, JdbcTemplate jdbcTemplate, long batchCount, String snNamePrefix){
            this.dataRepository = dataRepository;
            this.jdbcTemplate = jdbcTemplate;
            this.batchCount = batchCount;
            this.snNamePrefix = snNamePrefix;
        }

        public long nextSn(){
            long sn;
            final ReentrantLock lock = this.lock;
            synchronized (this) {
                if(current >= max) {
                    lock.lock();
                    try{
                        if(next == 0) {
                            //1: 第一次来获取订单号时，需要获取可用订单号。
                            //2: 当用完一半订单号，提前获取下一批次可用订单号的线程执行失败后，需要再此做一个获取下一批次的订单号的补救处理。
                            next = getCurrentAndAllocateNextBatchFromDB(snNamePrefix);
                        }
                        current = next;
                        max = next + batchCount;
                        next = 0;
                    } finally {
                        lock.unlock();
                    }
                }
                sn = current++;
            }
            if(sn == max - batchCount/2) {
                //当用完一半订单号的时候，提前生成下一批可用的订单号
                lock.lock();
                try {
                    next = getCurrentAndAllocateNextBatchFromDB(snNamePrefix);
                } finally {
                    lock.unlock();
                }
            }
            return sn;
        }

        private long getCurrentAndAllocateNextBatchFromDB(String snNamePrefix){
            return dataRepository.doInTicketTransaction(() -> {
                Map<String, Object> ticket = jdbcTemplate.queryForMap("select * from sn_ticket where name_prefix = ? for update", snNamePrefix);
                long currentValue = BeanUtil.getPropLong(ticket, "current");
                jdbcTemplate.update("update sn_ticket set current = current + ? where name_prefix  = ? ", batchCount, snNamePrefix);
                return currentValue;
            });
        }
    }



}
