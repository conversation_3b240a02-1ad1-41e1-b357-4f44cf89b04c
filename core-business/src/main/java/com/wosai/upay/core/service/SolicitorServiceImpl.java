package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.core.exception.CoreOnlyStatusDisabledCouldEnableException;
import com.wosai.upay.core.model.Solicitor;
import com.wosai.upay.core.model.SolicitorConfig;
import com.wosai.upay.core.model.user.SolicitorUser;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.user.api.service.UserService;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class SolicitorServiceImpl implements SolicitorService {

    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private SnGenerator snGenerator;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private UserService userService;

    private DataRepository repository;
    private Dao<Map<String, Object>> solicitorDao;
    private Dao<Map<String, Object>> solicitorConfigDao;

    @Autowired
    public SolicitorServiceImpl(DataRepository repository) {
        this.repository = repository;
        this.solicitorDao = repository.getSolicitorDao();
        this.solicitorConfigDao = repository.getSolicitorConfigDao();
    }

    @Override
    @Transactional(value = "transactionManager")
    public Map createSolicitorComplete(Map request) {
        SolicitorService solicitorService = SpringContextHolder.getBean(SolicitorService.class);

        // 关联account
        String accountId = null;
        if (request.get("account_id") != null) {
            accountId = (String) request.get("account_id");
            request.remove("account_id");
        }

        // 推广渠道配置
        List<Map> solicitorConfig = null;
        if (request.get("solicitor_config") != null) {
            solicitorConfig = (List<Map>) request.get("solicitor_config");
            request.remove("solicitor_config");
        }

        Map solicitor = solicitorService.createSolicitor(request);
        String solicitorId = BeanUtil.getPropString(solicitor, DaoConstants.ID);

        if (solicitorConfig != null && solicitorConfig.size() != 0) {
            for (Map config : solicitorConfig) {
                config.put(SolicitorConfig.SOLICITOR_ID, solicitorId);
                solicitorService.createSolicitorConfig(config);
            }
        }

        if (accountId != null) {
            Map solicitorUser = new HashMap();
            solicitorUser.put(SolicitorUser.ACCOUNT_ID, accountId);
            solicitorUser.put(SolicitorUser.SOLICITOR_ID, solicitorId);
            solicitor.put("account", userService.createSolicitorUser(solicitorUser));
        }
        return solicitor;
    }

    @Override
    public Map createSolicitor(Map solicitor) {
        CrudUtil.ignoreForCreate(solicitor);
        if (solicitor.get(DaoConstants.ID) == null) {
            solicitor.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        if (StringUtil.empty(BeanUtil.getPropString(solicitor, Solicitor.SN))){
            solicitor.put(Solicitor.SN, snGenerator.nextSolicitorSn());
        }
        solicitor.put(Solicitor.STATUS, Solicitor.STATUS_ENABLED);
        solicitorDao.save(solicitor);
        return solicitor;
    }

    @Override
    public void enableSolicitor(String solicitorId) {
        Map solicitorMininfo = businssCommonService.getSolicitorMinimalInfoById(solicitorId);
        if(BeanUtil.getPropInt(solicitorMininfo, ConstantUtil.KEY_STATUS, -1) != Solicitor.STATUS_DISABLED){
            throw new CoreOnlyStatusDisabledCouldEnableException(CoreException.getCodeDesc(CoreException.CODE_ONLY_STATUS_DISABLED_COULD_ENABLE));
        }
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, solicitorId,
                Solicitor.STATUS, Solicitor.STATUS_ENABLED
        );
        solicitorDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_SOLICITOR, solicitorMininfo);
    }

    @Override
    public void disableSolicitor(String solicitorId) {
        Map solicitorMininfo = businssCommonService.getSolicitorMinimalInfoById(solicitorId);
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(solicitorMininfo, DaoConstants.ID),
                Solicitor.STATUS, Solicitor.STATUS_DISABLED
        );
        solicitorDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_SOLICITOR, solicitorMininfo);
    }

    @Override
    public void closeSolicitor(String solicitorId) {
        Map solicitorMininfo = businssCommonService.getSolicitorMinimalInfoById(solicitorId);
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(solicitorMininfo, DaoConstants.ID),
                Solicitor.STATUS, Solicitor.STATUS_CLOSED
        );
        solicitorDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_SOLICITOR, solicitorMininfo);
    }

    @Override
    public void deleteSolicitor(String solicitorId) {
        solicitorDao.delete(solicitorId);
    }

    @Override
    public void deleteSolicitorBySn(String solicitorSn) {
        Map solicitor = getSolicitorBySn(solicitorSn);
        if (solicitor != null) {
            deleteSolicitor(BeanUtil.getPropString(solicitor, DaoConstants.ID));
        }
    }

    @Override
    public Map updateSolicitor(Map solicitor) {
        if (BeanUtil.getPropString(solicitor, DaoConstants.ID) == null && BeanUtil.getPropString(solicitor, Solicitor.SN) != null) {
            solicitor.put(DaoConstants.ID, getSolicitorBySn(BeanUtil.getPropString(solicitor, Solicitor.SN)));
        }
        CrudUtil.ignoreForUpdate(solicitor, new String[]{Solicitor.SN, Solicitor.STATUS});
        solicitorDao.updatePart(solicitor);
        Map updateRs = getSolicitor(BeanUtil.getPropString(solicitor, DaoConstants.ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_SOLICITOR, updateRs);
        return updateRs;
    }

    @Override
    public Map getSolicitor(String solicitorId) {
        if(StringUtil.empty(solicitorId)){
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(solicitorId);
        return solicitorDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getSolicitorBySn(String solicitorSn) {
        if(StringUtil.empty(solicitorSn)){
            return null;
        }
        Criteria criteria = Criteria.where(Solicitor.SN).is(solicitorSn);
        return solicitorDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findSolicitors(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put("sn", Solicitor.SN);
            put("category", Solicitor.CATEGORY);
            put("status", Solicitor.STATUS);
            put("cellphone", Solicitor.CELLPHONE);
            put("contact_name", Solicitor.CONTACT_NAME);
            put("contact_phone", Solicitor.CONTACT_PHONE);
            put("contact_cellphone", Solicitor.CONTACT_CELLPHONE);
//            put("contact_email", Solicitor.CONTACT_EMAIL);
//            put("contact_address", Solicitor.CONTACT_ADDRESS);
            put("deleted", DaoConstants.DELETED);
        }});
        String name = BeanUtil.getPropString(queryFilter, "name");
        if (!StringUtil.empty(name)) {
            criteria.with(Solicitor.NAME).like("%" + name + "%");
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = solicitorDao.filter(criteria).count();
        Filter filter = solicitorDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public Map createSolicitorConfig(Map solicitorConfig) {
        if (solicitorConfig.get(DaoConstants.ID) == null) {
            solicitorConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        solicitorConfigDao.save(solicitorConfig);
        return solicitorConfig;
    }

    @Override
    public void deleteSolicitorConfig(String solicitorConfigId) {
        solicitorConfigDao.delete(solicitorConfigId);
    }

    @Override
    public Map updateSolicitorConfig(Map solicitorConfig) {
        solicitorConfigDao.updatePart(solicitorConfig);
        return getSolicitorConfig(BeanUtil.getPropString(solicitorConfig, DaoConstants.ID));
    }

    private Map getSolicitorConfig(String solicitorConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(solicitorConfigId);
        return solicitorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getSolicitorConfigBySolicitorId(String solicitorId) {
        Criteria criteria = Criteria.where(SolicitorConfig.SOLICITOR_ID).is(solicitorId);
        return solicitorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findSolicitorConfigs(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(SolicitorConfig.SOLICITOR_ID, SolicitorConfig.SOLICITOR_ID);
            put(SolicitorConfig.PAYWAY, SolicitorConfig.PAYWAY);
            put(SolicitorConfig.B2C_FORMAL, SolicitorConfig.B2C_FORMAL);
            put(SolicitorConfig.B2C_STATUS, SolicitorConfig.B2C_STATUS);
            put(SolicitorConfig.C2B_FORMAL, SolicitorConfig.C2B_FORMAL);
            put(SolicitorConfig.C2B_STATUS, SolicitorConfig.C2B_STATUS);
            put(SolicitorConfig.WAP_FORMAL, SolicitorConfig.WAP_FORMAL);
            put(SolicitorConfig.WAP_STATUS, SolicitorConfig.WAP_STATUS);
            put(SolicitorConfig.MINI_FORMAL, SolicitorConfig.MINI_FORMAL);
            put(SolicitorConfig.MINI_STATUS, SolicitorConfig.MINI_STATUS);
            put(SolicitorConfig.EXTEND2_FORMAL, SolicitorConfig.EXTEND2_FORMAL);
            put(SolicitorConfig.EXTEND2_STATUS, SolicitorConfig.EXTEND2_STATUS);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = solicitorConfigDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = solicitorConfigDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public List getSolicitorConfigs(String solicitorId) {
        Criteria criteria = Criteria.where(SolicitorConfig.SOLICITOR_ID).is(solicitorId);
        Filter filter = solicitorConfigDao.filter(criteria);
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    @Override
    public Map<String, Object> getSolicitorTranslateInfoById(final String solicitorId) {
        return new HashMap<String, Object>() {{
            put(ConstantUtil.KEY_SOLICITOR_ID, solicitorId);
        }};
    }

    @Override
    public Map<String, Object> getSolicitorTranslateInfoBySn(final String solicitorSn) {
        return new HashMap<String, Object>() {{
            put(ConstantUtil.KEY_SOLICITOR_SN, solicitorSn);
        }};
    }

}
