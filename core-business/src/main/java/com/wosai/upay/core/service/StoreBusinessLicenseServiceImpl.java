package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.core.exception.CoreLicenseNotExistsException;
import com.wosai.upay.core.model.License;
import com.wosai.upay.core.model.StoreBusinessLicence;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.EffectiveTimeUtil;
import com.wosai.upay.core.util.RemoveSpaceCharsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;


@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class StoreBusinessLicenseServiceImpl implements StoreBusinessLicenseService {


    private Dao<Map<String, Object>> storeBusinessLicenseReadDao;
    private Dao<Map<String, Object>> storeBusinessLicenseWriteDao;
    @Autowired
    private DataRepository repository;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private McPreService mcPreService;

    @PostConstruct
    private void init() {
        storeBusinessLicenseReadDao = repository.getStoreBusinessLicenseReadDao();
        storeBusinessLicenseWriteDao = repository.getStoreBusinessLicenseWriteDao();
    }

    private static final String STORE_ID = "store_id";
    private static final String BUSINESS_LICENSE_ID = "business_license_id";

    private static final List<String> fields = Arrays.asList(StoreBusinessLicence.NUMBER, StoreBusinessLicence.NAME,
            StoreBusinessLicence.LEGAL_PERSON_NAME, StoreBusinessLicence.LEGAL_PERSON_ID_NUMBER,
            License.LICENSE_NAME, License.LICENSE_NUMBER);


    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int saveStoreBusinessLicense(Map storeBusinessLicense) {
        String storeId = (String) storeBusinessLicense.get(STORE_ID);
        if (getStoreBusinessLicenseByStoreId(storeId) != null) {
            return 0;
        }

        this.replaceXAndCheckIdValidity(storeBusinessLicense);
        RemoveSpaceCharsUtil.removeSpaceChars(storeBusinessLicense, fields);
        List<Map> storeLicenses = (List<Map>) storeBusinessLicense.remove("trade_license_list");

        String id = UUID.randomUUID().toString();
        storeBusinessLicense.put(ConstantUtil.KEY_ID, id);
        storeBusinessLicenseWriteDao.save(storeBusinessLicense);
        Boolean use_merchant_business_license = MapUtils.getBoolean(storeBusinessLicense, "use_merchant_business_license", false);
        if (use_merchant_business_license) {
            //复用情况下,这些数据应该存到license的中间表(营业执照id为商户营业执照id)
            if (storeLicenses != null && !storeLicenses.isEmpty()) {
                Map<String, Object> merchantBusiness = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(MapUtils.getString(storeBusinessLicense, "merchant_id"));

                for (Map license : storeLicenses) {
                    license.put(BUSINESS_LICENSE_ID, MapUtils.getString(merchantBusiness, "id"));
                }
                handleMcPreLicense(MapUtils.getString(merchantBusiness, "id"), storeLicenses);
                // storeLicenses 为空list,清空许可证
            } else if (storeLicenses != null && storeLicenses.size() == 0) {
                Map<String, Object> merchantBusiness = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(MapUtils.getString(storeBusinessLicense, "merchant_id"));
                licenseService.deleteAllLicenseByBusinessLicenseIdTruly(MapUtils.getString(merchantBusiness, "id"));
            }
        } else {
            //非复用情况单独保存至原表
            if (storeLicenses != null && !storeLicenses.isEmpty()) {
                for (Map storeLicense : storeLicenses) {
                    storeLicense.put(BUSINESS_LICENSE_ID, id);
                    licenseService.saveLicense(storeLicense);
                }
            } /*else if (storeLicenses != null && storeLicenses.size() == 0) {
                licenseService.deleteAllLicenseByBusinessLicenseIdTruly(id);
            }*/
        }

        return 1;
    }

    @Override
    public Map<String, Object> getStoreBusinessLicenseByStoreId(String storeId) {
        Criteria criteria = Criteria.where(STORE_ID).is(storeId).with(ConstantUtil.KEY_DELETED).is(false);
        Map<String, Object> storeBusinessLicense = storeBusinessLicenseReadDao.filter(criteria).fetchOne();
        if (storeBusinessLicense == null) {
            return null;
        }
        String id = (String) storeBusinessLicense.get("id");

        List storeLicenses = licenseService.getLicenseByBusinessLicenseId(id);
        storeBusinessLicense.put("trade_license_list", storeLicenses);
        return storeBusinessLicense;
    }

    @Override
    public Map<String, Object> getStoreBusinessLicenseById(String id) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(id).with(ConstantUtil.KEY_DELETED).is(false);
        Map<String, Object> storeBusinessLicense = storeBusinessLicenseReadDao.filter(criteria).fetchOne();
        if (storeBusinessLicense == null) {
            return null;
        }
        String businessLicenseId = (String) storeBusinessLicense.get("id");

        List storeLicenses = licenseService.getLicenseByBusinessLicenseId(businessLicenseId);
        storeBusinessLicense.put("trade_license_list", storeLicenses);
        return storeBusinessLicense;
    }

    @Override
    public int updateStoreBusinessLicense(Map storeBusinessLicense) {
        this.replaceXAndCheckIdValidity(storeBusinessLicense);
        RemoveSpaceCharsUtil.removeSpaceChars(storeBusinessLicense, fields);
        String id = BeanUtil.getPropString(storeBusinessLicense, "id", null);
        String storeId = BeanUtil.getPropString(storeBusinessLicense, STORE_ID, null);
        Criteria criteria = null;
        if (WosaiStringUtils.isNotBlank(id)) {
            criteria = Criteria.where(ConstantUtil.KEY_ID).is(id).with(ConstantUtil.KEY_DELETED).is(false);
        } else {
            criteria = Criteria.where(STORE_ID).is(storeId).with(ConstantUtil.KEY_DELETED).is(false);
        }

        Map<String, Object> businessLicense = storeBusinessLicenseReadDao.filter(criteria).fetchOne();
        if (businessLicense == null || businessLicense.size() == 0) {
            throw new CoreLicenseNotExistsException("无此营业执照信息");
        }
        id = (String) businessLicense.get("id");
        storeBusinessLicense.put(ConstantUtil.KEY_ID, id);
        CrudUtil.ignoreForUpdate(storeBusinessLicense, new String[]{StoreBusinessLicence.MERCHANT_ID, StoreBusinessLicence.STORE_ID});
        storeBusinessLicenseWriteDao.updatePart(storeBusinessLicense);
        return 1;
    }

    @Override
    public int deleteStoreBusinessLicenseById(String id) {
        licenseService.deleteAllLicenseByBusinessLicenseId(id);
        storeBusinessLicenseWriteDao.delete(id);
        return 1;
    }

    @Override
    public int deleteStoreBusinessLicenseByStoreId(String storeId) {
        Map<String, Object> storeBusinessLicense = getStoreBusinessLicenseByStoreId(storeId);
        if (storeBusinessLicense != null) {
            String id = (String) storeBusinessLicense.get("id");
            deleteStoreBusinessLicenseById(id);
            return 1;
        }
        return 0;
    }


    private void replaceXAndCheckIdValidity(Map license) {
        if (WosaiMapUtils.isNotEmpty(license)) {
            String validity = WosaiMapUtils.getString(license, StoreBusinessLicence.VALIDITY);
            if (WosaiStringUtils.isNotEmpty(validity)) {
                EffectiveTimeUtil.checkoutEffectiveTime(validity);
            }
            Integer idType = WosaiMapUtils.getInteger(license, StoreBusinessLicence.LEGAL_PERSON_ID_TYPE);
            if (Objects.isNull(idType) || idType != 1) {
                return;
            }
            String idNumber = WosaiMapUtils.getString(license, StoreBusinessLicence.LEGAL_PERSON_ID_NUMBER);
            if (WosaiStringUtils.isNotEmpty(idNumber) && idNumber.contains("x")) {
                idNumber = idNumber.replace("x", "X");
                license.put(StoreBusinessLicence.LEGAL_PERSON_ID_NUMBER, idNumber);
            }
            String idValidity = WosaiMapUtils.getString(license, StoreBusinessLicence.ID_VALIDITY);
            if (WosaiStringUtils.isNotEmpty(idValidity) && WosaiMapUtils.getInteger(license, StoreBusinessLicence.LEGAL_PERSON_ID_TYPE) == 1) {
                EffectiveTimeUtil.checkoutEffectiveTime(idValidity);
            }

        }
    }

    /**
     * 在门店维度传入的许可证,但需要落到商户维度,特殊处理一下,进中间表
     *
     * @param bizId    商户维度营业执照id
     * @param licenses 要保存的许可证(应该包含: 之前有的(商户维度) + 新添加的("门店维度的"))
     */
    private void handleMcPreLicense(String bizId, List<Map> licenses) {
        Map mcPre = new HashMap(16);
        mcPre.put("table_name", "license");
        mcPre.put("biz_id", bizId);
        mcPre.put("dev_code", "handleMcPreLicense");
        mcPre.put("data", JSON.toJSONString(licenses));

        Map beForeMcPre = mcPreService.findMcPre("license", bizId);
        if (MapUtils.isNotEmpty(beForeMcPre)) {
            mcPre.put("id", beForeMcPre.get("id"));
            mcPreService.updateMcPre(mcPre);
        } else {
            mcPreService.saveMcPre(mcPre);
        }

    }
}
