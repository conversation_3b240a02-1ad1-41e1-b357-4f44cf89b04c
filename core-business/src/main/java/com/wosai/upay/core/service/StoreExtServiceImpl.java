package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.exception.CommonInvalidParameterException;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author: l<PERSON><PERSON>qiang
 * @Date: 2020-08-24
 * @Description:
 */

@Service
@AutoJsonRpcServiceImpl
public class StoreExtServiceImpl implements StoreExtService {
    @Autowired
    Dao<Map<String, Object>> storeExtDao;

    @Autowired
    private PhotoInfoService photoInfoService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Autowired
    private RMQService rmqService;


    @Override
    public int createStoreExt(Map storeExt) {
        storeExtDao.save(storeExt);
        rmqService.writeUpdateStoreExt(null, storeExt);

        return 1;
    }

    @Override
    public int updateStoreExt(Map storeExt) {
        Map originStoreExt = findStoreExt(MapUtils.getString(storeExt, DaoConstants.ID));
        storeExtDao.updatePart(storeExt);
        Map updatedStoreExt = findStoreExt(MapUtils.getString(storeExt, DaoConstants.ID));

        rmqService.writeUpdateStoreExt(originStoreExt, updatedStoreExt);
        return 1;
    }

    @Override
    public Map findStoreExt(String storeExtId) {
        Criteria criteria = Criteria.where("id").is(storeExtId);
        return storeExtDao.filter(criteria).fetchOne();
    }

    @Override
    public Map findStoreExtByStoreId(String storeId) {
        Criteria criteria = Criteria.where("store_id").is(storeId);
        return storeExtDao.filter(criteria).fetchOne();
    }

    @Override
    public void deleteStoreExtByStoreId(String storeId) {
        Map storeExt = findStoreExtByStoreId(storeId);
        if (WosaiMapUtils.isEmpty(storeExt)) {
            return;
        }
        List<String> photos = new ArrayList<>();
        if (WosaiStringUtils.isNotEmpty(MapUtils.getString(storeExt, "brand_photo_id"))) {
            photos.add(MapUtils.getString(storeExt, "brand_photo_id"));
        }
        if (WosaiStringUtils.isNotEmpty(MapUtils.getString(storeExt, "indoor_material_photo_id"))) {
            photos.add(MapUtils.getString(storeExt, "indoor_material_photo_id"));
        }
        if (WosaiStringUtils.isNotEmpty(MapUtils.getString(storeExt, "outdoor_material_photo_id"))) {
            photos.add(MapUtils.getString(storeExt, "outdoor_material_photo_id"));
        }
        if (WosaiStringUtils.isNotEmpty(MapUtils.getString(storeExt, "product_price_id"))) {
            photos.add(MapUtils.getString(storeExt, "product_price_id"));
        }
        if (WosaiStringUtils.isNotEmpty(MapUtils.getString(storeExt, "audit_picture_id"))) {
            photos.add(MapUtils.getString(storeExt, "audit_picture_id"));
        }
        String otherPhotos = MapUtils.getString(storeExt, "other_photo_id");
        if (WosaiStringUtils.isNotEmpty(otherPhotos)) {
            photos.addAll(Arrays.asList(otherPhotos.split(",")));
        }
        if (WosaiCollectionUtils.isNotEmpty(photos)) {
            photoInfoService.deletePhotoInfos(photos);
        }

        storeExtDao.delete(BeanUtil.getPropString(storeExt, DaoConstants.ID));
    }

    @Override
    public List<Map> findStoreExtBatchByStoreIds(List<String> storeIds) {
        String sql = "select * from store_ext where store_id in (:storeIds)";

        List<Map> allResult = new ArrayList<>();
        List<Map> storeExt = null;

        //in 参数里一次性查50条
        for (int i = 0; i < storeIds.size(); i += 50) {
            storeExt = namedParameterJdbcTemplate.queryForList(sql, CollectionUtil.hashMap("storeIds", storeIds.subList(i, Math.min(i + 50, storeIds.size()))));
            if (WosaiCollectionUtils.isNotEmpty(storeExt)) {
                allResult.addAll(storeExt);
            }
        }

        return allResult;
    }

    @Override
    public Map<String, String> getBrandOnlyScenePhotoBatchByStoreIds(List<String> storeIds) {

        if (WosaiCollectionUtils.isEmpty(storeIds) || storeIds.size() > 200) {
            throw new CommonInvalidParameterException("参数为空或者一次性查询参数大于200个");
        }

        List<Map> storeExtBatch = findStoreExtBatchByStoreIds(storeIds);
        Map<String, String> result = new HashMap<>();
        if (WosaiCollectionUtils.isNotEmpty(storeExtBatch)) {
            for (Map extBatch : storeExtBatch) {

                //门头照id
                String brand_only_scene_photo_id = (String) extBatch.get("brand_only_scene_photo_id");

                if (WosaiStringUtils.isNotBlank(brand_only_scene_photo_id)) {

                    Map photoinfo = photoInfoService.findPhotoinfo(brand_only_scene_photo_id, null);

                    if (WosaiMapUtils.isNotEmpty(photoinfo)) {
                        result.put((String) extBatch.get("store_id"), (String) photoinfo.get("url"));
                    }
                }

            }
        }

        return result;
    }
}
