package com.wosai.upay.core.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.dao.jdbc.IndexHintContextHolder;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.bean.response.MerchantStoreResponse;
import com.wosai.upay.core.databus.StoreDataBusBiz;
import com.wosai.upay.core.datasource.DataSourceConstant;
import com.wosai.upay.core.datasource.DataSourceType;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.biz.BankInfoBiz;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.core.util.EsUtil;
import com.wosai.upay.core.util.MapValueUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by jianfree on 21/1/16.
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class StoreServiceImpl implements StoreService {

    private static final Logger logger = LoggerFactory.getLogger(StoreServiceImpl.class);

    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private SnGenerator snGenerator;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private RMQService rmqService;
    @Autowired
    private StoreExtService storeExtService;
    @Autowired
    private BankInfoBiz bankInfoBiz;
    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private StoreDataBusBiz storeDataBusBiz;

    @Autowired
    private BizLogFacade bizLogFacade;


    private Dao<Map<String, Object>> storeDao;

    @Autowired
    public StoreServiceImpl(DataRepository repository) {
        this.storeDao = repository.getStoreDao();
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map createStore(Map store) {
        CrudUtil.ignoreForCreate(store);
        return createStoreForMerchantCenter(store);
    }

    @Override
    public Map createStoreForMerchantCenter(Map store) {
        MapUtil.removeKeys(store, new String[]{Store.SN, Store.STATUS, Store.SOLICITOR_ID, Store.VENDOR_ID});
        // clientSn如果传递，则商户内必须唯一
        if (!StringUtil.empty(BeanUtil.getPropString(store, Store.CLIENT_SN)) && getStoreByClientSn(BeanUtil.getPropString(store, Store.MERCHANT_ID), BeanUtil.getPropString(store, Store.CLIENT_SN)) != null) {
            throw new CoreClientSnNotUniqueException("门店外部编号在商户内不唯一");
        }
        if (store.get(DaoConstants.ID) == null) {
            store.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        store.put(Store.SN, snGenerator.nextStoreSn());
        store.put(Store.STATUS, Store.STATUS_ENABLED); // 默认开启
        // 将商户的solicitor_id、vendor_id赋值给门店
        String merchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
        Map merchantMinInfo = businssCommonService.getMerchantMinimalInfoById(merchantId);
        store.put(Store.SOLICITOR_ID, merchantMinInfo.get(Merchant.SOLICITOR_ID));
        store.put(Store.VENDOR_ID, merchantMinInfo.get(Merchant.VENDOR_ID));
        bankInfoBiz.appendDistrictCode(store, true);
        storeDao.save(store);
        Map createdStore = getStore(BeanUtil.getPropString(store, DaoConstants.ID));
        rmqService.writeNewStoreCreated(createdStore);
        storeDataBusBiz.insert(createdStore);
        return createdStore;
    }


    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map updateStore(Map store) {
        store = MapValueUtil.filterNullValues(store);
        Map before = getStoreByStoreId(BeanUtil.getPropString(store, DaoConstants.ID));
        if (store.containsKey(Store.EXTRA)) {
            Map beforeExtra = WosaiMapUtils.getMap(before, Store.EXTRA, new HashMap());
            Map afterExtra = WosaiMapUtils.getMap(store, Store.EXTRA, new HashMap());
            beforeExtra.putAll(afterExtra);
            store.put(Store.EXTRA, beforeExtra);
        }

        CrudUtil.ignoreForUpdate(store, new String[]{Store.SN, Store.STATUS, Store.MERCHANT_ID, Store.SOLICITOR_ID, Store.VENDOR_ID});
        bankInfoBiz.appendDistrictCode(store, false);
        storeDao.updatePart(store);
        Map updateRs = getStoreByStoreId(BeanUtil.getPropString(store, DaoConstants.ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_STORE, updateRs);
        storeDataBusBiz.update(before, updateRs);
        String merchantSn = businssCommonService.getMerchantSnById(BeanUtil.getPropString(before, Store.MERCHANT_ID));
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(merchantSn);
        return updateRs;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map updateStoreAndLog(Map store, OpLogCreateRequest opLogCreateRequest) {
        Map before = getStoreByStoreId(BeanUtil.getPropString(store, DaoConstants.ID));
        Map after = updateStore(store);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, BeanUtil.getPropString(before, DaoConstants.ID), BeanUtil.getPropString(before, Store.MERCHANT_ID), OpLog.STORE_TEMPLATE_CODE, OpLog.STORE_TABLE_NAME, new ArrayList<>(), OpLog.STORE_CHANGE_KEY_LIST, OpLog.STORE_DESC_MAP, before, after);
        return after;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void disableStore(String storeId) {
        Map store = getStore(storeId);
        if (store == null) {
            throw new CoreStoreNotExistsException("门店不存在");
        }
        int preStatus = BeanUtil.getPropInt(store, Store.STATUS, -1);
        store.put(Store.STATUS, Store.STATUS_DISABLED);
        storeDao.updatePart(store);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_STORE, store);
        storeDataBusBiz.statusChange(store, preStatus, Store.STATUS_DISABLED);
        String merchantSn = businssCommonService.getMerchantSnById(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(merchantSn);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void enableStore(String storeId) {
        Map store = getStore(storeId);
        if (store == null) {
            throw new CoreStoreNotExistsException("门店不存在");
        }
        int preStatus = BeanUtil.getPropInt(store, Store.STATUS, -1);
        if (preStatus != Store.STATUS_DISABLED) {
            throw new CoreOnlyStatusDisabledCouldEnableException(CoreException.getCodeDesc(CoreException.CODE_ONLY_STATUS_DISABLED_COULD_ENABLE));
        }
        store.put(Store.STATUS, Store.STATUS_ENABLED);
        storeDao.updatePart(store);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_STORE, store);
        storeDataBusBiz.statusChange(store, preStatus, Store.STATUS_ENABLED);
        String merchantSn = businssCommonService.getMerchantSnById(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(merchantSn);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void closeStore(String storeId) {
        Map store = getStore(storeId);
        if (store == null) {
            throw new CoreStoreNotExistsException("门店不存在");
        }
        int preStatus = BeanUtil.getPropInt(store, Store.STATUS, -1);
        store.put(Store.STATUS, Store.STATUS_CLOSED);
        storeDao.updatePart(store);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_STORE, store);
        storeDataBusBiz.statusChange(store, preStatus, Store.STATUS_CLOSED);
        String merchantSn = businssCommonService.getMerchantSnById(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        SpringContextHolder.getBean(SupportService.class).removeCachedParams(merchantSn);
    }

    @Override
    public Map getStore(String storeId) {
        if (StringUtil.empty(storeId)) {
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(storeId);
        Map<String, Object> store = storeDao.filter(criteria).fetchOne();
        bankInfoBiz.resolveDistrict(store);
        return store;

    }


    @Override
    public Map getStoreByStoreId(String storeId) {
        return getStore(storeId);
    }

    @Override
    public Map getStoreByStoreSn(String storeSn) {
        if (StringUtil.empty(storeSn)) {
            return null;
        }
        Criteria criteria = Criteria.where(Store.SN).is(storeSn);
        Map<String, Object> store = storeDao.filter(criteria).fetchOne();
        bankInfoBiz.resolveDistrict(store);
        return store;
    }

    @Override
    public Map getStoreByClientSn(String merchantId, String clientSn) {
        Criteria criteria = Criteria.where(Store.MERCHANT_ID).is(merchantId).with(Store.CLIENT_SN).is(clientSn);
        Map<String, Object> store = storeDao.filter(criteria).fetchOne();
        bankInfoBiz.resolveDistrict(store);
        return store;
    }

    @Override
    public ListResult getStoreListByMerchantId(String merchantId, PageInfo pageInfo, Map queryFilter) {
        if (pageInfo == null) {
            pageInfo = new PageInfo();
        }
        ListResult listResult = new ListResult();
        String storeName = BeanUtil.getPropString(queryFilter, "store_name", "");
        String storeSn = BeanUtil.getPropString(queryFilter, "store_sn", "");
        String client_sn = BeanUtil.getPropString(queryFilter, Store.CLIENT_SN, "");
        Criteria criteria = Criteria.where(Store.MERCHANT_ID).is(merchantId);
        try {
            IndexHintContextHolder.setIndexHint("force index(idx_merchant_id_client_sn)");
            if (!storeName.isEmpty()) {
                criteria.with(Store.NAME).like("%" + storeName + "%");
            }
            if (!storeSn.isEmpty()) {
                criteria.with(Store.SN).is(storeSn);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(client_sn)) {
                criteria.with(Store.CLIENT_SN).is(client_sn);
            }
            SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
            listResult.setTotal(storeDao.filter(criteria).count());
            SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
            Filter filter = storeDao.filter(criteria);
            PageInfoUtil.pagination(pageInfo, filter);
            listResult.setRecords(CollectionUtil.iterator2list(filter.fetchAll()));
        } finally {
            IndexHintContextHolder.clear();
        }
        listResult.getRecords().forEach(item -> bankInfoBiz.resolveDistrict(item));
        return listResult;
    }

    @Override
    public ListResult getSimpleStoreListByMerchantId(String merchantId, PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo();
        }
        ListResult listResult = new ListResult();
        Criteria criteria = Criteria.where(Store.MERCHANT_ID).is(merchantId);
        try {
            IndexHintContextHolder.setIndexHint("force index(idx_merchant_id_client_sn)");
            SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
            listResult.setTotal(storeDao.filter(criteria).count());
            SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
            Filter filter = storeDao.filter(criteria, Arrays.asList(DaoConstants.ID, Store.SN, Store.NAME, Store.STATUS, Store.DISTRICT_CODE));
            PageInfoUtil.pagination(pageInfo, filter);
            listResult.setRecords(CollectionUtil.iterator2list(filter.fetchAll()));
        } finally {
            IndexHintContextHolder.clear();
        }
        return listResult;
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public ListResult getSimpleStoreListByMerchantIdFromSlaveDb(String merchantId, PageInfo pageInfo) {
        return getSimpleStoreListByMerchantId(merchantId, pageInfo);
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public List<String> getStoreIdListByMerchantId(String merchantId, PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo();
        }
        List<String> result = new ArrayList<>();
        Criteria criteria = Criteria.where(Store.MERCHANT_ID).is(merchantId);
        try {
            IndexHintContextHolder.setIndexHint("force index(idx_merchant_id_client_sn)");
            SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
            SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
            Filter filter = storeDao.filter(criteria, Arrays.asList(DaoConstants.ID));
            PageInfoUtil.pagination(pageInfo, filter);
            List<Map> storeIdList = CollectionUtil.iterator2list(filter.fetchAll());
            if (WosaiCollectionUtils.isNotEmpty(storeIdList)) {
                List<String> idList = storeIdList.stream().map(store -> MapUtils.getString(store, DaoConstants.ID)).distinct().collect(Collectors.toList());
                result.addAll(idList);
            }
        } finally {
            IndexHintContextHolder.clear();
        }
        return result;
    }



    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public ListResult getSimpleStoreListByMerchantIdOrStoreIdsFromSlaveDb(Map queryFilter, PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo();
        }
        String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
        List<String> storeIds = (List<String>) queryFilter.get("store_ids");
        String storeName = BeanUtil.getPropString(queryFilter, "store_name", "");
        String status = BeanUtil.getPropString(queryFilter, Store.STATUS);
        String storeSn = BeanUtil.getPropString(queryFilter, Store.SN);
        String clientSn = BeanUtil.getPropString(queryFilter, Store.CLIENT_SN);
        if (WosaiStringUtils.isEmpty(merchantId) && WosaiCollectionUtils.isEmpty(storeIds)) {
            throw new CoreInvalidParameterException("商户id和门店id列表不可同时为空");
        }
        List<Integer> statusList = (List<Integer>) queryFilter.get("status_list");

        ListResult listResult = new ListResult();
        Criteria criteria = Criteria.where(DaoConstants.ID).ne(null);
        try {
            if (WosaiStringUtils.isNotEmpty(merchantId)) {
                criteria.with(Store.MERCHANT_ID).is(merchantId);
            }
            if (WosaiCollectionUtils.isEmpty(storeIds)) {
                IndexHintContextHolder.setIndexHint("force index(idx_merchant_id_client_sn)");
            } else {
                criteria.with(DaoConstants.ID).in(storeIds);
            }
            if (WosaiStringUtils.isNotEmpty(storeName)) {
                criteria.with(Store.NAME).like("%" + storeName + "%");
            }
            if (WosaiStringUtils.isNotEmpty(status)) {
                criteria.with(Store.STATUS).is(status);
            }
            if (WosaiCollectionUtils.isNotEmpty(statusList)) {
                criteria.with(Store.STATUS).in(statusList);
            }
            if (WosaiStringUtils.isNotEmpty(storeSn)) {
                criteria.with(Store.SN).is(storeSn);
            }
            if (WosaiStringUtils.isNotEmpty(clientSn)) {
                criteria.with(Store.CLIENT_SN).is(clientSn);
            }

            SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
            listResult.setTotal(storeDao.filter(criteria).count());
            SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
            Filter filter = storeDao.filter(criteria, Arrays.asList(DaoConstants.ID, Store.SN, Store.NAME, Store.STATUS, Store.DISTRICT_CODE, Store.CLIENT_SN, Store.PROVINCE, Store.CITY, Store.DISTRICT, Store.CONTACT_NAME, Store.CONTACT_CELLPHONE, DaoConstants.CTIME));
            PageInfoUtil.pagination(pageInfo, filter);
            listResult.setRecords(CollectionUtil.iterator2list(filter.fetchAll()));
        } finally {
            IndexHintContextHolder.clear();
        }
        return listResult;
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public ListResult getStoreListByMerchantIdFromSlaveDb(String merchantId, PageInfo pageInfo, Map queryFilter) {
        return getStoreListByMerchantId(merchantId, pageInfo, queryFilter);
    }

    @Override
    public ListResult findStores(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        String storeSn = BeanUtil.getPropString(queryFilter, "store_sn");
        String storeName = BeanUtil.getPropString(queryFilter, "store_name");
        Criteria criteria = Criteria.where(DaoConstants.ID).ne(null);
        if (queryFilter.get("store_ids") != null && queryFilter.get("store_ids") instanceof List && !((List) queryFilter.get("store_ids")).isEmpty()) {
            criteria.with(ConstantUtil.KEY_ID).in((ArrayList) queryFilter.get("store_ids"));
        }
        if (queryFilter.get("status_list") != null && queryFilter.get("status_list") instanceof List && !((List) queryFilter.get("status_list")).isEmpty()) {
            criteria.with(Store.STATUS).in((ArrayList) queryFilter.get("status_list"));
        }
        if (!StringUtil.empty(storeSn)) {
            criteria.with(Store.SN).is(storeSn);
        }
        //该条件只有地图服务暂时用一下
        Integer neVerifyStatus = MapUtils.getInteger(queryFilter, "ne_verify_status");
        if (neVerifyStatus != null) {
            criteria.with(Store.VERIFY_STATUS).ne(neVerifyStatus);
        }
        if (!StringUtil.empty(storeName)) {
            try {
                // 构建Match Phrase查询
                MatchPhraseQueryBuilder matchPhraseQuery = QueryBuilders.matchPhraseQuery(Store.NAME, storeName);
                QueryBuilder conditionQuery = matchPhraseQuery;
                //获取商户ID列表
                Set<String> merchantIds = getMerchantIds(queryFilter);
                if (CollectionUtils.isNotEmpty(merchantIds)) {
                    // 创建Terms查询
                    TermsQueryBuilder termsQuery = QueryBuilders.termsQuery(Store.MERCHANT_ID, merchantIds);
                    // 创建Bool查询并将Terms 和 Match Phrase查询添加到Must子句
                    conditionQuery = QueryBuilders.boolQuery()
                            .must(termsQuery)
                            .must(matchPhraseQuery);
                }
                // 创建 SearchSourceBuilder
                SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
                searchSourceBuilder.query(conditionQuery);
                Criteria esCriteria = EsUtil.query(new EsUtil.QueryInfo("store"), searchSourceBuilder, "id", "name", storeName);
                if (esCriteria != null) {
                    logger.info("ES拼接前查询条件:" + criteria.toString());
                    criteria.withAnd(esCriteria);
                    logger.info("ES拼接后查询条件:" + criteria.toString());
                } else {
                    return new ListResult(0, new ArrayList<>());
                }
            } catch (Exception ex) {
                criteria.with(Store.NAME).like("%" + storeName + "%");
                logger.error("visit es error:" + ex.getMessage(), ex);
                criteria.with(Store.NAME).like("%" + storeName + "%");
            }
        }

        //获取商户ID列表
        Set<String> merchantIds = getMerchantIds(queryFilter);
        if (CollectionUtils.isNotEmpty(merchantIds)) {
            criteria.with(Store.MERCHANT_ID).in(merchantIds);
        }
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(Store.SN, Store.SN);
//            put(Store.NAME, Store.NAME);
            put(Store.STATUS, Store.STATUS);
            put(Store.RANK, Store.RANK);
            put(Store.CONTACT_PHONE, Store.CONTACT_PHONE);
            put(Store.CONTACT_CELLPHONE, Store.CONTACT_CELLPHONE);
            put(Store.CLIENT_SN, Store.CLIENT_SN);
            put(Store.MERCHANT_ID, Store.MERCHANT_ID);
            put(Store.SOLICITOR_ID, Store.SOLICITOR_ID);
            put(Store.VENDOR_ID, Store.VENDOR_ID);
            put(Store.VERIFY_STATUS, Store.VERIFY_STATUS);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});

        //以代码简练的方式从新封装 查询参数 by lijunjie
        //SimpleConditionUtil.setCriteriaParam(criteria,queryFilter,"store");

        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = storeDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = storeDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        list.forEach(item -> bankInfoBiz.resolveDistrict(item));
        return new ListResult(count, list);
    }

    @Override
    public List<Map<String, Object>> getChangeStore(long beginMtime, long endMtime) {
        Filter filter = storeDao.filter(Criteria.where(ConstantUtil.KEY_MTIME).ge(beginMtime)
                .with(ConstantUtil.KEY_MTIME).le(endMtime));
        List<Map<String, Object>> list = CollectionUtil.iterator2list(filter.fetchAll());
        list.forEach(item -> bankInfoBiz.resolveDistrict(item));
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    @Override
    public int deleteStoreById(String storeId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(storeId);
        Map<String, Object> store = storeDao.filter(criteria).fetchOne();
        storeExtService.deleteStoreExtByStoreId(storeId);
        storeDao.delete(storeId);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_STORE, store);
        return 1;
    }

    @Override
    public Map<String, Object> getStoreByTerminalSn(String terminalSn) {
        Map<String, Object> terminal = businssCommonService.getTerminalMinimalInfoBySn(terminalSn, true);
        return getStore(com.wosai.pantheon.util.MapUtil.getString(terminal, Terminal.STORE_ID));
    }

    @Override
    public Set<String> getStoreContentsByIds(List<String> ids) {
        List<String> columns = Arrays.asList(Store.OPERATION_CONTENTS);
        Set<String> contents = Sets.newHashSet();
        for (List<String> subIds : Lists.partition(ids, 1000)) {
            Criteria criteria = Criteria.where(DaoConstants.ID).in(subIds);
            for (Iterator<Map<String, Object>> iterator = storeDao.filter(criteria, columns).fetchAll(); Objects.nonNull(iterator) && iterator.hasNext(); ) {
                String content = com.wosai.pantheon.util.MapUtil.getString(iterator.next(), Store.OPERATION_CONTENTS);
                if (StringUtils.isEmpty(content)) {
                    continue;
                }
                contents.addAll(Arrays.asList(content.split(",")));
            }
        }
        return contents;
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    @Cacheable("getStoreProvinceBySn")
    public String getStoreProvinceBySn(String sn) {
        Map<String, Object> store = storeDao.filter(Criteria.where(Store.SN).is(sn), CollectionUtil.hashSet(Store.PROVINCE)).fetchOne();
        return com.wosai.pantheon.util.MapUtil.getString(store, Store.PROVINCE);
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public List<MerchantStoreResponse> getMerchantStoreIdListByMerchantIds(List<String> merchantIds, int maxStoreSize) {
        if (CollectionUtils.isEmpty(merchantIds)) {
            return Lists.newArrayList();
        }
        List<MerchantStoreResponse> responses = Lists.newArrayList();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(1);
        pageInfo.setPageSize(maxStoreSize);
        merchantIds.forEach(merchantId -> {
            List<String> storeIdList = this.getStoreIdListByMerchantId(merchantId, pageInfo);
            if (CollectionUtils.isEmpty(storeIdList)) {
                return;
            }
            MerchantStoreResponse response = new MerchantStoreResponse();
            response.setMerchantId(merchantId);
            response.setStoreIds(storeIdList);
            responses.add(response);
        });

        return responses;
    }

    /**
     * 获取商户ID列表 新老兼容
     *
     * @param queryFilter
     * @return
     */
    private Set<String> getMerchantIds(Map queryFilter) {
        Set<String> merchantIds = Sets.newHashSet();
        //兼容老业务
        String merchantId = BeanUtil.getPropString(queryFilter, Store.MERCHANT_ID);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(merchantId)) {
            merchantIds.add(merchantId);
        }
        //新业务都用列表形式
        List<String> merchantIds0 = (List<String>) BeanUtil.getProperty(queryFilter, "merchant_ids");
        if (CollectionUtils.isNotEmpty(merchantIds0)) {
            merchantIds.addAll(merchantIds0);
        }
        return merchantIds;
    }
}
