package com.wosai.upay.core.service;


import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.bean.request.MerchantAvailablePaywaysQueryRequest;
import com.wosai.upay.core.bean.response.GetTradeConfigurationResponse;
import com.wosai.upay.core.bean.response.MerchantAvailablePaywaysQueryResult;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.constant.PublicConstants;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.helper.CacheKeyHelper;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.PreRiskServiceImpl.UpayUtil;
import com.wosai.upay.core.service.cache.ParamCacheService;
import com.wosai.upay.core.service.redis.RedisService;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.core.util.CommonSwitchUtil;
import lombok.NoArgsConstructor;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;


@Service

@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class SupportServiceImpl implements SupportService{

    private static Logger logger = LoggerFactory.getLogger(SupportService.class);

    private static final String OPEN_PAYWAY_MERCHANT_CONFIG_KEY_TEMPLATE = "open_payway_mch_conf_%s_%s_%s";

    // 支付宝npos7 vender app id
    private static final String ALIPAY_NPOS7_VENDER_APP_APPID = "2024092000007714";

    private Dao<Map<String, Object>> storeDao;
    private Dao<Map<String, Object>> merchantDao;
    private Dao<Map<String, Object>> terminalDao;
    private Dao<Map<String, Object>> merchantConfigDao;

    Map<String, String> formalCheck = com.wosai.data.util.CollectionUtil.hashMap(MerchantConfig.B2C_STATUS, MerchantConfig.B2C_FORMAL,
            MerchantConfig.C2B_STATUS, MerchantConfig.C2B_FORMAL,
            MerchantConfig.WAP_STATUS, MerchantConfig.WAP_FORMAL,
            MerchantConfig.MINI_STATUS, MerchantConfig.MINI_FORMAL,
            MerchantConfig.H5_STATUS, MerchantConfig.H5_FORMAL,
            MerchantConfig.APP_STATUS, MerchantConfig.APP_FORMAL
        );
    Collection<String> basicStoreFields = CollectionUtil.hashSet(DaoConstants.ID,
            Store.SN,
            Store.NAME,
            Store.CITY,
            Store.STATUS,
            Store.CLIENT_SN,
            Store.MERCHANT_ID,
            Store.LONGITUDE,
            Store.LATITUDE,
            DaoConstants.CTIME,
            Store.EXTRA,
            Store.DISTRICT_CODE);
    Collection<String> basicTerminalFields = CollectionUtil.hashSet(DaoConstants.ID,
            Terminal.SN,
            Terminal.STORE_ID,
            Terminal.STATUS,
            Terminal.NAME,
            Terminal.DEVICE_FINGERPRINT,
            Terminal.VENDOR_APP_ID,
            Terminal.VENDOR_APP_APPID,
            Terminal.CATEGORY);
    private Interner<String> pool = Interners.newWeakInterner();

    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private RsaKeyService rsaKeyService;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private ParamCacheService paramCacheService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private RedisService redisService;

    @Autowired
    public SupportServiceImpl(DataRepository repository) {
        this.storeDao = repository.getStoreDao();
        this.merchantDao = repository.getMerchantDao();
        this.terminalDao = repository.getTerminalDao();
        this.merchantConfigDao = repository.getMerchantConfigDao();
    }


    @Override
    public Map getBasicParams(String wosaiStoreId, String terminalSn) {
        if(StringUtils.isEmpty(wosaiStoreId) && StringUtils.isEmpty(terminalSn)){
            throw new CoreInvalidParameterException("门店号和终端号不能同时为空!");
        }
        String hashKey = PublicConstants.UPAY_BASIC_PARAM + getMerchantSnByStoreSnOrTerminalSn(wosaiStoreId, terminalSn);
        String fieldKey = wosaiStoreId + "_" +terminalSn;
        return paramCacheService.loadParams(hashKey, fieldKey, () -> {
            return getBasicParamsFromDB(wosaiStoreId, terminalSn);
        });
    }

    @Override
    public Map getAllParams(String wosaiStoreId, String terminalSn, Integer payway, Integer subPayway) {
        return getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, TransactionParam.TRADE_APP_BASIC_PAY);
    }

    @Override
    public Map getAllParamsWithTradeApp(String wosaiStoreId, String terminalSn, Integer payway, Integer subPayway,
            String tradeApp) {
        tradeApp = StringUtil.empty(tradeApp) ? TransactionParam.TRADE_APP_BASIC_PAY : tradeApp;
        String merchantSn = getMerchantSnByStoreSnOrTerminalSn(wosaiStoreId, terminalSn);
        String redisCacheUpyAllParamKey = CacheKeyHelper.getRedisCacheUpyAllParamKey(merchantSn);
        String allFieldKey = StringUtil.join("_", wosaiStoreId, terminalSn, payway, subPayway, tradeApp);
        String finalTradeApp = tradeApp;
        Map<String, Object> context = paramCacheService.loadParams(redisCacheUpyAllParamKey, allFieldKey, () -> {
            Map<String, Object> result = getBasicParamsFromDB(wosaiStoreId, terminalSn);
            Map<String, Object> tradeParams = tradeConfigService.getTradeParamsWithTradeApp(payway, subPayway, finalTradeApp, result);
            result.putAll(tradeParams);
            result.put(TransactionParam.TRADE_APP, finalTradeApp);
            return result;
        });
        // 加载备用通道
        paramCacheService.loadMerchantBypassConfig(payway, subPayway, redisCacheUpyAllParamKey, allFieldKey, context,
                () -> tradeConfigService.getBypassTradeParams(context, payway, subPayway));
        return context;
    }

    @Override
    @Cacheable("RsaKeyData")
    public String getRsaKeyDataById(String rsaKeyId) {
        Map rsaKey = rsaKeyService.getRsaKey(rsaKeyId);
        return BeanUtil.getPropString(rsaKey, RsaKey.DATA);
    }

    @Override
    public String getAlipayV2AppAuthToken(String authAppId) {
        return tradeConfigService.getAlipayV2AppAuthToken(authAppId);
    }

    @Override
    public Map getAlipayV2AppAuthInfo(String authAppId, String storeId) {
        return tradeConfigService.getAlipayV2AppAuthInfo(authAppId, storeId);
    }

    @Override
    public void removeCachedParams(String merchantSn) {
        merchantSn = merchantSn.trim();
        redisService.removeCachedParams(merchantSn);
    }

    @Override
    public void removeCachedHuaBeiParams(String merchantSn) {
        Map<String,Object> merchantInfo = cacheService.getMerchantMinimalInfo(null,merchantSn);
        redisService.removeCachedHuabeiParams(BeanUtil.getPropString(merchantInfo,DaoConstants.ID));
    }

    @Override
    public void setCachedParamsFlag(boolean flag) {
        paramCacheService.setUsedCachedParams(flag);
    }


    @Override
    public Map getTranslateInfo(Map map) {
        return map;
    }

    @Override
    public List getTranslateInfoList(List list) {
        return list;
    }

    @Override
    public int getClearanceProvider(String merchantId) {
        return tradeConfigService.getClearanceProvider(merchantId);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getHuBeiTradeParams(String wosaiStoreId, String terminalSn) {
        Map<String, Object> params = getAllParams(wosaiStoreId, terminalSn, PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE);
        Map<String, Object> result = new HashMap<>();
        result.put(TransactionParam.ALIPAY_HUABEI_STATUS, BeanUtil.getPropInt(params, TransactionParam.ALIPAY_HUABEI_STATUS, TransactionParam.STATUS_CLOSED));
        result.put(TransactionParam.ALIPAY_HUABEI_LIMIT, BeanUtil.getPropInt(params, TransactionParam.ALIPAY_HUABEI_LIMIT, ApolloConfigurationCenterUtil.getHuabeiDefaultMinLimit()));
        result.put(TransactionParam.ALIPAY_HUABEI_PARAMS, BeanUtil.getProperty(params, TransactionParam.ALIPAY_HUABEI_PARAMS));
        result.put(TransactionParam.ALIPAY_CREDIT_PARAMS, BeanUtil.getProperty(params, TransactionParam.ALIPAY_CREDIT_PARAMS));
        return result;
    }

    @Override
    public Integer queryStatus(String merchantId, String switchKey) {
        return tradeConfigService.queryStatus(merchantId, switchKey);
    }

    private String getMerchantSnByStoreSnOrTerminalSn(String storeSn, String terminalSn) {
        String merchantSnCachekey = storeSn+"::"+terminalSn;
        Cache merchantSnEhcache = cacheManager.getCache(PublicConstants.TETMINAL_SN_OR_WOSAL_STORE_ID_MERCHANT_SN_EHCACHE);
        Element element = merchantSnEhcache.get(merchantSnCachekey);
        if(!Objects.isNull(element)){
            return element.getObjectValue().toString();
        }
        synchronized (pool.intern(merchantSnCachekey)){
            element = merchantSnEhcache.get(merchantSnCachekey);
            if(!Objects.isNull(element)){
                return element.getObjectValue().toString();
            }
            Map terminalOrStoreInfo = null;
            if(!StringUtil.empty(terminalSn)){
                terminalOrStoreInfo = businssCommonService.getTerminalMinimalInfoBySn(terminalSn);
            }else if(!StringUtil.empty(storeSn)){
                terminalOrStoreInfo = businssCommonService.getStoreMinimalInfoBySn(storeSn);
            }
            String merchantSn  = businssCommonService.getMerchantSnById(BeanUtil.getPropString(terminalOrStoreInfo, ConstantUtil.KEY_MERCHANT_ID));
            merchantSnEhcache.put(new Element(merchantSnCachekey, merchantSn));
            return merchantSn;
        }
    }

    @Override
    public boolean tradeIsFormal(String wosaiStoreId, String terminalSn, Integer payway, Integer subPayway) {
        String merchantSn = getMerchantSnByStoreSnOrTerminalSn(wosaiStoreId, terminalSn);
        if(StringUtil.empty(merchantSn)) {
            logger.warn("get merchant fail, terminal_sn: {}, store_sn: {}", terminalSn, wosaiStoreId);
            return false;
        }
        String merchantId = MapUtil.getString(cacheService.getMerchantMinimalInfo(null, merchantSn), DaoConstants.ID);
        Boolean isFormal = redisService.getMerchantIsFormalFromHash(StringUtil.join("", PublicConstants.UPAY_FORMAL, merchantSn), StringUtil.join("-", payway, subPayway));
        if(isFormal == null) {
            if(payway == null || subPayway == null) {
                isFormal = true;
                // 交易商户是否是微信和支付宝直连
                Criteria cr = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).in(UpayUtil.PAYWAY_ALIPAY, UpayUtil.PAYWAY_ALIPAY2, UpayUtil.PAYWAY_WEIXIN);
                Iterator<Map<String, Object>> merchantConfigs = merchantConfigDao.filter(cr).fetchAll();
                if(merchantConfigs != null) {
                    boolean alipayFormal = false;
                    while(merchantConfigs.hasNext()) {
                        Map<String, Object> merchantConfig = merchantConfigs.next();
                        boolean tmpFormal = true;
                        for (String statusKey : formalCheck.keySet()) {
                            if(MapUtil.getIntValue(merchantConfig, statusKey) == MerchantConfig.STATUS_OPENED) {
                                tmpFormal = tmpFormal && MapUtil.getBooleanValue(merchantConfig, formalCheck.get(statusKey));
                            }
                            if(!tmpFormal) {
                                break;
                            }
                        }
                        // 支付宝1.0 直连和支付宝2.0互斥，只要有1个是直连就看做是直连
                        if(MapUtil.getInteger(merchantConfig, MerchantConfig.PAYWAY) == UpayUtil.PAYWAY_ALIPAY || MapUtil.getInteger(merchantConfig, MerchantConfig.PAYWAY) == UpayUtil.PAYWAY_ALIPAY2) {
                            alipayFormal = alipayFormal || tmpFormal;
                        }else {
                            isFormal = isFormal && tmpFormal;
                        }

                        if(!isFormal) {
                            break;
                        }
                    }
                    isFormal = isFormal && alipayFormal;
                }
            } else {
                isFormal = false;
                Map<String, Object> allParams = null;
                try {
                    allParams = getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, TransactionParam.TRADE_APP_BASIC_PAY);
                }catch (Exception e) {
                    logger.warn("error in getAllParamsWithTradeApp", e);
                }
                if(allParams != null && UpayUtil.isFormal(allParams)) {
                    isFormal = true;
                }
            }
            redisService.setMerchantIsFormalToHash(StringUtil.join("", PublicConstants.UPAY_FORMAL, merchantSn), StringUtil.join("-", payway, subPayway), isFormal);
        }
        return isFormal;
    }

    @Override
    public GetTradeConfigurationResponse getTradeConfiguration(String wosaiStoreId, String terminalSn, Integer payway, Integer subPayway, String tradeAppId) {
        Map<String,Object> params = getAllParamsWithTradeApp(wosaiStoreId, terminalSn, payway, subPayway, tradeAppId);
        GetTradeConfigurationResponse response = new GetTradeConfigurationResponse();
        Integer provider = MapUtil.getInteger(params, TransactionParam.PROVIDER);
        response.setProvider(provider);
        response.setDirect(provider == null);
        boolean formal = true;
        for (String key : params.keySet()) {
            if(key != null && key.endsWith(CoreCommonConstants.SUFFIX_TRADE_PARAMS)){
                Map<String,Object> tradeConfig = MapUtil.getMap(params, key);
                formal = !MapUtil.getBooleanValue(tradeConfig, TransactionParam.LIQUIDATION_NEXT_DAY, false);
            }
        }
        response.setFormal(formal);
        boolean supportPlatformCoupon = false;
        if(!formal){
            int acquirer = MapUtil.getIntValue(params, TransactionParam.CLEARANCE_PROVIDER);
            Map<String, Object> metaAcquirer = businssCommonService.getMetaAcquirerById(acquirer);
            supportPlatformCoupon = Objects.equals(MapUtil.getIntValue(metaAcquirer, MetaAcquirer.SUPPORT_PLATFORM_COUPON, MetaAcquirer.NOT_SUPPORT), MetaAcquirer.SUPPORT);
        }
        response.setSupportPlatformCoupon(supportPlatformCoupon);
        return response;
    }

    /**
     * 判断当前商户号是否开通指定支付通道
     * <p>
     * Notice: 为了防止缓存穿透，这里如果DB没有数据也会写缓存
     *
     * @param merchantSn 商户SN
     * @param payway     支付源
     * @param subPayway  支付方式
     * @return
     */
    @Override
    public Map<String, Object> isOpenPaywayWithMerchant(String merchantSn, Integer payway, Integer subPayway) {
        if (StringUtils.isBlank(merchantSn)) {
            throw new CoreInvalidParameterException("商户SN为空");
        }
        if (Objects.isNull(payway)) {
            throw new CoreInvalidParameterException("支付源为空");
        }
        if (Objects.isNull(subPayway)) {
            throw new CoreInvalidParameterException("支付方式为空");
        }
        String cacheField = "IsOpenPayway_" + payway + "_" + subPayway;
        String cacheKey = CacheKeyHelper.getRedisCacheUpyAllParamKey(merchantSn);
        Boolean result = redisService.getMerchantIsFormalFromHash(cacheKey, cacheField);
        if (Objects.isNull(result)) {
            Map<String, Object> config = loadOpenPaywayWithMerchant(merchantSn, payway, subPayway);
            result = MapUtil.getBooleanValue(config, "isOpenPayway");
        }
        return ImmutableMap.of("is_active", result ? "1" : "0");
    }

    @Override
    public Map<String, Object> getTradeParamsWithMerchant(String merchantSn, Integer payway, Integer subPayway) {
        if (StringUtils.isBlank(merchantSn)) {
            throw new CoreInvalidParameterException("商户SN为空");
        }
        if (Objects.isNull(payway)) {
            throw new CoreInvalidParameterException("支付源为空");
        }
        if (Objects.isNull(subPayway)) {
            throw new CoreInvalidParameterException("支付方式为空");
        }
        //配置未开启，则直接返回
        Map<String, Object> openPawayResult = isOpenPaywayWithMerchant(merchantSn, payway, subPayway);
        if (!Objects.equals("1", MapUtil.getString(openPawayResult, "is_active"))) {
            return null;
        }
        String cacheField = "TradeParams_" + payway + "_" + subPayway;
        String cacheKey = CacheKeyHelper.getRedisCacheUpyAllParamKey(merchantSn);
        Map<String, Object> result = redisService.getMapValueFromHash(cacheKey, cacheField);
        if (Objects.isNull(result)) {
            Map<String, Object> config = loadOpenPaywayWithMerchant(merchantSn, payway, subPayway);
            result = MapUtil.getMap(config, "params");
        }
        return result;
    }

    @Override
    public Map getMerchantSwitchMchTime(String merchantId) {
        Map result = new HashMap();
        Map merchantUpdateMchTime = redisService.getMerchantUpdateMchTime(merchantId);
        if (merchantUpdateMchTime == null) {
            result = tradeConfigService.getMerchantSwitchMchTime(merchantId);
            redisService.setMerchantUpdateMchTime(merchantId, result);
        }
        return result;
    }

    @Override
    public List<MerchantAvailablePaywaysQueryResult> queryMerchantAvailablePayways(MerchantAvailablePaywaysQueryRequest request) {
        String merchantId = request.getMerchantId();
        Integer subPayway = request.getSubPayway();
        List<Integer> assignPayways = request.getAssignPayways();
        if (Objects.isNull(merchantId)) {
            throw new CoreInvalidParameterException("商户ID不能为空");
        }
        if (Objects.isNull(subPayway)) {
            throw new CoreInvalidParameterException("子支付方式不能为空");
        }
        if (CollectionUtils.isEmpty(assignPayways)) {
            throw new CoreInvalidParameterException("指定支付方式列表不能为空");
        }
        Map<String, Object> merchant = cacheService.getMerchantMinimalInfo(merchantId, null);
        String merchantSn = MapUtils.getString(merchant, Merchant.SN);
        if (Objects.isNull(merchantSn)) {
            throw new CoreInvalidParameterException("商户号不存在");
        }
        List<MerchantAvailablePaywaysQueryResult> results = Lists.newArrayList();
        for (Integer payway : assignPayways) {
            Map<String, Object> result = loadOpenPaywayMerchantConfig(merchantSn, payway, subPayway);
            boolean isOpenPayway = MapUtils.getBooleanValue(result, "isOpenPayway");
            MerchantAvailablePaywaysQueryResult queryResult;
            if (isOpenPayway) {
                queryResult = new MerchantAvailablePaywaysQueryResult();
                queryResult.setPayway(payway);
                queryResult.setProvider(MapUtils.getInteger(result, MerchantConfig.PROVIDER));
                results.add(queryResult);
            }
        }
        return results;
    }

    @Override
    public boolean isEnabledAutoVerification(String storeSn, String terminalSn) {
        Map basicParam = getBasicParams(storeSn, terminalSn);
        if (MapUtils.isEmpty(basicParam)) {
            logger.warn("商户基础交易参数为空, storeSn={}, terminalSn={}", storeSn, terminalSn);
            return false;
        }

        String commonSwitch = MapUtil.getString(basicParam, TransactionParam.COMMON_SWITCH);
        return CommonSwitchUtil.isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_AUTO_VERIFICATION);
    }

    @Override
    public Map get24InstallmentRateConfig(Map params) {
        return ApolloConfigurationCenterUtil.getDefault24InstallmentRateConfig();
    }

    @Override
    public Map getProviderBizStatus(Integer provider, String providerMchId) {
        if (null == provider) {
            throw new CoreInvalidParameterException("provider不能为空!");
        }
        if (StringUtils.isBlank(providerMchId)) {
            throw new CoreInvalidParameterException("providerMchId不能为空!");
        }
        Map providerParams = redisService.getProviderBizStatus(provider, providerMchId);
        if (MapUtils.isEmpty(providerParams)) {
            providerParams = tradeConfigService.getProviderBizStatus(provider, providerMchId);
            redisService.setProviderBizStatus(provider, providerMchId, providerParams);
        }
        return providerParams;
    }

    @Override
    public void removeCacheProviderBizStatus(Integer provider, String providerMchId) {
        redisService.removeCacheProviderBizStatus(provider, providerMchId);
    }

    private Map<String, Object> loadOpenPaywayMerchantConfig(String merchantSn, Integer payway
            , Integer subPayway) {
        String cacheKey = CacheKeyHelper.getRedisCacheUpyAllParamKey(merchantSn);
        String field = String.format(OPEN_PAYWAY_MERCHANT_CONFIG_KEY_TEMPLATE, merchantSn
                , payway, subPayway);
        Map map  = redisService.getMapValueFromHash(cacheKey, field);
        if (Objects.nonNull(map)) {
            return map;
        }
        Map<String, Object> result = loadDbOpenPaywayWithMerchant(merchantSn, payway, subPayway);
        redisService.setMapValueToHash(cacheKey, field, result);
        return result;
    }

    private Map<String, Object> loadOpenPaywayWithMerchant(String merchantSn, Integer payway, Integer subPayway) {
        Map<String, Object> result = loadDbOpenPaywayWithMerchant(merchantSn, payway, subPayway);
        // 加缓存
        String isOpenPaywayCacheField = "IsOpenPayway_" + payway + "_" + subPayway;
        String paramsCacheField = "TradeParams_" + payway + "_" + subPayway;
        String cacheKey = CacheKeyHelper.getRedisCacheUpyAllParamKey(merchantSn);
        redisService.setMerchantIsFormalToHash(cacheKey, isOpenPaywayCacheField, MapUtil.getBooleanValue(result, "isOpenPayway"));
        redisService.setMapValueToHash(cacheKey, paramsCacheField, MapUtil.getMap(result, "params"));
        return result;
    }

    private Map<String, Object> loadDbOpenPaywayWithMerchant(String merchantSn, Integer payway, Integer subPayway) {
        Map<String, Object> result = MapUtil.hashMap("isOpenPayway", false, "params", null);
        String subPaywayStatusName = TradeConfigServiceImpl.subPaywayStatusColName.get(subPayway + "");
        String subPaywayFormalName = TradeConfigServiceImpl.subPaywayFormalColName.get(subPayway + "");
        if (Objects.isNull(subPaywayStatusName)) {
            return result;
        }
        Map<String, Object> merchant = cacheService.getMerchantMinimalInfo(null, merchantSn);
        //商户不存在
        if (MapUtil.isEmpty(merchant)) {
            return result;
        }
        String merchantId = MapUtil.getString(merchant, DaoConstants.ID);
        Map<String, Object> merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (MapUtil.isEmpty(merchantConfig)) {
            return result;
        }
        result.put(MerchantConfig.PROVIDER, MapUtils.getInteger(merchantConfig, MerchantConfig.PROVIDER));
        int status = MapUtil.getIntValue(merchantConfig, subPaywayStatusName, MerchantConfig.STATUS_OPENED);
        boolean formal = MapUtil.getBooleanValue(merchantConfig, subPaywayFormalName, Boolean.FALSE);
        Map<String, Object> tradeParams = MapUtil.getMap(merchantConfig, MerchantConfig.PARAMS);
        if (status != MerchantConfig.STATUS_OPENED) {
            return result;
        }
        if (MapUtil.isEmpty(tradeParams)) {
            return result;
        }
        //索迪斯特殊处理
        if (Objects.equals(Payway.SODEXO.getCode(), payway)) {
            Map sodexoParams = MapUtil.getMap(tradeParams, TransactionParam.SODEXO_TRADE_PARAMS);
            if (!formal || MapUtil.isEmpty(sodexoParams)) {
                return result;
            }
        }
        result.put("isOpenPayway", status);
        result.put("params", tradeParams);
        return result;
    }

    private Map<String, Object> getBasicParamsFromDB(String wosaiStoreId, String terminalSn){
        Map<String, Object> context = new HashMap<String, Object>();

        Map<String, Object> store = null;
        Map<String, Object> terminal = null;
        if (!StringUtil.empty(terminalSn)) {
            terminal = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn), basicTerminalFields).fetchOne();
            if(Objects.isNull(terminal)) {
                throw new CoreTerminalNotExistsException(String.format("终端%s在系统中不存在", terminalSn));
            }
            int terminalStatus = BeanUtil.getPropInt(terminal, Terminal.STATUS);
            if(!Objects.equals(terminalStatus ,Terminal.STATUS_ACTIVATED)) {
                if(Objects.equals(terminalStatus, Terminal.STATUS_UNACTIVATED)){
                    throw new CoreTerminalNotActivatedException(String.format("终端%s未激活", terminalSn));
                }else if(Objects.equals(terminalStatus, Terminal.STATUS_DISABLED)){
                    throw new CoreTerminalNotActivatedException(String.format("终端%s已禁用", terminalSn));
                }else {
                    throw new CoreTerminalNotActivatedException(String.format("终端%s状态异常", terminalSn));
                }
            }
            store = storeDao.getPart((String)terminal.get(Terminal.STORE_ID), basicStoreFields);
            if (store == null) {
                throw new CoreStoreNotExistsException(String.format("终端%s的门店在系统中不存在", terminalSn));
            }
        }else if (!StringUtil.empty(wosaiStoreId)){
            store = storeDao.filter(Criteria.where(Store.SN).is(wosaiStoreId), basicStoreFields).fetchOne();
            if (store == null) {
                throw new CoreStoreNotExistsException(String.format("门店%s在系统中不存在", wosaiStoreId));
            }
        }
        if ( BeanUtil.getPropInt(store, Store.STATUS) != Store.STATUS_ENABLED) {
            throw new CoreStoreStatusAbnormalException("门店状态异常");
        }
        if (store == null) {
            throw new CoreStoreNotExistsException(String.format("终端%s或门店%s在系统中不存在", terminalSn, wosaiStoreId));
        }
        Map<String, Object> merchant = merchantDao.getPart((String) store.get(Store.MERCHANT_ID),
                CollectionUtil.hashSet(Merchant.NAME, Merchant.VENDOR_ID, Merchant.STATUS
                        , Merchant.SN, Merchant.COUNTRY, Merchant.CURRENCY, Merchant.LONGITUDE
                        , Merchant.LATITUDE, Merchant.DISTRICT_CODE));
        if (merchant == null) {
            throw new CoreMerchantNotExistsException("商户在系统中不存在");
        }
        if (BeanUtil.getPropInt(merchant, Merchant.STATUS) != Merchant.STATUS_ENABLED){
            throw new CoreMerchantStatusAbnormalException("商户状态异常");
        }

        context = CollectionUtil.hashMap(TransactionParam.VENDOR_ID, merchant.get(Merchant.VENDOR_ID),
                TransactionParam.MERCHANT_ID, merchant.get(DaoConstants.ID),
                TransactionParam.MERCHANT_SN, merchant.get(Merchant.SN),
                TransactionParam.MERCHANT_NAME, merchant.get(Merchant.NAME),
                TransactionParam.MERCHANT_COUNTRY, merchant.get(Merchant.COUNTRY),
                TransactionParam.CURRENCY, merchant.get(Merchant.CURRENCY),
                TransactionParam.LONGITUDE, Optional.ofNullable(store.get(Store.LONGITUDE))
                        .orElse(merchant.get(Merchant.LONGITUDE)),
                TransactionParam.LATITUDE, Optional.ofNullable(store.get(Store.LATITUDE))
                        .orElse(merchant.get(Merchant.LATITUDE)),
                TransactionParam.DISTRICT_CODE, Optional.ofNullable(store.get(Store.DISTRICT_CODE))
                        .orElse(merchant.get(Merchant.DISTRICT_CODE)),
                TransactionParam.STORE_ID, store.get(DaoConstants.ID),
                TransactionParam.STORE_SN, store.get(Store.SN),
                TransactionParam.STORE_CLIENT_SN, store.get(Store.CLIENT_SN),
                TransactionParam.STORE_NAME, store.get(Store.NAME),
                TransactionParam.STORE_CITY, store.get(Store.CITY));
        String storeCity = MapUtil.getString(store, Store.CITY, "");
        long ctime = MapUtil.getLongValue(store, DaoConstants.CTIME);
        long startTime = 1688140800000l; //20230701
        if(ctime >= startTime && storeCity.contains("宁波")){
            String offset = MapUtil.getString(MapUtil.getMap(store, Store.EXTRA), "offsetInfo");
            if(!StringUtil.empty(offset)){
                context.put(Store.EXTRA, CollectionUtil.hashMap(
                        "offsetInfo", offset
                ));
            }
        }
        if (terminal != null) {
            context.put(TransactionParam.TERMINAL_ID, terminal.get(DaoConstants.ID));
            context.put(TransactionParam.TERMINAL_SN, terminal.get(Terminal.SN));
            context.put(TransactionParam.TERMINAL_NAME, terminal.get(Terminal.NAME));
            context.put(TransactionParam.TERMINAL_VENDOR_APP_APPID, terminal.get(Terminal.VENDOR_APP_APPID));
            context.put(TransactionParam.TERMINAL_CATEGORY, terminal.get(Terminal.CATEGORY));

            Object vendorAppId = terminal.getOrDefault(Terminal.VENDOR_APP_APPID, "");
            if (Objects.nonNull(vendorAppId) && ALIPAY_NPOS7_VENDER_APP_APPID.equals(vendorAppId.toString())) {
                context.put(TransactionParam.TERMINAL_DEVICE_FINGERPRINT, terminal.get(Terminal.DEVICE_FINGERPRINT));

            }
        }
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map<String,Object> params = (Map<String, Object>) BeanUtil.getProperty(tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null), MerchantConfig.PARAMS);
        boolean isProtectPayerPrivacy = BeanUtil.getPropBoolean(params, TransactionParam.IS_PROTECT_PAYER_PRIVACY, false);
        if (isProtectPayerPrivacy){
            context.put(TransactionParam.IS_PROTECT_PAYER_PRIVACY, isProtectPayerPrivacy);
        }
        int merchantDuplicateClientSnOfOrder = BeanUtil.getPropInt(params, TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER);
        if (merchantDuplicateClientSnOfOrder != 0){
            context.put(TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER, merchantDuplicateClientSnOfOrder);
        }
        int historyRefundFlag = BeanUtil.getPropInt(params, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG);
        if (historyRefundFlag != 0){
            context.put(TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, BeanUtil.getPropInt(params, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_CLOSE));
        }
        boolean refundNonSqbOrder = BeanUtil.getPropBoolean(params, TransactionParam.REFUND_NON_SQB_ORDER);
        if (refundNonSqbOrder){
            context.put(TransactionParam.REFUND_NON_SQB_ORDER,refundNonSqbOrder);
        }
        // 清算通道
        Integer clearanceProvicer = MapUtil.getInteger(params, TransactionParam.CLEARANCE_PROVIDER);
        if(null != clearanceProvicer) {
            context.put(TransactionParam.CLEARANCE_PROVIDER, clearanceProvicer);
        }
        //直连商户退款是否需要退手续费标记
        Map<String, Object> isNeedRefundFeeFlag = MapUtil.getMap(params, TransactionParam.IS_NEED_REFUND_FEE_FLAG);
        if (MapUtils.isNotEmpty(isNeedRefundFeeFlag)) {
            context.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, isNeedRefundFeeFlag);
        }
        // 商户级别的各种开关
        Map<String, Object> switches = tradeConfigService.getSwitches(merchantId);
        if(switches != null && !switches.isEmpty()){
            context.putAll(switches);
        }
        //通用开关默认值处理
        commonSwitchDefaultValueProcess(context);

        //门店级别的基本参数，如门店收款权限、门店限额等
        Map<String, Object> storeConfig = tradeConfigService.getStoreConfigByStoreIdAndPayway(MapUtil.getString(store, DaoConstants.ID), null);
        Map<String, Object> storeConfigParams = MapUtils.getMap(storeConfig, StoreConfig.PARAMS);
        if (storeConfigParams != null && !storeConfigParams.isEmpty()){
            for (String key : TradeConfigServiceImpl.storeParamKeys){
                if (storeConfigParams.containsKey(key)){
                    setTradeParamValueByKey(key, context, storeConfigParams);
                }
            }
        }
        // 商户收付通品牌信息
        String sftBrandId = MapUtil.getString(params, TransactionParam.SFT_BRAND_ID);
        if (!StringUtil.empty(sftBrandId)) {
            context.put(TransactionParam.SFT_BRAND_ID, sftBrandId);
        }
        return context;
    }

    private void commonSwitchDefaultValueProcess(Map<String, Object> context) {
        String commonSwitchStr = MapUtil.getString(context, TransactionParam.COMMON_SWITCH);
        if (StringUtils.isBlank(commonSwitchStr)) {
            commonSwitchStr = TradeConfigServiceImpl.COMMON_SWITCH_BASE;
        }
        String treatedCommonSwitch = ApolloConfigurationCenterUtil.replaceCommonSwitchDefaultValue(commonSwitchStr);
        context.put(TransactionParam.COMMON_SWITCH, treatedCommonSwitch);
    }

    /**
     * 设置参数
     *
     * @param key
     */
    private void setTradeParamValueByKey(String key, Map<String, Object> context, Map<String, Object> tradeParam) {
        Object value = tradeParam.get(key);
        if (value instanceof Map) {
            context.putAll((Map) value);
        } else {
            if (value != null) {
                context.put(key, value);
            }
        }
    }
}
