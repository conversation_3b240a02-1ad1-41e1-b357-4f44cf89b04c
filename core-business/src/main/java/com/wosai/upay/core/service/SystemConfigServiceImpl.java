package com.wosai.upay.core.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.upay.core.exception.CoreIOException;
import com.wosai.upay.core.model.SystemConfig;
import com.wosai.upay.core.repository.DataRepository;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * Created by jianfree on 18/9/16.
 */
@Service
@NoArgsConstructor
public class SystemConfigServiceImpl implements SystemConfigService {
    private static final Logger logger = LoggerFactory.getLogger(SystemConfigService.class);

    @Autowired
    private ObjectMapper objectMapper;
    private Dao<Map<String, Object>> systemConfigDao;

    @Autowired
    public SystemConfigServiceImpl(DataRepository repository){
        this.systemConfigDao = repository.getSystemConfigDao();
    }


    @Override
    @Cacheable("systemConfigContent")
    public  <T> T getSystemConfigContentByName(String name){
        Map map = this.systemConfigDao.filter(Criteria.where(SystemConfig.NAME).is(name)).fetchOne();
        Object content = BeanUtil.getProperty(map, SystemConfig.CONTENT);
        if(content instanceof byte[]){
            String contentStr = new String((byte[]) content);
            contentStr = contentStr.trim();
            if(contentStr.startsWith("{") || contentStr.startsWith("[")){
                try {
                    return (T)objectMapper.readValue((byte[]) content, Object.class);
                } catch (IOException e) {
                    throw new CoreIOException("SystemConfig: "+ name + " not a legal json string ");
                }
            }else{
                return (T)new String((byte [])content);
            }
        }
        return null;
    }


}
