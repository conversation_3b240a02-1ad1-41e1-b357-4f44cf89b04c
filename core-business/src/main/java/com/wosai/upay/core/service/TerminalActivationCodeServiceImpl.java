package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.upay.core.model.TerminalActivationCode;
import com.wosai.upay.core.repository.DataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Auther: hrx
 * @Date: 2019-11-04
 * @Description:
 * @version: 1.0
 */
@Service
@AutoJsonRpcServiceImpl
public class TerminalActivationCodeServiceImpl implements TerminalActivationCodeService {

    private DataRepository repository;

    private Dao<Map<String, Object>> terminalActivationCodeDao;

    @Autowired
    public TerminalActivationCodeServiceImpl(DataRepository repository) {
        this.repository = repository;
        this.terminalActivationCodeDao = repository.getTerminalActivationCodeDao();
    }

    @Override
    public Map getActivationInfoByCode(String activationCode) {
        Criteria criteria = Criteria.where(TerminalActivationCode.CODE).is(activationCode);
        Map<String, Object> activationeInfo = terminalActivationCodeDao.filter(criteria).fetchOne();
        return activationeInfo;
    }
}
