package com.wosai.upay.core.service;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.*;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.risk.bean.req.BlistSceneReq;
import com.wosai.risk.bean.vo.SceneValidateVO;
import com.wosai.risk.service.IRiskBlistSceneService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.bean.OrderBy.OrderType;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.request.*;
import com.wosai.upay.core.bean.response.ChangeShiftsBatchQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckInResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckOutResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsQueryResponse;
import com.wosai.upay.core.bean.response.SqbLklTerminalActivateVerifyResponse;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.constant.CoreConstant;
import com.wosai.upay.core.constant.PublicConstants;
import com.wosai.upay.core.databus.TerminalDataBusBiz;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.biz.MetaProviderBiz;
import com.wosai.upay.core.service.redis.RedisService;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.core.util.CoreErrorScenesConstant;
import com.wosai.upay.core.util.JsonUtil;
import com.wosai.upay.core.util.RedisLock;
import facade.ICustomerRelationValidateFacade;
import lombok.NoArgsConstructor;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class TerminalServiceImpl implements TerminalService {
    private static final Logger logger = LoggerFactory.getLogger(TerminalServiceImpl.class);


    @Autowired
    private RMQService rmqService;
    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private KeyGenerator keyGenerator;
    @Autowired
    private SnGenerator snGenerator;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private SupportService supportService;
    @Autowired
    private TerminalActivationCodeService terminalActivationCodeService;
    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    RedisLock redisLock;
    @Autowired
    private TerminalDataBusBiz terminalDataBusBiz;
    @Autowired
    IRiskBlistSceneService iRiskBlistSceneService;
    @Autowired
    ICustomerRelationValidateFacade iCustomerRelationValidateFacade;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    ChangeShiftsService changeShiftsService;
    @Autowired
    RedisService redisService;
    @Autowired
    MetaProviderBiz metaProviderBiz;

    private DataRepository repository;
    private Dao<Map<String, Object>> terminalDao;
    private Dao<Map<String, Object>> terminalActivationCodeDao;
    private Dao<Map<String, Object>> storeConfigDao;
    private Dao<Map<String, Object>> merchantConfigDao;
    private Dao<Map<String, Object>> vendorAppDao;

    private SecureRandom random = new SecureRandom();
    private int genActivationCodeMaxRetries = 10;

    public static final String MERCHANT_NAME = "merchant_name";
    public static final String MERCHANT_SN = "merchant_sn";
    public static final String STORE_NAME = "store_name";
    public static final String STORE_SN = "store_sn";
    public static final String NEW_ACTIVATIONCODE_DELIMITER = "--";
    public static final String TERMINAL_NEXT_SECRET = "terminal_next_secret_";

    @Autowired
    private BizLogFacade bizLogFacade;

    @Autowired
    public TerminalServiceImpl(DataRepository repository) {
        this.repository = repository;
        this.terminalDao = repository.getTerminalDao();
        this.terminalActivationCodeDao = repository.getTerminalActivationCodeDao();
        this.storeConfigDao = repository.getStoreConfigDao();
        this.merchantConfigDao = repository.getMerchantConfigDao();
        this.vendorAppDao = repository.getVendorAppDao();
    }

    @Override
    public Map<String, Object> createActivationCode(String vendorSn,
                                                    String storeId,
                                                    long limits) {

        String uuid = uuidGenerator.nextUuid();
        return createActivationCode(vendorSn, storeId, limits, uuid);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map<String, Object> bindQrcodeTerminal(Map info) {
        String qrcode = BeanUtil.getPropString(info, Terminal.DEVICE_FINGERPRINT);
        String storeId = BeanUtil.getPropString(info, Terminal.STORE_ID);
        int typeValue = BeanUtil.getPropInt(info, Terminal.TYPE, Terminal.TYPE_WAP);
        Map storeInfo = businssCommonService.getStoreMinimalInfoById(storeId);
        Map terminal = terminalDao.filter(Criteria.where(Terminal.DEVICE_FINGERPRINT).is(qrcode).with(Terminal.STATUS).ne(Terminal.STATUS_UNACTIVATED)).fetchOne();;
        if (terminal != null) {
            throw new CoreInvalidParameterException("该门店码已进行过绑定");
        }
        String merchantId = BeanUtil.getPropString(storeInfo, Store.MERCHANT_ID);
        String clientSn = BeanUtil.getPropString(info, Terminal.CLIENT_SN);
        if (!StringUtil.empty(clientSn)) {
            terminal = terminalDao.filter(Criteria.where(Terminal.MERCHANT_ID).is(merchantId).with(Terminal.CLIENT_SN).is(clientSn)).fetchOne();
            if (terminal != null) {
                throw new CoreInvalidParameterException("外部终端号已绑定");
            }
        }

        String vendorAppAppid = BeanUtil.getPropString(info, Terminal.VENDOR_APP_APPID);
        verifyTerminalActivate(merchantId, vendorAppAppid);

        terminal = new HashMap();
        terminal.put(Terminal.DEVICE_FINGERPRINT, qrcode);
        terminal.put(Terminal.CLIENT_SN, clientSn);
        terminal.put(Terminal.STORE_ID, storeId);

        // 将商户的solicitor_id、vendor_id赋值给终端
        Map merchantMinInfo = businssCommonService.getMerchantMinimalInfoById(merchantId);
        String vendorId = BeanUtil.getPropString(merchantMinInfo, Terminal.VENDOR_ID);
        if (vendorId == null) {
            throw new CoreInvalidParameterException("商户信息异常，激活失败");
        }

        terminal.put(Terminal.MERCHANT_ID, merchantId);
        terminal.put(Terminal.SOLICITOR_ID, merchantMinInfo.get(Merchant.SOLICITOR_ID));
        terminal.put(Terminal.VENDOR_ID, vendorId);

        // vendor_app_id、vendor_app_appid
        String vendorAppId;
        if (WosaiStringUtils.isNotEmpty(vendorAppId = BeanUtil.getPropString(info, Terminal.VENDOR_APP_ID))) {
            terminal.put(Terminal.VENDOR_APP_ID, vendorAppId);
        }
        if (WosaiStringUtils.isNotEmpty(vendorAppAppid)) {
            //获取终端类目
            Map<String, Object> vendorAppInfo = vendorAppDao.filter(Criteria.where(VendorApp.APPID).is(vendorAppAppid)).fetchOne();
            Object category = MapUtil.getObject(vendorAppInfo, VendorApp.CATEGORY);
            terminal.put(Terminal.CATEGORY, category);
            if (Objects.isNull(category)
                    && ApolloConfigurationCenterUtil.vendorAppCategoryCheck()
                    && ApolloConfigurationCenterUtil.isShouqianbaVendorId(MapUtil.getString(vendorAppInfo, VendorApp.VENDOR_ID))){
                throw new CoreTerminalActivationException("vendorAppId对应的终端类目为空，不允许激活。");
            }
            terminal.put(Terminal.VENDOR_APP_APPID, vendorAppAppid);
        }


        terminal.put(ConstantUtil.KEY_ID, uuidGenerator.nextUuid());
        terminal.put(Terminal.SN, snGenerator.nextTerminalSn(null));
        terminal.put(Terminal.TYPE, typeValue);
        terminal.put(Terminal.NAME, BeanUtil.getPropString(info, Terminal.NAME));
        terminal.put(Terminal.STATUS, Terminal.STATUS_ACTIVATED);
        terminalDao.save(terminal);
        removeCachedParamsByTerminal(BeanUtil.getPropString(terminal, ConstantUtil.KEY_ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminal);
        terminalDataBusBiz.insert(getTerminal(BeanUtil.getPropString(terminal, ConstantUtil.KEY_ID)));
        return terminal;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void unbindQrcodeTerminal(String terminalId) {
        Map info = getTerminal(terminalId);
        if (WosaiMapUtils.isNotEmpty(info)) {
            if (isProhibitedUnbound(MapUtil.getString(info, Terminal.VENDOR_APP_APPID))) {
                throw new CoreTerminalBindErrorException("该类型终端禁止解绑");
            }
            int preStatus = BeanUtil.getPropInt(info, Terminal.STATUS, -1);
            Map terminal = CollectionUtil.hashMap(
                    ConstantUtil.KEY_ID, terminalId,
                    Terminal.CLIENT_SN, null,
                    Terminal.STATUS, Terminal.STATUS_UNACTIVATED
            );
            terminalDao.updatePart(terminal);
            cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminal);
            removeCachedParamsByTerminal(terminalId);
            terminalDataBusBiz.statusChange(info, preStatus, Terminal.STATUS_UNACTIVATED);
        }
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map<String, Object> createActivated(String vendorAppAppid,
                                               String storeSn,
                                               String clientSn,
                                               String deviceFp,
                                               String name,
                                               String osVer,
                                               String sdkVer,
                                               String longitude,
                                               String latitude,
                                               Object extra) {

        Map<String, Object> vendorApp = repository.getVendorAppDao().filter(Criteria.where(VendorApp.APPID).is(vendorAppAppid),
                CollectionUtil.hashSet(DaoConstants.ID, VendorApp.VENDOR_ID,
                        VendorApp.TYPE)).fetchOne();
        if (vendorApp == null) {
            throw new CoreVendorAppNotExistsException(String.format("服务商APP appid %s 不存在", vendorAppAppid));
        }
        String vendorAppId = (String) vendorApp.get(DaoConstants.ID);
        String vendorId = (String) vendorApp.get(VendorApp.VENDOR_ID);
        int vendorAppType = BeanUtil.getPropInt(vendorApp, VendorApp.TYPE);

        Map<String, Object> store = businssCommonService.getStoreMinimalInfoBySn(storeSn);
        String storeId = (String) store.get(DaoConstants.ID);

        String merchantId = (String) store.get(Store.MERCHANT_ID);
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        long merchantStatus = BeanUtil.getPropLong(merchant, Merchant.STATUS);
        if (merchantStatus != Merchant.STATUS_ENABLED){
            throw new CoreMerchantStatusAbnormalException("商户状态异常,终端不允许激活");
        }
        if(!ApolloConfigurationCenterUtil.isShouqianbaVendorId(vendorId) && !ApolloConfigurationCenterUtil.isShouqianbaCooperationAppIds(vendorAppAppid)){
            String storeVendorId = (String) store.get(Store.VENDOR_ID);
            if (!vendorId.equals(storeVendorId)) {
                throw new CoreVendorAppAcccessDeniedException(String.format("服务商APP不能为非同一服务商下的商户创建终端（vendorId = %s, storeVendorId = %s)", vendorId, storeVendorId));
            }
        }
        verifyTerminalActivate(merchantId, vendorAppAppid);
        String terminalId = uuidGenerator.nextUuid();
        if (StringUtil.empty(name)) {
            name = "New Term (createActivated) " + System.currentTimeMillis();
        }

        return activateNewTerminal(vendorId, vendorAppAppid,
                merchantId, storeId, terminalId,
                clientSn, deviceFp, name,
                osVer, sdkVer,
                longitude, latitude,
                vendorAppId, vendorAppType,
                extra, genSecret(terminalId));

    }

    @Override
    @Transactional(value = "transactionManager", isolation = Isolation.REPEATABLE_READ)
    public Map<String, Object> activate(String vendorSn,
                                        String code,
                                        String deviceId,
                                        String osInfo,
                                        String sdkVersion,
                                        String terminalType) {

        Map<String, Object> activationCodeInfo = findAndCheckActivationCode(vendorSn, code);
        useTerminalActivationCode(activationCodeInfo);
        String terminalId = BeanUtil.getPropString(activationCodeInfo, TerminalActivationCode.TERMINAL_ID);
        if (terminalId == null) {
            terminalId = uuidGenerator.nextUuid();
            String defaultTerminalName = (String) activationCodeInfo.get(TerminalActivationCode.DEFAULT_TERMINAL_NAME);
            String terminalName;
            if (StringUtil.empty(defaultTerminalName)) {
                terminalName = "New Term (activate) " + System.currentTimeMillis();
            } else {
                terminalName = defaultTerminalName;
            }
            String vendorId = (String) activationCodeInfo.get(TerminalActivationCode.VENDOR_ID);
            if (vendorId == null || vendorId.trim().length() == 0) {
                if (vendorSn == null || vendorSn.trim().length() == 0) {
                    throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_VENDOR_SN_ERROR, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
                } else {
                    vendorId = businssCommonService.getVendorIdBySn(vendorSn);
                }
            }
            return activateNewTerminal(vendorId,
                    vendorSn,
                    (String) activationCodeInfo.get(TerminalActivationCode.MERCHANT_ID),
                    (String) activationCodeInfo.get(TerminalActivationCode.STORE_ID),
                    terminalId,
                    null,     /* clientSn 旧的激活接口对于deviceId的定义很模糊，既是client_sn又是device_fingerprint */
                    deviceId, /* deviceFp 旧的激活接口对于deviceId的定义很模糊，既是client_sn又是device_fingerprint */
                    terminalName,
                    osInfo,
                    sdkVersion,
                    null, /* longitude */
                    null, /* latitude */
                    null, /* vendorAppId 旧的激活接口无法关联设备和服务商APP */
                    terminalType,
                    null, /* extra */
                    genSecret(terminalId));

        } else {
            return activateExistingTerminalById(terminalId,
                    genSecret(terminalId),
                    deviceId,
                    osInfo,
                    sdkVersion,
                    terminalType);

        }
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void disableTerminal(String terminalId) {
        Map terminalMininfo = businssCommonService.getTerminalMinimalInfoById(terminalId);
        int preStatus = BeanUtil.getPropInt(terminalMininfo, Terminal.STATUS, -1);
        if (preStatus != Terminal.STATUS_ACTIVATED) {
            throw new CoreOnlyStatusEnabledCouldDisableException(CoreException.getCodeDesc(CoreException.CODE_ONLY_STATUS_ACTIVATED_COULD_DISABLE));
        }
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(terminalMininfo, DaoConstants.ID),
                Terminal.STATUS, Terminal.STATUS_DISABLED
        );
        terminalDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminalMininfo);
        removeCachedParamsByTerminal(terminalId);
        terminalDataBusBiz.statusChange(terminalMininfo, preStatus, Terminal.STATUS_DISABLED);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void disableTerminalAndLog(String terminalId, OpLogCreateRequest opLogCreateRequest) {
        Map terminalMininfoBefore = businssCommonService.getTerminalMinimalInfoById(terminalId);
        disableTerminal(terminalId);
        Map terminalMininfoAfter = businssCommonService.getTerminalMinimalInfoById(terminalId);
        String merchantId = MapUtils.getString(terminalMininfoAfter, Terminal.MERCHANT_ID);
        if (StringUtils.isBlank(opLogCreateRequest.getRemark())) {
            opLogCreateRequest.setRemark("禁用终端");
        }
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.TERMINAL_TEMPLATE_CODE, OpLog.TERMINAL_TABLE_NAME, OpLog.FIXED_TERMINAL_KEY_LIST, OpLog.TERMINAL_STATUS_LIST, OpLog.TERMINAL_DESC_MAP, terminalMininfoBefore, terminalMininfoAfter);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void enableTerminal(String terminalId) {
        Map terminalMinInfo = businssCommonService.getTerminalMinimalInfoById(terminalId);
        int preStatus = BeanUtil.getPropInt(terminalMinInfo, Terminal.STATUS, -1);
        if (!Objects.equals(preStatus, Terminal.STATUS_DISABLED)) {
            if (Objects.equals(preStatus, Terminal.STATUS_UNACTIVATED)) {
                throw new CoreTerminalNotActivatedException(String.format("终端%s未激活", terminalId));
            } else if (Objects.equals(preStatus, Terminal.STATUS_ACTIVATED)) {
                throw new CoreTerminalNotActivatedException(String.format("终端%s正常", terminalId));
            } else {
                throw new CoreTerminalNotActivatedException(String.format("终端%s状态异常", terminalId));
            }
        }

        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(terminalMinInfo, DaoConstants.ID),
                Terminal.STATUS, Terminal.STATUS_ACTIVATED
        );
        terminalDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminalMinInfo);
        removeCachedParamsByTerminal(terminalId);
        terminalDataBusBiz.statusChange(terminalMinInfo, preStatus, Terminal.STATUS_ACTIVATED);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void enableTerminalAndLog(String terminalId, OpLogCreateRequest opLogCreateRequest) {
        Map terminalMininfoBefore = businssCommonService.getTerminalMinimalInfoById(terminalId);
        enableTerminal(terminalId);
        Map terminalMininfoAfter = businssCommonService.getTerminalMinimalInfoById(terminalId);
        String merchantId = MapUtils.getString(terminalMininfoAfter, Terminal.MERCHANT_ID);
        if (StringUtils.isBlank(opLogCreateRequest.getRemark())) {
            opLogCreateRequest.setRemark("激活终端");
        }
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.TERMINAL_TEMPLATE_CODE, OpLog.TERMINAL_TABLE_NAME, OpLog.FIXED_TERMINAL_KEY_LIST, OpLog.TERMINAL_STATUS_LIST, OpLog.TERMINAL_DESC_MAP, terminalMininfoBefore, terminalMininfoAfter);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void unbindTerminal(String terminalId) {
        Map terminalMininfo = getTerminal(terminalId);
        if (isProhibitedUnbound(MapUtil.getString(terminalMininfo, Terminal.VENDOR_APP_APPID))) {
            throw new CoreTerminalBindErrorException("该类型终端禁止解绑");
        }
        int preStatus = BeanUtil.getPropInt(terminalMininfo, Terminal.STATUS, -1);
        if(preStatus == Terminal.STATUS_UNACTIVATED){
            throw new CoreTerminalStatusAbnormalException("终端已经被解绑，不允许再次解绑");
        }
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(terminalMininfo, DaoConstants.ID),
                Terminal.STATUS, Terminal.STATUS_UNACTIVATED,
                Terminal.DEVICE_FINGERPRINT, renameDeviceFpOnUnbindTerminal(terminalMininfo),
                Terminal.CLIENT_SN, renameClientSnOnUnbindTerminal(terminalMininfo)
        );
        terminalDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminalMininfo);
        removeCachedParamsByTerminal(terminalId);
        terminalDataBusBiz.statusChange(terminalMininfo, preStatus, Terminal.STATUS_UNACTIVATED);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void unbindTerminalBySn(String terminalSn) {
        Map terminal = getTerminalBySn(terminalSn);
        unbindQrcodeTerminal(MapUtil.getString(terminal, DaoConstants.ID));
    }

    private String renameDeviceFpOnUnbindTerminal(Map terminal) {
        if (BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT) == null) {
            return null;
        }
        String deviceFp = System.currentTimeMillis() + "-deleted" + BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT);
        terminal.put(Terminal.DEVICE_FINGERPRINT, deviceFp);
        return deviceFp.length() < CoreConstant.TERMINAL_DEVICE_FINGERPRINT_COLUMN_MAX_LENGTH ? deviceFp : deviceFp.substring(0, CoreConstant.TERMINAL_DEVICE_FINGERPRINT_COLUMN_MAX_LENGTH);
    }

    private String renameClientSnOnUnbindTerminal(Map terminal) {
        if (BeanUtil.getPropString(terminal, Terminal.CLIENT_SN) == null) {
            return null;
        }
        String clientSn = System.currentTimeMillis() + "-deleted" + BeanUtil.getPropString(terminal, Terminal.CLIENT_SN);
        terminal.put(Terminal.CLIENT_SN, clientSn);
        return clientSn.length() < CoreConstant.TERMINAL_CLIENT_SN_COLUMN_MAX_LENGTH ? clientSn : clientSn.substring(0, CoreConstant.TERMINAL_CLIENT_SN_COLUMN_MAX_LENGTH);
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map updateTerminal(Map terminal) {
        Map before = getTerminalInfo(DaoConstants.ID, BeanUtil.getPropString(terminal, DaoConstants.ID));
        CrudUtil.ignoreForUpdate(terminal, new String[]{Terminal.SN, Terminal.STATUS, Terminal.LAST_SIGNON_TIME, Terminal.CURRENT_SECRET, Terminal.LAST_SECRET, Terminal.STORE_ID, Terminal.MERCHANT_ID, Terminal.SOLICITOR_ID, Terminal.VENDOR_APP_ID, Terminal.VENDOR_APP_APPID, Terminal.VENDOR_ID});
        terminalDao.updatePart(terminal);
        Map updateRs = getTerminalInfo(DaoConstants.ID, BeanUtil.getPropString(terminal, DaoConstants.ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, updateRs);
        String merchantSn = businssCommonService.getMerchantSnById(BeanUtil.getPropString(before, Store.MERCHANT_ID));
        supportService.removeCachedParams(merchantSn);
        terminalDataBusBiz.update(before, updateRs);
        redisService.removeTerminalSecret(MapUtil.getString(before, Terminal.SN));
        return updateRs;
    }

    @Override
    public Map updateTerminalAndLog(Map terminal, OpLogCreateRequestV2 opLogCreateRequest) {
        String terminalId = BeanUtil.getPropString(terminal, ConstantUtil.KEY_ID);
        Map<String, Object> before = getTerminal(terminalId);
        Map after = updateTerminal(terminal);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest,
                MapUtils.getString(before, Terminal.MERCHANT_ID),
                null,
                OpLog.TERMINAL_TEMPLATE_CODE,
                OpLog.TERMINAL_TABLE_NAME,
                OpLog.FIXED_TERMINAL_KEY_LIST,
                OpLog.TERMINAL_STATUS_LIST,
                OpLog.TERMINAL_DESC_MAP,
                before,
                after);
        return after;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map updateTerminalName(Map terminal) {
        Map<String, Object> update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(terminal, ConstantUtil.KEY_TERMINAL_ID),
                ConstantUtil.KEY_NAME, BeanUtil.getPropString(terminal, ConstantUtil.KEY_NAME)
        );

        return updateTerminal(update);
    }


    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getSecret(String terminalSn) {
        Map terminalSecret = null;
        try {
            terminalSecret = redisService.getTerminalSecret(terminalSn);
        } catch (Exception e) {
            logger.info("get redis terminal secret fail", e);
        }
        if (terminalSecret == null) {
            try {
                SphU.entry("com.wosai.upay.core.service.TerminalService:getSecret(java.lang.String)",EntryType.IN,1,terminalSn);
                terminalSecret = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn),
                        Arrays.asList(Terminal.SN, Terminal.CURRENT_SECRET, Terminal.LAST_SECRET, Terminal.NEXT_SECRET)).fetchOne();
                if (terminalSecret == null) {
                    throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_NOT_EXIST, CoreBizException.CODE_TERMINAL_NOT_EXISTS, CoreTerminalNotExistsException.class.getName());
                }
                try {
                    redisService.putTerminalSecret(terminalSn, terminalSecret);
                } catch (Exception e) {
                    logger.info("set redis terminal secret fail", e);
                }
            } catch (com.alibaba.csp.sentinel.slots.block.BlockException e){
                throw new CoreDataAccessException("请求过于频繁,请稍后重试");
            } finally {
                SphU.entryEnd(1, terminalSn);
            }
        }
        return terminalSecret;
    }

    @SuppressWarnings("unchecked")
    @Transactional(value = "transactionManager", isolation = Isolation.REPEATABLE_READ)
    @Override
    public Map<String, Object> prepareCheckin(String terminalSn) {
        Map<String, Object> terminal = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn),
//                CollectionUtil.hashSet(DaoConstants.ID, Terminal.NEXT_SECRET, Terminal.STORE_ID, Terminal.STATUS)).fetchOne();
                CollectionUtil.hashSet(DaoConstants.ID, Terminal.NEXT_SECRET, Terminal.STORE_ID)).fetchOne();
        if (terminal == null) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_NOT_EXIST, CoreBizException.CODE_TERMINAL_NOT_EXISTS, CoreTerminalNotExistsException.class.getName());
        }
//        int terminalStatus = BeanUtil.getPropInt(terminal, Terminal.STATUS);
//        if (terminalStatus != Terminal.STATUS_ACTIVATED) {
//            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_NOT_ACTIVATE, CoreBizException.CODE_TERMINAL_NOT_ACTIVATED, CoreTerminalNotActivatedException.class.getName());
//        }
        redisService.removeTerminalSecret(terminalSn);
        String nextSecret = (String) terminal.get(Terminal.NEXT_SECRET);
        if (StringUtil.empty(nextSecret)) {
            //加锁,terminal_next_secret_terminalId,锁5s
            String lockKey = TERMINAL_NEXT_SECRET + MapUtils.getString(terminal, DaoConstants.ID);
            nextSecret = genSecret((String) terminal.get(DaoConstants.ID));
            if (redisLock.tryLock(lockKey, nextSecret, 5, TimeUnit.SECONDS)) {
                terminal.put(Terminal.NEXT_SECRET, nextSecret);
                terminalDao.updatePart(terminal);
            }else {
                nextSecret = redisLock.getRedisValue(lockKey);
                //当redis中获取不到时，再查一遍数据库
                if (StringUtil.empty(nextSecret)){
                    terminal = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn),
                            CollectionUtil.hashSet(DaoConstants.ID, Terminal.NEXT_SECRET, Terminal.STORE_ID)).fetchOne();
                    nextSecret = MapUtils.getString(terminal, Terminal.NEXT_SECRET);
                }
                if (StringUtil.empty(nextSecret)){
                    throw new CoreTerminalMissingNextSecretException("终端下一个密钥为空");
                }
            }
        }
        Map<String, Object> store = storeService.getStore(BeanUtil.getPropString(terminal, Terminal.STORE_ID));

        // 查询预授权配置
        Map<String, Object> storeConfig = storeConfigDao.filter(Criteria.where(StoreConfig.STORE_ID).is(BeanUtil.getPropString(store, DaoConstants.ID)).with(StoreConfig.PAYWAY).is(null)).fetchOne();
        Map depositConfig = (Map) BeanUtil.getNestedProperty(storeConfig, String.format("%s.%s", StoreConfig.PARAMS, TransactionParam.DEPOSIT));
        if (null == depositConfig) {
            Map<String, Object> merchantConfig = merchantConfigDao.filter(Criteria.where(MerchantConfig.MERCHANT_ID).is(BeanUtil.getPropString(store, Store.MERCHANT_ID)).with(StoreConfig.PAYWAY).is(null)).fetchOne();
            depositConfig = (Map) BeanUtil.getNestedProperty(merchantConfig, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.DEPOSIT));
        }

        return CollectionUtil.hashMap(Terminal.SN, terminalSn,
                Terminal.NEXT_SECRET, nextSecret,
                STORE_NAME, BeanUtil.getPropString(store, Store.NAME),
                STORE_SN, BeanUtil.getPropString(store, Store.SN),
                TransactionParam.DEPOSIT, depositConfig);
    }

    @SuppressWarnings("unchecked")
    @Transactional(value = "transactionManager", isolation = Isolation.REPEATABLE_READ)
    @Override
    public Map<String, Object> prepareCheckinWithTermEnvInfo(String terminalSn, Map<String, Object> extraEnvInfo) {
        Map<String, Object> terminal = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn),
                CollectionUtil.hashSet(DaoConstants.ID, Terminal.NEXT_SECRET, Terminal.STORE_ID)).fetchOne();
        if (terminal == null) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_NOT_EXIST, CoreBizException.CODE_TERMINAL_NOT_EXISTS, CoreTerminalNotExistsException.class.getName());
        }
        redisService.removeTerminalSecret(terminalSn);
        String nextSecret = (String) terminal.get(Terminal.NEXT_SECRET);
        if (StringUtil.empty(nextSecret)) {
            //加锁,terminal_next_secret_terminalId,锁5s
            String lockKey = TERMINAL_NEXT_SECRET + MapUtils.getString(terminal, DaoConstants.ID);
            nextSecret = genSecret((String) terminal.get(DaoConstants.ID));
            if (redisLock.tryLock(lockKey, nextSecret, 5, TimeUnit.SECONDS)) {
                terminal.put(Terminal.NEXT_SECRET, nextSecret);
            }else {
                nextSecret = redisLock.getRedisValue(lockKey);
                //当redis中获取不到时，再查一遍数据库
                if (StringUtil.empty(nextSecret)){
                    terminal = terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn),
                            CollectionUtil.hashSet(DaoConstants.ID, Terminal.NEXT_SECRET, Terminal.STORE_ID)).fetchOne();
                    nextSecret = MapUtils.getString(terminal, Terminal.NEXT_SECRET);
                }
                if (StringUtil.empty(nextSecret)){
                    throw new CoreTerminalMissingNextSecretException("终端下一个密钥为空");
                }
            }
        }
        if (extraEnvInfo != null) {
            Map<String, Object> extra = (Map<String, Object>) BeanUtil.getProperty(terminal, Terminal.EXTRA);
            if (null == extra) {
                extra = new HashMap<>();
            }
            Map<String, Object> inputEnvInfo = new HashMap<>();
            for (String k : Terminal.EXTRA_ENV_LIST) {
                inputEnvInfo.put(k, BeanUtil.getPropString(extraEnvInfo, k));
            }
            extra.put(Terminal.EXTRA_ENV_INFO, inputEnvInfo);
            terminal.put(Terminal.EXTRA, extra);
        }
        terminalDao.updatePart(terminal);
        Map<String, Object> store = storeService.getStore(BeanUtil.getPropString(terminal, Terminal.STORE_ID));
        // 查询预授权配置
        Map<String, Object> storeConfig = storeConfigDao.filter(Criteria.where(StoreConfig.STORE_ID).is(BeanUtil.getPropString(store, DaoConstants.ID)).with(StoreConfig.PAYWAY).is(null)).fetchOne();
        Map depositConfig = (Map) BeanUtil.getNestedProperty(storeConfig, String.format("%s.%s", StoreConfig.PARAMS, TransactionParam.DEPOSIT));
        if (null == depositConfig) {
            Map<String, Object> merchantConfig = merchantConfigDao.filter(Criteria.where(MerchantConfig.MERCHANT_ID).is(BeanUtil.getPropString(store, Store.MERCHANT_ID)).with(StoreConfig.PAYWAY).is(null)).fetchOne();
            depositConfig = (Map) BeanUtil.getNestedProperty(merchantConfig, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.DEPOSIT));
        }
        return CollectionUtil.hashMap(Terminal.SN, terminalSn,
                Terminal.NEXT_SECRET, nextSecret,
                STORE_NAME, BeanUtil.getPropString(store, Store.NAME),
                STORE_SN, BeanUtil.getPropString(store, Store.SN),
                TransactionParam.DEPOSIT, depositConfig);
    }

    @SuppressWarnings("unchecked")
    @Transactional(value = "transactionManager", isolation = Isolation.REPEATABLE_READ)
    @Override
    public Map<String, Object> commitCheckin(String terminalSn,
                                             String nextSecret) {
        Map terminal = getTerminalBySn(terminalSn);
        if (terminal == null) {
            throw new CoreTerminalNotExistsException("终端不存在");
        }

        String pendingNextSecret = (String) terminal.get(Terminal.NEXT_SECRET);
        if (StringUtil.empty(pendingNextSecret)) {
            throw new CoreTerminalMissingNextSecretException("终端下一个密钥为空，请先提交prepareCheckin请求。");
        }

        if (nextSecret != null && !pendingNextSecret.equals(nextSecret)) {
            throw new CoreTerminalObsoleteNextSecretException("你传过来的终端密钥太老了");
        }
        redisService.removeTerminalSecret(terminalSn);
        Map updateValue = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(terminal, DaoConstants.ID)
        );

        updateValue.put(Terminal.LAST_SECRET, terminal.get(Terminal.CURRENT_SECRET));
        updateValue.put(Terminal.CURRENT_SECRET, pendingNextSecret);
        updateValue.put(Terminal.NEXT_SECRET, null);
        terminalDao.updatePart(updateValue);

        terminalDataBusBiz.update(terminal, getTerminal(BeanUtil.getPropString(terminal, DaoConstants.ID)));

        //释放redis锁
        String lockKey = TERMINAL_NEXT_SECRET + MapUtils.getString(terminal, DaoConstants.ID);
        redisLock.unlock(lockKey, pendingNextSecret);
        return CollectionUtil.hashMap(Terminal.SN, terminalSn,
                Terminal.CURRENT_SECRET, pendingNextSecret);
    }


    @Override
    public Map getTerminalByTerminalId(String terminalId) {
        return terminalDao.get(terminalId);
    }

    @Override
    public Map getTerminalByTerminalSn(String terminalSn) {
        return terminalDao.filter(Criteria.where(Terminal.SN).is(terminalSn)).fetchOne();
    }

    @Override
    public Map getTerminalByDeviceFingerprint(String deviceFingerprint) {
        Map terminal = terminalDao.filter(Criteria.where(Terminal.DEVICE_FINGERPRINT).is(deviceFingerprint).with(Terminal.STATUS).is(Terminal.STATUS_ACTIVATED)).fetchOne();
        if(!MapUtils.isEmpty(terminal)){
            return terminal;
        }
        //不存在状态正常的，返回最近创建时间最晚的
        PageInfo pageInfo = new PageInfo();
        pageInfo.setOrderBy(Arrays.asList(
                new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        Filter<Map<String,Object>> filter = terminalDao.filter(Criteria.where(Terminal.DEVICE_FINGERPRINT).is(deviceFingerprint));
        PageInfoUtil.pagination(pageInfo,filter);
        return filter.fetchOne();
    }

    @Override
    public Map getTerminalActivationCodeByTerminalId(String terminalId) {
        return terminalActivationCodeDao.filter(Criteria.where(TerminalActivationCode.TERMINAL_ID).is(terminalId)).fetchOne();
    }

    @Override
    public ListResult getTerminals(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter) {
        if (StringUtil.empty(storeId) && StringUtil.empty(merchantId)) {
            throw new CoreInvalidParameterException("storeId and merchantId can not be both null");
        }
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, Integer.MAX_VALUE, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        }
        if (queryFilter == null) {
            queryFilter = new HashMap();
        }
        queryFilter.put(Terminal.MERCHANT_ID, merchantId);
        queryFilter.put(Terminal.STORE_ID, storeId);
        return findTerminals(pageInfo, queryFilter);
    }

    @Override
    public long countTerminals(String merchantId, String storeId, Map queryFilter) {
        if (StringUtil.empty(storeId) && StringUtil.empty(merchantId)) {
            throw new CoreInvalidParameterException("storeId and merchantId can not be both null");
        }
        if (queryFilter == null) {
            queryFilter = new HashMap();
        }
        queryFilter.put(Terminal.MERCHANT_ID, merchantId);
        queryFilter.put(Terminal.STORE_ID, storeId);
        Criteria criteria = getTerminalsCriteria(queryFilter);
        return terminalDao.filter(criteria).count();
    }

    @Override
    public ListResult getActivationCodes(String merchantId, String storeId, PageInfo pageInfo, Map queryFilter) {
        if (StringUtil.empty(storeId) && StringUtil.empty(merchantId)) {
            throw new CoreInvalidParameterException("storeId and merchantId can not be both null");
        }
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, Integer.MAX_VALUE, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        }
        String terminalSn = BeanUtil.getPropString(queryFilter, "terminalSn");
        String status = BeanUtil.getPropString(queryFilter, "status");
        Criteria criteria = new Criteria();
        if (!StringUtil.empty(merchantId)) {
            criteria.with(TerminalActivationCode.MERCHANT_ID).is(merchantId);
        }
        if (!StringUtil.empty(storeId)) {
            criteria.with(TerminalActivationCode.STORE_ID).is(storeId);
        }
        if (!StringUtil.empty(terminalSn)) {
            Map terminal = getTerminalByTerminalSn(terminalSn);
            if (terminal == null) {
                return new ListResult(0, new ArrayList());
            } else {
                criteria.with(TerminalActivationCode.TERMINAL_ID).is(BeanUtil.getPropString(terminal, DaoConstants.ID));
            }
        }
        if (!StringUtil.empty(status)) {
            criteria.with(TerminalActivationCode.STATUS).is(status);
        }
        long count = terminalActivationCodeDao.filter(criteria).count();
        if (pageInfo.getOrderBy() == null || pageInfo.getOrderBy().size() == 0) {
            pageInfo.setOrderBy(Arrays.asList(
                    new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)
            ));
        }
        if (pageInfo.getDateStart() != null) {
            criteria.with(DaoConstants.CTIME).ge(pageInfo.getDateStart());
        }
        if (pageInfo.getDateEnd() != null) {
            criteria.with(DaoConstants.CTIME).lt(pageInfo.getDateEnd());
        }
        Filter filter = terminalActivationCodeDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }


    @Override
    public Map getTerminal(String terminalId) {
        if (StringUtil.empty(terminalId)) {
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(terminalId);
        return terminalDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getTerminalBySn(String terminalSn) {
        if (StringUtil.empty(terminalSn)) {
            return null;
        }
        Criteria criteria = Criteria.where(Terminal.SN).is(terminalSn);
        return terminalDao.filter(criteria).fetchOne();
    }

    /**
     * 查终端激活码SQL.
     */
    private static final String SELECT_TERMINAL_ACTIVATION_CODE_SQL = "select " + ConstantUtil.KEY_TERMINAL_ID
            + ", " + TerminalActivationCode.CODE + " from terminal_activation_code where " + ConstantUtil.KEY_TERMINAL_ID + " in (:ids)";

    @Override
    public ListResult findTerminals(PageInfo pageInfo, Map queryFilter) {
        checkFindTerminals(pageInfo, queryFilter);
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 10, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        }
        Criteria criteria = getTerminalsCriteria(queryFilter);

        if (!StringUtil.empty(BeanUtil.getPropString(queryFilter, "store_name"))) {
            Map query = CollectionUtil.hashMap("store_name", BeanUtil.getPropString(queryFilter, "store_name"),
                    Terminal.MERCHANT_ID, BeanUtil.getPropString(queryFilter, Terminal.MERCHANT_ID));
            ListResult storeMap = storeService.findStores(new PageInfo(1, 1000), query);
            List<String> storeIds = new ArrayList<>();
            if (storeMap.getTotal() > 0) {
                for (Map store : storeMap.getRecords()) {
                    storeIds.add(BeanUtil.getPropString(store, com.wosai.data.dao.DaoConstants.ID));
                }
                criteria.with(Terminal.STORE_ID).in(storeIds);
            } else {
                return new ListResult(0, new ArrayList<>());
            }
        }
        if (!StringUtil.empty(BeanUtil.getPropString(queryFilter, "store_sn"))){
           String storeSn = BeanUtil.getPropString(queryFilter, "store_sn");
           Map store = storeService.getStoreByStoreSn(storeSn);
           if (store != null){
               criteria.with(Terminal.STORE_ID).is(store.get(com.wosai.data.dao.DaoConstants.ID));
           } else {
               return new ListResult(0, new ArrayList<>());

           }
        }
        if (MapUtils.getInteger(queryFilter, "terminal_category") != null) {
            Set<String> vendorAppAppIds = getVendorAppAppIds(MapUtils.getInteger(queryFilter, "terminal_category"));
            if (vendorAppAppIds.size() > 0) {
                criteria.with(Terminal.VENDOR_APP_APPID).in(vendorAppAppIds);
            } else {
                return new ListResult(0, new ArrayList<>());
            }
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = terminalDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = terminalDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        // 是否需要查出激活码
        if (list.size() != 0 && "yes".equals(queryFilter.get("showActivationCode"))) {
            Set<String> ids = new HashSet<>();
            for (Map map : list) {
                ids.add(BeanUtil.getPropString(map, ConstantUtil.KEY_ID));
            }
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource());
            MapSqlParameterSource parameters = new MapSqlParameterSource();
            parameters.addValue("ids", ids);
            List<Map<String, Object>> activationCodes = namedParameterJdbcTemplate.queryForList(SELECT_TERMINAL_ACTIVATION_CODE_SQL, parameters);
            Map<String, String> activationCodesMap = new HashMap<String, String>();
            for (Map<String, Object> code : activationCodes) {
                activationCodesMap.put(BeanUtil.getPropString(code, ConstantUtil.KEY_TERMINAL_ID), BeanUtil.getPropString(code, TerminalActivationCode.CODE));
            }
            if (activationCodesMap.size() != 0) {
                for (Map map : list) {
                    map.put("activationCode", activationCodesMap.get(BeanUtil.getPropString(map, ConstantUtil.KEY_ID)));
                    map.put("activation_code", map.get("activationCode"));
                }
            }
        }
        return new ListResult(count, list);
    }

    /**
     * 校验查询终端校验条件，某些情况下不允许查询
     * @param pageInfo
     * @param queryFilter
     */
    private void checkFindTerminals(PageInfo pageInfo, Map queryFilter){
        if(!ApolloConfigurationCenterUtil.checkFindTerminals()){
            return;
        }
        if(queryFilter == null){
            return;
        }
        if(queryFilter.size() == 4){
            String str = JsonUtil.toJsonStr(queryFilter);
            if("{\"store_id\":\"\",\"statuses\":[1,2],\"deleted\":0,\"vendor_app_appids_ne\":[\"2016111100000033\"]}".equals(str)){
                throw new CoreInvalidParameterException("参数错误，缺少门店信息");
            }
        }
        if(queryFilter.size() == 1){
            String str = JsonUtil.toJsonStr(queryFilter);
            if("{\"device_fingerprint\":\"1\"}".equals(str)){
                throw new CoreInvalidParameterException("参数错误，缺少其他查询条件");
            }
        }

    }


    @Override
    public Map updateActivationCode(Map activationCode) {
        Map update = new HashMap();
        // 需要获取到被更新的激活码id
        if (activationCode.get(DaoConstants.ID) != null) {
            update.put(DaoConstants.ID, activationCode.get(DaoConstants.ID));
        } else if (activationCode.get(TerminalActivationCode.CODE) != null) {
            Filter filter = terminalActivationCodeDao.filter(Criteria.where(TerminalActivationCode.CODE)
                    .is(BeanUtil.getPropString(activationCode, TerminalActivationCode.CODE)));
            List list = CollectionUtil.iterator2list(filter.fetchAll());
            if (list.size() == 0) {
                throw new CoreInvalidParameterException("找不到激活码");
            } else if (list.size() == 1) {
                update.put(DaoConstants.ID, ((Map) list.get(0)).get(DaoConstants.ID));
            } else {
                throw new CoreInvalidParameterException("找到多个激活码，无法更新");
            }
        } else {
            throw new CoreInvalidParameterException("id或code至少填写一个");
        }

        if (activationCode.get(TerminalActivationCode.REMAINING) != null) {
            update.put(TerminalActivationCode.REMAINING, activationCode.get(TerminalActivationCode.REMAINING));
        }
        if (activationCode.get(TerminalActivationCode.EXPIRE_TIME) != null) {
            update.put(TerminalActivationCode.EXPIRE_TIME, activationCode.get(TerminalActivationCode.EXPIRE_TIME));
        }
        if (activationCode.get(TerminalActivationCode.STATUS) != null) {
            update.put(TerminalActivationCode.STATUS, activationCode.get(TerminalActivationCode.STATUS));
        }

        terminalActivationCodeDao.updatePart(update);
        return terminalActivationCodeDao.filter(Criteria.where(DaoConstants.ID).is(BeanUtil.getPropString(update, DaoConstants.ID))).fetchOne();
    }

    private Criteria getTerminalsCriteria(Map queryFilter) {
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(Terminal.SN, Terminal.SN);
            put(Terminal.DEVICE_FINGERPRINT, Terminal.DEVICE_FINGERPRINT);
            put(Terminal.TYPE, Terminal.TYPE);
            put(Terminal.STATUS, Terminal.STATUS);
            put(Terminal.CLIENT_SN, Terminal.CLIENT_SN);
            put(Terminal.TARGET_TYPE, Terminal.TARGET_TYPE);
            put(Terminal.STORE_ID, Terminal.STORE_ID);
            put(Terminal.MERCHANT_ID, Terminal.MERCHANT_ID);
            put(Terminal.SOLICITOR_ID, Terminal.SOLICITOR_ID);
            put(Terminal.VENDOR_ID, Terminal.VENDOR_ID);
            put(Terminal.VENDOR_APP_ID, Terminal.VENDOR_APP_ID);
            put(Terminal.VENDOR_APP_APPID, Terminal.VENDOR_APP_APPID);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        if (queryFilter.get("statuses") != null && queryFilter.get("statuses") instanceof List) {
            criteria.with(Terminal.STATUS).in((ArrayList) queryFilter.get("statuses"));
        } else {
            criteria.with(Terminal.STATUS).ne(Terminal.STATUS_UNACTIVATED); // 不显示未激活/解绑的终端
        }
        if (queryFilter.get("store_ids") != null && queryFilter.get("store_ids") instanceof List) {
            criteria.with(Terminal.STORE_ID).in((ArrayList) queryFilter.get("store_ids"));
        }
        if (queryFilter.get("types") != null && queryFilter.get("types") instanceof List) {
            criteria.with(Terminal.TYPE).in((ArrayList) queryFilter.get("types"));
        }
        if (queryFilter.get("types_ne") != null && queryFilter.get("types_ne") instanceof List) {
            criteria.withOr(Criteria.where(Terminal.TYPE).nin((ArrayList) queryFilter.get("types_ne")), Criteria.where(Terminal.TYPE).is(null));
        }
        if (queryFilter.get("vendor_app_appids_ne") != null && queryFilter.get("vendor_app_appids_ne") instanceof List) {
            criteria.withOr(Criteria.where(Terminal.VENDOR_APP_APPID).nin((ArrayList) queryFilter.get("vendor_app_appids_ne")), Criteria.where(Terminal.VENDOR_APP_APPID).is(null));
        }
        if (queryFilter.get("vendor_app_appids") != null && queryFilter.get("vendor_app_appids") instanceof List) {
            criteria.with(Terminal.VENDOR_APP_APPID).in((ArrayList) queryFilter.get("vendor_app_appids"));
        }

        //遍历key ，拿到对应的category的appids
        Set<String> keys = Terminal.GET_TERMINAL_GATEGORY.keySet();
        for (String key : keys) {
            if (MapUtils.getBoolean(queryFilter, key, false)) {
                addVendorAppAppIds(Terminal.GET_TERMINAL_GATEGORY.get(key), criteria);
            }
        }

        String name = BeanUtil.getPropString(queryFilter, "name");
        if (!StringUtil.empty(name)) {
            criteria.with(Terminal.NAME).like("%" + name + "%");
        }
        return criteria;
    }

    private void addVendorAppAppIds(int category, Criteria criteria) {
        criteria.with(Terminal.VENDOR_APP_APPID).in(getVendorAppAppIds(category));
    }

    private Set<String> getVendorAppAppIds(int category) {
        List<Map> vendorApps = getVendorAppsByCategory(category);
        Set<String> vendorAppAppIds = new HashSet<>();
        for (Map vendorApp : vendorApps) {
            vendorAppAppIds.add(MapUtils.getString(vendorApp, VendorApp.APPID));
        }
        return vendorAppAppIds;
    }

    private String genSecret(String terminalId) {
        return keyGenerator.nextKey(terminalId);
    }

    private Map<String, Object> findTerminalActivationCode(String vendorId, String vendorSn, String code) {
        Criteria criteria = Criteria.where(TerminalActivationCode.CODE).is(code);
        if (vendorId != null) {
            criteria.with(TerminalActivationCode.VENDOR_ID).is(vendorId);
        } else if (vendorSn != null) {
            criteria.with(TerminalActivationCode.VENDOR_SN).is(vendorSn);
        }
        Map<String, Object> activationInfo = terminalActivationCodeDao.filter(criteria).fetchOne();

        if (activationInfo == null) {
            throw new CoreScenesException(CoreErrorScenesConstant.ACTIVATION_CODE_NOT_EXIST, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        }

        if (BeanUtil.getPropInt(activationInfo, TerminalActivationCode.STATUS) == TerminalActivationCode.STATUS_ACTIVE
                && BeanUtil.getPropLong(activationInfo, TerminalActivationCode.EXPIRE_TIME) < System.currentTimeMillis()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> update = CollectionUtil.hashMap(DaoConstants.ID, activationInfo.get(DaoConstants.ID),
                    TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_EXPIRED);
            terminalActivationCodeDao.updatePart(update);
            activationInfo.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_EXPIRED);

        }
        return activationInfo;
    }

    private void useTerminalActivationCode(Map<String, Object> activationInfo) {
        long remaining = BeanUtil.getPropLong(activationInfo, TerminalActivationCode.REMAINING);
        if (remaining <= 0) {
            return;
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> update = CollectionUtil.hashMap(DaoConstants.ID, activationInfo.get(DaoConstants.ID),
                "$inc", CollectionUtil.hashMap(TerminalActivationCode.REMAINING, -1));
        if (remaining == 1) {
            update.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_USED);
        }
        terminalActivationCodeDao.updatePart(update);
    }

    private void updateTerminalInfo(Map<String, Object> terminal, String secret, String deviceFp, String osInfo, String sdkVersion, String vendorAppId, Object terminalType, int status) {
        terminal.put(Terminal.CURRENT_SECRET, secret);
        if (deviceFp != null) {
            terminal.put(Terminal.DEVICE_FINGERPRINT, deviceFp);
        }
        if (osInfo != null) {
            terminal.put(Terminal.OS_VERSION, osInfo);
        }
        if (sdkVersion != null) {
            terminal.put(Terminal.SDK_VERSION, sdkVersion);
        }
        if (vendorAppId != null) {
            //获取vendorAppId的终端类目
            Map<String, Object> vendorAppInfo = vendorAppDao.filter(Criteria.where(DaoConstants.ID).is(vendorAppId)).fetchOne();
            Object category = MapUtil.getObject(vendorAppInfo, VendorApp.CATEGORY);
            terminal.put(Terminal.CATEGORY, category);
            if (Objects.isNull(category)
                    && ApolloConfigurationCenterUtil.vendorAppCategoryCheck()
                    && ApolloConfigurationCenterUtil.isShouqianbaVendorId(MapUtil.getString(vendorAppInfo, VendorApp.VENDOR_ID))){
                throw new CoreTerminalActivationException("vendorAppId对应的终端类目为空，不允许激活。");
            }
            terminal.put(Terminal.VENDOR_APP_ID, vendorAppId);
            terminal.put(Terminal.VENDOR_APP_APPID, businssCommonService.getVendorAppAppIdById(vendorAppId));
        }
        if (terminalType != null) {
            if (terminalType instanceof Integer) {
                terminal.put(Terminal.TYPE, terminalType);
            } else if (terminalType instanceof String) {
                terminal.put(Terminal.TYPE, Integer.parseInt((String) terminalType));
            }
        }
        terminal.put(Terminal.STATUS, status);
    }

    private Map<String, Object> activateExistingTerminalById(String terminalId, String secret, String deviceFp, String osInfo, String sdkVersion, String terminalType) {
        Map<String, Object> terminal = terminalDao.getPart(terminalId, CollectionUtil.hashSet(Terminal.SN,
                Terminal.DEVICE_FINGERPRINT,
                Terminal.NAME,
                Terminal.OS_VERSION,
                Terminal.SDK_VERSION,
                Terminal.TYPE));

        updateTerminalInfo(terminal, secret, deviceFp, osInfo, sdkVersion, null, terminalType, Terminal.STATUS_ACTIVATED);
        terminalDao.updatePart(terminal);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminal);
        removeCachedParamsByTerminal(terminalId);
        return terminal;
    }

    private Map<String, Object> activateNewTerminal(String vendorId, String vendorSnOrVendorAppAppid,
                                                    String merchantId, String storeId, String terminalId,
                                                    String clientSn, String deviceFp, String name,
                                                    String osInfo, String sdkVersion,
                                                    String longitude, String latitude,
                                                    String vendorAppId, Object terminalType,
                                                    Object extra, String secret) {
        @SuppressWarnings("unchecked")
        Map<String, Object> terminal = CollectionUtil.hashMap(DaoConstants.ID, terminalId,
                Terminal.SN, snGenerator.nextTerminalSn(vendorSnOrVendorAppAppid),
                Terminal.NAME, name,
                Terminal.CLIENT_SN, clientSn,
                Terminal.MERCHANT_ID, merchantId,
                Terminal.STORE_ID, storeId);

        if (vendorId != null) {
            terminal.put(Terminal.VENDOR_ID, vendorId);
        }
        if (longitude != null) {
            terminal.put(Terminal.LONGITUDE, longitude);
        }
        if (latitude != null) {
            terminal.put(Terminal.LATITUDE, latitude);
        }
        if (extra != null) {
            terminal.put(Terminal.EXTRA, extra);
        }
        if (name != null) {
            terminal.put(Terminal.NAME, name);
        }
        updateTerminalInfo(terminal, secret, deviceFp, osInfo, sdkVersion, vendorAppId, terminalType, Terminal.STATUS_ACTIVATED);
        try {
            terminalDao.save(terminal);
        } catch (DataIntegrityViolationException e) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_REPEAT_ACTIVATE, CoreBizException.CODE_CLIENT_SN_NOT_UNIQUE,
                    CoreClientSnNotUniqueException.class.getName());
        }
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminal);
        removeCachedParamsByTerminal(terminalId);
        Map<String, Object> merchant = merchantService.getMerchant(merchantId);
        Map<String, Object> store = storeService.getStore(storeId);
        terminal.put(MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
        terminal.put(MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
        terminal.put(STORE_NAME, BeanUtil.getPropString(store, Store.NAME));
        terminal.put(STORE_SN, BeanUtil.getPropString(store, Store.SN));
        terminalDataBusBiz.insert(getTerminal(terminalId));
        return terminal;
    }

    private String genActivationCode() {
        String prefixSevenNum = String.format("%07d", random.nextInt(10000000));
        int lastNum = Math.abs(prefixSevenNum.hashCode()) % 10;
        return String.format("%s%s", prefixSevenNum, lastNum);
    }

    private void saveActivationCode(Map<String, Object> activationCode) {
        for (int attempt = 0; attempt < genActivationCodeMaxRetries; ++attempt) {
            activationCode.put(TerminalActivationCode.CODE, genActivationCode());
            try {
                terminalActivationCodeDao.save(activationCode);
                return;
            } catch (DataIntegrityViolationException e) {
                logger.warn("attempt {} failed to save terminal activation code", attempt, e);
            }
        }
        throw new CoreTerminalActivationCodeGenerationException(String.format("经过%d次重试后无法创建激活码", genActivationCodeMaxRetries));
    }

    private Map<String, Object> createActivationCode(String vendorSn, String storeId, long limits, String uuid) {
        @SuppressWarnings("unchecked")
        Map<String, Object> activation = CollectionUtil.hashMap(DaoConstants.ID, uuid);
        Map<String, Object> storeMinInfo = businssCommonService.getStoreMinimalInfoById(storeId);
        if (vendorSn != null) {
            Map<String, Object> vendor = businssCommonService.getVendorMinimalInfoBySn(vendorSn);

            if (!vendor.get(DaoConstants.ID).equals(storeMinInfo.get(Store.VENDOR_ID))) {
                throw new CoreVendorAccessDenied(String.format("服务商序ID %s 和商户记录 %s 不匹配", vendor.get(DaoConstants.ID), storeMinInfo.get(Store.VENDOR_ID)));
            }
        } else {
            Map<String, Object> vendor = businssCommonService.getVendorMinimalInfoById(BeanUtil.getPropString(storeMinInfo, Store.VENDOR_ID));
            vendorSn = BeanUtil.getPropString(vendor, Vendor.SN);
        }
        activation.put(TerminalActivationCode.VENDOR_SN, vendorSn);
        activation.put(TerminalActivationCode.VENDOR_ID, storeMinInfo.get(Store.VENDOR_ID));
        activation.put(TerminalActivationCode.MERCHANT_ID, storeMinInfo.get(Store.MERCHANT_ID));
        activation.put(TerminalActivationCode.STORE_ID, storeId);
        activation.put(TerminalActivationCode.USAGE_LIMITS, limits);
        activation.put(TerminalActivationCode.REMAINING, limits);
        activation.put(TerminalActivationCode.EXPIRE_TIME, System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L);

        saveActivationCode(activation);
        return activation;
    }

    @Override
    public Map<String, Object> createActivationCodeV2(String solicitorSn,
                                                      String code,
                                                      String storeSn,
                                                      String defaultTerminalName,
                                                      long limit) {

        @SuppressWarnings("unchecked")
        Map<String, Object> activationCode = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid());
        Map<String, Object> store = repository.getStoreDao().filter(Criteria.where(Store.SN).is(storeSn),
                CollectionUtil.hashSet(DaoConstants.ID,
                        Store.VENDOR_ID,
                        Store.SOLICITOR_ID,
                        Store.MERCHANT_ID)).fetchOne();
        if (store == null) {
            throw new CoreStoreNotExistsException(String.format("门店SN %s 系统中不存在", storeSn));
        }
        if (solicitorSn != null) {
            String storeSolicitorId = BeanUtil.getPropString(store, Store.SOLICITOR_ID);
            if (storeSolicitorId != null) {
                Map<String, Object> storeSolicitor = repository.getSolicitorDao().getPart(storeSolicitorId,
                        CollectionUtil.hashSet(Solicitor.SN));
                if (storeSolicitor == null) {
                    throw new CoreSolicitorNotExistsException(String.format("门店的推广者ID %s 系统中不存在", storeSolicitorId));
                }
                String storeSolicitorSn = BeanUtil.getPropString(storeSolicitor, Solicitor.SN);

                if (!solicitorSn.equals(storeSolicitorSn)) {
                    throw new CoreSolicitorAccessDeniedException(String.format("推广者SN %s 和门店记录中的字段 %s 不匹配", solicitorSn, storeSolicitorSn));
                }
            }
        }

        /*
        String storeVendorId = BeanUtil.getPropString(store, Store.VENDOR_ID);
        Map<String, Object> storeVendor = repository.getVendorDao().getPart(storeVendorId,
                CollectionUtil.hashSet(Vendor.SN));
        if (storeVendor == null) {
            throw new CoreVendorNotExistsException(String.format("服务商ID %s 系统中不存在", storeVendorId));
        }
        activationCode.put(TerminalActivationCode.VENDOR_ID, storeVendorId);
        activationCode.put(TerminalActivationCode.VENDOR_SN, (String) storeVendor.get(Vendor.SN));
        */
        activationCode.put(TerminalActivationCode.MERCHANT_ID, (String) store.get(Store.MERCHANT_ID));
        activationCode.put(TerminalActivationCode.STORE_ID, (String) store.get(DaoConstants.ID));
        activationCode.put(TerminalActivationCode.USAGE_LIMITS, limit);
        activationCode.put(TerminalActivationCode.REMAINING, limit);
        activationCode.put(TerminalActivationCode.EXPIRE_TIME, ndaysFromNow(7));
        if (!StringUtil.empty(defaultTerminalName)) {
            activationCode.put(TerminalActivationCode.DEFAULT_TERMINAL_NAME, defaultTerminalName);
        }

        if (code != null) {
            activationCode.put(TerminalActivationCode.CODE, code);
            try {
                terminalActivationCodeDao.save(activationCode);
            } catch (DataIntegrityViolationException e) {
                logger.warn("failed to save terminal activation code using designated number {}", code, e);
                throw new CoreTerminalActivationCodeGenerationException(String.format("无法使用指定的数字%s创建激活码", code), e);
            }
        } else {
            saveActivationCode(activationCode);
        }

        return activationCode;
    }

    @Override
    public Map<String, Object> createActivationCodeV2AndLog(String solicitorSn, String code, String storeSn, String defaultTerminalName, long limit, OpLogCreateRequest opLogCreateRequest) {
        Map<String, Object> result = createActivationCodeV2(solicitorSn, code, storeSn, defaultTerminalName, limit);
        String usageLimits = BeanUtil.getPropString(result, TerminalActivationCode.USAGE_LIMITS);
        String remaining = BeanUtil.getPropString(result, TerminalActivationCode.REMAINING);
        String expireTime = BeanUtil.getPropString(result, TerminalActivationCode.EXPIRE_TIME);
        String merchant_id = BeanUtil.getPropString(result, TerminalActivationCode.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(result, TerminalActivationCode.STORE_ID);
        Map after = CollectionUtil.hashMap(
                TerminalActivationCode.CODE, code,
                TerminalActivationCode.DEFAULT_TERMINAL_NAME, defaultTerminalName,
                TerminalActivationCode.STATUS, "1",
                TerminalActivationCode.USAGE_LIMITS, usageLimits,
                TerminalActivationCode.REMAINING, remaining,
                TerminalActivationCode.EXPIRE_TIME, expireTime,
                TerminalActivationCode.STORE_ID, storeId,
                TerminalActivationCode.MERCHANT_ID, merchant_id
        );
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest,
                merchant_id,
                null,
                OpLog.ACTIVATION_CODE_TEMPLATE_CODE,
                OpLog.ACTIVATION_CODE_TABLE_NAME,
                OpLog.FIXED_ACTIVATION_CODE_KEY_LIST,
                OpLog.ACTIVATION_CODE_CHANGE_KEY_LIST,
                OpLog.ACTIVATION_CODE_DESC_MAP,
                new HashMap<>(),
                after);
        return result;
    }

    @Override
    public SqbLklTerminalActivateVerifyResponse sqbLklTerminalActivateVerify(String merchantId, String code, String vendorAppAppid) {
        if(merchantId == null && code != null){
            Map codeInfo = terminalActivationCodeService.getActivationInfoByCode(code);
            if(codeInfo == null){
                throw new CoreInvalidParameterException("激活码不存在");
            }
            merchantId = MapUtil.getString(codeInfo, TerminalActivationCode.MERCHANT_ID);
        }
        if(merchantId == null){
            throw new CoreInvalidParameterException("商户信息不存在");
        }
        boolean isLklMerchant = iCustomerRelationValidateFacade.isLakalaPayMerchant(merchantId);
        if(isLklMerchant && ApolloConfigurationCenterUtil.getSqbLklCorpSqbSpecialVendorAppids().contains(vendorAppAppid)){
            //拉卡拉商户不能激活收钱吧特有的终端应用
            return new SqbLklTerminalActivateVerifyResponse(false, isLklMerchant);
        }
        if(!isLklMerchant && ApolloConfigurationCenterUtil.getSqbLklCorpLklSpecialVendorAppids().contains(vendorAppAppid)){
            //收钱吧商户不能激活拉卡拉特有的终端应用
            return new SqbLklTerminalActivateVerifyResponse(false, isLklMerchant);
        }
        return new SqbLklTerminalActivateVerifyResponse(true, isLklMerchant);
    }

    @Override
    @Transactional(value = "transactionManager")
    public Map<String, Object> activateV2(String vendorAppAppid,
                                          String code,
                                          String clientSn,
                                          String deviceFingerprint,
                                          String name,
                                          String osVer,
                                          String sdkVer,
                                          String longitude,
                                          String latitude,
                                          Object extraInfo) {

        // 对于某些特定的 vendorAppAppid 要求 deviceFingerprint 不能为空
        checkDeviceFingerprint(vendorAppAppid, deviceFingerprint);

        Map<String, Object> vendorApp = repository.getVendorAppDao().filter(Criteria.where(VendorApp.APPID).is(vendorAppAppid),
                CollectionUtil.hashSet(DaoConstants.ID, VendorApp.VENDOR_ID,
                        VendorApp.TYPE)).fetchOne();
        if (vendorApp == null) {
            throw new CoreScenesException(CoreErrorScenesConstant.APP_ID_NOT_EXIST, CoreBizException.CODE_VENDOR_APP_NOT_EXITS, CoreVendorAppNotExistsException.class.getName());
        }
        String vendorAppId = (String) vendorApp.get(DaoConstants.ID);
        String vendorId = (String) vendorApp.get(VendorApp.VENDOR_ID);
        int vendorAppType = BeanUtil.getPropInt(vendorApp, VendorApp.TYPE);

        if (!StringUtil.empty(deviceFingerprint)) {
            Criteria criteria = Criteria.where(Terminal.VENDOR_APP_ID).is(vendorAppId)
                    .with(Terminal.DEVICE_FINGERPRINT).is(deviceFingerprint).with(Terminal.STATUS).in(Arrays.asList(Terminal.STATUS_ACTIVATED, Terminal.STATUS_DISABLED));
            long countExisting = repository.getTerminalDao().filter(criteria).count();
            if (countExisting > 0) {
                throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_ALERDY_ACTIVATE, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
            }
        }

        Map<String, Object> activationCodeInfo = findAndCheckActivationCode(vendorAppAppid, code);
        verifyTerminalActivate(MapUtil.getString(activationCodeInfo, TerminalActivationCode.MERCHANT_ID), vendorAppAppid);
        useTerminalActivationCode(activationCodeInfo);
        String terminalId = uuidGenerator.nextUuid();
        if (StringUtil.empty(name)) {
            String defaultTerminalName = (String) activationCodeInfo.get(TerminalActivationCode.DEFAULT_TERMINAL_NAME);
            if (StringUtil.empty(defaultTerminalName)) {
                name = "New Term (activateV2) " + System.currentTimeMillis();
            } else {
                name = defaultTerminalName;
            }
        }
        Map<String, Object> terminalInfo = activateNewTerminal(vendorId,
                vendorAppAppid,
                (String) activationCodeInfo.get(TerminalActivationCode.MERCHANT_ID),
                (String) activationCodeInfo.get(TerminalActivationCode.STORE_ID),
                terminalId,
                clientSn,
                deviceFingerprint,
                name,
                osVer,
                sdkVer,
                longitude,
                latitude,
                vendorAppId,
                vendorAppType,
                extraInfo,
                genSecret(terminalId));
        writeTerminalStatusActive(terminalId, code);
        return terminalInfo;

    }

    private static List<String> posAppids = Arrays.asList("2016081800000003", "2017020600000069", "2019010700001242", "2019010700001243", "2019010700001244");

    private void checkDeviceFingerprint(String vendorAppAppid, String deviceFingerprint) {
        if (posAppids.contains(vendorAppAppid) && WosaiStringUtils.isEmpty(deviceFingerprint)) {
            throw new CoreInvalidParameterException("激活失败，SN丢失，请核对后重试");
        }
//        关闭意锐版PC插件新增激活入口，无法再激活意锐版PC插件
        if ("2017030600000083".equalsIgnoreCase(vendorAppAppid)) {
            throw new CoreInvalidParameterException("暂不支持该终端入网，请联系您的客户经理 EJ48");
        }
    }

    private void writeTerminalStatusActive(String terminalId, String code) {
        Map msg = getTerminal(terminalId);
        msg.put("code", code);
        rmqService.writeTerminalActive(msg);
    }

    private Map<String, Object> findAndCheckActivationCode(String vendorSnOrVendorAppId, String code) {
        String snSuffix = "";
        boolean needCheck = code.contains(NEW_ACTIVATIONCODE_DELIMITER);
        if (needCheck) {
            String[] arr = code.split(NEW_ACTIVATIONCODE_DELIMITER);
            snSuffix = arr[0];
            code = arr[1];
        }
        Map<String, Object> activationCodeInfo = findTerminalActivationCode(null, null, code);
        Map<String, Object> merchant = merchantService.getMerchant(BeanUtil.getPropString(activationCodeInfo, TerminalActivationCode.MERCHANT_ID));
        long merchantStatus = BeanUtil.getPropLong(merchant, Merchant.STATUS);
        if (merchantStatus != Merchant.STATUS_ENABLED){
            throw new CoreMerchantStatusAbnormalException("商户状态异常，终端不允许激活");
        }
        if (needCheck) {
            String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            if (StringUtil.empty(merchantSn) || !merchantSn.endsWith(snSuffix)) {
                throw new CoreScenesException(CoreErrorScenesConstant.ACTIVATION_CODE_NOT_MATCH, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
            }
        }
        int status = BeanUtil.getPropInt(activationCodeInfo, TerminalActivationCode.STATUS);
        if (status == TerminalActivationCode.STATUS_USED) {
            throw new CoreScenesException(CoreErrorScenesConstant.ACTIVATION_CODE_ALERDY_ACTIVATE, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        } else if (status == TerminalActivationCode.STATUS_EXPIRED) {
            throw new CoreScenesException(CoreErrorScenesConstant.ACTIVATION_CODE_ALERDY_EXPIRED, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        }
        if(StringUtil.empty(vendorSnOrVendorAppId)) {
            vendorSnOrVendorAppId = MapUtil.getString(activationCodeInfo, TerminalActivationCode.VENDOR_SN);
        }
        //风控激活管控校验
        if(!allowActivateForRisk(BeanUtil.getPropString(activationCodeInfo, TerminalActivationCode.STORE_ID), vendorSnOrVendorAppId)){
            throw new CoreScenesException(CoreErrorScenesConstant.CITY_ACTIVATE_LIMIT, CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        }
        return activationCodeInfo;
    }


    @Override
    public Map<String, Object> getTerminalByDeviceFp(String vendorAppAppid,
                                                     String deviceFingerprint) {
        String vendorAppId = null;
        if (vendorAppAppid != null) {
            Map<String, Object> vendorApp = repository.getVendorAppDao().filter(Criteria.where(VendorApp.APPID).is(vendorAppAppid),
                    CollectionUtil.hashSet(DaoConstants.ID, VendorApp.VENDOR_ID,
                            VendorApp.TYPE)).fetchOne();
            if (vendorApp == null) {
                throw new CoreScenesException(CoreErrorScenesConstant.APP_ID_NOT_EXIST, CoreBizException.CODE_VENDOR_APP_NOT_EXITS, CoreVendorAppNotExistsException.class.getName());
            }
            vendorAppId = (String) vendorApp.get(DaoConstants.ID);
        }

        Criteria criteria = Criteria.where(Terminal.DEVICE_FINGERPRINT).is(deviceFingerprint).with(Terminal.STATUS).is(Terminal.STATUS_ACTIVATED);
        if (vendorAppId != null) {
            criteria.with(Terminal.VENDOR_APP_ID).is(vendorAppId);
        }
        Filter filter = repository.getTerminalDao().filter(criteria, CollectionUtil.hashSet(
                DaoConstants.ID,
                Terminal.STORE_ID,
                Terminal.MERCHANT_ID,
                Terminal.SN,
                Terminal.NAME,
                Terminal.CLIENT_SN,
                Terminal.DEVICE_FINGERPRINT,
                Terminal.CURRENT_SECRET,
                Terminal.STATUS));
        filter.limit(2);
        List<Map<String, Object>> terminals = CollectionUtil.iterator2list(filter.fetchAll());
        if (terminals == null || terminals.size() == 0) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_NOT_ACTIVATE, CoreBizException.CODE_TERMINAL_NOT_EXISTS, CoreTerminalNotExistsException.class.getName());
        }
        if (terminals.size() == 2) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_MATCH_REPEAT, CoreBizException.CODE_TERMINAL_NOT_EXISTS, CoreTerminalNotExistsException.class.getName());
        }
        Map<String, Object> terminal = terminals.get(0);
        String storeId = MapUtil.getString(terminal, Terminal.STORE_ID);
        Map<String, Object> store = cacheService.getStoreMinimalInfo(storeId, null);
        terminal.put(ConstantUtil.KEY_STORE_SN, MapUtil.getString(store, Store.SN));
        terminal.put(ConstantUtil.KEY_STORE_NAME, MapUtil.getString(store, Store.NAME));
        String merchantId = MapUtil.getString(terminal, Terminal.MERCHANT_ID);
        Map<String, Object> merchant = cacheService.getMerchantMinimalInfo(merchantId, null);
        terminal.put(ConstantUtil.KEY_MERCHANT_SN, MapUtil.getString(merchant, Merchant.SN));
        terminal.put(ConstantUtil.KEY_MERCHANT_NAME, MapUtil.getString(merchant, Merchant.NAME));
        return terminal;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map<String, Object> unbindTerminalByDeviceFp(String vendorAppAppid,
                                                        String deviceFingerprint) {
        if (isProhibitedUnbound(vendorAppAppid)) {
            throw new CoreTerminalBindErrorException("该类型终端禁止解绑");
        }
        Map<String, Object> terminal = getTerminalByDeviceFp(vendorAppAppid, deviceFingerprint);
        int preStatus = BeanUtil.getPropInt(terminal, Terminal.STATUS, -1);
        String terminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
        @SuppressWarnings("unchecked")
        Map<String, Object> update = CollectionUtil.hashMap(DaoConstants.ID, terminalId,
                Terminal.STATUS, Terminal.STATUS_UNACTIVATED,
                Terminal.DEVICE_FINGERPRINT, renameDeviceFpOnUnbindTerminal(terminal),
                Terminal.CLIENT_SN, renameClientSnOnUnbindTerminal(terminal)
        );
        terminalDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_TERMINAL, terminal);
        removeCachedParamsByTerminal(terminalId);
        terminalDataBusBiz.statusChange(terminal, preStatus, Terminal.STATUS_UNACTIVATED);
        return terminalDao.get(terminalId);
    }


    @Override
    /**
     * 旧系统substore terminal迁移到新系统规则
     * substore substore_no(customStoreId) -> store client_sn
     * terminal terminal_no(terminalId) -> terminal client_sn
     */
    public String getTerminalSnByLegacySdkParam(String wosaiStoreId, String customStoreId, String terminalId) {
        //
        if (StringUtil.empty(terminalId)) {
            return null;
        }
        Map storeMiniInfo = cacheService.getStoreMinimalInfo(null, wosaiStoreId);
        if (storeMiniInfo == null) {
            return null;
        }
        String merchantId = BeanUtil.getPropString(storeMiniInfo, ConstantUtil.KEY_MERCHANT_ID);
        Map store = null;
        if (!StringUtil.empty(customStoreId)) {
            store = storeService.getStoreByClientSn(merchantId, customStoreId);
        }
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        Criteria criteria = Criteria.where(Terminal.MERCHANT_ID).is(merchantId).with(Terminal.CLIENT_SN).is(terminalId);
        if (!StringUtil.empty(storeId)) {
            criteria.with(Terminal.STORE_ID).is(storeId);
        }
        Map terminal = terminalDao.filter(criteria).fetchOne();
        return BeanUtil.getPropString(terminal, Terminal.SN);
    }


    @Override
    public String getStoreSnByLegacySdkParam(String wosaiStoreId, String customStoreId, String terminalId) {
        Map storeMiniInfo = cacheService.getStoreMinimalInfo(null, wosaiStoreId);
        if (storeMiniInfo == null) {
            return wosaiStoreId;
        }
        String merchantId = BeanUtil.getPropString(storeMiniInfo, ConstantUtil.KEY_MERCHANT_ID);
        Map store = null;
        if (!StringUtil.empty(customStoreId)) {
            store = storeService.getStoreByClientSn(merchantId, customStoreId);
        }
        if (store != null) {
            return BeanUtil.getPropString(store, Store.SN);
        } else {
            Criteria criteria = Criteria.where(Terminal.MERCHANT_ID).is(merchantId).with(Terminal.CLIENT_SN).is(terminalId);
            Map terminal = terminalDao.filter(criteria).fetchOne();
            if (terminal != null) {
                String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
                store = cacheService.getStoreMinimalInfo(storeId, null);
                if (store != null) {
                    return BeanUtil.getPropString(store, Store.SN);
                } else {
                    return wosaiStoreId;
                }
            } else {
                return wosaiStoreId;
            }
        }
    }

    @Override
    /**
     * 旧系统store表mpos_list迁移到新系统规则
     * posSn --> device_fingerprint
     */
    public String getTerminalSnByLegacyPosSn(String wosaiStoreId, String posSn) {
        if (StringUtil.empty(posSn)) {
            return null;
        }
        Map storeMiniInfo = cacheService.getStoreMinimalInfo(null, wosaiStoreId);
        if (storeMiniInfo == null) {
            return null;
        }
        String merchantId = BeanUtil.getPropString(storeMiniInfo, ConstantUtil.KEY_MERCHANT_ID);
        Criteria criteria = Criteria.where(Terminal.MERCHANT_ID).is(merchantId).with(Terminal.DEVICE_FINGERPRINT).is(posSn).with(Terminal.STATUS).is(Terminal.STATUS_ACTIVATED);
        Filter<Map<String, Object>> filter = terminalDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map terminal = filter.fetchOne();
        return BeanUtil.getPropString(terminal, Terminal.SN);
    }

    @Override
    /**
     * 旧系统里面有把收银员当做终端，迁移时，已同步迁移到terminal中
     *
     */
    public String getTerminalSnByLegacyUserId(String wosaiStoreId, String legacyUserId) {
        return getTerminalSnByLegacySdkParam(wosaiStoreId, null, legacyUserId);
    }

    private static long ndaysFromNow(int n) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DATE, n);
        return cal.getTimeInMillis();

    }

    private void removeCachedParamsByTerminal(String terminalId) {
        Map terminal = getTerminal(terminalId);
        Map merchant = merchantService.getMerchantByMerchantId(BeanUtil.getPropString(terminal, "merchant_id"));
        supportService.removeCachedParams(BeanUtil.getPropString(merchant, ConstantUtil.KEY_SN));
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public Map<String, Object> changeShiftsCheckIn(String terminalSn) {
        ChangeShiftsCheckInResponse response = changeShiftsService.changeShiftsCheckIn(new ChangeShiftsCheckInRequest(terminalSn));
        return MapUtil.hashMap(ChangeShifts.BATCH_SN, response.getBatchSn(), ChangeShifts.START_DATE, response.getStartDate());
    }

    @Override
    @Transactional(value = "transactionManager")
    public Map<String, Object> changeShiftsCheckOut(String terminalSn, String batchSn, String cashierNo) {
        ChangeShiftsCheckOutResponse response = changeShiftsService.changeShiftsCheckOut(new ChangeShiftsCheckOutRequest(terminalSn, batchSn, cashierNo));
        return MapUtil.hashMap(ChangeShifts.BATCH_SN, response.getBatchSn(), ChangeShifts.END_DATE, response.getEndDate(), ChangeShifts.NEXT_BATCH_SN, response.getNextBatchSn());
    }

    @Override
    public Map getTerminalChangeShiftsInfo(String terminalSn, String batchSn) {
        ChangeShiftsQueryResponse response = changeShiftsService.getChangeShiftsInfo(new ChangeShiftsQueryRequest(terminalSn, null, null, batchSn, true));
        if (response != null) {
            return MapUtil.hashMap(DaoConstants.ID, response.getId(),
                    ChangeShifts.BATCH_SN, response.getBatchSn(),
                    ChangeShifts.CASHIER_NO, response.getCashierNo(),
                    ChangeShifts.TYPE, response.getType(),
                    ChangeShifts.START_DATE, response.getStartDate(),
                    ChangeShifts.END_DATE, response.getEndDate(),
                    ChangeShifts.SERVICE_ID, response.getServiceId(),
                    TransactionParam.TERMINAL_ID, response.getServiceId(),
                    ChangeShifts.EXTRA, response.getExtra(),
                    DaoConstants.CTIME, response.getCtime(),
                    DaoConstants.MTIME, response.getMtime()
            );
        }
        return null;
    }

    @Override
    public ListResult getTerminalChangeShiftsList(String terminalSn, Long startDate, Long endDate, PageInfo pageInfo) {
        ChangeShiftsBatchQueryRequest request = new ChangeShiftsBatchQueryRequest();
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        request.setTerminalSn(terminalSn);
        request.setReturnUnCheckout(Boolean.TRUE);
        if (pageInfo != null) {
            request.setPage(pageInfo.getPage());
            request.setPageSize(pageInfo.getPageSize());
            if (pageInfo.getOrderBy() != null) {
                for (OrderBy orderBy : pageInfo.getOrderBy()) {
                    if (Objects.equals(orderBy.getField(), DaoConstants.CTIME)) {
                        if (orderBy.getOrder() != null && orderBy.getOrder() == OrderType.ASC) {
                            request.setOrderBy(ChangeShiftsBatchQueryRequest.ORDER_BY_CTIME_ASC);
                        }
                    }
                }
            }
        }
        ChangeShiftsBatchQueryResponse response = changeShiftsService.getChangeShiftsList(request);
        ListResult listResult = new ListResult(response.getTotal(), null);
        if (response.getRecords() != null) {
            listResult.setRecords(response.getRecords().stream().map(record -> {
                return MapUtil.hashMap(DaoConstants.ID, record.getId(),
                        ChangeShifts.BATCH_SN, record.getBatchSn(),
                        ChangeShifts.CASHIER_NO, record.getCashierNo(),
                        ChangeShifts.TYPE, record.getType(),
                        ChangeShifts.START_DATE, record.getStartDate(),
                        ChangeShifts.END_DATE, record.getEndDate(),
                        ChangeShifts.SERVICE_ID, record.getServiceId(),
                        TransactionParam.TERMINAL_ID, record.getServiceId(),
                        ChangeShifts.EXTRA, record.getExtra(),
                        DaoConstants.CTIME, record.getCtime(),
                        DaoConstants.MTIME, record.getMtime()
                    );
            }).collect(Collectors.toList()));
        }
        return listResult;
    }

    @Override
    public Map<String, Object> getTerminalExtendedInfoForUpayQrcode(String terminalId) {
        //从数据库中获取terminal, store, merchant 所需字段，不获取所有字段，避免加解密服务的调用。
        Map<String,Object> terminal = terminalDao.getPart(terminalId, Arrays.asList(DaoConstants.ID, Terminal.SN, Terminal.NAME, Terminal.STORE_ID, Terminal.VENDOR_APP_APPID));
        if(terminal == null){
            return null;
        }
        return buildTerminalExtendedInfo(terminal);
    }

    @Override
    public Map<String, Object> getTerminalExtendedInfoByTerminalSn(String terminalSn) {
        //从数据库中获取terminal, store, merchant 所需字段，不获取所有字段，避免加解密服务的调用。
        Criteria criteria = Criteria.where(Terminal.SN).in(terminalSn);
        Map<String,Object> terminal = terminalDao.filter(criteria, CollectionUtil.hashSet(DaoConstants.ID, Terminal.SN, Terminal.NAME, Terminal.STORE_ID, Terminal.VENDOR_APP_APPID)).fetchOne();
        if(terminal == null){
            return null;
        }
        return buildTerminalExtendedInfo(terminal);
    }

    private Map<String, Object> buildTerminalExtendedInfo(Map<String, Object> terminal) {
        Map<String,Object> info = new HashMap<>();
        info.put(ConstantUtil.KEY_TERMINAL_ID, BeanUtil.getPropString(terminal, DaoConstants.ID));
        info.put(ConstantUtil.KEY_TERMINAL_SN, BeanUtil.getPropString(terminal, Terminal.SN));
        info.put(ConstantUtil.KEY_TERMINAL_NAME, BeanUtil.getPropString(terminal, Terminal.NAME));
        info.put(ConstantUtil.KEY_VENDOR_APP_APPID, BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID));
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        Map<String,Object> store = repository.getStoreDao().getPart(storeId, Arrays.asList(Store.SN, Store.NAME, Store.MERCHANT_ID, Store.PROVINCE, Store.CITY, Store.LOGO));
        if(store == null){
            return null;
        }
        info.put(ConstantUtil.KEY_STORE_ID, storeId);
        info.put(ConstantUtil.KEY_STORE_SN, BeanUtil.getPropString(store, Store.SN));
        info.put(ConstantUtil.KEY_STORE_NAME, BeanUtil.getPropString(store, Store.NAME));
        info.put(CoreCommonConstants.KEY_STORE_PROVINCE, BeanUtil.getPropString(store, Store.PROVINCE));
        info.put(CoreCommonConstants.KEY_STORE_CITY, BeanUtil.getPropString(store, Store.CITY));
        info.put(CoreCommonConstants.KEY_STORE_LOGO, BeanUtil.getPropString(store, Store.LOGO));
        String merchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
        Map<String,Object> merchant = repository.getMerchantDao().getPart(merchantId, Arrays.asList(Merchant.SN, Merchant.NAME, Merchant.ALIAS, Merchant.BUSINESS_NAME, Merchant.LOGO, Merchant.CURRENCY));
        if(merchant == null){
            return null;
        }
        info.put(ConstantUtil.KEY_MERCHANT_ID, merchantId);
        info.put(ConstantUtil.KEY_MERCHANT_SN, BeanUtil.getPropString(merchant, Merchant.SN));
        info.put(ConstantUtil.KEY_MERCHANT_NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
        info.put(CoreCommonConstants.KEY_MERCHANT_ALIAS, BeanUtil.getPropString(merchant, Merchant.ALIAS));
        info.put(CoreCommonConstants.KEY_MERCHANT_BUSINESS_NAME, BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME));
        info.put(CoreCommonConstants.KEY_MERCHANT_LOGO, BeanUtil.getPropString(merchant, Merchant.LOGO));
        String currency = MapUtil.getString(merchant, Merchant.CURRENCY);
        if(com.wosai.pantheon.util.StringUtil.isEmpty(currency)){
            currency = TransactionParam.UPAY_DEFAULT_CURRENCY_CNY;
        }

        info.put(Merchant.CURRENCY, currency);
        return info;
    }


    @Override
    public Map<String, Object> getTerminalVendorAppIdMapping() {
        return ApolloConfigurationCenterUtil.getTerminalVendorAppIdMapping();
    }

    @Override
    public void transforTerminal(String terminalId, String merchantId, String storeId) {
        Map update = CollectionUtil.hashMap(DaoConstants.ID, terminalId, Terminal.MERCHANT_ID, merchantId, Terminal.STORE_ID, storeId);
        terminalDao.updatePart(update);

    }

    private List<String> getQrcodeVendorAppIds() {
       return  ApolloConfigurationCenterUtil.getQrcodeVendorAppIds();
    }


    public Map getTerminalInfo(String column, String value) {
        Map terminal;
        if (Terminal.DEVICE_FINGERPRINT.equals(column)) {
            terminal = terminalDao.filter(Criteria.where(Terminal.DEVICE_FINGERPRINT).is(value)).fetchOne();
        } else if (Terminal.SN.equals(column)) {
            terminal = terminalDao.filter(Criteria.where(Terminal.SN).is(value)).fetchOne();
        } else if (DaoConstants.ID.equals(column)) {
            terminal = terminalDao.filter(Criteria.where(DaoConstants.ID).is(value)).fetchOne();
        } else {
            throw new CoreTerminalNotExistsException(String.format("can not find terminal by column: %s  and value: %s ", column, value));
        }
        if (terminal == null) {
            throw new CoreScenesException(CoreErrorScenesConstant.TERMINAL_NOT_EXIST, CoreBizException.CODE_TERMINAL_NOT_EXISTS, CoreTerminalNotExistsException.class.getName());
        }
        return terminal;
    }

    /**
     * 风控激活校验
     * @param storeId
     * @param vendorSnOrVendorAppId
     */
    private boolean allowActivateForRisk(String storeId, String vendorSnOrVendorAppId){
        Map<String, Object> store = storeService.getStoreByStoreId(storeId);
        String city = MapUtil.getString(store, Merchant.CITY);
        String district = MapUtil.getString(store, Merchant.DISTRICT);
        //城市或者区县为空，允许激活
        if(WosaiStringUtils.isEmpty(city) || WosaiStringUtils.isEmpty(district)){
            return true;
        }
        //不在管控的城市里面，允许激活, 属于特定类型终端，允许激活
        if(!ApolloConfigurationCenterUtil.activateCityNeedLimit(city, district, vendorSnOrVendorAppId)){
            return true;
        }
        String merchantId = MapUtil.getString(store, Store.MERCHANT_ID);
        String merchantSn = businssCommonService.getMerchantSnById(merchantId);
        TradeConfigService tradeConfigService = SpringContextHolder.getBean(TradeConfigService.class);
        //不属于拉卡拉结算，允许激活
        if(tradeConfigService.getClearanceProvider(merchantId) != TradeConfigService.CLEARANCE_PROVIDER_LKL){
            return true;
        }
        //如果商户在风控白名单里面，则允许激活
        boolean inWhiteList = inWhiteMerchantListWhenCheckRisk(merchantId, merchantSn);
        if(inWhiteList){
            return true;
        }
        //如果商户 支付宝&微信&云闪付&翼支付 为直连或者间连直清，则允许激活
        boolean allFormal = allFormalWhenCheckRisk(merchantId);
        if(allFormal){
            return true;
        }
        return false;
    }


    /**
     * 是否存在于白名单
     * @param merchantId
     * @param merchantSn
     * @return
     */
    private boolean inWhiteMerchantListWhenCheckRisk(String merchantId, String merchantSn){
        BlistSceneReq blistSceneReq = new BlistSceneReq();
        blistSceneReq.setId(merchantId);
        blistSceneReq.setCode("ynbmd");
        blistSceneReq.setBusinessParam(MapUtil.hashMap("merchant_sn", merchantSn));
        SceneValidateVO result = iRiskBlistSceneService.validateByBizParam(blistSceneReq);
        //返回true表示没有命中名单, false表示命中了名单，说明在加入的白名单里面
        return !result.isValidateResult();
    }

    /**
     * 是否全部都是直连
     * @param merchantId
     * @return
     */
    private boolean allFormalWhenCheckRisk(String merchantId){
        TradeConfigService tradeConfigService = SpringContextHolder.getBean(TradeConfigService.class);
        SystemConfigService systemConfigService = SpringContextHolder.getBean(SystemConfigService.class);
        boolean allFormal = true;
        int [] payways = {TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.PAYWAY_UNIONPAY};
        List<Map<String,Object>> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, payways);
        Boolean alipayV1B2cFormal = null;
        Boolean alipayV2B2cFormal = null;
        for (Map<String, Object> merchantConfig : merchantConfigs) {
            int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if(payway == TradeConfigService.PAYWAY_ALIPAY){
                alipayV1B2cFormal = BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL);
            }else if(payway == TradeConfigService.PAYWAY_ALIPAY2) {
                alipayV2B2cFormal = BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL);
            }else{
                boolean formal = BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, false);
                if(!formal){
                    Integer provider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER);
                    if (!metaProviderBiz.isIndirect(provider)) {
                        allFormal = false;
                        break;
                    }
                }
            }
        }
        //合并支付宝v1-v2, 如果有一个直连则为直连,因为交易的时候就是这样的来获取交易参数的
        boolean alipayB2cFormal = alipayV1B2cFormal || alipayV2B2cFormal;
        if(!alipayB2cFormal){
            allFormal  = false;
        }
        return allFormal;
    }

    /**
     * 终端激活验证
     *
     * @param merchantId
     * @param vendorAppAppId
     */
    private void verifyTerminalActivate(String merchantId, String vendorAppAppId) {
        // 收钱吧与拉卡拉不允许激活各自特有的终端
        checkSqbLklCrossActivate(merchantId, vendorAppAppId);
        // 验证是否为插件激活服务关停的终端应用
        if (ApolloConfigurationCenterUtil.getPluginActiveDisableVendorAppids().contains(vendorAppAppId)) {
            throw new CoreScenesException(CoreErrorScenesConstant.PLUGIN_ACTIVATION_SERVICE_CLOSED,
                    CoreBizException.CODE_TERMINAL_ACTIVATION_ERROR, CoreTerminalActivationException.class.getName());
        }
    }

    /**
     * 收钱吧与拉卡拉不允许激活各自特有的终端
     * @param merchantId
     * @param vendorAppAppid
     */
    private void checkSqbLklCrossActivate(String merchantId, String vendorAppAppid){
        if(vendorAppAppid == null){
            return;
        }
        SqbLklTerminalActivateVerifyResponse response = sqbLklTerminalActivateVerify(merchantId, null, vendorAppAppid);
        if(!response.isPass()){
            String message = response.isLklMerchant() ? "拉卡拉的商户不允许激活收钱吧特有的终端设备" : "收钱吧的商户不允许激活拉卡拉特有的终端设备";
            throw new CoreTerminalActivationException(message);
        }
    }

    @Override
    public List<Map> getVendorAppsByCategory(Integer category) {
        String key = "key_" + category;
        Cache vendorAppAppIds = cacheManager.getCache(PublicConstants.VENDOR_APP_APPIDS);
        Element element = vendorAppAppIds.get(key);
        if (!Objects.isNull(element)) {
            return (List<Map>) element.getObjectValue();
        }
        PageInfo pageInfo = new PageInfo(1, 10000, null, null, null);
        Criteria criteria = new Criteria();
        if(category != null){
            criteria.with(VendorApp.CATEGORY).is(category);
        }else{
            criteria.with(VendorApp.CATEGORY).ne(null);
        }
        criteria.with(VendorApp.VENDOR_ID).in(ApolloConfigurationCenterUtil.getShouqianbaVendorIds());
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        Filter filter = vendorAppDao.filter(criteria, CollectionUtil.hashSet(VendorApp.NAME, VendorApp.TYPE, VendorApp.CATEGORY, VendorApp.APPID));
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        vendorAppAppIds.put(new Element(key, list));
        return list;
    }

    @Override
    public boolean isProhibitedUnbound(String vendorAppAppid) {
        return ApolloConfigurationCenterUtil.isTerminalProhibitedUnbound(vendorAppAppid);
    }
    @Override
    public List<Map> getSimpleTerminalsByTerminalIds(List<String> terminalIds) {
        Criteria criteria = Criteria.where(DaoConstants.ID).in(terminalIds);
        Filter filter = terminalDao.filter(criteria, CollectionUtil.hashSet(DaoConstants.ID, Terminal.SN, Terminal.NAME, Terminal.DEVICE_FINGERPRINT, Terminal.TYPE, Terminal.CLIENT_SN, Terminal.VENDOR_APP_APPID, DaoConstants.CTIME));
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    @Override
    public List<Map> getSimpleTerminalsByDeviceFingerprints(String merchantId, String storeId, List<String> deviceFingerprint, Map queryFilter) {
        if (StringUtil.empty(storeId) && StringUtil.empty(merchantId)) {
            throw new CoreInvalidParameterException("storeId and merchantId can not be both null");
        }
        Criteria criteria = Criteria.where(Terminal.MERCHANT_ID).is(merchantId).with(Terminal.STORE_ID).is(storeId).with(Terminal.DEVICE_FINGERPRINT).in(deviceFingerprint);
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(Terminal.SN, Terminal.SN);
            put(Terminal.TYPE, Terminal.TYPE);
            put(Terminal.STATUS, Terminal.STATUS);
            put(Terminal.CLIENT_SN, Terminal.CLIENT_SN);
            put(Terminal.TARGET_TYPE, Terminal.TARGET_TYPE);
            put(Terminal.SOLICITOR_ID, Terminal.SOLICITOR_ID);
            put(Terminal.VENDOR_ID, Terminal.VENDOR_ID);
            put(Terminal.VENDOR_APP_ID, Terminal.VENDOR_APP_ID);
            put(Terminal.VENDOR_APP_APPID, Terminal.VENDOR_APP_APPID);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        Filter filter = terminalDao.filter(criteria, CollectionUtil.hashSet(DaoConstants.ID, Terminal.SN, Terminal.NAME, Terminal.DEVICE_FINGERPRINT, Terminal.TYPE, Terminal.CLIENT_SN, Terminal.VENDOR_APP_APPID));
        return CollectionUtil.iterator2list(filter.fetchAll());
    }

    public List<String> findAllVendorApp(String key, String type) {
        Filter filter = null;
        Criteria criteria = null;
        List<String> vendorAppNames = new ArrayList<>();
        //查询vendor_app_id
        if (Objects.equals(type, CoreCommonConstants.FIND_ALL_VENDOR_APP_NAME_TYPE_MERCHANT)) {
            Map merchant = merchantService.getMerchantBySn(key);
            String merchantId = MapUtil.getString(merchant, DaoConstants.ID);
            if(Objects.isNull(merchantId)) {
                return vendorAppNames;
            }
            criteria = Criteria.where(Terminal.MERCHANT_ID).is(merchantId).with(Terminal.VENDOR_APP_ID).ne(null).with(Terminal.STATUS).ne(Terminal.STATUS_CHANGE_TYPE_UNACTIVATE);

        } else if (Objects.equals(type, CoreCommonConstants.FIND_ALL_VENDOR_APP_NAME_TYPE_TERMINAL)) {
            criteria = Criteria.where(Terminal.SN).is(key).with(Terminal.VENDOR_APP_ID).ne(null).with(Terminal.STATUS).ne(Terminal.STATUS_CHANGE_TYPE_UNACTIVATE);

        } else if (Objects.equals(type, CoreCommonConstants.FIND_ALL_VENDOR_APP_NAME_TYPE_DEVICE_FINGERPRINT)) {
            criteria = Criteria.where(Terminal.DEVICE_FINGERPRINT).is(key).with(Terminal.VENDOR_APP_ID).ne(null).with(Terminal.STATUS).ne(Terminal.STATUS_CHANGE_TYPE_UNACTIVATE);

        }
        filter = terminalDao.filter(criteria, CollectionUtil.hashSet(Terminal.VENDOR_APP_ID));
        //限制5000条
        filter.limit(5000);
        List<Map> vendorApp = CollectionUtil.iterator2list(filter.fetchAll());
        if(com.wosai.pantheon.util.CollectionUtil.isEmpty(vendorApp)) {
            return vendorAppNames;
        }
        Set<String> vendorAppIds = new HashSet<>();
        for(Map map : vendorApp) {
            vendorAppIds.add(MapUtils.getString(map, Terminal.VENDOR_APP_ID));
        }
        criteria = Criteria.where(DaoConstants.ID).in(vendorAppIds);
        //返回结果
        filter = vendorAppDao.filter(criteria, CollectionUtil.hashSet(VendorApp.NAME));

        List<Map> vendorAPP = CollectionUtil.iterator2list(filter.fetchAll());
        for(Map map : vendorAPP) {
            vendorAppNames.add(MapUtils.getString(map, VendorApp.NAME));
        }
        return vendorAppNames;
    }
}
