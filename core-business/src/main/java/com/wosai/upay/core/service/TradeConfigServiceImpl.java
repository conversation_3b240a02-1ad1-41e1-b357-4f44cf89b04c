package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.StringUtils;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.aop.backend.domain.OperationPayData;
import com.wosai.aop.backend.service.OperationHomePageService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.*;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.Digest;
import com.wosai.pub.alipay.authinto.exception.ExceptionBase;
import com.wosai.pub.alipay.authinto.service.AlipayStoreService;
import com.wosai.trade.service.MerchantConfigBypassService;
import com.wosai.trade.service.result.MerchantConfigBypassQueryResult;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.MapUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.model.TermInfo;
import com.wosai.upay.core.bean.model.TradeAppConfig;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.*;
import com.wosai.upay.core.bean.response.AllMerchantConfigResponse;
import com.wosai.upay.core.bean.response.MerchantAvailablePaywaysQueryResult;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.constant.*;
import com.wosai.upay.core.databus.MerchantConfigDataBusBiz;
import com.wosai.upay.core.datasource.DataSourceConstant;
import com.wosai.upay.core.datasource.DataSourceType;
import com.wosai.upay.core.enums.MetaProviderFlagEnum;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.meta.SubPayway;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.request.HopeEduMerchantConfigRequest;
import com.wosai.upay.core.model.request.LakalaMerchantConfigRequest;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.biz.MetaProviderBiz;
import com.wosai.upay.core.service.redis.RedisService;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.core.util.CommonSwitchUtil;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.core.util.FakeRequestUtil;
import com.wosai.upay.core.util.JsonUtil;
import com.wosai.upay.merchant.contract.model.MerchantProviderTradeParams;
import com.wosai.upay.merchant.contract.service.LakalaService;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import com.wosai.upay.signature.service.CcbKeyService;
import com.wosai.upay.util.DateTimeUtil;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.InvalidParameterException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wosai.upay.core.model.Terminal.CATEGORY_APP;

/**
 * Created by jianfree on 30/12/15.
 */
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class TradeConfigServiceImpl implements TradeConfigService {
    private static final Logger logger = LoggerFactory.getLogger(TradeConfigServiceImpl.class);
    @Autowired
    SolicitorService solicitorService;
    @Autowired
    BusinssCommonService businssCommonService;
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    ProviderTradeParamsService providerTradeParamsService;
    @Resource
    private CcbKeyService ccbKeyService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private StoreService storeService;
    @Autowired
    @Lazy
    private TerminalService terminalService;
    @Autowired
    LakalaService lakalaService;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    private DataRepository dataRepository;

    @Autowired
    private MerchantConfigDataBusBiz merchantConfigDataBusBiz;

    @Autowired
    private SimpleRedisLock simpleRedisLock;

    @Autowired
    private MerchantConfigBypassService merchantConfigBypassService;

    @Resource
    private OperationHomePageService operationHomePageService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    MetaProviderBiz metaProviderBiz;
    @Autowired
    BizLogFacade bizLogFacade;
    @Autowired
    ExternalExtraFacade externalExtraFacade;

    public static final int LAKALA_V3_PROVIDER = 1032;
    public static final Set<Integer> LAKALA_V3_COMPATIBILITY_PROVIDER = Sets
            .newHashSet(1016, 1017, 1033, 1034);

    public static String REDIS_KEY_ALIPAY_APP_AUTH_TOKEN_PREFIX = "alipay_app_auth_token:";
    public static String REDIS_KEY_ALIPAY_APP_AUTH_TOKEN_SHOP_ID_PREFIX = "alipay_auth_token_shopid:";
    public static String REDIS_KEY_CREATE_MERCHANT_CONFIG_PREFIX = "create_merchant_config:";

    public static String DEFAULT_FEE_RATE_ALIPAYV2_FORMAL = "0.55"; //支付宝v2正式商户默认费率, 用于记账

    // sub pay way 开启状态
    public static final Integer SUB_PAYWAY_ACTIVE_STATUS = 1;
    // 院校通b2c agent name
    public static final String HOPE_EDU_B2C_AGENT_NAME = "1061_32_*_false_true_0001";

    public static final String COMMON_SWITCH_BASE = "22222222" + "22222222" + "22222222" + "22222222";
    private static final String COMMON_SWITCH_FORMATTER = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.SWITCHES, TransactionParam.COMMON_SWITCH);
    public static final String LAKALA_MCHR_ID_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID);
    public static final String LAKALA_TERM_ID_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID);
    public static final String LAKALA_TERM_NO_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO);
    public static final String FUYOU_MCHR_ID_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.FUYOU_PROVIDER_MCH_ID);
    public static final String FUYOU_TERM_ID_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.FUYOU_TRADE_PARAMS, TransactionParam.FUYOU_BANK_TERM_ID);
    public static final String SYB_MCHR_ID_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.TL_SYB_TRADE_PARAMS, TransactionParam.TL_SYB_CUS_ID);
    public static final String SYB_TERM_ID_KEY = String.format("%s.%s.%s"
            , MerchantConfig.PARAMS, TransactionParam.TL_SYB_TRADE_PARAMS, TransactionParam.TL_SYB_TERM_ID);


    public static final int[] DEFAULT_PAYWAYS = new int[]{PAYWAY_ALIPAY, PAYWAY_ALIPAY2, PAYWAY_WEIXIN, PAYWAY_BAIFUBAO, PAYWAY_JD,
            PAYWAY_QQWALLET, PAYWAY_APPLEPAY, PAYWAY_LAKALAWALLET, PAYWAY_UNIONPAY, PAYWAY_BESTPAY, PAYWAY_WEIXIN_HK, PAYWAY_CMCC,
            PAYWAY_ALIPAY_INTL, PAYWAY_SODEXO, PAYWAY_DCEP, PAYWAY_GIFT_CARD, PAYWAY_FOXCONN, PAYWAY_GRABPAY, PAYWAY_BANKACCOUNT};

    //payway+""+subPayway 与 params里面配置参数key的映射
    @SuppressWarnings("unchecked")
    public static final Map<String, String> paywaySubpaywayTradeParamsKey = CollectionUtil.hashMap(
            PAYWAY_ALIPAY + "" + SUB_PAYWAY_BARCODE, TransactionParam.ALIPAY_V1_TRADE_PARAMS,
            PAYWAY_ALIPAY + "" + SUB_PAYWAY_QRCODE, TransactionParam.ALIPAY_V1_TRADE_PARAMS,
            PAYWAY_ALIPAY + "" + SUB_PAYWAY_WAP, TransactionParam.ALIPAY_WAP_TRADE_PARAMS,
            PAYWAY_ALIPAY + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_ALIPAY2 + "" + SUB_PAYWAY_BARCODE, TransactionParam.ALIPAY_V2_TRADE_PARAMS,
            PAYWAY_ALIPAY2 + "" + SUB_PAYWAY_QRCODE, TransactionParam.ALIPAY_V2_TRADE_PARAMS,
            PAYWAY_ALIPAY2 + "" + SUB_PAYWAY_WAP, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS,
            PAYWAY_ALIPAY2 + "" + SUB_PAYWAY_MINI, TransactionParam.ALIPAY_MINI_V2_TRADE_PARAMS,
            PAYWAY_ALIPAY2 + "" + SUB_PAYWAY_APP, TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS,
            PAYWAY_ALIPAY2 + "" + SUB_PAYWAY_H5, TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS,
            PAYWAY_WEIXIN + "" + SUB_PAYWAY_BARCODE, TransactionParam.WEIXIN_TRADE_PARAMS,
            PAYWAY_WEIXIN + "" + SUB_PAYWAY_QRCODE, TransactionParam.WEIXIN_TRADE_PARAMS,
            PAYWAY_WEIXIN + "" + SUB_PAYWAY_WAP, TransactionParam.WEIXIN_WAP_TRADE_PARAMS,
            PAYWAY_WEIXIN + "" + SUB_PAYWAY_MINI, TransactionParam.WEIXIN_MINI_TRADE_PARAMS,
            PAYWAY_WEIXIN + "" + SUB_PAYWAY_H5, TransactionParam.WEIXIN_H5_TRADE_PARAMS,
            PAYWAY_WEIXIN + "" + SUB_PAYWAY_APP, TransactionParam.WEIXIN_APP_TRADE_PARAMS,
            PAYWAY_BAIFUBAO + "" + SUB_PAYWAY_BARCODE, TransactionParam.BAIFUBAO_TRADE_PARAMS,
            PAYWAY_BAIFUBAO + "" + SUB_PAYWAY_QRCODE, TransactionParam.BAIFUBAO_TRADE_PARAMS,
            PAYWAY_BAIFUBAO + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_BAIFUBAO + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_QQWALLET + "" + SUB_PAYWAY_BARCODE, TransactionParam.QQ_TRADE_PARAMS,
            PAYWAY_QQWALLET + "" + SUB_PAYWAY_QRCODE, TransactionParam.QQ_TRADE_PARAMS,
            PAYWAY_QQWALLET + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_QQWALLET + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_APPLEPAY + "" + SUB_PAYWAY_BARCODE, TransactionParam.NFC_TRADE_PARAMS,
            PAYWAY_APPLEPAY + "" + SUB_PAYWAY_QRCODE, null,
            PAYWAY_APPLEPAY + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_APPLEPAY + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_LKL_UNIONPAY + "" + SUB_PAYWAY_BARCODE, null,
            PAYWAY_LKL_UNIONPAY + "" + SUB_PAYWAY_QRCODE, null,
            PAYWAY_LKL_UNIONPAY + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_LKL_UNIONPAY + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_BARCODE, null,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_QRCODE, null,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_WEIXIN_HK + "" + SUB_PAYWAY_BARCODE, TransactionParam.WEIXIN_TRADE_PARAMS,
            PAYWAY_WEIXIN_HK + "" + SUB_PAYWAY_QRCODE, null,
            PAYWAY_WEIXIN_HK + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_WEIXIN_HK + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_CMCC + "" + SUB_PAYWAY_BARCODE, TransactionParam.CMCC_TRADE_PARAMS,
            PAYWAY_CMCC + "" + SUB_PAYWAY_QRCODE, null,
            PAYWAY_CMCC + "" + SUB_PAYWAY_WAP, null,
            PAYWAY_CMCC + "" + SUB_PAYWAY_MINI, null,
            Payway.CMB_APP.getCode() + "" + SUB_PAYWAY_BARCODE, TransactionParam.CMB_APP_TRADE_PARAMS,
            Payway.CMB_APP.getCode() + "" + SUB_PAYWAY_QRCODE, null,
            Payway.CMB_APP.getCode() + "" + SUB_PAYWAY_WAP, null,
            Payway.CMB_APP.getCode() + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_BARCODE, TransactionParam.BESTPAY_TRADE_PARAMS,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_QRCODE, null,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_WAP, TransactionParam.BESTPAY_TRADE_PARAMS,
            PAYWAY_BESTPAY + "" + SUB_PAYWAY_MINI, null,
            PAYWAY_ALIPAY_INTL + "" + SUB_PAYWAY_BARCODE, TransactionParam.ALIPAY_INTL_TRADE_PARAMS,
            PAYWAY_GIFT_CARD + "" + SUB_PAYWAY_BARCODE, TransactionParam.GIFT_CARD_TRADE_PARAMS,
            PAYWAY_WELFARE_CARD + "" + SUB_PAYWAY_BARCODE, TransactionParam.GIFT_CARD_TRADE_PARAMS,
            PAYWAY_SODEXO + "" + SUB_PAYWAY_BARCODE, TransactionParam.SODEXO_TRADE_PARAMS,
            PAYWAY_SODEXO + "" + SUB_PAYWAY_MINI, TransactionParam.SODEXO_TRADE_PARAMS,
            PAYWAY_PREPAID_CARD + "" + SUB_PAYWAY_BARCODE, TransactionParam.PREPAID_CARD_TRADE_PARAMS,
            PAYWAY_PREPAID_CARD + "" + SUB_PAYWAY_QRCODE, TransactionParam.PREPAID_CARD_TRADE_PARAMS,
            PAYWAY_PREPAID_CARD + "" + SUB_PAYWAY_WAP, TransactionParam.PREPAID_CARD_TRADE_PARAMS,
            PAYWAY_PREPAID_CARD + "" + SUB_PAYWAY_MINI, TransactionParam.PREPAID_CARD_TRADE_PARAMS,
            PAYWAY_GRABPAY + "" + SUB_PAYWAY_BARCODE, TransactionParam.GRABPAY_TRADE_PARAMS,
            PAYWAY_CCB_GIFT_CARD + "" + SUB_PAYWAY_BARCODE, TransactionParam.CCB_GIFT_CARD_TRADE_PARAMS
    );

    //provider 与 params里面配置参数key的映射
    //已废弃，换为从systemConfig中读取
    @Deprecated
    @SuppressWarnings("unchecked")
    public static final Map<String, String> providerTradeParamsKey = CollectionUtil.hashMap(
            PROVIDER_CIBBANK + "", TransactionParam.CIBBANK_TRADE_PARAMS,
            PROVIDER_LAKALA + "", TransactionParam.LAKALA_TRADE_PARAMS,
            PROVIDER_CITICBANK + "", TransactionParam.CITICBANK_TRADE_PARAMS
    );

    //subPayway 与 对应是否正式字段的映射
    @SuppressWarnings("unchecked")
    public static final Map<String, String> subPaywayFormalColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfig.B2C_FORMAL,
            SUB_PAYWAY_QRCODE + "", MerchantConfig.C2B_FORMAL,
            SUB_PAYWAY_WAP + "", MerchantConfig.WAP_FORMAL,
            SUB_PAYWAY_MINI + "", MerchantConfig.MINI_FORMAL,
            SUB_PAYWAY_APP + "", MerchantConfig.APP_FORMAL,
            SUB_PAYWAY_H5 + "", MerchantConfig.H5_FORMAL
    );
    //subPayway 与 对应费率字段的映射
    @SuppressWarnings("unchecked")
    public static final Map<String, String> subPaywayFeeRateColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfig.B2C_FEE_RATE,
            SUB_PAYWAY_QRCODE + "", MerchantConfig.C2B_FEE_RATE,
            SUB_PAYWAY_WAP + "", MerchantConfig.WAP_FEE_RATE,
            SUB_PAYWAY_MINI + "", MerchantConfig.MINI_FEE_RATE,
            SUB_PAYWAY_APP + "", MerchantConfig.APP_FEE_RATE,
            SUB_PAYWAY_H5 + "", MerchantConfig.H5_FEE_RATE
    );
    //subPayway 与 对应开通状态的映射
    @SuppressWarnings("unchecked")
    public static final Map<String, String> subPaywayStatusColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfig.B2C_STATUS,
            SUB_PAYWAY_QRCODE + "", MerchantConfig.C2B_STATUS,
            SUB_PAYWAY_WAP + "", MerchantConfig.WAP_STATUS,
            SUB_PAYWAY_MINI + "", MerchantConfig.MINI_STATUS,
            SUB_PAYWAY_APP + "", MerchantConfig.APP_STATUS,
            SUB_PAYWAY_H5 + "", MerchantConfig.H5_STATUS
    );

    //subPayway 与受理商的映射 (merchant_config merchant_config_custom)
    @SuppressWarnings("unchecked")
    public static final Map<String, String> subPaywayAgentNameColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfig.B2C_AGENT_NAME,
            SUB_PAYWAY_QRCODE + "", MerchantConfig.C2B_AGENT_NAME,
            SUB_PAYWAY_WAP + "", MerchantConfig.WAP_AGENT_NAME,
            SUB_PAYWAY_MINI + "", MerchantConfig.MINI_AGENT_NAME,
            SUB_PAYWAY_APP + "", MerchantConfig.APP_AGENT_NAME,
            SUB_PAYWAY_H5 + "", MerchantConfig.H5_AGENT_NAME
    );

    //门店jibie级别交易校验那参数，比如门店交易限额、门店级别开关（门店级收款权限开关）
    public static final List<String> storeParamKeys = Arrays.asList(TransactionParam.SWITCHES,TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS);

    //交易校验参数,比如交易限额
    public static final List<String> tradeValidateParamKeys = Arrays.asList(TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS,
            TransactionParam.MERCHANT_DAILY_MAX_CREDIT_LIMIT_TRANS, TransactionParam.MERCHANT_MONTHLY_MAX_CREDIT_LIMIT_TRANS,
            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, TransactionParam.DEPOSIT,
            TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, TransactionParam.CATEGORY_MERCHANT_SINGLE_MAX_OF_TRAN,
            TransactionParam.PAYWAY_DAY_CREDIT_LIMITS, TransactionParam.PAYWAY_MONTH_CREDIT_LIMITS,
            TransactionParam.MERCHANT_BANKCARD_SINGLE_MAX_LIMIT, TransactionParam.MERCHANT_BANKCARD_DAY_MAX_LIMIT, TransactionParam.MERCHANT_BANKCARD_MONTH_MAX_LIMIT,
            TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT, TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT

    );
    
    public static final List<String> mergeParamsKey = Arrays.asList(TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS,
            TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, TransactionParam.DEPOSIT,
            TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, TransactionParam.LADDER_FEE_RATES, MerchantConfig.LADDER_STATUS, TransactionParam.LADDER_FEE_RATE_TAG,TransactionParam.FEE_RATE_TAG,
            TransactionParam.PARAMS_BANKCARD_FEE, MerchantConfig.CHANNEL_STATUS, TransactionParam.CHANNEL_FEE_RATE_TAG,
            TransactionParam.ALIPAY_HUABEI_STATUS, TransactionParam.ALIPAY_HUABEI_PARAMS, TransactionParam.ALIPAY_HUABEI_LIMIT, TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT,
            TransactionParam.CHANNEL_LADDER_FEE_RATES, TransactionParam.FEE_RATE_TYPE, TransactionParam.FITNESS_STATUS, TransactionParam.JD_BAITIAO_STATUS, TransactionParam.JD_BAITIAO_LIMIT
    );

    //subPayway 与MerchantConfigCustom表value字段的映射
    @SuppressWarnings("unchecked")
    public static final Map<String, String> subPaywaysValueColName = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE + "", MerchantConfigCustom.B2C_VALUE,
            SUB_PAYWAY_QRCODE + "", MerchantConfigCustom.C2B_VALUE,
            SUB_PAYWAY_WAP + "", MerchantConfigCustom.WAP_VALUE,
            SUB_PAYWAY_MINI + "", MerchantConfigCustom.MINI_VALUE,
            SUB_PAYWAY_APP + "", MerchantConfigCustom.APP_VALUE,
            SUB_PAYWAY_H5 + "", MerchantConfigCustom.H5_VALUE
    );

    public static final Map<Integer, String[]> subpaywayCloNames = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE, new String[]{MerchantConfig.B2C_FEE_RATE, MerchantConfig.B2C_FORMAL, MerchantConfig.B2C_STATUS, MerchantConfig.PROVIDER},
            SUB_PAYWAY_QRCODE, new String[]{MerchantConfig.C2B_FEE_RATE, MerchantConfig.C2B_FORMAL, MerchantConfig.C2B_STATUS, MerchantConfig.PROVIDER},
            SUB_PAYWAY_WAP, new String[]{MerchantConfig.WAP_FEE_RATE, MerchantConfig.WAP_FORMAL, MerchantConfig.WAP_STATUS, MerchantConfig.PROVIDER},
            SUB_PAYWAY_MINI, new String[]{MerchantConfig.MINI_FEE_RATE, MerchantConfig.MINI_FORMAL, MerchantConfig.MINI_STATUS, MerchantConfig.PROVIDER},
            SUB_PAYWAY_APP, new String[]{MerchantConfig.APP_FEE_RATE, MerchantConfig.APP_FORMAL, MerchantConfig.APP_STATUS, MerchantConfig.PROVIDER},
            SUB_PAYWAY_H5, new String[]{MerchantConfig.H5_FEE_RATE, MerchantConfig.H5_FORMAL, MerchantConfig.H5_STATUS, MerchantConfig.PROVIDER},
            null, new String[]{
                    MerchantConfig.B2C_FEE_RATE, MerchantConfig.B2C_FORMAL, MerchantConfig.B2C_STATUS,
                    MerchantConfig.C2B_FEE_RATE, MerchantConfig.C2B_FORMAL, MerchantConfig.C2B_STATUS,
                    MerchantConfig.WAP_FEE_RATE, MerchantConfig.WAP_FORMAL, MerchantConfig.WAP_STATUS,
                    MerchantConfig.MINI_FEE_RATE, MerchantConfig.MINI_FORMAL, MerchantConfig.MINI_STATUS,
                    MerchantConfig.APP_FEE_RATE, MerchantConfig.APP_FORMAL, MerchantConfig.APP_STATUS,
                    MerchantConfig.H5_FEE_RATE, MerchantConfig.H5_FORMAL, MerchantConfig.H5_STATUS,
                    MerchantConfig.PROVIDER
            }
    );

    Map<Integer, Map<String, String>> specialCols = CollectionUtil.hashMap(
            SUB_PAYWAY_BARCODE, CollectionUtil.hashMap(MerchantConfig.B2C_AGENT_NAME, MerchantConfig.B2C_FORMAL),
            SUB_PAYWAY_QRCODE, CollectionUtil.hashMap(MerchantConfig.C2B_AGENT_NAME, MerchantConfig.C2B_FORMAL),
            SUB_PAYWAY_WAP, CollectionUtil.hashMap(MerchantConfig.WAP_AGENT_NAME, MerchantConfig.WAP_FORMAL),
            SUB_PAYWAY_MINI, CollectionUtil.hashMap(MerchantConfig.MINI_AGENT_NAME, MerchantConfig.MINI_FORMAL),
            SUB_PAYWAY_APP, CollectionUtil.hashMap(MerchantConfig.APP_AGENT_NAME, MerchantConfig.APP_FORMAL),
            SUB_PAYWAY_H5, CollectionUtil.hashMap(MerchantConfig.H5_AGENT_NAME, MerchantConfig.H5_FORMAL),
            null, CollectionUtil.hashMap(MerchantConfig.B2C_AGENT_NAME, MerchantConfig.B2C_FORMAL,
                    MerchantConfig.C2B_AGENT_NAME, MerchantConfig.C2B_FORMAL,
                    MerchantConfig.WAP_AGENT_NAME, MerchantConfig.WAP_FORMAL,
                    MerchantConfig.MINI_AGENT_NAME, MerchantConfig.MINI_FORMAL,
                    MerchantConfig.APP_AGENT_NAME, MerchantConfig.APP_FORMAL,
                    MerchantConfig.H5_AGENT_NAME, MerchantConfig.H5_FORMAL
            )
    );

    // 适用于间连服务商 template：交易参数模板；notEmpty：交易参数中的非空参数，需按照最小级别设置
    Map<String, Map<String, Object>> tradeParamsTemplates = CollectionUtil.hashMap(
            TransactionParam.CIBBANK_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CIBBANK_MCH_ID, null,
                            TransactionParam.CIBBANK_MCH_KEY, null,
                            TransactionParam.CIBBANK_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.CIBBANK_WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.CIBBANK_WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.CIBBANK_WEIXIN_MINI_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.CIBBANK_MCH_ID)
            ),
            TransactionParam.CITICBANK_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CITICBANK_MCH_ID, null,
                            TransactionParam.CITICBANK_MCH_KEY, null,
                            TransactionParam.CITICBANK_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.CITICBANK_WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.CITICBANK_WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.CITICBANK_WEIXIN_MINI_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.CITICBANK_MCH_ID)
            ),
            TransactionParam.LAKALA_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.LAKALA_MERC_ID, null,
                            TransactionParam.LAKALA_TERM_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.LAKALA_MERC_ID)
            ),
            TransactionParam.LAKALA_WANMA_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.LAKALA_WANMA_MCH_ID, null,
                            TransactionParam.ALIPAY_PID, null,
                            TransactionParam.WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.LAKALA_WANMA_MCH_ID)
            ),
            TransactionParam.NUCC_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.NUCC_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.NUCC_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.NUCC_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.NUCC_SP_MCH_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.NUCC_WEIXIN_SUB_MCH_ID, TransactionParam.NUCC_ALIPAY_SUB_MCH_ID, TransactionParam.NUCC_SP_MCH_ID)
            ),
            TransactionParam.UNION_PAY_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID)
            ),
            TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.LAKALA_UNION_PAY_PROVIDER_MCH_ID, null,
                            TransactionParam.LAKALA_UNION_PAY_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, null,
                            TransactionParam.ALIPAY_SERVICE_ID, null,
                            TransactionParam.ALIPAY_MCH_CATEGORY, null,
                            TransactionParam.ALIPAY_STORE_ID, null,
                            TransactionParam.LAKALA_UNION_PAY_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.TRADE_PARAMS_MERCHANT_NAME, null,
                            TransactionParam.IS_AFFILIATED, false,
                            TransactionParam.ORIGINAL_PROVIDER_MCH_ID, null
                            ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.LAKALA_UNION_PAY_WEIXIN_SUB_MCH_ID, TransactionParam.LAKALA_UNION_PAY_ALIPAY_SUB_MCH_ID)
            ),
            TransactionParam.UNION_PAY_OPEN_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_OPEN_MCH_ID, null,
                            TransactionParam.UNION_PAY_OPEN_TERM_ID, null,
                            TransactionParam.LIQUIDATION_NEXT_DAY, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.UNION_PAY_OPEN_MCH_ID)
            ),
            TransactionParam.UNION_PAY_DIRECT_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, null,
                            TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, null,
                            TransactionParam.ALIPAY_SERVICE_ID, null,
                            TransactionParam.ALIPAY_MCH_CATEGORY, null,
                            TransactionParam.UNION_PAY_DIRECT_ALIPAY_STORE_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID, TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID)
            ),
            TransactionParam.UNION_PAY_TL_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", Maps.newHashMap(),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.UNION_PAY_TL_UNION_MCH_ID,
                            TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID,
                            TransactionParam.UNION_PAY_TL_ALIPAY_SUB_MCH_ID)
            ),
            TransactionParam.CHINAUMS_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CHINAUMS_MCH_CODE, null,
                            TransactionParam.CHINAUMS_TERM_CODE, null,
                            TransactionParam.CHINAUMS_CSB_MCH_CODE, null,
                            TransactionParam.CHINAUMS_CSB_TERM_CODE, null,
                            TransactionParam.CHINAUMS_APP_ID, null,
                            TransactionParam.CHINAUMS_APP_KEY, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.CHINAUMS_TERM_CODE, TransactionParam.CHINAUMS_CSB_TERM_CODE)
            ),
            TransactionParam.UNION_PAY_ONLINE_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_ONLINE_MCH_ID, null,
                            TransactionParam.UNION_PAY_ONLINE_CERT_ID, null,
                            TransactionParam.UNION_PAY_ONLINE_PRIVATE_KEY, null,
                            TransactionParam.UNION_PAY_ONLINE_BIZ_TYPE, null

                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.UNION_PAY_ONLINE_MCH_ID)
            ),
            TransactionParam.UEPAY_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.UEPAY_MERCHANT_NO, null,
                            TransactionParam.UEPAY_SECRET_KEY, null,
                            TransactionParam.UEPAY_STORE_CODE, null,
                            TransactionParam.UEPAY_TERMINAL_CODE, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.UEPAY_MERCHANT_NO, TransactionParam.UEPAY_SECRET_KEY)
            ),
            TransactionParam.CMB_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CMB_APP_ID, null,
                            TransactionParam.CMB_SECRET, null,
                            TransactionParam.SQB_PRIVATE_KEY, null,
                            TransactionParam.CMB_PUBLIC_KEY, null,
                            TransactionParam.MER_ID, null,
                            TransactionParam.USER_ID, null,
                            TransactionParam.WEIXIN_SUB_APP_ID, null,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.CMB_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.CMB_ALIPAY_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.MER_ID, TransactionParam.USER_ID)
            ),
            TransactionParam.PSBCBANK_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.PSBCBANK_PROVIDER_MCH_ID, null,
                            TransactionParam.PSBCBANK_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.PSBCBANK_WEIXIN_SUB_MCH_ID, null

                    ),
                    "notEmpty", CollectionUtil.hashSet()
            ),
            TransactionParam.CGBBANK_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CGBBANK_PROVIDER_MCH_ID, null,
                            TransactionParam.CGBBANK_TERMINAL_ID, null,
                            TransactionParam.CGBBANK_PRE_TERMINAL_ID, null,
                            TransactionParam.CGBBANK_LATITUDE, null,
                            TransactionParam.CGBBANK_LONGITUDE, null,
                            TransactionParam.CGBBANK_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.CGBBANK_ALIPAY_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.CGBBANK_PROVIDER_MCH_ID)
            ),
            TransactionParam.FOXCONN_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.FOXCONN_EQUIPMENT_SN, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.FOXCONN_EQUIPMENT_SN)
            ),
            TransactionParam.HXBANK_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.HXBANK_DEVELOP_APP_ID, null,
                            TransactionParam.HXBANK_PROVIDER_MCH_ID, null,
                            TransactionParam.HXBANK_PROVIDER_SERVICE_ID, null,
                            TransactionParam.HXBANK_PROVIDER_TERM_ID, null,
                            TransactionParam.HXBANK_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.HXBANK_WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.HXBANK_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.HXBANK_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.SQB_HXBANK_PRIVATE_KEY, null,
                            TransactionParam.HXBANK_PUBLIC_KEY, null,
                            TransactionParam.HXBANK_DCEP_BUS_CTGTY, null,
                            TransactionParam.HXBANK_VERSION, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.HXBANK_DEVELOP_APP_ID, TransactionParam.HXBANK_PROVIDER_MCH_ID)
            ),
            TransactionParam.CCB_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CCB_INST_NO, null,
                            TransactionParam.CCB_MERCHANT_ID, null,
                            TransactionParam.CCB_TERMINAL_ID, null,
                            TransactionParam.CCB_TERMINAL_NO, null,
                            TransactionParam.CCB_POS_ID, null,
                            TransactionParam.CCB_BRANCH_ID, null,
                            TransactionParam.CCB_PUBLIC_KEY, null,
                            TransactionParam.CCB_SECRET_KEY, null,
                            TransactionParam.CCB_WX_SUB_APPID, null,
                            TransactionParam.CCB_WX_MINI_SUB_APPID, null,
                            TransactionParam.CCB_WX_MINI_SUB_APPSECRET, null,
                            TransactionParam.CCB_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.CCB_ALIPAY_SUB_MCH_ID, null

                    ),
                    "notEmpty", CollectionUtil.hashSet()
            ),
            TransactionParam.ICBC_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.ICBC_MER_ID, null,
                            TransactionParam.ICBC_MER_PRTCL_NO, null,
                            TransactionParam.ICBC_APP_ID, null,
                            TransactionParam.ICBC_PRIVATE_KEY, null,
                            TransactionParam.ICBC_PUBLIC_KEY, null,
                            TransactionParam.ICBC_WX_SUB_APPID, null,
                            TransactionParam.ICBC_WX_MINI_SUB_APPID, null,
                            TransactionParam.ICBC_WX_MINI_SUB_APPSECRET, null,
                            TransactionParam.ICBC_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.ICBC_ALIPAY_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet()
            ),
            TransactionParam.TL_SYB_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.TL_SYB_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.TL_SYB_ALIPAY_SYS_PID, null,
                            TransactionParam.TL_SYB_APP_Id, null,
                            TransactionParam.TL_SYB_ORG_ID, null,
                            TransactionParam.TL_SYB_CUS_ID, null,
                            TransactionParam.TL_SYB_PRIVATE_KEY, null,
                            TransactionParam.TL_SYB_PUBLIC_KEY, null,
                            TransactionParam.TL_SYB_SUB_APPID, null,
                            TransactionParam.TL_SYB_SUB_APPSECRET, null,
                            TransactionParam.TL_SYB_SUB_SUB_MCH_ID, null,
                            TransactionParam.TL_SYB_MINI_SUB_APPID, null,
                            TransactionParam.TL_SYB_MINI_SUB_APPSECRET, null,
                            TransactionParam.TL_SYB_UNION_MCH_ID, null,
                            TransactionParam.TL_SYB_CERT_ID, null
                            ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.TL_SYB_CUS_ID)
            ),
            TransactionParam.CCB_GIFT_CARD_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CCB_GIFT_CARD_PARAMS_BUSINESS_ID, null,
                            TransactionParam.CCB_GIFT_CARD_PARAMS_CAMPUS_ID, null,
                            TransactionParam.CCB_GIFT_CARD_PARAMS_VPOS_ID, null,
                            TransactionParam.CCB_GIFT_CARD_PARAMS_CORP_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.CCB_GIFT_CARD_PARAMS_BUSINESS_ID)
            ),
            TransactionParam.HAIKE_UNION_PAY_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.HAIKE_UNION_PAY_AGENT_NO, null,
                            TransactionParam.HAIKE_UNION_PAY_CERT_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_PROVIDER_MCH_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_MCH_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_APP_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_SUB_APP_SECRET, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_WEIXIN_MINI_SUB_APP_SECRET, null,
                            TransactionParam.HAIKE_UNION_PAY_ALIPAY_APP_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_SYS_PROVIDER_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_UNION_MER_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_UNION_MER_NAME, null,
                            TransactionParam.HAIKE_UNION_PAY_UNION_CERT_ID, null,
                            TransactionParam.HAIKE_UNION_PAY_UNION_ACQ_CODE, null,
                            TransactionParam.HAIKE_UNION_PAY_UNION_PNR_INS_ID_CD, null,
                            TransactionParam.HAIKE_UNION_PAY_UNION_MCH_CAT_CODE, null,
                            TransactionParam.TRADE_PARAMS_MERCHANT_NAME, null,
                            TransactionParam.IS_AFFILIATED, false,
                            TransactionParam.ORIGINAL_PROVIDER_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.HAIKE_UNION_PAY_WEIXIN_SUB_MCH_ID, TransactionParam.HAIKE_UNION_PAY_ALIPAY_SUB_MCH_ID, TransactionParam.HAIKE_UNION_PAY_UNION_MER_ID)
            ),
            TransactionParam.FUYOU_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.FUYOU_AGENT_NO, null,
                            TransactionParam.FUYOU_PROVIDER_MCH_ID, null,
                            TransactionParam.FUYOU_CHANNEL_ID, null,
                            TransactionParam.FUYOU_WEIXIN_APP_ID, null,
                            TransactionParam.FUYOU_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.FUYOU_WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.FUYOU_WEIXIN_MCH_ID, null,
                            TransactionParam.FUYOU_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.FUYOU_ALIPAY_APP_ID, null,
                            TransactionParam.FUYOU_SYS_PROVIDER_ID, null,
                            TransactionParam.FUYOU_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.FUYOU_BANK_QUERY_PRIVATE_KEY, null,
                            TransactionParam.FUYOU_BANK_TERM_ID, null,
                            TransactionParam.FUYOU_UNION_PAY_OPEN_MCH_ID, null,
                            TransactionParam.LIQUIDATION_NEXT_DAY, null

                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.FUYOU_PROVIDER_MCH_ID)
            ),
            TransactionParam.GUOTONG_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.GUOTONG_PROVIDER_MCH_ID, null,
                            TransactionParam.GUOTONG_CHANNEL_ID, null,
                            TransactionParam.GUOTONG_WEIXIN_APP_ID, null,
                            TransactionParam.GUOTONG_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.GUOTONG_WEIXIN_MINI_SUB_APP_ID, null,
                            TransactionParam.GUOTONG_WEIXIN_MCH_ID, null,
                            TransactionParam.GUOTONG_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.GUOTONG_ALIPAY_APP_ID, null,
                            TransactionParam.GUOTONG_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.GUOTONG_UNION_PAY_OPEN_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.GUOTONG_PROVIDER_MCH_ID)
            ), TransactionParam.PAB_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.PAB_SM2_USER_ID, null,
                            TransactionParam.PRIVATE_KEY, null,
                            TransactionParam.S_SIGNCERT_ID, null,
                            TransactionParam.PAB_PAY_B2C_TERMINAL_ID, null,
                            TransactionParam.PAB_PAY_OTHER_B2C_TERMINAL_ID, null,
                            TransactionParam.PAB_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.PAB_PAY_WEIXIN_SUB_APP_ID, null,
                            TransactionParam.FUYOU_WEIXIN_SUB_MCH_ID, null

                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.PAB_PAY_PROVIDER_MCH_ID)
            ),
            TransactionParam.BOCOM_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.BOCOM_APP_ID, null,
                            TransactionParam.BOCOM_PROVIDER_MCH_ID, null,
                            TransactionParam.BOCOM_WEIXIN_MCH_ID, null,
                            TransactionParam.BOCOM_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.BOCOM_SYS_PROVIDER_ID, null,
                            TransactionParam.BOCOM_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.BOCOM_PRIVATE_KEY, null,
                            TransactionParam.BOCOM_PUBLIC_KEY, null

                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.BOCOM_APP_ID, TransactionParam.BOCOM_PROVIDER_MCH_ID))
            ,
            TransactionParam.ABC_UP_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.ABC_COUNTNO, null,
                            TransactionParam.ABC_PUBLIC_KEY, null,
                            TransactionParam.ABC_WEIXIN_MCH_ID, null,
                            TransactionParam.ABC_WEIXIN_SUB_MCH_ID, null,
                            TransactionParam.ABC_AILIPAY_SUB_MCH_ID,null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.ABC_COUNTNO)
            ),
            TransactionParam.ENTPAY_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.ENTPAY_PLATFORM_ID, null,
                            TransactionParam.ENTPAY_ENT_ID, null,
                            TransactionParam.ENTPAY_ENT_NAME, null,
                            TransactionParam.ENTPAY_PRIVATE_SERIAL_NO, null,
                            TransactionParam.ENTPAY_PUBLIC_SERIAL_NO, null,
                            TransactionParam.ENTPAY_PRIVATE_KEY, null,
                            TransactionParam.ENTPAY_PUBLIC_KEY, null

                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.ENTPAY_PLATFORM_ID, TransactionParam.ENTPAY_ENT_ID, TransactionParam.ENTPAY_ENT_NAME)
            ),
            TransactionParam.ZJTLCB_UP_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.APP_ID,null,
                            TransactionParam.ZJTLCB_APP_SECRET_KEY, null,
                            TransactionParam.ZJTLCB_SM2_PRIVATE_KEY, null,
                            TransactionParam.ZJTLCB_PUBLIC_KEY, null,
                            TransactionParam.ZJTLCB_CHANNEL_CODE,null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.PROVIDER_MCH_ID)
            ),
            TransactionParam.FJNX_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.FJNX_PUBLIC_KEY,null,
                            TransactionParam.ZJTLCB_APP_SECRET_KEY, null,
                            TransactionParam.FJNX_PRIVATE_KEY, null,
                            TransactionParam.FJNX_PASSWORD, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.PROVIDER_MCH_ID)
            ),
            TransactionParam.CMBC_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.CMBC_PLATFORM_ID, null,
                            TransactionParam.CMBC_MCH_ID, null,
                            TransactionParam.CMBC_PRIVATE_KEY, null,
                            TransactionParam.CMBC_PRIVATE_KEY_PASSWORD, null,
                            TransactionParam.CMBC_PUBLIC_KEY, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.CMBC_MCH_ID)
            ),
            TransactionParam.JYCARD_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.JYCARD_PROVIDER_MCH_ID, null,
                            TransactionParam.JYCARD_ACC_TYPE, null,
                            TransactionParam.JYCARD_APP_KEY, null,
                            TransactionParam.JYCARD_APP_SECRET, null,
                            TransactionParam.JYCARD_PRIVATE_KEY, null,
                            TransactionParam.JYCARD_PUBLIC_KEY, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.JYCARD_PROVIDER_MCH_ID)
            ),
            TransactionParam.JSB_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.JSB_PROVIDER_MCH_ID, null,
                            TransactionParam.JSB_PROVIDER_MCH_UUID, null,
                            TransactionParam.JSB_CHANNEL_PARTNER_ID, null,
                            TransactionParam.JSB_SQB_PRIVATE_KEY, null,
                            TransactionParam.JSB_PROVIDER_PUBLIC_KEY, null,
                            TransactionParam.JSB_UNION_PAY_SUB_MCH_ID, null,
                            TransactionParam.JSB_ALIPAY_APP_ID, null,
                            TransactionParam.JSB_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.JSB_WECHAT_SUB_APP_ID, null,
                            TransactionParam.JSB_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.JSB_WECHAT_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.JSB_PROVIDER_MCH_ID)
            ),
            TransactionParam.TL_S2P_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.TL_S2P_PROVIDER_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.TL_S2P_PROVIDER_MCH_ID)
            ),
            TransactionParam.YOP_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.YOP_PROVIDER_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.YOP_PROVIDER_MCH_ID)
            ),
            TransactionParam.PKX_AIRPORT_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.PKX_AIRPORT_PROVIDER_MCH_ID, null,
                            TransactionParam.PKX_AIRPORT_MALL_ID, null,
                            TransactionParam.PKX_AIRPORT_SELLER_ID, null,
                            TransactionParam.PKX_AIRPORT_USERNAME, null,
                            TransactionParam.PKX_AIRPORT_SIGN_KEY, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.PKX_AIRPORT_PROVIDER_MCH_ID)
            ),
            TransactionParam.LZCCB_UP_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.LZCCB_UP_TRADE_PARAMS, null,
                            TransactionParam.LZCCB_PROVIDER_MCH_ID, null,
                            TransactionParam.LZCCB_PROVIDER_STORE_SN, null,
                            TransactionParam.LZCCB_SM2_PKEY, null,
                            TransactionParam.LZCCB_SM3_KEY, null,
                            TransactionParam.LZCCB_APP_ID, null,
                            TransactionParam.LZCCB_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.LZCCB_WECHAT_SUB_APP_ID, null,
                            TransactionParam.LZCCB_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.LZCCB_WECHAT_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.LZCCB_PROVIDER_MCH_ID)
            ),
            TransactionParam.ZTKX_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.ZTKX_PROVIDER_MCH_ID, null,
                            TransactionParam.PLATFORM_MCH_ID, null,
                            TransactionParam.ZTKX_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.ZTKX_WECHAT_SUB_APP_ID, null,
                            TransactionParam.ZTKX_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.ZTKX_WECHAT_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.ZTKX_PROVIDER_MCH_ID)
            ),
            TransactionParam.HOPE_EDU_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.HOPE_EDU_CHANNEL_CODE, null,
                            TransactionParam.HOPE_EDU_PRIVATE_KEY, null,
                            TransactionParam.HOPE_EDU_PUBLIC_KEY, null
                    ),
                    "notEmpty", CollectionUtil.hashSet()
            ),
            TransactionParam.MACAU_PASS_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.MACAU_PASS_SUB_MERCHANT_ID, null,
                            TransactionParam.MACAU_PASS_SUB_MERCHANT_NAME, null,
                            TransactionParam.MACAU_PASS_SUB_MERCHANT_INDUSTRY, null,
                            TransactionParam.MACAU_PASS_STORE_NAME, null,
                            TransactionParam.MACAU_PASS_STORE_ID, null,
                            TransactionParam.MACAU_PASS_TERMINAL_ID, null,
                            TransactionParam.MACAU_PASS_WEIXIN_SUB_APPID, null,
                            TransactionParam.MACAU_PASS_WEIXIN_MINI_APPID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.MACAU_PASS_SUB_MERCHANT_ID)
            ),
            TransactionParam.BOC_UP_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.BOC_UP_TRADE_PARAMS, null,
                            TransactionParam.BOC_PROVIDER_MCH_ID, null,
                            TransactionParam.BOC_KEY_STORE_PATH_ID, null,
                            TransactionParam.BOC_KEY_STORE_PASSWORD, null,
                            TransactionParam.BOC_KEY_PASSWORD, null,
                            TransactionParam.BOC_ROOT_CERTIFICATE_PATH_ID, null,
                            TransactionParam.BOC_PAY_KEY_STORE_PATH_ID, null,
                            TransactionParam.BOC_PAY_KEY_STORE_PASSWORD, null,
                            TransactionParam.BOC_PAY_KEY_PASSWORD, null,
                            TransactionParam.BOC_PAY_ROOT_CERTIFICATE_PATH_ID, null,
                            TransactionParam.BOC_PAY_PROVIDER_MCH_ID, null,
                            TransactionParam.BOC_PAY_PROVIDER_TERM_ID, null,
                            TransactionParam.BOC_PAY_BUSS_ID, null,
                            TransactionParam.BOC_APP_ID, null,
                            TransactionParam.BOC_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.BOC_WECHAT_SUB_APP_ID, null,
                            TransactionParam.BOC_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.BOC_WECHAT_SUB_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.BOC_PROVIDER_MCH_ID)
            ),
            TransactionParam.XZX_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.XZX_MCH_ACCT_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.XZX_MCH_ACCT_ID)
            ),
            TransactionParam.YEAH_PAY_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.YEAH_PAY_MERCHANT_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.YEAH_PAY_MERCHANT_ID)
            ),
            TransactionParam.PSBC_SX_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.PSBC_SX_PROVIDER_PARTNER_ID, null,
                            TransactionParam.PSBC_SX_PROVIDER_MCH_ID, null,
                            TransactionParam.PSBC_SX_SOP_PUBLIC_KEY, null,
                            TransactionParam.PSBC_SX_SQB_PRIVATE_KEY, null,
                            TransactionParam.PSBC_SX_SQB_PUBLIC_KEY, null,
                            TransactionParam.PSBC_SX_SQB_APP_ID, null,
                            TransactionParam.PSBC_SX_PLATFORM_ID, null,
                            TransactionParam.PSBC_SX_APP_ID, null,
                            TransactionParam.PSBC_SX_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.PSBC_SX_WECHAT_SUB_APP_ID, null,
                            TransactionParam.PSBC_SX_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.PSBC_SX_WECHAT_SUB_MCH_ID, null

                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.PSBC_SX_PROVIDER_MCH_ID)
            ),
            TransactionParam.AIRWALLEX_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.AIRWALLEX_PROVIDER_MCH_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.AIRWALLEX_PROVIDER_MCH_ID)
            ),
            TransactionParam.WECARD_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.WECARD_PROVIDER_MCH_ID, null,
                            TransactionParam.WECARD_SECRET_ID, null,
                            TransactionParam.WECARD_OCODE, null,
                            TransactionParam.WECARD_PRIVATE_KEY, null,
                            TransactionParam.WECARD_PUBLIC_KEY, null,
                            TransactionParam.WECARD_PROVIDER_PUBLIC_KEY, null,
                            TransactionParam.WECARD_WX_APPID, null,
                            TransactionParam.WECARD_WX_SUB_APPID, null,
                            TransactionParam.WECARD_WX_MINI_SUB_APP_ID, null,
                            TransactionParam.WECARD_ALI_APPID,null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.WECARD_PROVIDER_MCH_ID)
            ),
            TransactionParam.BCS_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.BCS_PROVIDER_MCH_ID, null,
                            TransactionParam.BCS_ALIPAY_SUB_MCH_ID, null,
                            TransactionParam.BCS_WECHAT_SUB_MCH_ID, null,
                            TransactionParam.BCS_WECHAT_SUB_APP_ID, null,
                            TransactionParam.BCS_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.BCS_WECHAT_SUB_APP_SECRET, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.BCS_PROVIDER_MCH_ID)
            ),
            TransactionParam.ABCBANK_TRADE_PARAMS, CollectionUtil.hashMap(
                    "template", CollectionUtil.hashMap(
                            TransactionParam.ABCBANK_PROVIDER_MCH_ID, null,
                            TransactionParam.ABCBANK_CHANNEL_PARTNER_ID, null,
                            TransactionParam.ABCBANK_PROVIDER_APP_ID, null,
                            TransactionParam.ABCBANK_APP_SECRET, null,
                            TransactionParam.ABCBANK_SQB_PRIVATE_KEY, null,
                            TransactionParam.ABCBANK_PROVIDER_PUBLIC_KEY, null,
                            TransactionParam.ABCBANK_UNION_PAY_SUB_MCH_ID, null,
                            TransactionParam.ABCBANK_ALIPAY_APP_ID, null,
                            TransactionParam.ABCBANK_WECHAT_SUB_APP_ID, null,
                            TransactionParam.ABCBANK_WECHAT_MINI_SUB_APP_ID, null,
                            TransactionParam.ABCBANK_DEVICE_TYPE, null,
                            TransactionParam.ABCBANK_PROVIDER_TERM_ID, null
                    ),
                    "notEmpty", CollectionUtil.hashSet(TransactionParam.ABCBANK_PROVIDER_MCH_ID)
            )
    );

    public static final Map<String/*vendor_app_appid*/, String/*terminal_type*/> TERMINAL_APP_ID_TYPE = CollectionUtil
            .hashMap(
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "POS终端",
                    "****************", "扫码王",
                    "****************", "扫码王",
                    "****************", "扫码王",
                    "****************", "扫码王",
                    "****************", "扫码王",
                    "****************", "扫码王",
                    "****************", "扫码王",
                    "2018050700000676", "扫码王",
                    "2018051400000699", "扫码王",
                    "2018051400000700", "扫码王",
                    "2018051400000701", "扫码王",
                    "2018052200000725", "扫码王",
                    "2017030600000083", "收银插件",
                    "2018041000000638", "收银插件",
                    "2018111100000001", "自助点餐码",
                    "2019012900001333", "久久折",
                    "****************", "收钱音箱收款码",
                    "****************", "收钱吧App",
                    "****************", "收款门店码",
                    "****************", "收款门店码"
            );

    public static final List<String> UN_MERGE_APP_PARAM_KEYS = Arrays.asList(TransactionParam.LADDER_FEE_RATES, MerchantConfig.LADDER_STATUS, TransactionParam.LADDER_FEE_RATE_TAG,TransactionParam.FEE_RATE_TAG,
            TransactionParam.PARAMS_BANKCARD_FEE, MerchantConfig.CHANNEL_STATUS, TransactionParam.CHANNEL_FEE_RATE_TAG, TransactionParam.FEE_RATE_TYPE
    );

    private Dao<Map<String, Object>> storeConfigDao;
    private Dao<Map<String, Object>> storeAppConfigDao;
    private Dao<Map<String, Object>> merchantConfigDao;
    private Dao<Map<String, Object>> merchantAppConfigDao;
    private Dao<Map<String, Object>> merchantConfigCustomDao;
    private Dao<Map<String, Object>> vendorConfigDao;
    private Dao<Map<String, Object>> solicitorConfigDao;
    private Dao<Map<String, Object>> agentDao;
    private Dao<Map<String, Object>> merchantDao;
    private Dao<Map<String, Object>> storeDao;
    private Dao<Map<String, Object>> terminalConfigDao;
    private Dao<Map<String, Object>> tradeExtConfigDao;
    private Dao<Map<String, Object>> externalExtraConfigDao;


    @Autowired
    private MerchantService merchantService;
    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private AlipayStoreService alipayStoreService;
    @Autowired
    private RsaKeyService rsaKeyService;

    @Autowired
    public TradeConfigServiceImpl(DataRepository repository) {
        this.storeConfigDao = repository.getStoreConfigDao();
        this.storeAppConfigDao = repository.getStoreAppConfigDao();
        this.merchantConfigDao = repository.getMerchantConfigDao();
        this.merchantConfigCustomDao = repository.getMerchantConfigCustomDao();
        this.vendorConfigDao = repository.getVendorConfigDao();
        this.solicitorConfigDao = repository.getSolicitorConfigDao();
        this.agentDao = repository.getAgentDao();
        this.merchantDao = repository.getMerchantDao();
        this.storeDao = repository.getStoreDao();
        this.terminalConfigDao = repository.getTerminalConfigDao();
        this.merchantAppConfigDao = repository.getMerchantAppConfigDao();
        this.tradeExtConfigDao = repository.getTradeExtConfigDao();
        this.externalExtraConfigDao = repository.getExternalExtraConfigDao();
    }

    @Override
    public Map createVendorConfig(Map vendorConfig) {
        if (vendorConfig.get(DaoConstants.ID) == null) {
            vendorConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        vendorConfigDao.save(vendorConfig);
        return vendorConfig;
    }

    @Override
    public void deleteVendorConfig(String vendorConfigId) {
        vendorConfigDao.delete(vendorConfigId);
    }

    @Override
    public Map updateVendorConfig(Map vendorConfig) {
        vendorConfigDao.updatePart(vendorConfig);
        return getVendorConfig(BeanUtil.getPropString(vendorConfig, DaoConstants.ID));
    }

    @Override
    public Map getVendorConfig(String vendorConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(vendorConfigId);
        return vendorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public Map createAgent(Map agent) {
        if (agent.get(DaoConstants.ID) == null) {
            agent.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        agentDao.save(agent);
        return agent;
    }

    @Override
    public void deleteAgent(String agentId) {
        agentDao.delete(agentId);
    }

    @Override
    public Map updateAgent(Map agent) {
        agentDao.updatePart(agent);
        return getAgent(BeanUtil.getPropString(agent, DaoConstants.ID));
    }

    @Override
    public Map getAgent(String agentId) {
        return agentDao.get(agentId);
    }

    @Override
    public ListResult findAgents(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        String details = BeanUtil.getPropString(queryFilter, Agent.DETAILS);
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(Agent.NAME, Agent.NAME);
            put(Agent.PROVIDER, Agent.PROVIDER);
            put(Agent.PAYWAY, Agent.PAYWAY);
            put(Agent.SUB_PAYWAY, Agent.SUB_PAYWAY);
            put(Agent.FORMAL, Agent.FORMAL);
            put(Agent.IS_PRIMARY, Agent.IS_PRIMARY);

        }});
        if (!StringUtil.empty(details)) {
            criteria.with(Agent.DETAILS).like("%" + details + "%");
        }
        List<String> paramsList = (List<String>) BeanUtil.getProperty(queryFilter, Agent.PARAMS);
        if (paramsList != null && !paramsList.isEmpty()) {
            for (String param : paramsList) {
                criteria.with(Agent.PARAMS).like("%" + param + "%");
            }
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = agentDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = agentDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public Map getAgentByName(String name) {
        Criteria criteria = Criteria.where(Agent.NAME).is(name);
        return agentDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getChannelByAgentName(String name) {
        Map result = new HashMap();
        Object[] names = name.split("_");
        int provider = (int) names[0];
        Map agent = getAgentByName(name);
        Map tradeParams = (Map) BeanUtil.getNestedProperty(agent, String.format("%s.%s", Agent.PARAMS, getTradeParamsKeyByProvider(provider)));
        int payway = BeanUtil.getPropInt(agent, Agent.PAYWAY);
        String channelNo = null;
        String parentMerchantId = null;
        switch (provider) {
            case 1003:
                channelNo = BeanUtil.getPropString(tradeParams, TransactionParam.CITICBANK_SIGN_AGENT_NO);
                parentMerchantId = BeanUtil.getPropString(tradeParams, TransactionParam.CITICBANK_GROUP_NO);
                break;
            case 1008:
                channelNo = BeanUtil.getPropString(tradeParams, TransactionParam.SWIFTPASS_SIGN_AGENT_NO);
                parentMerchantId = BeanUtil.getPropString(tradeParams, TransactionParam.SWIFTPASS_GROUP_NO);
                break;
            case 1011:
                channelNo = BeanUtil.getPropString(tradeParams, TransactionParam.LAKALA_WNAMA_RECE_ORG_NO);
                break;
            case 1013:
                channelNo = BeanUtil.getPropString(tradeParams, TransactionParam.NUCC_CHANNEL_ID);
                if (payway == 2) {
                    parentMerchantId = BeanUtil.getPropString(tradeParams, TransactionParam.NUCC_ALIPAY_PID);
                }
                if (payway == 3) {
                    parentMerchantId = BeanUtil.getPropString(tradeParams, TransactionParam.NUCC_WEIXIN_MCH_ID);
                }
                break;
            case 1014:
                channelNo = BeanUtil.getPropString(tradeParams, TransactionParam.UNION_PAY_CHANNEL_ID);
                if (payway == 2) {
                    parentMerchantId = BeanUtil.getPropString(tradeParams, TransactionParam.UNION_PAY_ALIPAY_APP_ID);
                }
                if (payway == 3) {
                    parentMerchantId = BeanUtil.getPropString(tradeParams, TransactionParam.UNION_PAY_WEIXIN_MCH_ID);
                }
                break;
            default:
                return new HashMap();
        }
        result.put(MerchantProviderTradeParams.CHANNEL_NO, channelNo);
        result.put(MerchantProviderTradeParams.PARENT_MERCHANT_ID, parentMerchantId);
        result.put(Agent.PAYWAY, payway);
        result.put(Agent.PROVIDER, provider);
        return result;
    }

    @Override
    public String getMerchantDefaultTrailAgentName(Integer provider, Map merchantInfo) {
        //todo 根据provider获取默认的agentName
        String province = BeanUtil.getPropString(merchantInfo, Merchant.PROVINCE);
        String city = BeanUtil.getPropString(merchantInfo, Merchant.CITY);
        Map content = systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_AGENT_SELECT_INFO);
        String agentName = BeanUtil.getPropString(content, city);
        if (agentName == null) {
            agentName = BeanUtil.getPropString(content, province);
        }
        return agentName;
    }


    @Override
    public Map getTradeParamsKeyActivityKeyMap() {
        return systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_TRADE_PARAMS_KEY_ACTIVITY_KEY);
    }

    @Override
    public Map createMerchantConfig(Map merchantConfig) {
        if(Objects.isNull(MapUtil.getPropValue(merchantConfig, MerchantConfig.PAYWAY))) {
            Map defaultMerchantConfig = getMerchantConfigByMerchantIdAndPayway(BeanUtil.getPropString(merchantConfig, MerchantConfig.MERCHANT_ID), null);
            // payway = null 的数据由于唯一索引约束失效，再次调用时，当做数据变更处理
            if(!Objects.isNull(defaultMerchantConfig)) {
                merchantConfig.put(DaoConstants.ID, defaultMerchantConfig.get(DaoConstants.ID));
                merchantConfig.put(DaoConstants.VERSION, defaultMerchantConfig.get(DaoConstants.VERSION));
                // params 值做部分替换，不做全量覆盖
                Map<String, Object> updateParams = (Map<String, Object>) merchantConfig.get(MerchantConfig.PARAMS);
                if(Objects.isNull(updateParams)){
                    merchantConfig.remove(MerchantConfig.PARAMS);
                }else if(!Objects.isNull(defaultMerchantConfig.get(MerchantConfig.PARAMS))){
                    ((Map)defaultMerchantConfig.get(MerchantConfig.PARAMS)).putAll(updateParams);
                    merchantConfig.put(MerchantConfig.PARAMS, defaultMerchantConfig.get(MerchantConfig.PARAMS));
                }
            }else {
                // 并发redis锁，单个商户同一时刻只允许创建1条，锁30s后自动过期，再次调用时会走上面的变更流程
                if(!simpleRedisLock.tryLock(REDIS_KEY_CREATE_MERCHANT_CONFIG_PREFIX + BeanUtil.getPropString(merchantConfig, MerchantConfig.MERCHANT_ID),
                        uuidGenerator.nextUuid(), 30, TimeUnit.SECONDS)) {
                    throw new CoreMerchantConfigAbnormalException("创建失败，请稍后再试！");
                }
            }
        }
        checkConfig(merchantConfig);
        if (merchantConfig.get(DaoConstants.ID) == null) {
            merchantConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        saveMerchantConfig(merchantConfig);
        return merchantConfig;
    }

    @Override
    public void deleteMerchantConfig(String merchantConfigId) {
        merchantConfigDao.delete(merchantConfigId);
        return;
    }

    @Override
    public Map updateMerchantConfigWithoutMessage(Map merchantConfig) {
        checkConfig(merchantConfig);
        merchantConfigDao.updatePart(merchantConfig);
        return getMerchantConfig(BeanUtil.getPropString(merchantConfig, DaoConstants.ID));
    }

    @Override
    public Map updateMerchantConfig(Map merchantConfig) {
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Map before = getMerchantConfig(id);
        checkConfig(merchantConfig);
        updateMerchantConfigWithRetry(merchantConfig);
        Map updateResult = getMerchantConfig(id);
        try {
            merchantConfigDataBusBiz.feeRateChange(before, updateResult);
        } catch (Exception e) {
            logger.error("{} 写入事件表失败", id, e);
        }
        return updateResult;
    }

    @Override
    public Map updateMerchantConfigAndLog(Map merchantConfig, OpLogCreateRequest opLogCreateRequest) {
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Map before = getMerchantConfig(id);
        Map after = updateMerchantConfig(merchantConfig);
        // provider 名称转化
        if (merchantConfig != null && merchantConfig.containsKey(MerchantConfig.PROVIDER)) {
            Integer beforeProviderId = MapUtils.getInteger(before, MerchantConfig.PROVIDER);
            Integer afterProviderId = MapUtils.getInteger(after, MerchantConfig.PROVIDER);
            if (beforeProviderId != null && afterProviderId != null && !beforeProviderId.equals(afterProviderId)) {
                Map<String, Object> beforeMetaProvider = businssCommonService.getMetaProviderById(beforeProviderId);
                before.put(MerchantConfig.PROVIDER, MapUtils.getString(beforeMetaProvider, MetaProvider.NAME));
                Map<String, Object> afterMetaProvider = businssCommonService.getMetaProviderById(afterProviderId);
                after.put(MerchantConfig.PROVIDER, MapUtils.getString(afterMetaProvider, MetaProvider.NAME));
            }
        }
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
        return after;
    }

    @Override
    public Map updateMerchantConfigAndLogV2(Map merchantConfig, OpLogCreateRequestV2 opLogCreateRequest) {
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Map before = getMerchantConfig(id);
        Map after = updateMerchantConfig(merchantConfig);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
        return after;
    }

    @Override
    public Map updateMerchantConfigByMerchantSnAndPayway(Map merchantConfig) {
        String merchantSn = BeanUtil.getPropString(merchantConfig, ConstantUtil.KEY_MERCHANT_SN);
        String merchantId = businssCommonService.getMerchantIdBySn(merchantSn);
        Map config = getMerchantConfigByMerchantIdAndPayway(merchantId, merchantConfig.get(MerchantConfig.PAYWAY) != null ? BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY) : null);
        MapUtil.removeKeys(merchantConfig, new String[]{ConstantUtil.KEY_MERCHANT_SN, MerchantConfig.PAYWAY});
        merchantConfig.put(DaoConstants.ID, BeanUtil.getPropString(config, DaoConstants.ID));
        TradeConfigService tradeConfigService = SpringContextHolder.getBean(TradeConfigService.class);
        return tradeConfigService.updateMerchantConfig(merchantConfig);
    }

    @Override
    public Map updateMerchantConfigByMerchantSnAndPaywayAndLog(Map merchantConfig, OpLogCreateRequestV2 opLogCreateRequest) {
        Integer payway = org.apache.commons.collections4.MapUtils.getInteger(merchantConfig, MerchantConfig.PAYWAY);
        String merchantId = BeanUtil.getPropString(merchantConfig, MerchantConfig.MERCHANT_ID);
        Map<String, Object> before = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map result = updateMerchantConfigByMerchantSnAndPayway(merchantConfig);
        Map<String, Object> after = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
        return result;
    }

    @Override
    public Map getMerchantConfig(String merchantConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantConfigId);
        return merchantConfigDao.filter(criteria).fetchOne();
    }

    @Override
    @DataSourceType(sourceType = DataSourceConstant.SLAVE)
    public List<String> getAlipayV2ChangeMerchantIds(long beginMtime, long endMtime) {
        Filter filter = merchantConfigDao.filter(Criteria.where(ConstantUtil.KEY_MTIME).ge(beginMtime)
                .with(ConstantUtil.KEY_MTIME).le(endMtime).with(MerchantConfig.PAYWAY).is(TradeConfigService.PAYWAY_ALIPAY2));
        List<Map<String, Object>> merchantConfigs = CollectionUtil.iterator2list(filter.fetchAll());
        List<String> merchantIds = new ArrayList<>();
        for(Map merchantConfig : merchantConfigs){
            merchantIds.add(MapUtils.getString(merchantConfig, Store.MERCHANT_ID));
        }
        return merchantIds;
    }


    @Override
    public Map createMerchantConfigCustom(Map merchantConfigCustom) {
        Integer type = BeanUtil.getPropInt(merchantConfigCustom, MerchantConfigCustom.TYPE);
        String merchantId = BeanUtil.getPropString(merchantConfigCustom, MerchantConfigCustom.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(merchantConfigCustom, MerchantConfigCustom.STORE_ID);
        Map exists = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, storeId, type);
        if(exists == null){
            if (merchantConfigCustom.get(DaoConstants.ID) == null) {
                merchantConfigCustom.put(DaoConstants.ID, uuidGenerator.nextUuid());
            }
            merchantConfigCustomDao.save(merchantConfigCustom);
            return merchantConfigCustom;
        }else{
            exists.putAll(merchantConfigCustom);
            merchantConfigCustomDao.updatePart(exists);
            return exists;
        }
    }


    @Override
    public void deleteMerchantConfigCustom(String merchantConfigCustomId) {
        merchantConfigCustomDao.delete(merchantConfigCustomId);
    }

    @Override
    public Map updateMerchantConfigCustom(Map merchantConfigCustom) {
        merchantConfigCustomDao.updatePart(merchantConfigCustom);
        return getMerchantConfigCustom(BeanUtil.getPropString(merchantConfigCustom, DaoConstants.ID));
    }

    @Override
    public Map getMerchantConfigCustom(String merchantConfigCustomId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantConfigCustomId);
        return merchantConfigCustomDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getMerchantConfigCustomByMerchantIdAndStoreIdAndType(String merchantId, String storeId, int type) {
        if (Objects.isNull(merchantId)) {
            throw new CoreInvalidParameterException("商户id不能为空");
        }
        Criteria criteria = Criteria.where(MerchantConfigCustom.MERCHANT_ID).is(merchantId);
        criteria.with(MerchantConfigCustom.STORE_ID).is(storeId);
        criteria.with(MerchantConfigCustom.TYPE).is(type);
        return merchantConfigCustomDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findMerchantConfigCustoms(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE);
            put(MerchantConfigCustom.MERCHANT_ID, MerchantConfigCustom.MERCHANT_ID);
            put(MerchantConfigCustom.STORE_ID, MerchantConfigCustom.STORE_ID);
            put(MerchantConfigCustom.B2C_VALUE, MerchantConfigCustom.B2C_VALUE);
            put(MerchantConfigCustom.C2B_VALUE, MerchantConfigCustom.C2B_VALUE);
            put(MerchantConfigCustom.WAP_VALUE, MerchantConfigCustom.WAP_VALUE);
            put(MerchantConfigCustom.MINI_VALUE, MerchantConfigCustom.MINI_VALUE);
            put(MerchantConfigCustom.MINI_AGENT_NAME, MerchantConfigCustom.MINI_AGENT_NAME);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = merchantConfigCustomDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = merchantConfigCustomDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }


    @Override
    public Map createStoreConfig(Map storeConfig) {
        if (storeConfig.get(DaoConstants.ID) == null) {
            storeConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        storeConfigDao.save(storeConfig);
        return storeConfig;
    }

    @Override
    public void deleteStoreConfig(String storeConfigId) {
        storeConfigDao.delete(storeConfigId);
    }

    @Override
    public Map updateStoreConfig(Map storeConfig) {
        storeConfigDao.updatePart(storeConfig);
        return getStoreConfig(BeanUtil.getPropString(storeConfig, DaoConstants.ID));
    }

    @Override
    public Map getStoreConfig(String storeConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(storeConfigId);
        return storeConfigDao.filter(criteria).fetchOne();
    }


    @Override
    public Map createSolicitorConfig(Map solicitorConfig) {
        checkSolicitorConfig(solicitorConfig);
        if (solicitorConfig.get(DaoConstants.ID) == null) {
            solicitorConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        solicitorConfigDao.save(solicitorConfig);
        return solicitorConfig;
    }

    @Override
    public void deleteSolicitorConfig(String solicitorConfigId) {
        solicitorConfigDao.delete(solicitorConfigId);
    }

    @Override
    public Map updateSolicitorConfig(Map solicitorConfig) {
        checkSolicitorConfig(solicitorConfig);
        solicitorConfigDao.updatePart(solicitorConfig);
        return getSolicitorConfig(BeanUtil.getPropString(solicitorConfig, DaoConstants.ID));
    }


    @Override
    public Map getSolicitorConfig(String solicitorConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(solicitorConfigId);
        return solicitorConfigDao.filter(criteria).fetchOne();
    }


    @Override
    public Map createTerminalConfig(Map terminalConfig) {
        if (terminalConfig.get(DaoConstants.ID) == null) {
            terminalConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        Map params = MapUtils.getMap(terminalConfig, TerminalConfig.PARAMS, new HashMap());
        if (!Objects.equals(MapUtils.getInteger(params, TerminalConfig.LADDER_STATUS), TerminalConfig.STATUS_OPENED)){
            params.put(TerminalConfig.LADDER_STATUS, TerminalConfig.STATUS_CLOSED);
            terminalConfig.put(TerminalConfig.PARAMS, params);
        }
        terminalConfigDao.save(terminalConfig);
        return terminalConfig;
    }

    @Override
    public void deleteTerminalConfig(String terminalConfigId) {
        terminalConfigDao.delete(terminalConfigId);
    }

    @Override
    public Map updateTerminalConfig(Map terminalConfig) {
        Map params = MapUtils.getMap(terminalConfig, TerminalConfig.PARAMS);
        terminalConfigDao.updatePart(terminalConfig);
        return getTerminalConfig(BeanUtil.getPropString(terminalConfig, DaoConstants.ID));
    }

    @Override
    public Map getTerminalConfig(String terminalConfigId) {
        return terminalConfigDao.get(terminalConfigId);
    }

    @Override
    public void updateTerminalConfigStatus(Map terminalConfig) {
        Integer payway = null;
        String paywayStr = BeanUtil.getPropString(terminalConfig, TerminalConfig.PAYWAY);
        if (StringUtils.isNumeric(paywayStr)) {
            payway = Integer.valueOf(paywayStr);
        }

        Criteria criteria = Criteria.where(TerminalConfig.TERMINAL_ID)
                .is(BeanUtil.getPropString(terminalConfig, TerminalConfig.TERMINAL_ID))
                .with(TerminalConfig.PAYWAY).is(payway);
        //根据终端id、payway查询终端配置是否存在
        Map<String, Object> baseTerminalConfig = terminalConfigDao.filter(criteria).fetchOne();

        Map insertOrUpdateConfig = CollectionUtil.hashMap(
                TerminalConfig.TERMINAL_ID, BeanUtil.getPropString(terminalConfig, TerminalConfig.TERMINAL_ID),
                TerminalConfig.PAYWAY, payway
        );
        if (terminalConfig.containsKey(TerminalConfig.B2C_STATUS)) {
            configStatusProcess(terminalConfig, insertOrUpdateConfig, TerminalConfig.B2C_STATUS);
        }
        if (terminalConfig.containsKey(TerminalConfig.C2B_STATUS)) {
            configStatusProcess(terminalConfig, insertOrUpdateConfig, TerminalConfig.C2B_STATUS);
        }
        if (terminalConfig.containsKey(TerminalConfig.WAP_STATUS)) {
            configStatusProcess(terminalConfig, insertOrUpdateConfig, TerminalConfig.WAP_STATUS);
        }
        if (terminalConfig.containsKey(TerminalConfig.MINI_STATUS)) {
            configStatusProcess(terminalConfig, insertOrUpdateConfig, TerminalConfig.MINI_STATUS);
        }
        if (terminalConfig.containsKey(TerminalConfig.APP_STATUS)) {
            configStatusProcess(terminalConfig, insertOrUpdateConfig, TerminalConfig.APP_STATUS);
        }
        if (terminalConfig.containsKey(TerminalConfig.H5_STATUS)) {
            configStatusProcess(terminalConfig, insertOrUpdateConfig, TerminalConfig.H5_STATUS);
        }

        //不存在则新增
        if (MapUtils.isEmpty(baseTerminalConfig)) {

            insertOrUpdateConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
            terminalConfigDao.save(insertOrUpdateConfig);
            return;
        }

        //存在则更新
        insertOrUpdateConfig.put(DaoConstants.ID, BeanUtil.getPropString(baseTerminalConfig, DaoConstants.ID));
        terminalConfigDao.updatePart(insertOrUpdateConfig);
    }

    @Override
    public void updateTerminalConfigStatusAndLog(Map terminalConfig, OpLogCreateRequest opLogCreateRequest) {
        String terminalId = BeanUtil.getPropString(terminalConfig, ConstantUtil.KEY_TERMINAL_ID);
        Integer payWay = BeanUtil.getPropInt(terminalConfig, TerminalConfig.PAYWAY, -1);
        payWay = payWay == -1 ? null : payWay;
        Map before = getTerminalConfigByTerminalIdAndPayway(terminalId, payWay);
        updateTerminalConfigStatus(terminalConfig);
        Map after = getTerminalConfigByTerminalIdAndPayway(terminalId, payWay);
        Map terminal = terminalService.getTerminalByTerminalId(terminalId);
        String merchantId = MapUtils.getString(terminal, MerchantConfig.MERCHANT_ID);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, TerminalConfig.TERMINAL_ID), merchantId, OpLog.TERMINAL_CONFIG_TEMPLATE_CODE, OpLog.TERMINAL_CONFIG_TABLE_NAME, new ArrayList<>(), OpLog.TERMINAL_CONFIG_CHANGE_KEY_LIST, OpLog.TERMINAL_CONFIG_DESC_MAP, before, after);
    }

    private void configStatusProcess(Map terminalConfig, Map insertOrUpdateConfig, String key) {
        Object status = terminalConfig.get(key);
        checkUpdateTerminalConfigStatus(status);
        insertOrUpdateConfig.put(key, status);
    }

    private void checkUpdateTerminalConfigStatus(Object status) {
        if (status == null) {
            return;
        }
        String statusStr = String.valueOf(status);
        if (statusStr != null) {
            if (!"0".equals(statusStr) && !"1".equals(statusStr)) {
                throw new IllegalArgumentException("终端交易参数配置状态不合法");
            }
        }
    }


    @Override
    public void batchUpdateTerminalConfigStatus(List<Map> terminalConfigs) {
        if (CollectionUtils.isEmpty(terminalConfigs)) {
            return;
        }
        for (Map terminalConfig : terminalConfigs) {
            updateTerminalConfigStatus(terminalConfig);
        }
    }

    @Override
    public List getMerchantConfigsByMerchantId(String merchantId) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId);
        return CollectionUtil.iterator2list(merchantConfigDao.filter(criteria).fetchAll());
    }

    public List getStoreConfigsByStoreId(String storeId) {
        Criteria criteria = Criteria.where(StoreConfig.STORE_ID).is(storeId);
        return CollectionUtil.iterator2list(storeConfigDao.filter(criteria).fetchAll());
    }

    @Override
    public List getTerminalConfigsByTerminalId(String terminalId) {
        Criteria criteria = Criteria.where(TerminalConfig.TERMINAL_ID).is(terminalId);
        return CollectionUtil.iterator2list(terminalConfigDao.filter(criteria).fetchAll());
    }


    @Override
    public Map getMerchantConfigByMerchantIdAndPayway(String merchantId, Integer payway) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(payway);
        return merchantConfigDao.filter(criteria).fetchOne();
    }

    public Map getStoreConfigByStoreIdAndPayway(String storeId, Integer payway) {
        Criteria criteria = Criteria.where(StoreConfig.STORE_ID).is(storeId).with(MerchantConfig.PAYWAY).is(payway);
        return storeConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getTerminalConfigByTerminalIdAndPayway(String terminalId, Integer payway) {
        Criteria criteria = Criteria.where(TerminalConfig.TERMINAL_ID).is(terminalId).with(MerchantConfig.PAYWAY).is(payway);
        return terminalConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getAnalyzedMerchantConfigByMerchantIdAndPayway(String merchantId, Integer payway) {
        List<Map> merchantConfigs = getAnalyzedMerchantConfigsByPayWayList(merchantId, new int[]{payway});
        if (merchantConfigs != null && merchantConfigs.size() == 1) {
            return merchantConfigs.get(0);
        }
        return null;
    }

    public Map getAnalyzedStoreConfigByStoreIdAndPayway(String storeId, Integer payway) {
        List<Map> merchantConfigs = getAnalyzedStoreConfigsByPayWayList(storeId, new int[]{payway}, null);
        if (merchantConfigs != null && merchantConfigs.size() == 1) {
            return merchantConfigs.get(0);
        }
        return null;
    }

    public Map getAnalyzedMerchantConfigByMerchantIdAndPaywayAndSubpayway(String merchantId, Integer payway, Integer subPway, String tradeApp) {
        List<Map> merchantConfigs = getAnalyzedMerchantConfigsByPayWayListAndSubpayway(merchantId, new int[]{payway}, subPway, tradeApp);
        if (merchantConfigs != null && merchantConfigs.size() == 1) {
            return merchantConfigs.get(0);
        }
        return null;
    }

    public Map getAnalyzedStoreConfigByMerchantIdAndPayway(String storeId, Integer payway, Integer subPway) {
        List<Map> storeConfigs = getAnalyzedStoreConfigsByPayWayList(storeId, new int[]{payway}, subPway);
        if (storeConfigs != null && storeConfigs.size() == 1) {
            return storeConfigs.get(0);
        }
        return null;
    }

    public Map getAnalyzedTerminalConfigByTerminalIdAndPayway(String terminalId, Integer payway, Integer subPway) {
        List<Map> terminalConfigs = getAnalyzedTerminalConfigsByPayWayListAndSubpayway(terminalId
                , new int[]{payway}, subPway);
        if (terminalConfigs != null && terminalConfigs.size() == 1) {
            return terminalConfigs.get(0);
        }

        return null;
    }

    @Override
    public int getAnalyzedMerchantConfigStatus(String merchantId, Integer payway, Integer subPayway) {
        Map merchantConfig = getAnalyzedMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        String status = BeanUtil.getPropString(merchantConfig, subPaywayStatusColName.get(subPayway + ""));
        if (status == null) {
            return MerchantConfig.STATUS_CLOSED;
        } else {
            return Integer.parseInt(status);
        }
    }

    @Override
    public Map getVendorConfigByVendorId(String vendorId) {
        Criteria criteria = Criteria.where(VendorConfig.VENDOR_ID).is(vendorId);
        return vendorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public List getSolicitorConfigsBySolicitorId(String solicitorId) {
        Criteria criteria = Criteria.where(SolicitorConfig.SOLICITOR_ID).is(solicitorId);
        return CollectionUtil.iterator2list(solicitorConfigDao.filter(criteria).fetchAll());
    }

    @Override
    public Map getSolicitorConfigsBySolicitorIdAndPayway(String solicitorId, Integer payway) {
        Criteria criteria = Criteria.where(SolicitorConfig.SOLICITOR_ID).is(solicitorId).with(SolicitorConfig.PAYWAY).is(payway);
        return solicitorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getAnalyzedSolicitorConfigsBySolicitorIdAndPayway(String solicitorId, Integer payway) {
        List<Map> solicitorConfigs = getAnalyzedSolicitorConfigs(solicitorId);
        for (Map solicitorConfig : solicitorConfigs) {
            int currentPayway = BeanUtil.getPropInt(solicitorConfig, SolicitorConfig.PAYWAY);
            if (currentPayway == payway) {
                return solicitorConfig;
            }
        }
        return null;
    }

    @Override
    public Map getTradeParams(int payway, int subPayway, Map context) {
        return getTradeParamsWithTradeApp(payway, subPayway, TransactionParam.TRADE_APP_BASIC_PAY, context);
    }

    @Override
    public Map getTradeParamsWithTradeApp(int payway, int subPayway, String tradeApp, Map context) {
        // 参数校验
        preValidateTradeParams(tradeApp, context);

        //支付宝小程序支付暂时复用wap支付参数
        int originSubPayway = subPayway;
        if (subPayway == SUB_PAYWAY_MINI && (payway == PAYWAY_ALIPAY2 || payway == PAYWAY_ALIPAY)) {
            subPayway = SUB_PAYWAY_WAP;
        }
        String merchantId = (String) context.get(TransactionParam.MERCHANT_ID);
        String storeId = (String) context.get(TransactionParam.STORE_ID);
        // 根据配置重置并获取payway
        int originPayway = payway;
        payway = resetUnionPayway(originPayway, merchantId, storeId);
        String currency = (String) context.get(TransactionParam.CURRENCY);
        Object[] paywayConfig = guessAlipayNewPayway(merchantId, payway, subPayway, currency, TransactionParam.TRADE_APP_BASIC_PAY);
        payway = (int) paywayConfig[0];
        Map merchantConfig = paywayConfig.length > 1 ? (Map) paywayConfig[1] : getAnalyzedMerchantConfigByMerchantIdAndPaywayAndSubpayway(merchantId, payway, subPayway, TransactionParam.TRADE_APP_BASIC_PAY);
        boolean isBasicPay = isBasicPay(tradeApp);
        if (!isBasicPay && null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("业务方未配置交易参数");
        }

        Map<String, Object> merchantTradeParam = new HashMap<>();
        Map mergerConfig = mergeStoreAndTerminalConfig(payway, subPayway, TransactionParam.TRADE_APP_BASIC_PAY, merchantConfig, context);
        //用支付应用门店与商户配置的费率信息更新
        updateMerchantConfigFeeInfoByAppMerchantAndStoreConfig(payway, subPayway, tradeApp, mergerConfig, context);

        Boolean formal = BeanUtil.getPropBoolean(mergerConfig, subPaywayFormalColName.get(subPayway + ""), false);
        //仅针对间连通道
        if (!formal) {
            Integer status = com.wosai.pantheon.util.MapUtil.getInteger(context, TransactionParam.PAY_STATUS);
            if (status != null && status == TransactionParam.STATUS_CLOSED) {
                throw new CoreMerchantConfigAbnormalException("商户收款权限被关闭，请联系您的客户经理");
            }
            status = com.wosai.pantheon.util.MapUtil.getInteger(context, TransactionParam.STORE_PAY_STATUS);
            if (status != null && status == TransactionParam.STATUS_CLOSED) {
                throw new CoreStoreConfigAbnormalException("门店收款权限被关闭，请联系您的客户经理");
            }
        } else {
            //直连通道 provider 设置为 null
            mergerConfig.put(MerchantConfig.PROVIDER, null);
        }
        Integer provider = BeanUtil.getPropString(mergerConfig, MerchantConfig.PROVIDER) != null ? BeanUtil.getPropInt(mergerConfig, MerchantConfig.PROVIDER) : null;

        checkStatusAndFormal(mergerConfig, payway, subPayway);
        merchantTradeParam = getAnalyzedTradeParams(mergerConfig, payway, subPayway);
        Map<String, Object> defaultTradeParam = getAgentTradeParams(mergerConfig, subPayway);
        if (payway == PAYWAY_WEIXIN) {
            specialDealWeixinTradeParam(defaultTradeParam, context, provider, payway, subPayway);
        }

        for (String key : defaultTradeParam.keySet()) {
            replaceTradeParamValueByKey(key, defaultTradeParam, merchantTradeParam);
        }
        if (provider != null && provider == PROVIDER_CIBBANK) {
            specialDealCIBBankTradeParams(defaultTradeParam, context, payway, subPayway);
        }
        if (provider != null && provider == PROVIDER_CITICBANK) {
            specialDealCITICBankTradeParams(defaultTradeParam, context, payway, subPayway);
        }

        if(provider != null && provider == PROVIDER_ZJTLCB) {
            specialDealZJTLCBTradeParams(defaultTradeParam, context);
        }

        //江苏银行添加扩展的参数
        if(provider != null && Objects.equals(provider, Provider.JSB_BANK.getCode())) {
            addJSBExtraParams(context);
        }

        formatParams(defaultTradeParam);
        Map<String, Object> config = CollectionUtil.hashMap(
                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, defaultTradeParam.get(TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS),
                TransactionParam.MERCHANT_DAILY_MAX_CREDIT_LIMIT_TRANS, defaultTradeParam.get(TransactionParam.MERCHANT_DAILY_MAX_CREDIT_LIMIT_TRANS),
                TransactionParam.MERCHANT_MONTHLY_MAX_CREDIT_LIMIT_TRANS, defaultTradeParam.get(TransactionParam.MERCHANT_MONTHLY_MAX_CREDIT_LIMIT_TRANS),
                TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT, defaultTradeParam.get(TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT),
                TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT, defaultTradeParam.get(TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT),

                TransactionParam.PAYWAY_DAY_CREDIT_LIMITS, defaultTradeParam.get(TransactionParam.PAYWAY_DAY_CREDIT_LIMITS),
                TransactionParam.PAYWAY_MONTH_CREDIT_LIMITS, defaultTradeParam.get(TransactionParam.PAYWAY_MONTH_CREDIT_LIMITS),
                TransactionParam.MERCHANT_BANKCARD_SINGLE_MAX_LIMIT, defaultTradeParam.get(TransactionParam.MERCHANT_BANKCARD_SINGLE_MAX_LIMIT),
                TransactionParam.MERCHANT_BANKCARD_DAY_MAX_LIMIT, defaultTradeParam.get(TransactionParam.MERCHANT_BANKCARD_DAY_MAX_LIMIT),
                TransactionParam.MERCHANT_BANKCARD_MONTH_MAX_LIMIT, defaultTradeParam.get(TransactionParam.MERCHANT_BANKCARD_MONTH_MAX_LIMIT),
                TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, defaultTradeParam.get(TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS),
                TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, defaultTradeParam.get(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN),
                TransactionParam.ALLOW_CREDIT_PAY, merchantTradeParam.get(TransactionParam.ALLOW_CREDIT_PAY),
                TransactionParam.USE_CLIENT_STORE_SN, merchantTradeParam.get(TransactionParam.USE_CLIENT_STORE_SN),
                TransactionParam.DEPOSIT, merchantTradeParam.get(TransactionParam.DEPOSIT),
                TransactionParam.IS_SENT_STORE_ID, merchantTradeParam.get(TransactionParam.IS_SENT_STORE_ID),
                TransactionParam.IS_NEED_REFUND_FEE_FLAG, merchantTradeParam.get(TransactionParam.IS_NEED_REFUND_FEE_FLAG),
                TransactionParam.HIT_PAYWAY, getHitPayway(payway, originPayway)
        );


        //商户终端类型单笔限额配置, 计算生效交易单笔限额值
        String terminalCategory = BeanUtil.getPropString(context, TransactionParam.TERMINAL_CATEGORY, "");
        String quota = null;
        Map merchantSingleMaxTerminalCategory = com.wosai.pantheon.util.MapUtil.getMap(merchantTradeParam, TransactionParam.CATEGORY_MERCHANT_SINGLE_MAX_OF_TRAN, Maps.newHashMap());
        // tradeApp 校验
        boolean tradeAppCheck = Objects.isNull(tradeApp) || TransactionParam.TRADE_APP_BASIC_PAY.equals(tradeApp);
        if (tradeAppCheck && terminalCategory.equals(String.valueOf(CATEGORY_APP)) && subPayway == SUB_PAYWAY_BARCODE) {
            // 当终端类目是收钱吧APP并且发起BSC交易时,才获取收钱吧APP类型终端单笔交易限额
            quota = BeanUtil.getPropString(merchantSingleMaxTerminalCategory, terminalCategory);
        } else if (!terminalCategory.equals(String.valueOf(CATEGORY_APP))) {
            // 当终端类型不是收钱吧APP, 也获取终端单笔交易限额
            quota = BeanUtil.getPropString(merchantSingleMaxTerminalCategory, terminalCategory);
        }
        calculateMerchantSingleMax(config, payway, subPayway, quota, tradeApp);

        if (payway == PAYWAY_ALIPAY2) {
            specialDealAlipayV2TradeParams(defaultTradeParam, context, provider, payway, subPayway);
        }

        String tradeParamsKey;
        if (!formal && provider != null && provider != payway && BeanUtil.getPropString(merchantTradeParam, getTradeParamsKeyByProvider(provider)) != null) {
            tradeParamsKey = getTradeParamsKeyByProvider(provider);
            // 间连通道的交易参数需要再做一次非空校验
            Map tradeParam = (Map) defaultTradeParam.get(tradeParamsKey);
            if (null != tradeParam && !checkTradeParamNotEmpty(tradeParam, tradeParamsKey)) {
                defaultTradeParam.remove(tradeParamsKey);
            }
        } else {
            tradeParamsKey = paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway);
        }

        Map<String, Object> tradeParams = (Map<String, Object>) defaultTradeParam.get(tradeParamsKey);
        if (tradeParams == null) {
            throw new CoreMerchantConfigAbnormalException("商户交易参数配置异常");
        } else {
            //走威富通通道的微信交易不能用大商户进行交易
            if (payway == PAYWAY_WEIXIN && provider != null && ApolloConfigurationCenterUtil.getWftProviders().contains(provider)) {
                for (String key : tradeParams.keySet()) {
                    if (key.endsWith(TransactionParam.SWIFTPASS_MCH_ID_SUFFIX)) {
                        String mchId = BeanUtil.getPropString(tradeParams, key);
                        if (mchId != null && Arrays.asList("100520017600", "021470000001", "**********", "**********", "**********", "**********", "**********").contains(mchId)) {
                            throw new CoreMerchantConfigAbnormalException("微信商户报备中，请耐心等待");
                        }
                    }
                }
            }
            if(!tradeParams.containsKey(TransactionParam.LIQUIDATION_NEXT_DAY)){
                logger.warn("交易参数没有喔噻结算字段");
            }

            // 直连&间连结算方式不返回结算通道（结算通道不准确）
            config.remove(TransactionParam.CLEARANCE_PROVIDER);
            if (!formal && BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY)) {
                int clearanceProvider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PARAMS + "." + TransactionParam.CLEARANCE_PROVIDER, CLEARANCE_PROVIDER_LKL);
                // 非支付业务的结算通道通过provider反推
                if (CLEARANCE_PROVIDER_SWITCH == clearanceProvider) {
                    throw new CoreMerchantConfigAbnormalException("商户正在切换清算通道，请耐心等待");
                }
                if (!isBasicPay) {
                    if (PROVIDER_UNIONPAY_TL == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_TL;
                    } else if (PROVIDER_CHINAUMS == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_YS;
                    } else if (PROVIDER_SYB == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_SYB;
                    } else if (PROVIDER_FUYOU == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_FUYOU;
                    } else if (PROVIDER_HAIKE_UNION_PAY == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_HAIKE;
                    } else if (PROVIDER_GUOTONG == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_GUOTONG;
                    } else if (PROVIDER_ZTKX == provider) {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_ZTKX;
                    } else {
                        clearanceProvider = TransactionParam.CLEARANCE_PROVIDER_LKL;
                    }
                }
                if (provider != null) {
                    if (TransactionParam.CLEARANCE_PROVIDER_TL == clearanceProvider) {
                        if (PROVIDER_UNIONPAY_TL != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_YS == clearanceProvider) {
                        if (PROVIDER_CHINAUMS != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_LKL == clearanceProvider) {
                        if (PROVIDER_UNIONPAY_TL == provider 
                                || PROVIDER_CHINAUMS == provider 
                                || PROVIDER_FUYOU == provider
                                || PROVIDER_GUOTONG == provider
                                || PROVIDER_SYB == provider
                                || PROVIDER_ZTKX == provider
                                || PROVIDER_HAIKE_UNION_PAY == provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_SYB == clearanceProvider) {
                        if (PROVIDER_SYB != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_HAIKE == clearanceProvider) {
                        if (PROVIDER_HAIKE_UNION_PAY != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_FUYOU == clearanceProvider) {
                        if (PROVIDER_FUYOU != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_GUOTONG == clearanceProvider) {
                        if (PROVIDER_GUOTONG != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else if (TransactionParam.CLEARANCE_PROVIDER_ZTKX == clearanceProvider) {
                        if (PROVIDER_ZTKX != provider) {
                            throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                        }
                    } else {
                        throw new CoreMerchantConfigAbnormalException("商户清算通道参数错误");
                    }
                }
                config.put(TransactionParam.CLEARANCE_PROVIDER, clearanceProvider);
                // 间连提现转间连直清
                if (Objects.equals(Provider.FUYOU.getCode(), provider)) {
                    String commonSwitch = com.wosai.pantheon.util.MapUtil.getString(context, TransactionParam.COMMON_SWITCH);
                    if (CommonSwitchUtil.isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_LIQUIDATION_NEXT_DAY_INDIRECT)) {
                        logger.info("商户{}间连提现转间连直清", com.wosai.pantheon.util.MapUtil.getString(context, TransactionParam.MERCHANT_SN));
                        tradeParams.put(TransactionParam.LIQUIDATION_NEXT_DAY, false);
                    }
                }
            }
            String feeType = com.wosai.pantheon.util.MapUtil.getString(mergerConfig, MerchantConfig.FEE_RATE_TYPE);
            if (!StringUtil.empty(feeType)) {
                // 新版本费率设置
                if (MerchantConfig.FEE_RATE_TYPE_LADDER.equals(feeType)) {
                    if (null != BeanUtil.getProperty(mergerConfig, MerchantConfig.LADDER_FEE_RATES)
                            && BeanUtil.getProperty(mergerConfig, MerchantConfig.LADDER_FEE_RATES) instanceof List) {
                        List<Map> tradeConfigs = new ArrayList();
                        for (Map ladderConfig : (List<Map>) BeanUtil.getProperty(mergerConfig, MerchantConfig.LADDER_FEE_RATES)) {
                            tradeConfigs.add(
                                    CollectionUtil.hashMap(
                                            MerchantConfig.LADDER_FEE_RATE_MIN, BeanUtil.getProperty(ladderConfig, MerchantConfig.LADDER_FEE_RATE_MIN),
                                            MerchantConfig.LADDER_FEE_RATE_MAX, BeanUtil.getProperty(ladderConfig, MerchantConfig.LADDER_FEE_RATE_MAX),
                                            TransactionParam.FEE_RATE, BeanUtil.getPropString(ladderConfig, subPaywayFeeRateColName.get(subPayway + ""))
                                    ));
                        }
                        tradeParams.put(MerchantConfig.LADDER_FEE_RATES, tradeConfigs);
                    } else {
                        throw new CoreMerchantConfigAbnormalException("商户交易参数配置异常");
                    }
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL.equals(feeType)) {
                    tradeParams.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(mergerConfig, TransactionParam.PARAMS_BANKCARD_FEE));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER.equals(feeType)) {
                    Object channelLadderFee =  BeanUtil.getProperty(mergerConfig, TransactionParam.CHANNEL_LADDER_FEE_RATES);
                    if (null != channelLadderFee && channelLadderFee instanceof Map) {
                        Map<String, List<Map>> newChannelLadderFeeRate = new HashMap<>();
                        String subPaywayStr = subPayway + "";
                        for (Map.Entry<String, List<Map>> channelLadderFeeRate : ((Map<String, List<Map>>)channelLadderFee).entrySet()) {
                            newChannelLadderFeeRate.put(channelLadderFeeRate.getKey(), channelLadderFeeRate.getValue().stream().map(ladderFeeRates -> {
                                return CollectionUtil.hashMap(
                                        MerchantConfig.LADDER_FEE_RATE_MIN, BeanUtil.getProperty(ladderFeeRates, MerchantConfig.LADDER_FEE_RATE_MIN),
                                        MerchantConfig.LADDER_FEE_RATE_MAX, BeanUtil.getProperty(ladderFeeRates, MerchantConfig.LADDER_FEE_RATE_MAX),
                                        TransactionParam.FEE_RATE, BeanUtil.getPropString(ladderFeeRates, subPaywayFeeRateColName.get(subPaywayStr))
                                );
                            }).collect(Collectors.toList()));
                        }
                        tradeParams.put(TransactionParam.CHANNEL_LADDER_FEE_RATES, newChannelLadderFeeRate);
                    } else {
                        throw new CoreMerchantConfigAbnormalException("商户交易参数配置异常");
                    }
                }
            } else {
                // 老版本费率设置
                // 设置渠道费率
                if (BeanUtil.getPropInt(mergerConfig, MerchantConfig.CHANNEL_STATUS) == MerchantConfig.STATUS_OPENED) {
                    Map bankCardFeeConfig = (Map) BeanUtil.getNestedProperty(mergerConfig, TransactionParam.PARAMS_BANKCARD_FEE);
                    BeanUtil.setNestedProperty(tradeParams, TransactionParam.PARAMS_BANKCARD_FEE, bankCardFeeConfig);
                    Map<String, String> feeRateTag = buildChannelFeeRateTag(subPayway, mergerConfig);
                    BeanUtil.setNestedProperty(tradeParams, TransactionParam.FEE_RATE_TAG, feeRateTag);
                    BeanUtil.setNestedProperty(mergerConfig, TransactionParam.FEE_RATE_TAG, feeRateTag);
                }
                // 设置特优费率
                else if (BeanUtil.getPropInt(mergerConfig, MerchantConfig.LADDER_STATUS) == MerchantConfig.STATUS_OPENED) {
                    if (null != BeanUtil.getProperty(mergerConfig, MerchantConfig.LADDER_FEE_RATES)
                            && BeanUtil.getProperty(mergerConfig, MerchantConfig.LADDER_FEE_RATES) instanceof List) {
                        List<Map> tradeConfigs = new ArrayList();
                        for (Map ladderConfig : (List<Map>) BeanUtil.getProperty(mergerConfig, MerchantConfig.LADDER_FEE_RATES)) {
                            tradeConfigs.add(
                                    CollectionUtil.hashMap(
                                            MerchantConfig.LADDER_FEE_RATE_MIN, BeanUtil.getProperty(ladderConfig, MerchantConfig.LADDER_FEE_RATE_MIN),
                                            MerchantConfig.LADDER_FEE_RATE_MAX, BeanUtil.getProperty(ladderConfig, MerchantConfig.LADDER_FEE_RATE_MAX),
                                            TransactionParam.FEE_RATE, BeanUtil.getPropString(ladderConfig, subPaywayFeeRateColName.get(subPayway + ""))
                                    ));
                        }
                        tradeParams.put(MerchantConfig.LADDER_FEE_RATES, tradeConfigs);
                        Map<String, String> feeRateTag = buildLadderFeeRateTag(subPayway, mergerConfig);
                        tradeParams.put(TransactionParam.FEE_RATE_TAG, feeRateTag);
                        mergerConfig.put(TransactionParam.FEE_RATE_TAG, feeRateTag);
                    } else {
                        throw new CoreMerchantConfigAbnormalException("商户交易参数配置异常");
                    }
                }
            }
            //设置费率tag
            Map feeRateTag = (Map) BeanUtil.getProperty(mergerConfig, TransactionParam.FEE_RATE_TAG);
            if (MapUtils.isNotEmpty(feeRateTag)) {
                tradeParams.put(TransactionParam.FEE_RATE_TAG, feeRateTag);
            }

            //特殊处理fee_rate_tag ladder_fee_rate_tag
            specialDealFeeRateTag(tradeParams, subPayway, originSubPayway);

            if (payway == PAYWAY_ALIPAY2) {
                //设置花呗的参数信息
                int huabeiStatus = BeanUtil.getPropInt(merchantConfig, TransactionParam.ALIPAY_HUABEI_STATUS, ApolloConfigurationCenterUtil.getHuabeiDefaultStatus());
                config.put(TransactionParam.ALIPAY_HUABEI_STATUS, huabeiStatus);
                if (huabeiStatus == MerchantConfig.STATUS_OPENED) {
                    config.put(TransactionParam.ALIPAY_HUABEI_LIMIT, BeanUtil.getPropInt(merchantConfig, TransactionParam.ALIPAY_HUABEI_LIMIT, ApolloConfigurationCenterUtil.getHuabeiDefaultMinLimit()));
                    config.put(TransactionParam.ALIPAY_HUABEI_PARAMS, ApolloConfigurationCenterUtil.getHuabeiConfigParams());
                    config.put(TransactionParam.ALIPAY_CREDIT_PARAMS, ApolloConfigurationCenterUtil.getCreditConfigParams());
                    // 添加花呗商户付息参数
                    if(merchantConfig.containsKey(TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT)
                                && BeanUtil.getPropInt(config, TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_SWITCH) == TransactionParam.CLEARANCE_PROVIDER_LKL 
                                && (provider == PROVIDER_DIRECT_UNIONPAY || provider == PROVIDER_LAKALA_UNION_PAY)) {
                        config.put(TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT, merchantConfig.get(TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT));
                    }
                }
                int fitnessStatus = BeanUtil.getPropInt(merchantConfig, TransactionParam.FITNESS_STATUS, TransactionParam.FITNESS_STATUS_CLOSE);
                if (fitnessStatus == TransactionParam.FITNESS_STATUS_OPEN) {
                    Object fitnessParams = BeanUtil.getNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + TransactionParam.FITNESS_PARAMS);
                    if (fitnessParams != null && fitnessParams instanceof Map) {
                        Map<String, Object> fitnessParamsMap = (Map<String, Object>) fitnessParams;
                        //签单易小程序生效的是mini支付方式,需要取小程序费率
                        fitnessParamsMap.put(TransactionParam.FEE_RATE, MapUtils.getString(merchantConfig, MerchantConfig.MINI_FEE_RATE));
                        config.put(TransactionParam.FITNESS_PARAMS, fitnessParamsMap);
                    }
                }
            }

            if (payway == PAYWAY_WEIXIN && formal) {
                if (merchantConfig.containsKey(TransactionParam.WEIXIN_INSTALLMENT_STATUS)) {
                    config.put(TransactionParam.WEIXIN_INSTALLMENT_STATUS, merchantConfig.get(TransactionParam.WEIXIN_INSTALLMENT_STATUS));
                }
            }

            // 通联、海科、邮储银联云闪付返回商户城市编码
            if(payway == PAYWAY_UNIONPAY &&
                    (TransactionParam.UNION_PAY_TL_TRADE_PARAMS.equals(tradeParamsKey)
                            || TransactionParam.HAIKE_UNION_PAY_TRADE_PARAMS.equals(tradeParamsKey)
                            || TransactionParam.PSBCBANK_TRADE_PARAMS.equals(tradeParamsKey))) {
                Map merchant = merchantDao.getPart(merchantId, Arrays.asList(Merchant.PROVINCE, Merchant.CITY));
                String code = ApolloConfigurationCenterUtil.getMerchantDistrictCode(com.wosai.pantheon.util.MapUtil.getString(merchant, Merchant.PROVINCE), 
                                com.wosai.pantheon.util.MapUtil.getString(merchant, Merchant.CITY));
                config.put(TransactionParam.AREA_INFO, code);
            }
            if (payway == PAYWAY_JD) {
                //京东目前只支持白条分期支付
                int baitiaoStatus = BeanUtil.getPropInt(merchantConfig, TransactionParam.JD_BAITIAO_STATUS, ApolloConfigurationCenterUtil.getBaitiaoDefaultStatus());
                config.put(TransactionParam.JD_BAITIAO_STATUS, baitiaoStatus);
                if (baitiaoStatus == MerchantConfig.STATUS_OPENED) {
                    config.put(TransactionParam.JD_BAITIAO_LIMIT, BeanUtil.getPropInt(merchantConfig, TransactionParam.JD_BAITIAO_LIMIT, ApolloConfigurationCenterUtil.getBaitiaoDefaultLimit()));
                    config.put(TransactionParam.JD_BAITIAO_PARAMS, ApolloConfigurationCenterUtil.getBaitiaoConfigParams());// 添加花呗商户付息参数
                }
            }

            // 银商b2c和csb使用的是不同的商户号和终端号
            if(provider != null && provider == PROVIDER_CHINAUMS) {
                if(subPayway != SUB_PAYWAY_BARCODE) {
                    tradeParams.put(TransactionParam.CHINAUMS_MCH_CODE, tradeParams.get(TransactionParam.CHINAUMS_CSB_MCH_CODE));
                    tradeParams.put(TransactionParam.CHINAUMS_TERM_CODE, tradeParams.get(TransactionParam.CHINAUMS_CSB_TERM_CODE));
                }
                tradeParams.remove(TransactionParam.CHINAUMS_CSB_MCH_CODE);
                tradeParams.remove(TransactionParam.CHINAUMS_CSB_TERM_CODE);
            }
            if (provider != null && provider == LAKALA_UNION_PAY_V3 && payway == PAYWAY_BANKCARD){
                specialDealLakalaBankcardParams(tradeParams, mergerConfig, tradeApp);
            }
            //招行生活APP
            if (payway == Payway.CMB_APP.getCode()) {
                String storeClientSn = BeanUtil.getPropString(context, TransactionParam.STORE_CLIENT_SN);
                tradeParams.put(TransactionParam.CMB_APP_STR_NO, storeClientSn);
            }
            // 商户交易参数是直连+间连，此时配置的结算通道是间连通道，需要移除值
            if (formal && provider != null) {
                provider = null;
            }
        }
        Map finalTradeParams = (Map) defaultTradeParam.get(tradeParamsKey);
        if (Objects.nonNull(provider) && provider == PROVIDER_CCB && subPayway == SUB_PAYWAY_BARCODE) {
            ccbB2CKeyProcess(finalTradeParams);
        }
        //t9机器的厂商必须跟商户所在的通道是一致的才允许交易
        String terminalVendorAppId = MapUtils.getString(context, TransactionParam.TERMINAL_VENDOR_APP_APPID, "");
        boolean liquidationNextDay = MapUtils.getBooleanValue(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY);
        if (liquidationNextDay && provider != null) {
            Map<String, List<Integer>> vendorT9ProviderMap = ApolloConfigurationCenterUtil.getVendorT9ProviderMap();
            if (vendorT9ProviderMap != null && vendorT9ProviderMap.containsKey(terminalVendorAppId)) {
                List<Integer> supportProvider = vendorT9ProviderMap.get(terminalVendorAppId);
                if (supportProvider != null && !supportProvider.contains(provider.intValue())) {
                    throw new CoreMerchantConfigAbnormalException("商户收款权限被关闭，请联系您的客户经理");
                }
            }
        }

        config.put(TransactionParam.PROVIDER, provider);
        config.put(tradeParamsKey, finalTradeParams);

        Integer providerInt = MapUtils.getInteger(mergerConfig, MerchantConfig.PROVIDER);
        mergeTradeExtConfig(config, finalTradeParams, context, providerInt, payway, subPayway);
        externalExtraFacade.checkExternalConfig(payway, config);
        return config;
    }

    private void preValidateTradeParams(String tradeApp, Map context) {
        String terminalVendorAppId = MapUtils.getString(context, TransactionParam.TERMINAL_VENDOR_APP_APPID, "");
        if (ApolloConfigurationCenterUtil.getPayDisableVendorAppIds().contains(terminalVendorAppId)) {
            throw new CoreTerminalNotActivatedException("应用收款权限已禁用，请联系您的客户经理");
        }
    }


    public Map<String, Object> getBypassTradeParams(Map<String, Object> context, Integer payway, Integer subPayway) {
        String merchantId = MapUtils.getString(context, TransactionParam.MERCHANT_ID);
        if (payway == Payway.ALIPAY.getCode()) {
            payway = Payway.ALIPAY2.getCode();
        }
        MerchantConfigBypassQueryResult resp = merchantConfigBypassService.getByMerchantIdAndPayway(merchantId, payway);
        Map<String, Object> tradeParams = null;
        if (resp == null) {
            return tradeParams;
        }
        String agentName = null;
        if (subPayway == SubPayway.BARCODE.getCode()) {
            agentName = resp.getB2cAgentName();
        } else if (subPayway == SubPayway.QRCODE.getCode()) {
            agentName = resp.getC2bAgentName();
        } else if (subPayway == SubPayway.WAP.getCode()) {
            agentName = resp.getWapAgentName();
        } else if (subPayway == SubPayway.MINI.getCode()) {
            agentName = resp.getC2bAgentName();
        } else if (subPayway == SubPayway.H5.getCode()) {
            agentName = resp.getH5AgentName();
        } else if (subPayway == SubPayway.APP.getCode()) {
            agentName = resp.getAppAgentName();
        }
        if (!StringUtil.empty(agentName)) {
            Map<String, Object> agent = getAgentByName(agentName);
            if (agent != null && agent.get(Agent.PARAMS) != null) {
                tradeParams = (Map<String, Object>) agent.get(Agent.PARAMS);
            } else {
                return null;
            }
            Integer provider = com.wosai.pantheon.util.MapUtil.getInteger(agent, Agent.PROVIDER);
            // 通道可用性校验
            if (!isThisProviderSupport(provider, payway, subPayway)) {
                logger.warn("merchant bypass config error, provider can not support");
                return null;
            }
            // 备用通道
            String replaceTradeParamsKey = getTradeParamsKeyByProvider(provider);
            if (StringUtil.empty(replaceTradeParamsKey)) {
                logger.warn("merchant bypass config error, trade_params_key is null");
                return null;
            }
            replaceTradeParamValueByKey(replaceTradeParamsKey, tradeParams, resp.getParams());
            tradeParams.put(TransactionParam.PROVIDER, provider);
            // 当前备用通道都为拉卡拉，后期发生变动后需要变更逻辑
            tradeParams.put(TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL);
            // 替换通道特有的交易参数配置
            mergeTradeExtConfig(tradeParams, (Map<String, Object>) tradeParams.get(replaceTradeParamsKey), context, provider, payway, subPayway);
        }
        return tradeParams;
    }

    private void mergeTradeExtConfig(Map<String, Object> config, Map<String, Object> tradeParams, Map context, Integer provider, int payway, Integer subPayway) {
        String merchantSn = MapUtils.getString(context, TransactionParam.MERCHANT_SN);
        String storeSn = MapUtils.getString(context, TransactionParam.STORE_SN);
        String terminalSn = MapUtils.getString(context, TransactionParam.TERMINAL_SN);

        Map<String, Object> tradeExtConfig = new HashMap<>();

        if (Objects.nonNull(merchantSn) && merchantSn.length() > 0) {
            doMergeTradeExtConfig(merchantSn, TradeExtConfigQueryRequest.SN_TYPE_MERCHANT, provider, tradeExtConfig, subPayway);
        }

        if (Objects.nonNull(storeSn) && storeSn.length() > 0) {
            doMergeTradeExtConfig(storeSn, TradeExtConfigQueryRequest.SN_TYPE_STORE, provider, tradeExtConfig, subPayway);
        }

        if (Objects.nonNull(terminalSn) && terminalSn.length() > 0) {
            doMergeTradeExtConfig(terminalSn, TradeExtConfigQueryRequest.SN_TYPE_TERMINAL, provider, tradeExtConfig, subPayway);
        }

        String providerMchId = MapUtils.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);
        if (Objects.nonNull(providerMchId) && providerMchId.length() > 0) {
            doMergeTradeExtConfig(providerMchId, TradeExtConfigQueryRequest.SN_TYPE_PROVIDER_MCH, provider, tradeExtConfig, subPayway);
        }

        String subMchId = "";
        if (Payway.ALIPAY.getCode() == payway || Payway.ALIPAY2.getCode() == payway) {
            subMchId = BeanUtil.getPropString(tradeParams, TransactionParam.ALIPAY_SUB_MCH_ID);
        } else if (Payway.WEIXIN.getCode() == payway) {
            subMchId = BeanUtil.getPropString(tradeParams, TransactionParam.WEIXIN_SUB_MCH_ID);
        }
        if (Objects.nonNull(subMchId)) {
            subMchId = subMchId.trim();
        }

        //商户:子商户号
        if ((Objects.nonNull(merchantSn) && merchantSn.length() > 0) && (Objects.nonNull(subMchId) && subMchId.length() > 0)) {
            String merchantSubMchId = String.format("%s:%s", merchantSn, subMchId);
            doMergeTradeExtConfig(merchantSubMchId, TradeExtConfigQueryRequest.SN_TYPE_MERCHANT_SUB_MCH, provider, tradeExtConfig, subPayway);
        }

        //门店:子商户号
        if ((Objects.nonNull(storeSn) && storeSn.length() > 0) && (Objects.nonNull(subMchId) && subMchId.length() > 0)) {
            String storeSubMchId = String.format("%s:%s", storeSn, subMchId);
            doMergeTradeExtConfig(storeSubMchId, TradeExtConfigQueryRequest.SN_TYPE_STORE_SUB_MCH, provider, tradeExtConfig, subPayway);
        }

        if (MapUtils.isNotEmpty(tradeExtConfig)) {
            config.putAll(tradeExtConfig);
        }
        if (MapUtils.isEmpty(tradeExtConfig)
                && LAKALA_V3_COMPATIBILITY_PROVIDER.contains(provider)) {
            mergeTradeExtConfig(config, tradeParams, context, LAKALA_V3_PROVIDER, payway, subPayway);
            return;
        }
        replaceTermInfoWithTradeParams(config, tradeParams, payway, provider);

        config.put(TransactionParam.CHANNEL_NAME, ApolloConfigurationCenterUtil.getChannelName(provider));
    }


    /**
     * 使用交易参数里的终端号替换外层的term_info信息
     *
     * @param config
     * @param tradeParams
     * @param payway
     */
    private void replaceTermInfoWithTradeParams(Map<String, Object> config, Map<String, Object> tradeParams, Integer payway, Integer provider) {
        if ((Objects.equals(PAYWAY_BANKCARD, payway) || Objects.equals(PAYWAY_APPLEPAY, payway)) && Objects.equals(LAKALA_V3_PROVIDER, provider)) {
            String termNo = MapUtils.getString(tradeParams, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO);

            if (!StringUtil.empty(termNo)) {
                Map termInfo = MapUtils.getMap(config, TransactionParam.TRADE_EXT_TERM_INFO);
                if (MapUtils.isNotEmpty(termInfo)) {
                    termInfo.put(TransactionParam.TRADE_EXT_TERM_ID, termNo);
                }

                if (!StringUtils.isEmpty(MapUtils.getString(config, TransactionParam.TRADE_EXT_TERM_ID))) {
                    config.put(TransactionParam.TRADE_EXT_TERM_ID, termNo);
                }
            }
        }
    }

    private void doMergeTradeExtConfig(String sn, Integer snType, Integer provider, Map<String, Object> tradeExtConfig, Integer subPayway) {
        TradeExtConfigQueryRequest request = new TradeExtConfigQueryRequest();
        request.setProvider(provider);
        request.setSn(sn);
        request.setSnType(snType);
        TradeExtConfigQueryResponse response = queryTradeExtConfig(request);
        if (Objects.nonNull(response)) {
            convertResponseTermInfo(response, subPayway);
            Map<String, Object> providerMchExtConfig = JsonUtil.convertToObject(response.getContent(), Map.class);
            if (MapUtils.isNotEmpty(providerMchExtConfig)) {
                providerMchExtConfig.forEach((k, v) -> {
                    if (Objects.nonNull(v)) {
                        tradeExtConfig.put(k, v);
                    }
                });
            }
        }
    }

    /**
     *  term_info 优先级高于 term_id , 转换term_id 为term_info里面的信息，方便后续进行优先级替换
     * @param response
     */
    private void convertResponseTermInfo(TradeExtConfigQueryResponse response, Integer subPayway){
        TradeExtConfigContentModel content = response.getContent();
        if(content == null){
            return;
        }
        if(content.getTermId() != null && content.getTermInfo() == null){
            TermInfo termInfo = new TermInfo();
            termInfo.setTermId(content.getTermId());
            content.setTermInfo(termInfo);
        }
        if(content.getTermInfo() != null){
            content.setTermId(content.getTermInfo().getTermId());
        }

        // 银商参数支持按门店导入 PAY-13352
        if (Provider.CHINA_UMS.getCode().equals(response.getProvider())
                && content.getTermInfo() != null
                && content.getTermInfo().getTermId() != null) {
            String[] termIds = content.getTermInfo().getTermId().split(",");

            if (termIds.length >= 2) {
                String termId = termIds[0];
                if (subPayway != null && !subPayway.equals(SUB_PAYWAY_BARCODE)) {
                    termId = termIds[1];
                }
                content.setTermId(termId);
                if (content.getTermInfo() != null) {
                    content.getTermInfo().setTermId(termId);
                }
            }
        }
    }

    private void calculateMerchantSingleMax(Map config, Integer payway, Integer subPayway, String quota, String tradeApp) {
        Object merchantSingleMaxOfTran = BeanUtil.getProperty(config, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN);
        Double categorySingleMax = null;
        if (Objects.nonNull(quota)) {
            categorySingleMax = Double.parseDouble(quota);
        }
        //1. 如果有商户单笔限额配置：和终端类目商户单笔限额值相比，取最小值
        if (merchantSingleMaxOfTran != null) {
            if (merchantSingleMaxOfTran instanceof String) {
                Double limit = Double.parseDouble(((String) merchantSingleMaxOfTran));
                //限额配置替换
                if (Objects.nonNull(categorySingleMax) && categorySingleMax < limit){
                    config.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, quota);
                }
            } else if (merchantSingleMaxOfTran instanceof Map) {
                String limitString = BeanUtil.getPropString(merchantSingleMaxOfTran, String.format("%s.%s", payway, subPayway));
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(limitString)){
                    //1.如果有商户限额配置
                    Double limit = Double.parseDouble(limitString);
                    //限额配置替换
                    if (Objects.nonNull(categorySingleMax) && categorySingleMax < limit){
                        config.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, quota);
                    } else {
                        config.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, limitString);
                    }
                } else {
                    //2.如果没有商户限额配置
                    config.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, quota);
                }
            }
        } else if (Objects.nonNull(categorySingleMax)){
            //2.没有商户单笔限额配置，却有终端类目的单笔限额配置：取终端类目的单笔限额值
            config.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, quota);
        }

        if(Objects.equals(ApolloConfigurationCenterUtil.getPhonePosTradeApp(),tradeApp)) {
            //手机外卡限额相关配置
            Map<String, Object> defaultPhonePosTradeLimitConfig = ApolloConfigurationCenterUtil.getDefaultPhonePosTradeLimitConfig();
            if (!config.containsKey(TransactionParam.PHONE_POS_SINGLE_TRAN_LIMIT)) {
                config.put(TransactionParam.PHONE_POS_SINGLE_TRAN_LIMIT, MapUtils.getLongValue(defaultPhonePosTradeLimitConfig, TransactionParam.PHONE_POS_SINGLE_TRAN_LIMIT));
            }
            if (!config.containsKey(TransactionParam.PHONE_POS_DAY_TRAN_LIMIT)) {
                config.put(TransactionParam.PHONE_POS_DAY_TRAN_LIMIT, MapUtils.getLongValue(defaultPhonePosTradeLimitConfig, TransactionParam.PHONE_POS_DAY_TRAN_LIMIT));
            }
        }
    }



    private void ccbB2CKeyProcess(Map finalTradeParams) {
        String instNo = BeanUtil.getPropString(finalTradeParams, TransactionParam.CCB_INST_NO);
        String ccbMerchantId = BeanUtil.getPropString(finalTradeParams
                , TransactionParam.CCB_MERCHANT_ID);
        String ccbTerminalId = BeanUtil.getPropString(finalTradeParams
                , TransactionParam.CCB_TERMINAL_ID);
        String key;
        try {
            if (!FakeRequestUtil.isFakeRequest()) {
                key = ccbKeyService.queryKey(instNo, ccbMerchantId, ccbTerminalId);
            } else {
                key = Digest.md5(UUID.randomUUID().toString().getBytes()).toUpperCase();
            }
            finalTradeParams.put(TransactionParam.CCB_SECRET_KEY, key);
        } catch (Exception e) {
            logger.error("[建行获取密钥]>>>>>>调用异常, 异常栈: ", e);
            throw new CoreIOException(e.getMessage(), e);
        }
    }

    private Map mergeStoreAndTerminalConfig(int payway, int subPayway, String tradeApp, Map merchantConfig, Map baseContext) {
        String storeId = com.wosai.pantheon.util.MapUtil.getString(baseContext, TransactionParam.STORE_ID);
        String terminalId = com.wosai.pantheon.util.MapUtil.getString(baseContext, TransactionParam.TERMINAL_ID);
        Map mergerConfig = merchantConfig;
        // 非支付业务不支持交易参数配置到门店和终端
        if (!isBasicPay(tradeApp)) {
            return mergerConfig;
        }
        // 商户二级支付方式状态正常时才获取门店的交易配置
        if (MerchantConfig.STATUS_OPENED == BeanUtil.getPropInt(merchantConfig, subPaywayStatusColName.get(subPayway + ""))) {
            // 门店存在交易参数配置且二级支付方式状态为正常时，优先使用门店的配置
            Map storeConfig = getAnalyzedStoreConfigByMerchantIdAndPayway(storeId, payway, subPayway);
            //二级支付方式状态
            if (StoreConfig.STATUS_CLOSED == BeanUtil.getPropInt(storeConfig, subPaywayStatusColName.get(subPayway + ""), StoreConfig.STATUS_OPENED)) {
                throw new CoreStoreConfigAbnormalException("该门店收款功能已关闭");
            }
            // 替换费率、状态和渠道信息
            for (String col : subpaywayCloNames.get(subPayway)) {
                replaceTradeParamValueByKey(col, merchantConfig, storeConfig);
            }
            // 替换agentname
            replaceTradeParamValueByKey(subPaywayAgentNameColName.get(subPayway + ""), mergerConfig, storeConfig);
            if (!StringUtil.empty(BeanUtil.getPropString(storeConfig, DaoConstants.ID))) {
                // 替换params配置
                Map<String, Object> mergeParams = (Map) BeanUtil.getProperty(mergerConfig, MerchantConfig.PARAMS);
                Map<String, Object> storeParams = (Map) BeanUtil.getProperty(storeConfig, MerchantConfig.PARAMS);
                if (null != storeParams && storeParams.size() > 0) {
                    if(mergeParams == null) {
                        mergeParams = new HashMap<String, Object>();
                        mergerConfig.put(MerchantConfig.PARAMS, mergeParams);
                    } else if(mergeParams.containsKey(TransactionParam.FEE_RATE_TYPE)
                                && !storeParams.containsKey(TransactionParam.FEE_RATE_TYPE)
                                && (storeParams.get(TransactionParam.LADDER_FEE_RATES) != null
                                        || storeParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null
                                        || storeConfig.get(subPaywayFeeRateColName.get(subPayway + "")) != null)) {
                        // 由于套餐生成方式做了调整，会出现新老数据不兼容的情况，需要移除父级别的费率类型
                        mergeParams.remove(TransactionParam.FEE_RATE_TYPE);
                        mergerConfig.remove(TransactionParam.FEE_RATE_TYPE);
                    }
                    for (String col : storeParams.keySet()) {
                        replaceTradeParamValueByKey(col, mergeParams, storeParams);
                    }
                } else if (mergeParams.containsKey(TransactionParam.FEE_RATE_TYPE)
                                && storeConfig.get(subPaywayFeeRateColName.get(subPayway + "")) != null) {
                    // 由于套餐生成方式做了调整，会出现新老数据不兼容的情况，需要移除父级别的费率类型
                    mergeParams.remove(TransactionParam.FEE_RATE_TYPE);
                    mergerConfig.remove(TransactionParam.FEE_RATE_TYPE);
                }
                
                // 替换限额等信息
                for (String col : mergeParamsKey) {
                    if(storeConfig.containsKey(col)) {
                        replaceTradeParamValueByKey(col, mergerConfig, storeConfig);
                    }
                }
            }
            if (!StringUtil.empty(terminalId)) {
                Map terminalConfig = getAnalyzedTerminalConfigByTerminalIdAndPayway(terminalId, payway, subPayway);
                //终端支付方式是否开启
                if (TerminalConfig.STATUS_CLOSED == BeanUtil.getPropInt(terminalConfig, subPaywayStatusColName.get(subPayway + ""), TerminalConfig.STATUS_OPENED)) {
                    String terminalType = getTerminalType(terminalId);
                    if (terminalType != null && terminalType.length() > 0) {
                        terminalType = "（" + terminalType + "）";
                    } else {
                        terminalType = "";
                    }
                    throw new CoreTerminalConfigAbnormalException("该终端" + terminalType + "收款功能已关闭，请使用其他终端收款");
                }
                boolean hasTerminalConfig = !StringUtil.empty(BeanUtil.getPropString(terminalConfig, DaoConstants.ID));
                // 由于银行卡交易会配置一条终端的固定费率，新的取值逻辑会导致校验channel_status报错，如果终端层级不存在param配置，或不存在fee_rate_type时，不取默认的固定费率
                if (hasTerminalConfig && payway == TradeConfigService.PAYWAY_BANKCARD) {
                    Map<String, Object> terminalParams = (Map) BeanUtil.getProperty(terminalConfig, MerchantConfig.PARAMS);
                    if (terminalParams == null || !terminalParams.containsKey(TransactionParam.FEE_RATE_TYPE)) {
                        terminalConfig.put(subPaywayFeeRateColName.get(subPayway + ""), null);
                    }
                }
                // 替换费率、状态和渠道信息
                for (String col : subpaywayCloNames.get(subPayway)) {
                    replaceTradeParamValueByKey(col, merchantConfig, terminalConfig);
                }
                // 替换agentname
                replaceTradeParamValueByKey(subPaywayAgentNameColName.get(subPayway + "")
                        , mergerConfig, terminalConfig);

                if (hasTerminalConfig) {
                    // 替换params配置
                    Map<String, Object> mergeParams = (Map) BeanUtil.getProperty(mergerConfig
                            , MerchantConfig.PARAMS);
                    Map<String, Object> terminalParams = (Map) BeanUtil.getProperty(terminalConfig
                            , MerchantConfig.PARAMS);

                    if (terminalParams != null && terminalParams.size() > 0) {
                        if(mergeParams == null) {
                            mergeParams = new HashMap<String, Object>();
                            mergerConfig.put(MerchantConfig.PARAMS, mergeParams);
                        } else if(mergeParams.containsKey(TransactionParam.FEE_RATE_TYPE)
                                    && !terminalParams.containsKey(TransactionParam.FEE_RATE_TYPE)
                                    && (terminalParams.get(TransactionParam.LADDER_FEE_RATES) != null
                                            || terminalParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null
                                            || terminalConfig.get(subPaywayFeeRateColName.get(subPayway + "")) != null)) {
                            // 由于套餐生成方式做了调整，会出现新老数据不兼容的情况，需要移除父级别的费率类型
                            mergeParams.remove(TransactionParam.FEE_RATE_TYPE);
                            mergerConfig.remove(TransactionParam.FEE_RATE_TYPE);
                        }
                        for (String col : terminalParams.keySet()) {
                            replaceTradeParamValueByKey(col, mergeParams, terminalParams);
                        }
                    } else if (mergeParams.containsKey(TransactionParam.FEE_RATE_TYPE)
                                && terminalParams.get(subPaywayFeeRateColName.get(subPayway + "")) != null) {
                        // 由于套餐生成方式做了调整，会出现新老数据不兼容的情况，需要移除父级别的费率类型
                        mergeParams.remove(TransactionParam.FEE_RATE_TYPE);
                        mergerConfig.remove(TransactionParam.FEE_RATE_TYPE);
                    }
                    
                    // 替换限额等信息
                    for (String col : mergeParamsKey) {
                        if(terminalConfig.containsKey(col)) {
                            replaceTradeParamValueByKey(col, mergerConfig, terminalConfig);
                        }
                    }
                }
            }
        }
        return mergerConfig;
    }

    /**
     * 合并应用商户与门店费率相关配置
     * @param payway
     * @param subPayway
     * @param tradeApp
     * @param merchantConfig
     * @param baseContext
     * @return
     */
    private void updateMerchantConfigFeeInfoByAppMerchantAndStoreConfig(int payway, int subPayway, String tradeApp, Map merchantConfig, Map baseContext) {
        if(isBasicPay(tradeApp)){
            return;
        }
        String merchantId = com.wosai.pantheon.util.MapUtil.getString(baseContext, TransactionParam.MERCHANT_ID);
        Map<String, Object> appMerchantConfig = getMerchantConfigByMerchantIdAndPaywayAndTradeApp(merchantId, payway, tradeApp);
        updateMerchantConfigFeeInfoByAppMerchantAndStoreConfig(payway, subPayway, merchantConfig, appMerchantConfig);

        String storeId = com.wosai.pantheon.util.MapUtil.getString(baseContext, TransactionParam.STORE_ID);
        Map<String, Object> appStoreConfig = getStoreAppConfigByStoreIdAndPaywayAndApp(storeId, payway, tradeApp);
        updateMerchantConfigFeeInfoByAppMerchantAndStoreConfig(payway, subPayway, merchantConfig, appStoreConfig);
    }


    private void updateMerchantConfigFeeInfoByAppMerchantAndStoreConfig(int payway, int subPayway, Map merchantConfig, Map appMerchantOrStoreConfig){
        if(appMerchantOrStoreConfig == null){
            return;
        }
        String subPaywayCode = subPayway + "";
        Map<String, Object> currentParams = (Map) BeanUtil.getProperty(appMerchantOrStoreConfig, MerchantConfig.PARAMS);
        if (currentParams == null) {
            currentParams = new HashMap();
        }
        //二级支付方式状态
        if (StoreConfig.STATUS_CLOSED == BeanUtil.getPropInt(merchantConfig, subPaywayStatusColName.get(subPaywayCode), StoreConfig.STATUS_OPENED)) {
            throw new CoreStoreConfigAbnormalException("该门店收款功能已关闭");
        }
        // 替换费率、状态和渠道信息
        for (String col : subpaywayCloNames.get(subPayway)) {
            replaceTradeParamValueByKey(col, merchantConfig, appMerchantOrStoreConfig);
        }
        // 替换agentname
        replaceTradeParamValueByKey(subPaywayAgentNameColName.get(subPaywayCode), merchantConfig, appMerchantOrStoreConfig);
        // 替换params配置
        Map<String, Object> mergeParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        boolean removeMergeParamsCache = true;
        if(mergeParams == null) {
            mergeParams = new HashMap<String, Object>();
            merchantConfig.put(MerchantConfig.PARAMS, mergeParams);
            removeMergeParamsCache = false;
        }
        // 业务方的交易配置不再是继承关系，如果商户有配置费率信息时，使用当前通道配置
        if (currentParams.get(TransactionParam.LADDER_FEE_RATES) != null 
                || currentParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null
                || currentParams.get(TransactionParam.CHANNEL_LADDER_FEE_RATES) != null
                || appMerchantOrStoreConfig.get(subPaywayFeeRateColName.get(subPayway + "")) != null) {
            for(String key : UN_MERGE_APP_PARAM_KEYS) {
                merchantConfig.remove(key);
                if (removeMergeParamsCache) {
                    mergeParams.remove(key);
                }
            }
        }
        if (null != currentParams && currentParams.size() > 0) {
            for (String col : currentParams.keySet()) {
                replaceTradeParamValueByKey(col, mergeParams, currentParams);
            }
        }
        // 替换限额等信息
        for (String col : mergeParamsKey) {
            if(currentParams.containsKey(col)) {
                replaceTradeParamValueByKey(col, merchantConfig, appMerchantOrStoreConfig);
            }
        }
        String feeType = com.wosai.pantheon.util.MapUtil.getString(currentParams, MerchantConfig.FEE_RATE_TYPE);
        if (!StringUtil.empty(feeType)) {
            // 新版本费率配置
            if (MerchantConfig.FEE_RATE_TYPE_LADDER.equals(feeType)) {
                merchantConfig.put(TransactionParam.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.LADDER_FEE_RATES));
            } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL.equals(feeType)) {
                merchantConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
            } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER.equals(feeType)) {
                merchantConfig.put(TransactionParam.CHANNEL_LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.CHANNEL_LADDER_FEE_RATES));
            }
            merchantConfig.put(MerchantConfig.FEE_RATE_TYPE, feeType);
            merchantConfig.put(TransactionParam.FEE_RATE_TAG, buildFeeRateTag(subPayway, currentParams));
        } else {
            // 老版本费率配置
            // 添加分级费率配置
            if (!StringUtil.empty(BeanUtil.getPropString(currentParams, MerchantConfig.LADDER_STATUS))) {
                merchantConfig.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(currentParams, MerchantConfig.LADDER_STATUS));
                merchantConfig.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, MerchantConfig.LADDER_FEE_RATES));
                merchantConfig.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subPayway, currentParams));
            }
            // 添加资金渠道费率相关配置
            if (payway == Payway.BANKCARD.getCode() && isNotEmptyChannelFeeRate(currentParams, null)) {
                merchantConfig.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(currentParams, null));
                merchantConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                merchantConfig.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subPayway, currentParams));
            }
            // 添加费率变更信息
            Map feeRateTag = (Map) BeanUtil.getProperty(currentParams, TransactionParam.FEE_RATE_TAG);
            if (MapUtils.isNotEmpty(feeRateTag) && BeanUtil.getPropString(appMerchantOrStoreConfig, subPaywayFeeRateColName.get(subPaywayCode)) != null) {
                merchantConfig.put(TransactionParam.FEE_RATE_TAG, buildFeeRateTag(subPayway, currentParams));
            }
        }
    }


    @Override
    public Map getMerchantTradeValidateParams(String merchantId) {
        Map defaultValidateParams = systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_DEFAULT_MERCHANT_TRADE_VALIDATE_PARAMS);
        if (defaultValidateParams == null) {
            defaultValidateParams = getTradeValidateParamsTemplate();
        }
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (merchantParams == null) {
            merchantParams = new HashMap();
        }
        Map validateParams = new HashMap();
        for (String key : tradeValidateParamKeys) {
            validateParams.put(key, merchantParams.get(key) != null ? merchantParams.get(key) : defaultValidateParams.get(key));
        }
        return validateParams;
    }

    @Override
    public void updateMerchantTradeValidateParams(String merchantId, Map validateParams) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, null);
        }
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            for (String key : tradeValidateParamKeys) {
                if (validateParams.containsKey(key)) {
                    Object value = BeanUtil.getProperty(validateParams, key);
                    if (value instanceof String && StringUtils.isEmpty((String) value)) {
                        //如果值为""字符串或者空map,则设置为null
                        value = null;
                    } else if (value instanceof Map && ((Map) value).isEmpty()) {
                        value = null;
                    }
                    merchantConfigParams.put(key, value);
                }
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, updateParamsFunc);
    }

    @Override
    public void updateMerchantDailyPaywayMaxSumOfTrans(String merchantId, int payway, Map<String, String> subPaywayConfig) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, null);
        }
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            Map<String, Map<String, String>> paywayConfig = (Map<String, Map<String, String>>) merchantConfigParams.get(TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);
            if (paywayConfig == null) {
                paywayConfig = new HashMap<>();
                merchantConfigParams.put(TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, paywayConfig);
            }
            paywayConfig.put(payway + "", subPaywayConfig);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), updateParamsFunc);

    }

    @Override
    public Boolean getMerchantIsProtectPayerPrivacy(String merchantId) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (merchantParams == null || merchantParams.get(TransactionParam.IS_PROTECT_PAYER_PRIVACY) == null) {
            return null;
        } else {
            return BeanUtil.getPropBoolean(merchantParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY);
        }
    }

    @Override
    public void updateMerchantIsProtectPayerPrivacy(String merchantId, boolean isProtectPayerPrivacy) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, null);
        }
        Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (merchantParams == null) {
            merchantParams = new HashMap();
            merchantConfig.put(MerchantConfig.PARAMS, merchantParams);
        }
        merchantParams.put(TransactionParam.IS_PROTECT_PAYER_PRIVACY, isProtectPayerPrivacy);
        saveMerchantConfig(merchantConfig);
    }

    @Override
    public Map getMerchantIsLimitCreditCard(String merchantId) {
        Map<String, Object> creditLimitConfigs = new HashMap<>();
        Map defaultConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map defaultParams = (Map) BeanUtil.getProperty(defaultConfig, MerchantConfig.PARAMS);
        String defaultCreditCardSwitch = defaultParams == null ? TransactionParam.CREDIT_PAY_ENABLE : BeanUtil.getPropString(defaultParams, TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE);
        List configs = getMerchantConfigsByMerchantId(merchantId);
        if (configs != null && configs.size() > 0) {
            for (int i = 0; i < configs.size(); i++) {
                if (BeanUtil.getProperty(configs.get(i), MerchantConfig.PAYWAY) != null) {
                    String payway = BeanUtil.getProperty(configs.get(i), MerchantConfig.PAYWAY) + "";
                    String currCreditCardLimit = (String) BeanUtil.getNestedProperty(configs.get(i), MerchantConfig.PARAMS + "." + TransactionParam.ALLOW_CREDIT_PAY);
                    if (StringUtil.empty(currCreditCardLimit)) {
                        currCreditCardLimit = defaultCreditCardSwitch;
                    }
                    creditLimitConfigs.put(payway, currCreditCardLimit);
                }
            }
        }
        return creditLimitConfigs;
    }


    public void updateMerchantIsLimitCreditCard(String merchantId, Integer payway, String creditCardPayLimit) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        Map params = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap();
        }
        params.put(TransactionParam.ALLOW_CREDIT_PAY, creditCardPayLimit);
        merchantConfig.put(MerchantConfig.PARAMS, params);
        saveMerchantConfig(merchantConfig);
    }

    @Override
    public void updateAlipayV1TradeParams(String merchantId, Map alipayTradeParams) {
        String partner = BeanUtil.getPropString(alipayTradeParams, TransactionParam.PARTNER);
        String appKey = BeanUtil.getPropString(alipayTradeParams, TransactionParam.APP_KEY);
        String agentName = BeanUtil.getPropString(alipayTradeParams, AGENT_NAME);
        boolean formal = false;
        if (!StringUtil.empty(appKey)) {
            formal = true;
        }
        if (formal) {
            Map tradeParams = CollectionUtil.hashMap(
                    TransactionParam.PARTNER, partner,
                    TransactionParam.APP_KEY, appKey
            );
            updateFormalTradeParams(merchantId, PAYWAY_ALIPAY, true, true, null, agentName, agentName, null, TransactionParam.ALIPAY_V1_TRADE_PARAMS, tradeParams);
        } else {
            updateFormalTradeParams(merchantId, PAYWAY_ALIPAY, false, false, null, null, null, null, TransactionParam.ALIPAY_V1_TRADE_PARAMS, null);
        }
    }

    @Override
    public void updateAlipayV1TradeParamsAndLog(String merchantId, Map alipayTradeParams, OpLogCreateRequestV2 opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_ALIPAY);
        updateAlipayV1TradeParams(merchantId, alipayTradeParams);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_ALIPAY);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }


    @Override
    public void updateAlipayV2TradeParams(String merchantId, Map alipayTradeParams) {
        updateAlipayTradeParams(merchantId, alipayTradeParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS, false, true);
    }


    @Override
    public void updateAlipayV2TradeParamsAndLog(String merchantId, Map alipayTradeParams, OpLogCreateRequestV2 opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_ALIPAY2);
        updateAlipayV2TradeParams(merchantId, alipayTradeParams);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_ALIPAY2);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }


    @Override
    public void updateByAlipayV2TradeParams(String merchantId, Map alipayTradeParams, boolean restoreDefaultFeeRate) {
        updateAlipayTradeParams(merchantId, alipayTradeParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS, false, restoreDefaultFeeRate);
    }

    @Override
    public void updateAlipayV2WapTradeParams(String merchantId, Map alipayWapTradeParams) {
        updateAlipayTradeParams(merchantId, alipayWapTradeParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, true, true);
    }

    @Override
    public void addAlipayV2WapMiniAppId(String merchantId, String miniOpAppId) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, TradeConfigService.PAYWAY_ALIPAY2);
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            Map<String,Object> tradeParams = com.wosai.pantheon.util.MapUtil.getMap(merchantConfigParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
            if(tradeParams == null){
                throw new CoreInvalidParameterException("trade params is null");
            }
            tradeParams.put(TransactionParam.ALIPAY_OP_APP_ID, miniOpAppId);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), updateParamsFunc);
        redisService.removeCachedParams(businssCommonService.getMerchantSnById(merchantId));
    }

    @Override
    @SuppressWarnings("unchecked")
    public void updateAlipayV2AppTradeParams(String merchantId, Map alipayAppTradeParams) {
        Map<String, Object> merchantConfig = merchantConfigDao.filter(new Criteria(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_ALIPAY2)).fetchOne();
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, PAYWAY_ALIPAY2);
        }
        String feeRate = BeanUtil.getPropString(alipayAppTradeParams, TransactionParam.FEE_RATE);
        feeRate = StringUtil.empty(feeRate) ? DEFAULT_FEE_RATE_ALIPAYV2_FORMAL : feeRate;
        String appId = BeanUtil.getPropString(alipayAppTradeParams, TransactionParam.APP_ID);
        String privateKey = BeanUtil.getPropString(alipayAppTradeParams, TransactionParam.PRIVATE_KEY);
        String privateKeyId = rsaKeyService.storeRsaKey(privateKey);

        Map<String, Object> basicChange  = com.wosai.pantheon.util.MapUtil.hashMap(MerchantConfig.APP_FORMAL, true,
                    MerchantConfig.APP_AGENT_NAME, null,
                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.APP_FEE_RATE, feeRate
                );
        Map<String, Object> appTradeParams = com.wosai.pantheon.util.MapUtil.hashMap(
                    TransactionParam.APP_ID, appId,
                    TransactionParam.PRIVATE_KEY, privateKeyId,
                    TransactionParam.SIGN_TYPE, TransactionParam.SIGN_TYPE_RSA2,
                    TransactionParam.FEE_RATE, feeRate
                );
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS, appTradeParams);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), basicChange, updateParamsFunc);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void updateAlipayV2H5TradeParams(String merchantId, Map alipayH5TradeParams) {
        Map<String, Object> merchantConfig = merchantConfigDao.filter(new Criteria(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_ALIPAY2)).fetchOne();
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, PAYWAY_ALIPAY2);
        }
        String appId = BeanUtil.getPropString(alipayH5TradeParams, TransactionParam.APP_ID);
        String privateKey = BeanUtil.getPropString(alipayH5TradeParams, TransactionParam.PRIVATE_KEY);
        String privateKeyId = rsaKeyService.storeRsaKey(privateKey);
        String feeRate = BeanUtil.getPropString(alipayH5TradeParams, TransactionParam.FEE_RATE);
        feeRate = StringUtil.empty(feeRate) ? DEFAULT_FEE_RATE_ALIPAYV2_FORMAL : feeRate;

        Map<String, Object> basicChange = com.wosai.pantheon.util.MapUtil.hashMap(MerchantConfig.H5_FORMAL, true,
                    MerchantConfig.H5_AGENT_NAME, null,
                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.H5_FEE_RATE, feeRate
                );

        Map<String, Object> h5TradeParams = com.wosai.pantheon.util.MapUtil.hashMap(TransactionParam.APP_ID, appId,
                    TransactionParam.PRIVATE_KEY, privateKeyId,
                    TransactionParam.SIGN_TYPE, TransactionParam.SIGN_TYPE_RSA2,
                    TransactionParam.FEE_RATE, feeRate
                );
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS, h5TradeParams);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), basicChange, updateParamsFunc);
    }

    @Override
    public void updateAlipayV2AllTradeParams(String merchantId, Map alipayTradeParams) {
        updateAlipayTradeParams(merchantId, alipayTradeParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS, false, true);
        updateAlipayTradeParams(merchantId, alipayTradeParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, true, true);
    }

    private void updateAlipayTradeParams(String merchantId, Map alipayTradeParams, String tradeParamsKey, boolean isWap, boolean restoreDefaultFeeRate) {
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        //不存在此商户，则不做任何处理
        if (merchant == null) {
            return;
        }
        String authAppId = BeanUtil.getPropString(alipayTradeParams, TransactionParam.ALIPAY_AUTH_APP_ID);
        String authToken = BeanUtil.getPropString(alipayTradeParams, TransactionParam.APP_AUTH_TOKEN);
        String agentName = BeanUtil.getPropString(alipayTradeParams, AGENT_NAME);
        boolean formal = false;
        if (!StringUtil.empty(authAppId) || !StringUtil.empty(authToken)) {
            formal = true;
        }
        if (formal) {
            Map tradeParams = new HashMap();
            List<String> keys = Arrays.asList(
                    TransactionParam.ALIPAY_AUTH_APP_ID,
                    TransactionParam.APP_AUTH_TOKEN,
                    TransactionParam.ALIPAY_MCH_CATEGORY,
                    TransactionParam.ALIPAY_MCH_ID
            );
            for (String key : keys) {
                String value = BeanUtil.getPropString(alipayTradeParams, key);
                if (!StringUtil.empty(value)) {
                    tradeParams.put(key, value);
                }
            }

            try {
                //间连切直连 关闭花呗
                updateHuabeiParamsById(merchantId, new HashMap<String, Object>() {{
                    put(TransactionParam.ALIPAY_HUABEI_STATUS, Merchant.STATUS_DISABLED);
                }});
            } catch (Exception ex) {
                logger.info("update huabei params err:{}", ex);
            }
            if (isWap) {
                updateFormalTradeParams(merchantId, PAYWAY_ALIPAY2, null, null, true, null, null, agentName, tradeParamsKey, tradeParams);
            } else {
                updateFormalTradeParams(merchantId, PAYWAY_ALIPAY2, true, true, null, agentName, agentName, null, tradeParamsKey, tradeParams);
            }
            //恢复默认手续费
            if (restoreDefaultFeeRate) {
                //正式商户，设置默认费率，用于对账单记录手续费等
                Map merchantConfig = new HashMap();
                merchantConfig.put(MerchantConfig.PAYWAY, PAYWAY_ALIPAY2);
                if (isWap) {
                    merchantConfig.put(MerchantConfig.WAP_FEE_RATE, DEFAULT_FEE_RATE_ALIPAYV2_FORMAL);
                } else {
                    merchantConfig.put(MerchantConfig.B2C_FEE_RATE, DEFAULT_FEE_RATE_ALIPAYV2_FORMAL);
                    merchantConfig.put(MerchantConfig.C2B_FEE_RATE, DEFAULT_FEE_RATE_ALIPAYV2_FORMAL);
                }
                updateMerchantConfigStatusAndFeeRate(merchantId, merchantConfig);
            }
        } else {
            try {
                //直连切间连 开启花呗
                updateHuabeiParamsById(merchantId, new HashMap<String, Object>() {{
                    put(TransactionParam.ALIPAY_HUABEI_STATUS, Merchant.STATUS_ENABLED);
                }});
            } catch (Exception ex) {
                logger.info("update huabei params err:{}", ex);
            }
            if (isWap) {
                updateFormalTradeParams(merchantId, PAYWAY_ALIPAY2, null, null, false, null, null, null, tradeParamsKey, null);
            } else {
                updateFormalTradeParams(merchantId, PAYWAY_ALIPAY2, false, false, null, null, null, null, tradeParamsKey, null);
            }
        }
        // 根据商户号删除交易参数缓存
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, ConstantUtil.KEY_SN));
        redisService.removeCachedHuabeiParams(BeanUtil.getPropString(merchant, DaoConstants.ID));
    }


    @Override
    public void updateWeixinTradeParams(String merchantId, Map weixinTradeParams) {
        updateWeixinTradeParams(merchantId, weixinTradeParams, TransactionParam.WEIXIN_TRADE_PARAMS);
    }

    @Override
    public void updateWeixinTradeParamsAndLog(String merchantId, Map weixinTradeParams, OpLogCreateRequest opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN);
        updateWeixinTradeParams(merchantId, weixinTradeParams);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }

    @Override
    public void updateWeixinWapTradeParams(String merchantId, Map weixinWapTradeParams) {
        updateWeixinTradeParams(merchantId, weixinWapTradeParams, TransactionParam.WEIXIN_WAP_TRADE_PARAMS);
    }

    @Override
    public void updateWeixinWapTradeParamsAndLog(String merchantId, Map weixinWapTradeParams, OpLogCreateRequest opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN);
        updateWeixinWapTradeParams(merchantId, weixinWapTradeParams);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }

    @Override
    public void updateWeixinMiniTradeParams(String merchantId, Map weixinMiniTradeParams) {
        updateWeixinTradeParams(merchantId, weixinMiniTradeParams, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
    }

    @Override
    public void updateWeixinH5TradeParams(String merchantId, Map weixinH5TradeParams) {
        updateWeixinTradeParams(merchantId, weixinH5TradeParams, TransactionParam.WEIXIN_H5_TRADE_PARAMS);
    }

    @Override
    public void updateWeixinAppTradeParams(String merchantId, Map weixinAppTradeParams) {
        updateWeixinTradeParams(merchantId, weixinAppTradeParams, TransactionParam.WEIXIN_APP_TRADE_PARAMS);
    }

    private void updateWeixinTradeParams(String merchantId, Map weixinTradeParams, String tradeParamsKey) {
        //入参去除首尾空格
        String weixinSubMchId = BeanUtil.getPropString(weixinTradeParams, TransactionParam.WEIXIN_SUB_MCH_ID);
        if (!Objects.isNull(weixinSubMchId)) {
            weixinSubMchId = weixinSubMchId.trim();
        }
        String weixinSubAppId = BeanUtil.getPropString(weixinTradeParams, TransactionParam.WEIXIN_SUB_APP_ID, "").trim();
        String weixinSubAppsecret = BeanUtil.getPropString(weixinTradeParams, TransactionParam.WEIXIN_SUB_APP_SECRET, "").trim();

        String agentName = BeanUtil.getPropString(weixinTradeParams, AGENT_NAME);
        boolean isWap = TransactionParam.WEIXIN_WAP_TRADE_PARAMS.equals(tradeParamsKey) ? true : false;
        boolean isMini = TransactionParam.WEIXIN_MINI_TRADE_PARAMS.equals(tradeParamsKey) ? true : false;
        boolean isH5 = TransactionParam.WEIXIN_H5_TRADE_PARAMS.equals(tradeParamsKey) ? true : false;
        boolean isApp = TransactionParam.WEIXIN_APP_TRADE_PARAMS.equals(tradeParamsKey) ? true : false;
//        if (isWap) {
//            if ((!StringUtil.empty(weixinSubAppId) && StringUtil.empty(weixinSubAppsecret))
//                    || (StringUtil.empty(weixinSubAppId) && !StringUtil.empty(weixinSubAppsecret))) {
//                throw new CoreInvalidParameterException("weixin_sub_appsecret 与 weixin_sub_appid 不能一个为空，一个有值");
//            }
//        }

        boolean formal = false;
        if (!StringUtil.empty(weixinSubMchId)) {
            formal = true;
        }
        if (formal) {
            Map tradeParams = CollectionUtil.hashMap(
                    TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId,
                    TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId
            );
            if (isWap) {
                tradeParams.put(TransactionParam.WEIXIN_SUB_APP_SECRET, weixinSubAppsecret);
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, true, null, null, agentName, tradeParamsKey, tradeParams);
            } else if (isMini) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, null, true, null, null,
                        null, null, null, agentName, null, null, tradeParamsKey, tradeParams);
            } else if (isH5) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, null, null, true, null,
                        null, null, null, null, agentName, null, tradeParamsKey, tradeParams);
            } else if (isApp) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, null, null, null, true,
                        null, null, null, null, null, agentName, tradeParamsKey, tradeParams);
            } else {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, true, true, null, agentName, agentName, null, tradeParamsKey, tradeParams);
            }
        } else {
            if (isWap) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, false, null, null, null, tradeParamsKey, null);
            } else if (isMini) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, null, false, null, null,
                        null, null, null, null, null, null, tradeParamsKey, null);
            } else if (isH5) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, null, null, false, null,
                        null, null, null, null, null, null, tradeParamsKey, null);
            } else if (isApp) {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, null, null, null, null, null, false,
                        null, null, null, null, null, null, tradeParamsKey, null);
            } else {
                updateFormalTradeParams(merchantId, PAYWAY_WEIXIN, false, false, null, null, null, null, tradeParamsKey, null);
            }
        }
    }


    @Override
    public void updateWeixinHKTradeParams(String merchantId, Map weixinHkTradeParams) {
        updateWeixinHkTradeParams(merchantId, weixinHkTradeParams, TransactionParam.WEIXIN_TRADE_PARAMS);
    }

    @Override
    public void updateWeixinHKTradeParamsAndLog(String merchantId, Map weixinHkTradeParams, OpLogCreateRequest opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN_HK);
        updateWeixinHKTradeParams(merchantId, weixinHkTradeParams);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN_HK);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }


    private void updateWeixinHkTradeParams(String merchantId, Map weixinTradeParams, String tradeParamsKey) {
        String weixinMchId = BeanUtil.getPropString(weixinTradeParams, TransactionParam.WEIXIN_MCH_ID);
        String weixinAppId = BeanUtil.getPropString(weixinTradeParams, TransactionParam.WEIXIN_APP_ID, "");
        String agentName = BeanUtil.getPropString(weixinTradeParams, AGENT_NAME);
        boolean formal = false;
        if (!StringUtil.empty(weixinMchId)) {
            formal = true;
        }
        if (formal) {
            if ((!StringUtil.empty(weixinMchId) && StringUtil.empty(weixinAppId))
                    || (StringUtil.empty(weixinMchId) && !StringUtil.empty(weixinAppId))) {
                throw new CoreInvalidParameterException("weixin_mch_id 与 weixin_appid 不能一个为空，一个有值");
            }

            Map tradeParams = CollectionUtil.hashMap(
                    TransactionParam.WEIXIN_APP_ID, weixinAppId,
                    TransactionParam.WEIXIN_MCH_ID, weixinMchId
            );

            updateFormalTradeParams(merchantId, PAYWAY_WEIXIN_HK, true, true, null, agentName, agentName, null, tradeParamsKey, tradeParams);
        } else {
            updateFormalTradeParams(merchantId, PAYWAY_WEIXIN_HK, false, false, null, null, null, null, tradeParamsKey, null);
        }
    }

    @Override
    public void updateAlipayIntlTradeParams(String merchantId, Map alipayTradeParams) {
        String alipayMerchantId = BeanUtil.getPropString(alipayTradeParams, TransactionParam.ALIPAY_INTL_MERCHANT_ID);
        String alipayClientId = BeanUtil.getPropString(alipayTradeParams, TransactionParam.ALIPAY_INTL_CLIENT_ID);
        String alipayPrivateKey = BeanUtil.getPropString(alipayTradeParams, TransactionParam.ALIPAY_INTL_PRIVATE_KEY);
        String alipayPrivateKeyId = rsaKeyService.storeRsaKey(alipayPrivateKey);
        String alipayMerchantIndustry = BeanUtil.getPropString(alipayTradeParams, TransactionParam.ALIPAY_INTL_MERCHANT_ALIPAY_INDUSTRY);
        String agentName = BeanUtil.getPropString(alipayTradeParams, AGENT_NAME);
        Map tradeParams = CollectionUtil.hashMap(
                TransactionParam.ALIPAY_INTL_CLIENT_ID, alipayClientId,
                TransactionParam.ALIPAY_INTL_PRIVATE_KEY, alipayPrivateKeyId,
                TransactionParam.ALIPAY_INTL_MERCHANT_ID, alipayMerchantId,
                TransactionParam.ALIPAY_INTL_MERCHANT_ALIPAY_INDUSTRY, alipayMerchantIndustry
        );
        updateFormalTradeParams(merchantId, PAYWAY_ALIPAY_INTL, true, null, null, agentName, null, null, TransactionParam.ALIPAY_INTL_TRADE_PARAMS, tradeParams);
    }

    @Override
    public void updateBestPayTradeParams(String merchantId, Map bestPayTradeParams) {
        String bestPayMerchantId = BeanUtil.getPropString(bestPayTradeParams, TransactionParam.BESTPAY_MERCHANT_ID);
        String bestPayMerchantKey = BeanUtil.getPropString(bestPayTradeParams, TransactionParam.BESTPAY_MERCHANT_KEY);
        String bestPayMerchantPwd = BeanUtil.getPropString(bestPayTradeParams, TransactionParam.BESTPAY_MERCHANT_PWD);
        String bestPaySubMerchantId = BeanUtil.getPropString(bestPayTradeParams, TransactionParam.BESTPAY_SUB_MERCHANT_ID);
        String bestPayStoreId = BeanUtil.getPropString(bestPayTradeParams, TransactionParam.BESTPAY_STORE_ID);
        String bestLedgerDetail = BeanUtil.getPropString(bestPayTradeParams, TransactionParam.BESTPAY_LEDGERDETAIL);
        String agentName = BeanUtil.getPropString(bestPayTradeParams, AGENT_NAME);
        boolean formal = false;
        if (!StringUtil.empty(bestPayMerchantId)) {
            formal = true;
        }
        if (formal) {
            Map tradeParams = CollectionUtil.hashMap(
                    TransactionParam.BESTPAY_MERCHANT_ID, bestPayMerchantId,
                    TransactionParam.BESTPAY_MERCHANT_KEY, bestPayMerchantKey,
                    TransactionParam.BESTPAY_MERCHANT_PWD, bestPayMerchantPwd,
                    TransactionParam.BESTPAY_SUB_MERCHANT_ID, bestPaySubMerchantId,
                    TransactionParam.BESTPAY_STORE_ID, bestPayStoreId,
                    TransactionParam.BESTPAY_LEDGERDETAIL, bestLedgerDetail
            );
            updateFormalTradeParams(merchantId, PAYWAY_BESTPAY, true, null, true, agentName, null, agentName, TransactionParam.BESTPAY_TRADE_PARAMS, tradeParams);
        } else {
            updateFormalTradeParams(merchantId, PAYWAY_BESTPAY, false, null, false, null, null, null, TransactionParam.BESTPAY_TRADE_PARAMS, null);
        }
    }

    @Override
    public void updateCMCCTradeParams(String merchantId, Map cmccTradeParams) {
        updateCMCCTradeParams(merchantId, cmccTradeParams, TransactionParam.CMCC_TRADE_PARAMS);
    }

    private void updateCMCCTradeParams(String merchantId, Map cmccTradeParams, String tradeParamsKey) {
        String cmccMerchantId = BeanUtil.getPropString(cmccTradeParams, TransactionParam.CMCC_MERCHANT_ID);
        String cmccMerchantKey = BeanUtil.getPropString(cmccTradeParams, TransactionParam.CMCC_MERCHANT_KEY);

        boolean formal = false;
        if (!StringUtil.empty(cmccMerchantId)) {
            formal = true;
        }
        if (formal) {
            Map tradeParams = CollectionUtil.hashMap(
                    TransactionParam.CMCC_MERCHANT_ID, cmccMerchantId,
                    TransactionParam.CMCC_MERCHANT_KEY, cmccMerchantKey
            );
            updateFormalTradeParams(merchantId, PAYWAY_CMCC, true, false, null, null, null, null, tradeParamsKey, tradeParams);
        } else {
            updateFormalTradeParams(merchantId, PAYWAY_CMCC, false, false, null, null, null, null, tradeParamsKey, null);
        }
    }

    @Override
    public void updateSodexoTradeParams(String merchantId, boolean needClear, Map sodexoTradeParams) {
        if (needClear) {
            updateFormalTradeParams(merchantId, PAYWAY_SODEXO, false, null
                    , null, null, null, null
                    , TransactionParam.SODEXO_TRADE_PARAMS, null);

            return;
        }

        if (MapUtils.isEmpty(sodexoTradeParams)) {
            return;
        }
        String sodexoClientId = BeanUtil.getPropString(sodexoTradeParams, TransactionParam.SODEXO_CLIENT_ID);
        String sodexoClientSecret = BeanUtil.getPropString(sodexoTradeParams, TransactionParam.SODEXO_CLIENT_SECRET);
        String sodexoMid = BeanUtil.getPropString(sodexoTradeParams, TransactionParam.SODEXO_MID);
        String sodexoTid = BeanUtil.getPropString(sodexoTradeParams, TransactionParam.SODEXO_TID);

        Map tradeParams = new HashMap();
        if (sodexoTradeParams.containsKey(TransactionParam.SODEXO_CLIENT_ID)) {
            tradeParams.put(TransactionParam.SODEXO_CLIENT_ID, sodexoClientId);
        }
        if (sodexoTradeParams.containsKey(TransactionParam.SODEXO_CLIENT_SECRET)) {
            tradeParams.put(TransactionParam.SODEXO_CLIENT_SECRET, sodexoClientSecret);
        }
        if (sodexoTradeParams.containsKey(TransactionParam.SODEXO_MID)) {
            tradeParams.put(TransactionParam.SODEXO_MID, sodexoMid);
        }
        if (sodexoTradeParams.containsKey(TransactionParam.SODEXO_TID)) {
            tradeParams.put(TransactionParam.SODEXO_TID, sodexoTid);
        }

        if (MapUtils.isNotEmpty(tradeParams)) {
            updateFormalTradeParams(merchantId, PAYWAY_SODEXO, true, null
                    , null, null, null, null
                    , TransactionParam.SODEXO_TRADE_PARAMS, tradeParams);
        }
    }

    @Override
    public void updateHopeEduTradeParams(HopeEduMerchantConfigRequest request) {
        if (Objects.isNull(request) || StringUtils.isEmpty(request.getMerchantId())) {
            return;
        }

        String merchantId = request.getMerchantId();
        Map<String, Object> tradeParams = new HashMap<>();
        Integer b2cStatus = SUB_PAYWAY_ACTIVE_STATUS.equals(request.getB2cStatus()) ?
                MerchantConfig.STATUS_OPENED : MerchantConfig.STATUS_CLOSED;
        boolean b2cFormal = false;

        Map<String, Object> merchantConfig = merchantConfigDao.filter(new Criteria(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_HOPE_EDU)).fetchOne();
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, PAYWAY_HOPE_EDU);
        }

        Map<String, Object> basicChange  = com.wosai.pantheon.util.MapUtil.hashMap(
                MerchantConfig.B2C_FORMAL, b2cFormal,
                MerchantConfig.B2C_AGENT_NAME, HOPE_EDU_B2C_AGENT_NAME,
                MerchantConfig.B2C_STATUS, b2cStatus,
                MerchantConfig.B2C_FEE_RATE, "0",
                MerchantConfig.PROVIDER, Provider.HOPE_EDU.getCode()
        );

        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.HOPE_EDU_TRADE_PARAMS, tradeParams);
            return merchantConfigParams;
        };

        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), basicChange, updateParamsFunc);

        redisService.removeCachedParams(businssCommonService.getMerchantSnById(merchantId));

        // 操作日志
        Map before = merchantConfig;
        Map after = getMerchantConfig(BeanUtil.getPropString(merchantConfig, DaoConstants.ID));

        before.put(MerchantConfig.PROVIDER, Provider.HOPE_EDU.getName());
        after.put(MerchantConfig.PROVIDER, Provider.HOPE_EDU.getName());
        after.put(MerchantConfig.B2C_STATUS, b2cStatus);

        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setPlatformCode("SPA");
        opLogCreateRequest.setOpUserName("system");
        opLogCreateRequest.setOpUserId("system");
        if (b2cStatus.equals(MerchantConfig.STATUS_OPENED)) {
            opLogCreateRequest.setRemark("新希望集团商户启用交易参数");
        } else {
            opLogCreateRequest.setRemark("新希望集团商户关闭交易参数");
        }
        opLogCreateRequest.setOuterSceneTraceId("");
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }

    @Override
    public Map<String, Object> updateLakalaApplePayParams(LakalaMerchantConfigRequest request) {
        int payway = request.getPayway();
        if(payway != PAYWAY_APPLEPAY){
            throw new CoreInvalidParameterException("支付方式错误");
        }
        String merchantId = request.getMerchantId();
        String agentName = request.getAgentName();
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if(before == null){
            before = initMerchantConfig(merchantId, payway);
        }
        String merchantConfigId = com.wosai.pantheon.util.MapUtil.getString(before, DaoConstants.ID);
        Map<String,Object> params = com.wosai.pantheon.util.MapUtil.hashMap(
                TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID, request.getProviderMchId(),
                TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO, request.getProviderTerminalNo()
        );
        Map<String, Object> basicChange  = com.wosai.pantheon.util.MapUtil.hashMap(
                MerchantConfig.WAP_AGENT_NAME, agentName,
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.PROVIDER, Provider.LAKALA_UNION_PAY_V3.getCode()
        );

        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, params);
            return merchantConfigParams;
        };

        updateMerchantConfigWithRetry(merchantConfigId, basicChange, updateParamsFunc);
        redisService.removeCachedParams(businssCommonService.getMerchantSnById(merchantId));

        // 操作日志
        Map after = getMerchantConfig(merchantConfigId);
        OpLogCreateRequest opLogCreateRequest = new OpLogCreateRequest();
        opLogCreateRequest.setPlatformCode(BizLogFacade.DEFAULT_PLATFORM_CODE);
        opLogCreateRequest.setOpUserName(BizLogFacade.KEY_OP_USER_NAME_SYSTEM);
        opLogCreateRequest.setOpUserId(BizLogFacade.KEY_OP_USER_NAME_SYSTEM);
        opLogCreateRequest.setRemark("修改 apple pay 交易参数");
        opLogCreateRequest.setOuterSceneTraceId("");
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
        return after;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void updateCIBBankTradeParams(String merchantId, Map cibbankTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.CIBBANK_TRADE_PARAMS, cibbankTradeParams);

    }

    @Override
    public Map getCIBBankTradeParams(String merchantId) {
        return getProviderTradeParamsByKey(merchantId, TransactionParam.CIBBANK_TRADE_PARAMS);
    }

    @Override
    public void updateLakalaTradeParams(String merchantId, Map lakalaTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.LAKALA_TRADE_PARAMS, lakalaTradeParams);
    }

    @Override
    public void updateTLTradeParams(String merchantId, Map lakalaTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.TL_TRADE_PARAMS, lakalaTradeParams);
    }

    @Override
    public void updateYSTradeParams(String merchantId, Map lakalaTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.YS_TRADE_PARAMS, lakalaTradeParams);
    }

    @Override
    public void updateHKTradeParams(String merchantId, Map hkTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.HK_TRADE_PARAMS, hkTradeParams);
    }

    @Override
    public void updateGuotongTradeParams(String merchantId, Map gtTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.GUOTONG_TRADE_PARAMS, gtTradeParams);
    }

    @Override
    public void updateZTKXTradeParams(String merchantId, Map ztkxTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.ZTKX_TRADE_PARAMS, ztkxTradeParams);
    }

    @Override
    public void updateClearanceProvider(String merchantId, int clearanceProvider) {
        updateProviderTradeParams(merchantId, TransactionParam.CLEARANCE_PROVIDER, clearanceProvider);
        Map merchant = businssCommonService.getMerchantMinimalInfoById(merchantId);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
    }

    @Override
    public int getClearanceProvider(String merchantId) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) return CLEARANCE_PROVIDER_LKL;
        Map params = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            return CLEARANCE_PROVIDER_LKL;
        }
        return BeanUtil.getPropInt(params, TransactionParam.CLEARANCE_PROVIDER, CLEARANCE_PROVIDER_LKL);
    }


    @Override
    public Map getLakalaTradeParams(String merchantId) {
        return getProviderTradeParamsByKey(merchantId, TransactionParam.LAKALA_TRADE_PARAMS);
    }

    @Override
    public Map getWftTradeParams(String merchantId) {
        String mchNo = null;
        String pMchId = null;
        String pMchPayId = null;
        // 查payway=2配置
        Map merchantAlipayConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, MerchantConfig.PAYWAY_ALIPAY_V2);
        Map alipayParams = (Map) BeanUtil.getProperty(merchantAlipayConfig, MerchantConfig.PARAMS);
        String aKey = String.format("%s.%s", TransactionParam.CITICBANK_TRADE_PARAMS, TransactionParam.CITICBANK_MCH_ID);
        mchNo = (String) BeanUtil.getNestedProperty(alipayParams, aKey);
        if (mchNo != null) {
            Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
            Map params = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
            String key = String.format("%s.%s", TransactionParam.PROVIDER_ENROLLED_INFO, "1003");
            Map eParams = (Map) BeanUtil.getNestedProperty(params, key);
            if (eParams != null) {
                for (Object wKey : eParams.keySet()) {
                    Map info = (Map) eParams.get(wKey);
                    if (info != null && info.get(TransactionParam.CITICBANK_TRADE_PARAMS) != null) {
                        Map cInfo = (Map) info.get(TransactionParam.CITICBANK_TRADE_PARAMS);
                        if (mchNo.equals(cInfo.get(TransactionParam.CITICBANK_MCH_ID))) {
                            pMchId = (String) wKey;
                            pMchPayId = (String) cInfo.get(TransactionParam.CITICBANK_MCH_PAY_ID);
                        }
                    }
                }
            }
        }
        return CollectionUtil.hashMap(TransactionParam.CITICBANK_GROUP_NO, pMchId, TransactionParam.CITICBANK_MCH_ID, mchNo, TransactionParam.CITICBANK_MCH_PAY_ID, pMchPayId);
    }

    @Override
    public void updateCITICBankTradeParams(String merchantId, Map citicbankTradeParams) {
        updateProviderTradeParams(merchantId, TransactionParam.CITICBANK_TRADE_PARAMS, citicbankTradeParams);
    }

    @Override
    public Map getCITICBankTradeParams(String merchantId) {
        return getProviderTradeParamsByKey(merchantId, TransactionParam.CITICBANK_TRADE_PARAMS);
    }

    @Override
    public void updateHXBankMerchantConfig(Map<String, Object> hxBankMerchantConfig) {

        String merchantId = MapUtils.getString(hxBankMerchantConfig, MerchantConfig.MERCHANT_ID);
        Map<String, Object> merchant = merchantService.getMerchant(merchantId);
        if (MapUtils.isEmpty(merchant)){
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        int payway = MapUtils.getIntValue(hxBankMerchantConfig, MerchantConfig.PAYWAY);
        Map<String, Object> merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (merchantConfig == null) {
            throw new CoreMerchantConfigAbnormalException("未配置交易参数");
        }
        logger.info("商户[{}]支付方式[{}]的原交易参数是[{}]", merchantId, payway, JsonUtil.toJsonStr(merchantConfig));
        Map hxbankTradeParams = (Map) hxBankMerchantConfig.get(TransactionParam.HXBANK_TRADE_PARAMS);
        hxBankMerchantConfig.remove(TransactionParam.HXBANK_TRADE_PARAMS);
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.HXBANK_TRADE_PARAMS, hxbankTradeParams);
            return merchantConfigParams;
        };

        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), hxBankMerchantConfig, updateParamsFunc);

        //删除商户交易配置缓存
        redisService.removeCachedParams(MapUtils.getString(merchant, Merchant.SN));
        merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        logger.info("商户[{}]支付方式[{}]切换之后的交易参数是[{}]", merchantId, payway, JsonUtil.toJsonStr(merchantConfig));
    }

    @Override
    public void updateHXBankMerchantAppConfig(Map<String, Object> hxBankMerchantConfig) {
        String merchantId = MapUtils.getString(hxBankMerchantConfig, MerchantAppConfig.MERCHANT_ID);
        Integer payway = MapUtils.getIntValue(hxBankMerchantConfig, MerchantAppConfig.PAYWAY);
        Map<String, Object> merchant = merchantService.getMerchant(merchantId);
        if (MapUtils.isEmpty(merchant)){
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        List<Map<String, Object>> merchantAppConfigs = getMerchantAppConfigByMerchantIdAndApp(merchantId, null);
        merchantAppConfigs.forEach(merchantAppConfig -> {
            if (payway != MapUtils.getIntValue(merchantAppConfig, MerchantAppConfig.PAYWAY)) {
                return;
            }
            logger.info("商户[{}]支付方式[{}]的原应用交易参数是[{}]", merchantId, payway, JsonUtil.toJsonStr(merchantAppConfig));
            Map<String, Object> appConfig = new HashMap<>();
            appConfig.put(DaoConstants.ID, MapUtils.getString(merchantAppConfig, DaoConstants.ID));
            appConfig.put(MerchantAppConfig.PROVIDER, MapUtils.getString(hxBankMerchantConfig, MerchantAppConfig.PROVIDER));
            appConfig.putAll(BeanUtil.getPart(hxBankMerchantConfig, Arrays.asList(MerchantAppConfig.B2C_AGENT_NAME, MerchantAppConfig.B2C_STATUS,
                    MerchantAppConfig.C2B_AGENT_NAME, MerchantAppConfig.C2B_STATUS,
                    MerchantAppConfig.WAP_AGENT_NAME, MerchantAppConfig.WAP_STATUS,
                    MerchantAppConfig.MINI_AGENT_NAME, MerchantAppConfig.MINI_STATUS)));

            Map<String, Object> tradeParams = MapUtils.getMap(merchantAppConfig, MerchantAppConfig.PARAMS);
            if (MapUtils.isEmpty(tradeParams)){
                tradeParams = new HashMap();
            }
            tradeParams.put(TransactionParam.HXBANK_TRADE_PARAMS, MapUtils.getMap(hxBankMerchantConfig, TransactionParam.HXBANK_TRADE_PARAMS));
            appConfig.put(MerchantAppConfig.PARAMS, tradeParams);
            merchantAppConfigDao.updatePart(appConfig);

            //删除商户交易配置缓存
            redisService.removeCachedParams(MapUtils.getString(merchant, Merchant.SN));
            Map<String, Object> newMerchantAppConfig = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, MapUtils.getString(merchantAppConfig, MerchantAppConfig.APP_ID));
            logger.info("商户[{}]支付方式[{}]切换之后的应用交易参数是[{}]", merchantId, payway, JsonUtil.toJsonStr(newMerchantAppConfig));
        });
    }

    @Override
    public void updateProviderTradeParams(String merchantId, Integer provider, Map tradeParams) {
        updateProviderTradeParams(merchantId, getTradeParamsKeyByProvider(provider), tradeParams);
    }

    @Override
    public Map getProviderTradeParams(String merchantId, Integer provider) {
        return getProviderTradeParamsByKey(merchantId, getTradeParamsKeyByProvider(provider));
    }

    /**
     * 设置拉卡拉，兴业，中信等商户号信息
     *
     * @param merchantId
     * @param key
     * @param tradeParams
     */
    private void updateProviderTradeParams(String merchantId, String key, Object tradeParams) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) {
            initMerchantConfig(merchantId, null);
            merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        }
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(key, tradeParams);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), updateParamsFunc);
    }

    /**
     * 获取拉卡拉，兴业，中信等商户号信息
     *
     * @param merchantId
     * @param key
     * @return
     */
    private Map getProviderTradeParamsByKey(String merchantId, String key) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map params = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        return (Map) BeanUtil.getProperty(params, key);
    }

    /**
     * 更新b2c,c2b wap 是否正式以及 正式的参数
     *
     * @param merchantId
     * @param payway
     * @param b2cFormal
     * @param c2bFormal
     * @param wapFormal
     * @param tradeParamsKey
     * @param tradeParams
     */
    private void updateFormalTradeParams(String merchantId, int payway, Boolean b2cFormal, Boolean c2bFormal, Boolean wapFormal, String b2cAgentName, String c2bAgentName, String wapAgentName, String tradeParamsKey, Map tradeParams) {
        updateFormalTradeParams(merchantId, payway, b2cFormal, c2bFormal, wapFormal, null, null, null, b2cAgentName, c2bAgentName, wapAgentName, null, null, null, tradeParamsKey, tradeParams);
    }

    /**
     * 更新b2c,c2b wap, mini 是否正式以及 正式的参数
     *
     * @param merchantId
     * @param payway
     * @param b2cFormal
     * @param c2bFormal
     * @param wapFormal
     * @param miniFormal
     * @param h5Formal
     * @param appFormal
     * @param b2cAgentName
     * @param c2bAgentName
     * @param wapAgentName
     * @param miniAgentName
     * @param h5AgentName
     * @param appAgentName
     * @param tradeParamsKey
     * @param tradeParams
     */
    private void updateFormalTradeParams(String merchantId, int payway, Boolean b2cFormal, Boolean c2bFormal, Boolean wapFormal, Boolean miniFormal, Boolean h5Formal, Boolean appFormal, String b2cAgentName, String c2bAgentName, String wapAgentName, String miniAgentName, String h5AgentName, String appAgentName, String tradeParamsKey, Map tradeParams) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, payway);
        }
        if (b2cFormal != null) {
            merchantConfig.put(MerchantConfig.B2C_FORMAL, b2cFormal);
            merchantConfig.put(MerchantConfig.B2C_AGENT_NAME, b2cAgentName);
        }
        if (c2bFormal != null) {
            merchantConfig.put(MerchantConfig.C2B_FORMAL, c2bFormal);
            merchantConfig.put(MerchantConfig.C2B_AGENT_NAME, c2bAgentName);
        }
        if (wapFormal != null) {
            merchantConfig.put(MerchantConfig.WAP_FORMAL, wapFormal);
            merchantConfig.put(MerchantConfig.WAP_AGENT_NAME, wapAgentName);
        }
        if (miniFormal != null) {
            merchantConfig.put(MerchantConfig.MINI_FORMAL, miniFormal);
            merchantConfig.put(MerchantConfig.MINI_AGENT_NAME, miniAgentName);
        }
        if (h5Formal != null) {
            merchantConfig.put(MerchantConfig.H5_FORMAL, h5Formal);
            merchantConfig.put(MerchantConfig.H5_AGENT_NAME, h5AgentName);
        }
        if (appFormal != null) {
            merchantConfig.put(MerchantConfig.APP_FORMAL, appFormal);
            merchantConfig.put(MerchantConfig.APP_AGENT_NAME, appAgentName);
        }

        //如果是支付宝，开通正式默认打开对应的收款通道
        if (payway == PAYWAY_ALIPAY || payway == PAYWAY_ALIPAY2) {
            if (b2cFormal != null && b2cFormal) {
                merchantConfig.put(MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED);
            }
            if (c2bFormal != null && c2bFormal) {
                merchantConfig.put(MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED);
            }
            if (wapFormal != null && wapFormal) {
                merchantConfig.put(MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED);
            }
        }
        //如果是支付宝1.0, 关闭正式，默认关闭对应的收款通道
        if (payway == PAYWAY_ALIPAY) {
            if (b2cFormal != null && !b2cFormal) {
                merchantConfig.put(MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_CLOSED);
            }
            if (c2bFormal != null && !c2bFormal) {
                merchantConfig.put(MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_CLOSED);
            }
            if (wapFormal != null && !wapFormal) {
                merchantConfig.put(MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_CLOSED);
            }
        }
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            if (tradeParams == null || tradeParams.isEmpty()) {
                merchantConfigParams.remove(tradeParamsKey);
            } else {
                merchantConfigParams.put(tradeParamsKey, tradeParams);
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), merchantConfig, updateParamsFunc);
    }

    @Override
    public void updateMerchantConfigStatusAndFeeRateObeySolicitor(String merchantId, Map merchantConfigRequest) {
        Map merchant = merchantService.getMerchant(merchantId);
        if (merchant == null) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        String solicitorId = BeanUtil.getPropString(merchant, Merchant.SOLICITOR_ID);
        if (solicitorId == null || solicitorId.length() == 0) {
            throw new CoreInvalidParameterException("商户没有对应的推广渠道");
        }
        int payway = BeanUtil.getPropInt(merchantConfigRequest, MerchantConfig.PAYWAY);
        Map currentSolicitorConfig = getAnalyzedSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
        Map merchantConfig;
        if ("yes".equals(merchantConfigRequest.get("new"))) {
            merchantConfig = merchantConfigRequest; // 新增费率
            merchantConfigRequest.remove("new");
        } else {
            merchantConfig = getAnalyzedMerchantConfigByMerchantIdAndPayway(merchantId, payway); // 更新费率
        }
        List<String> feeRateKeys = Arrays.asList(SolicitorConfig.B2C_FEE_RATE, SolicitorConfig.C2B_FEE_RATE, SolicitorConfig.WAP_FEE_RATE);
        for (String feeRateKey : feeRateKeys) {
            if (merchantConfig.containsKey(feeRateKey)) {
                String solicitorFeeRate = BeanUtil.getPropString(currentSolicitorConfig, feeRateKey);
                String merchantFeeRate = BeanUtil.getPropString(merchantConfig, feeRateKey);
                if (StringUtil.empty(merchantFeeRate)) {
                    throw new CoreInvalidParameterException("商户" + feeRateKey + " 费率不能为空");
                }
                if (StringUtil.empty(solicitorFeeRate)) {
                    throw new CoreInvalidParameterException("推广者" + feeRateKey + " 费率配置为空,不能编辑商户费率");
                }
                //推广者修改商户费率只能只能调高，不能调低
                if (Double.parseDouble(solicitorFeeRate) > Double.parseDouble(merchantFeeRate)) {
                    throw new CoreInvalidParameterException("商户费率不能低于推广者默认的费率");
                }
                if (Double.parseDouble(merchantFeeRate) < ConstantUtil.MIN_FEE_RATE || Double.parseDouble(merchantFeeRate) > ConstantUtil.MAX_FEE_RATE) {
                    throw new CoreInvalidParameterException("商户费率超出规定范围");
                }
            }
        }
        merchantConfigRequest.remove(ConstantUtil.KEY_MERCHANT_ID);
        updateMerchantConfigStatusAndFeeRate(merchantId, merchantConfigRequest);
    }

    @Override
    public void updateMerchantConfigStatusAndFeeRate(String merchantId, Map merchantConfig) {
        Map merchant = merchantService.getMerchant(merchantId);
        if (null == merchant) {
            throw new CoreInvalidParameterException("商户不存在");
        }
        // 设置阶梯费率配置
        String ladderFeeStatus = BeanUtil.getPropString(merchantConfig, MerchantConfig.LADDER_STATUS);
        List<Map> ladderFeeConfig = (List<Map>) BeanUtil.getProperty(merchantConfig, MerchantConfig.LADDER_FEE_RATES);
        Map<String, String> ladderFeeRateTag = (Map<String, String>) BeanUtil.getProperty(merchantConfig, MerchantConfig.LADDER_FEE_RATE_TAG);
        Map<String, String> feeRateTag = (Map<String, String>) BeanUtil.getProperty(merchantConfig, TransactionParam.FEE_RATE_TAG);
        Map<String, Object> updateParams = com.wosai.pantheon.util.MapUtil.getMap(merchantConfig, MerchantConfig.PARAMS);
        if (MapUtils.isEmpty(feeRateTag)) {
            feeRateTag = com.wosai.pantheon.util.MapUtil.getMap(updateParams, TransactionParam.FEE_RATE_TAG);
        }
        merchantConfig = BeanUtil.getPart(merchantConfig, CollectionUtil.hashSet(
                MerchantConfig.B2C_FEE_RATE, MerchantConfig.B2C_STATUS,
                MerchantConfig.C2B_FEE_RATE, MerchantConfig.C2B_STATUS,
                MerchantConfig.WAP_FEE_RATE, MerchantConfig.WAP_STATUS,
                MerchantConfig.MINI_FEE_RATE, MerchantConfig.MINI_STATUS,
                MerchantConfig.H5_FEE_RATE, MerchantConfig.H5_STATUS,
                MerchantConfig.APP_FEE_RATE, MerchantConfig.APP_STATUS,
                MerchantConfig.PAYWAY, MerchantConfig.PARAMS
        ));
        checkConfig(merchantConfig);

        int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
        Map existsMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (!StringUtil.empty(ladderFeeStatus) && (ladderFeeStatus.equals(MerchantConfig.STATUS_OPENED + "")
                || ladderFeeStatus.equals(MerchantConfig.STATUS_CLOSED + ""))) {
            Map params = new LinkedHashMap<>();
            if (null != (Map) BeanUtil.getProperty(existsMerchantConfig, MerchantConfig.PARAMS)) {
                params.putAll((Map) BeanUtil.getProperty(existsMerchantConfig, MerchantConfig.PARAMS));
            }

            // 支付源交易参数未配置时，取payway=null的默认配置
            if (null == existsMerchantConfig
                    || null == existsMerchantConfig.get(MerchantConfig.PARAMS)
                    || !((Map) existsMerchantConfig.get(MerchantConfig.PARAMS)).containsKey(MerchantConfig.LADDER_STATUS)) {
                Map defaultConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
                params.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropString(defaultConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)));
                params.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getPropString(defaultConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_FEE_RATES)));
                params.put(MerchantConfig.LADDER_FEE_RATE_TAG, BeanUtil.getPropString(defaultConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_FEE_RATE_TAG)));
            }

            if (StringUtil.empty(BeanUtil.getPropString(params, MerchantConfig.LADDER_STATUS))) {
                throw new CoreMerchantConfigAbnormalException("商户未开通特优费率，无法进行特优费率变更");
            }

            // 如果特优费率状态发生变化，需更新所有payway对应的merchant_config的ladder_status配置
            if (!ladderFeeStatus.equals(BeanUtil.getPropString(params, MerchantConfig.LADDER_STATUS))) {
                List<Integer> paywayList = new ArrayList(Arrays.asList(ArrayUtils.toObject(DEFAULT_PAYWAYS)));
                paywayList.add(null);
                for (Integer changePayway : paywayList) {
                    if (changePayway != null && changePayway == payway) {
                        continue;
                    }

                    Map config = getMerchantConfigByMerchantIdAndPayway(merchantId, changePayway);
                    if (null != config) {
                        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
                            merchantConfigParams.put(MerchantConfig.LADDER_STATUS, Integer.valueOf(ladderFeeStatus));
                            return merchantConfigParams;
                        };
                        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(config, DaoConstants.ID), updateParamsFunc);
                    }
                }
            }

            params.put(MerchantConfig.LADDER_STATUS, Integer.valueOf(ladderFeeStatus));
            params.put(MerchantConfig.LADDER_FEE_RATES, ladderFeeConfig);
            params.put(MerchantConfig.LADDER_FEE_RATE_TAG, ladderFeeRateTag);
            merchantConfig.put(MerchantConfig.PARAMS, params);
        }

        if (MapUtils.isEmpty(feeRateTag)) {
            feeRateTag = new HashMap<>();
        }
        String b2cFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.B2C_FEE_RATE);
        String c2bFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.C2B_FEE_RATE);
        String wapFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.WAP_FEE_RATE);
        String miniFeeRate = BeanUtil.getPropString(merchantConfig, MerchantConfig.MINI_FEE_RATE);
        if (!StringUtils.isEmpty(b2cFeeRate)) {
            feeRateTag.putIfAbsent(SUB_PAYWAY_BARCODE + "", "");
        }
        if (!StringUtils.isEmpty(c2bFeeRate)) {
            feeRateTag.putIfAbsent(SUB_PAYWAY_QRCODE + "", "");
        }
        if (!StringUtils.isEmpty(wapFeeRate)) {
            feeRateTag.putIfAbsent(SUB_PAYWAY_WAP + "", "");
        }
        if (!StringUtils.isEmpty(miniFeeRate)) {
            feeRateTag.putIfAbsent(SUB_PAYWAY_MINI + "", "");
        }
        if (MapUtils.isNotEmpty(feeRateTag) || MapUtils.isNotEmpty(updateParams)) {
            Map existParams = (Map) BeanUtil.getProperty(existsMerchantConfig, MerchantConfig.PARAMS);
            Map oldFeeRateTag = (Map) BeanUtil.getProperty(existParams, TransactionParam.FEE_RATE_TAG);
            if (oldFeeRateTag != null) {
                oldFeeRateTag.putAll(feeRateTag);
                feeRateTag = oldFeeRateTag;
            }
            // 需要变费率信息时，移除原先配置的fee_rate_type 和相关的配置
            String newFeeRateType = BeanUtil.getPropString(updateParams, MerchantConfig.FEE_RATE_TYPE);
            if (MapUtils.isNotEmpty(existParams) && !StringUtil.empty(newFeeRateType)) {
                existParams.remove(MerchantConfig.LADDER_FEE_RATE_TAG);
                existParams.remove(MerchantConfig.LADDER_FEE_RATES);
                existParams.remove(MerchantConfig.LADDER_STATUS);
                existParams.remove(MerchantConfig.CHANNEL_FEE_RATE_TAG);
                existParams.remove(MerchantConfig.CHANNEL_STATUS);
                existParams.remove(MerchantConfig.FEE_RATE_TYPE);
                existParams.remove(TransactionParam.CHANNEL_FEE_RATE);
                existParams.remove(TransactionParam.PARAMS_BANKCARD_FEE);
                existParams.remove(TransactionParam.CHANNEL_LADDER_FEE_RATES);
            }
            Map mergeParams = new HashMap();
            if (MapUtils.isNotEmpty(existParams)) {
                mergeParams.putAll(existParams);
            }
            if (MapUtils.isNotEmpty(updateParams)) {
                mergeParams.putAll(updateParams);
            }



            mergeParams.put(TransactionParam.FEE_RATE_TAG, feeRateTag);
            merchantConfig.put(MerchantConfig.PARAMS, mergeParams);
        }

        if (existsMerchantConfig != null) {
            merchantConfig.put(DaoConstants.ID, BeanUtil.getPropString(existsMerchantConfig, DaoConstants.ID));
            updateMerchantConfigWithRetry(merchantConfig);
        } else {
            merchantConfig.put(MerchantConfig.MERCHANT_ID, merchantId);
            merchantConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
            merchantConfig.put(MerchantConfig.B2C_STATUS, BeanUtil.getPropInt(merchantConfig, MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED));
            merchantConfig.put(MerchantConfig.C2B_STATUS, BeanUtil.getPropInt(merchantConfig, MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED));
            merchantConfig.put(MerchantConfig.WAP_STATUS, BeanUtil.getPropInt(merchantConfig, MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED));
            merchantConfig.put(MerchantConfig.MINI_STATUS, BeanUtil.getPropInt(merchantConfig, MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED));
            saveMerchantConfig(merchantConfig);
        }

        // 删除费率缓存
        redisTemplate.delete(PublicConstants.UPAY_BASIC_PARAM + BeanUtil.getPropString(merchant, Merchant.SN));
        redisTemplate.delete(PublicConstants.UPAY_ALL_PARAM + BeanUtil.getPropString(merchant, Merchant.SN));
        // 发送变更消息
        try {
            merchantConfigDataBusBiz.feeRateChange(existsMerchantConfig == null ? new HashMap() : existsMerchantConfig, getMerchantConfigByMerchantIdAndPayway(merchantId, payway));
        } catch (Exception e) {
            logger.error("{} {} 费率变更写入事件表失败", merchantId, payway, e);
        }
    }

    @Override
    public void updateMerchantConfigStatusAndFeeRateAndLog(String merchantId, Map merchantConfig, OpLogCreateRequestV2 opLogCreateRequest) {
        Integer payway = org.apache.commons.collections4.MapUtils.getInteger(merchantConfig, MerchantConfig.PAYWAY);
        Map<String, Object> before = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        updateMerchantConfigStatusAndFeeRate(merchantId, merchantConfig);
        Map<String, Object> after = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_KEY_LIST, OpLog.MERCHANT_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_CONFIG_DESC_MAP, before, after);
    }

    @Override
    public void updateMerchantConfigStatus(String merchantId, List<Map> updateMerchantConfigs) {
        if (CollectionUtils.isEmpty(updateMerchantConfigs)) {
            return;
        }
        List<Integer> validStatusList = Arrays.asList(
                MerchantConfig.STATUS_CLOSED,
                MerchantConfig.STATUS_OPENED
        );
        for (Map updateMerchantConfig : updateMerchantConfigs) {
            Integer payway = MapUtils.getInteger(updateMerchantConfig, MerchantConfig.PAYWAY);
            Map<String, Object> updatePart = BeanUtil.getPart(updateMerchantConfig, Arrays.asList(
                    MerchantConfig.B2C_STATUS, MerchantConfig.C2B_STATUS,
                    MerchantConfig.WAP_STATUS, MerchantConfig.MINI_STATUS,
                    MerchantConfig.H5_STATUS, MerchantConfig.APP_STATUS));
            if(updatePart.isEmpty()){
                continue;
            }
            long count = updatePart.values().stream().filter(o -> !validStatusList.contains(o)).count();
            if (count > 0) {
                throw new CoreInvalidParameterException("payway：" + payway + "，更新状态不正确");
            }
            Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
            if (merchantConfig == null) {
                merchantConfig = initMerchantConfig(merchantId, payway);
            }
            String merchantConfigId = MapUtils.getString(merchantConfig, DaoConstants.ID);
            updatePart.put(DaoConstants.ID, merchantConfigId);
            updateMerchantConfigWithRetry(updatePart);
        }
        String merchantSn = businssCommonService.getMerchantSnById(merchantId);
        SupportService supportService = SpringContextHolder.getBean(SupportService.class);
        supportService.removeCachedParams(merchantSn);
    }

    @Override
    public void updateMerchantB2cStatus(String merchantId, int b2cStatus) {
        List<Integer> validStatusList = Arrays.asList(
                MerchantConfig.STATUS_CLOSED,
                MerchantConfig.STATUS_OPENED
        );
        if (!validStatusList.contains(b2cStatus)) {
            throw new CoreInvalidParameterException("b2c 状态不正确");
        }
        List<Map> list = getAnalyzedMerchantConfigs(merchantId);
        for (Map merchantConfig : list) {
            if (BeanUtil.getProperty(merchantConfig, MerchantConfig.PAYWAY) != null) {
                int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
                updateMerchantConfigStatusAndFeeRate(merchantId, CollectionUtil.hashMap(
                        MerchantConfig.B2C_STATUS, b2cStatus,
                        MerchantConfig.PAYWAY, payway
                ));
            }
        }
        String merchantSn = businssCommonService.getMerchantSnById(merchantId);
        SupportService supportService = SpringContextHolder.getBean(SupportService.class);
        supportService.removeCachedParams(merchantSn);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void updateMerchantConfigProvider(String merchantId, Integer payway, Integer provider) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (merchantConfig == null) {
            initMerchantConfig(merchantId, payway);
            merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        }
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), 
                com.wosai.pantheon.util.MapUtil.hashMap(MerchantConfig.PROVIDER, provider), 
                (Function)null);
    }

    @Override
    public void updateMerchantConfigProvider(String merchantId, Integer payway, Integer provider, String agentName) {
            updateWftOrNuccOrUnionPayMerchantConfigProvider(merchantId, payway, provider, agentName, null, null, null);
    }

    @Override
    public boolean updateMerchantConfigProviderCommon(Map params) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfig.MERCHANT_ID);
        String agentName = BeanUtil.getPropString(params, AGENT_NAME);
        int payway = BeanUtil.getPropInt(params, MerchantConfig.PAYWAY, -1);
        int provider = BeanUtil.getPropInt(params, MerchantConfig.PROVIDER);
        String providerMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.PROVIDER_MERCHANT_ID);
        String alipayMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.ALIPAY_MERCHANT_ID);
        String weixinMerchantId = BeanUtil.getPropString(params, MerchantProviderTradeParams.WEIXIN_MERCHANT_ID);
        String weixinAppid = BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_ID);
        String weixinAppSecret = BeanUtil.getPropString(params, TransactionParam.WEIXIN_SUB_APP_SECRET);
        String weixinMiniAppid = BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
        String weixinMiniAppSecret = BeanUtil.getPropString(params, TransactionParam.WEIXIN_MINI_SUB_APP_SECRET);
        return updateMerchantConfigProviderHandle(merchantId, payway, provider, agentName, providerMerchantId, alipayMerchantId, weixinMerchantId, weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret);
    }

    private boolean updateMerchantConfigProviderHandle(String merchantId, int payway, int provider, String agentName, String providerMerchantId, String alipayMerchantId, String weixinMerchantId, String weixinAppid, String weixinAppSecret, String weixinMiniAppid, String weixinMiniAppSecret) {
        Map<String, Object> merchant = businssCommonService.getMerchantMinimalInfoById(merchantId);
        if (merchant == null) {
            return false;
        }
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (payway == -1 && provider == 0 && StringUtil.empty(agentName)) {
            logger.error("updateMerchantConfigProviderHandle:当payway不为null, provider不为null时，必须设置agentName值");
            return false;
        }
        if (merchantConfig == null) {
            initMerchantConfig(merchantId, payway);
            merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        }
        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PROVIDER, provider
        );
        if (payway == -1) {
            updateInfo.put(MerchantConfig.B2C_AGENT_NAME, agentName);
            updateInfo.put(MerchantConfig.C2B_AGENT_NAME, agentName);
            updateInfo.put(MerchantConfig.WAP_AGENT_NAME, agentName);
            updateInfo.put(MerchantConfig.MINI_AGENT_NAME, agentName);
        } else {
            //如果正式，不替换对应的agentName
            for (String subPaywayColumn : subPaywayAgentNameColName.keySet()) {
                String agentNameColumn = subPaywayAgentNameColName.get(subPaywayColumn);
                String formalColumn = subPaywayFormalColName.get(subPaywayColumn);
                boolean formal = BeanUtil.getPropBoolean(merchantConfig, formalColumn, false);
                if (!formal) {
                    updateInfo.put(agentNameColumn, agentName);
                }
            }
        }
        Map<String, Object> paramsChange = new HashMap<String, Object>();
        if (payway != -1 && !StringUtil.empty(agentName) && provider != 0) {
            String tradeParamKey = getTradeParamsKeyByProvider(provider);
            Map<String, Object> tradeParams = (Map) BeanUtil.getNestedProperty(getAgentByName(agentName), String.format("%s.%s", Agent.PARAMS, tradeParamKey));
            if (tradeParams == null) {
                logger.error("updateMerchantConfigProviderHandle:tradeParams is null");
                return false;
            }
            Map merchantTradeParams = getMerchantTradeParams(provider, payway, providerMerchantId, alipayMerchantId, weixinMerchantId, weixinAppid, weixinAppSecret, weixinMiniAppid, weixinMiniAppSecret, tradeParams);
            if (merchantTradeParams == null) {
                return false;
            }
            paramsChange.put(tradeParamKey, merchantTradeParams);
        }
        Function<Map<String, Object>, Map<String, Object>> changeFunction = (merchantConfigParams) -> {
            Map<String, String> providerTradeParamsKey = metaProviderBiz.getTradeParamKeys();
            //清空设置到大商户下面的子商户号配置
            if (!merchantConfigParams.isEmpty()) {
                if (providerTradeParamsKey != null) {
                    for (String providerKey : providerTradeParamsKey.keySet()) {
                        //lakala 比较特殊，它还负责结算
                        if (!providerKey.equals(PROVIDER_LAKALA + "")) {
                            merchantConfigParams.remove(providerTradeParamsKey.get(providerKey));
                        }
                    }
                }
            }
            merchantConfigParams.putAll(paramsChange);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(updateInfo, DaoConstants.ID), updateInfo, changeFunction);
        return true;
    }


    private Map getMerchantTradeParams(int provider, int payway, String providerMerchantId, String alipayMerchantId, String weixinMerchantId, String weixinAppid, String weixinAppSecret, String weixinMiniAppid, String weixinMiniAppSecret, Map<String, Object> tradeParams) {
        switch (provider) {
            case PROVIDER_NUCC:
                if (payway == PAYWAY_ALIPAY2 && !StringUtil.empty(alipayMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.NUCC_ALIPAY_SUB_MCH_ID, alipayMerchantId);
                }
                if (payway == PAYWAY_WEIXIN && !StringUtil.empty(weixinMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.NUCC_WEIXIN_SUB_MCH_ID, weixinMerchantId,
                            TransactionParam.NUCC_WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.NUCC_WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
            case PROVIDER_UNIONPAY:
                if (payway == PAYWAY_ALIPAY2 && !StringUtil.empty(alipayMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID, alipayMerchantId);
                }
                if (payway == PAYWAY_WEIXIN && !StringUtil.empty(weixinMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, weixinMerchantId,
                            TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.UNION_PAY_WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
            case PROVIDER_LKLWANMA:
                if (payway == PAYWAY_ALIPAY2 && !StringUtil.empty(alipayMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.ALIPAY_PID, alipayMerchantId,
                            TransactionParam.LAKALA_WANMA_MCH_ID, providerMerchantId);
                }
                if (payway == PAYWAY_WEIXIN && !StringUtil.empty(weixinMerchantId)) {
                    return CollectionUtil.hashMap(
                            TransactionParam.WEIXIN_SUB_MCH_ID, weixinMerchantId,
                            TransactionParam.LAKALA_WANMA_MCH_ID, providerMerchantId,
                            TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                            TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                            TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret);
                }
            default:
                String agentDefaultMchId = null;
                String agentWeixinSubAppid = null;
                String mchIdKey = null;
                for (String key : tradeParams.keySet()) {
                    if (key.endsWith(TransactionParam.SWIFTPASS_MCH_ID_SUFFIX)) {
                        mchIdKey = key;
                        agentDefaultMchId = BeanUtil.getPropString(tradeParams, key);
                    } else if (key.equals(TransactionParam.WEIXIN_SUB_APP_ID)) {
                        agentWeixinSubAppid = BeanUtil.getPropString(tradeParams, key);
                    }
                }
                if (payway == PAYWAY_WEIXIN && StringUtil.empty(agentWeixinSubAppid) && StringUtil.empty(agentDefaultMchId) && !StringUtil.empty(weixinAppid) && !StringUtil.empty(weixinMiniAppid)) {
                    //同一个商户有可能在同一个渠道报备了多次，区别在于报备的公众号不一样
                    if (!StringUtil.empty(providerMerchantId)) {
                        return CollectionUtil.hashMap(
                                mchIdKey, providerMerchantId,
                                TransactionParam.WEIXIN_SUB_APP_ID, weixinAppid,
                                TransactionParam.WEIXIN_SUB_APP_SECRET, weixinAppSecret,
                                TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniAppid,
                                TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniAppSecret
                        );
                    }
                }
                if (payway == PAYWAY_ALIPAY2) {
                    return CollectionUtil.hashMap(mchIdKey, providerMerchantId);
                }
        }
        return null;
    }

    @Override
    public void updateWeixinMerchantConfigProvider(String merchantId, Integer provider, String agentName, String subAppid, String subAppsecret) {
        updateWftOrNuccOrUnionPayMerchantConfigProvider(merchantId, PAYWAY_WEIXIN, provider, agentName, subAppid, subAppsecret, null);
    }

    @Override
    public void updateWeixinAllMerchantConfigProvider(String merchantId, Integer provider, String agentName, String subAppid, String subAppsecret, String miniSubAppid, String miniSubAppsecret) {
        updateWftOrNuccOrUnionPayMerchantConfigProvider(merchantId, PAYWAY_WEIXIN, provider, agentName, subAppid, subAppsecret, miniSubAppid);
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN);
        if (merchantConfig == null) {
            initMerchantConfig(merchantId, PAYWAY_WEIXIN);
            merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_WEIXIN);
        }
        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PROVIDER, provider
        );
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> merchantConfigParams;
        String subTradeParamsKey = getTradeParamsKeyByProvider(provider);
        if (!StringUtil.empty(subTradeParamsKey)) {
            paramsChangeFunc = (merchantConfigParams) -> {
                Map<String, String> subTradeParams = (Map<String, String>) merchantConfigParams.get(subTradeParamsKey);
                if (subTradeParams == null) {
                    subTradeParams = new HashMap<>();
                    merchantConfigParams.put(subTradeParamsKey, subTradeParams);
                }
                subTradeParams.put(TransactionParam.WEIXIN_MINI_SUB_APP_ID, miniSubAppid);
                subTradeParams.put(TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, miniSubAppsecret);
                return merchantConfigParams;
            };
        }
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(updateInfo, DaoConstants.ID), updateInfo, paramsChangeFunc);
    }


    private void updateWftOrNuccOrUnionPayMerchantConfigProvider(String merchantId, Integer payway, Integer provider, String agentName, String subAppid, String subAppsecret, String miniSubAppid) {
        Map<String, Object> merchant = businssCommonService.getMerchantMinimalInfoById(merchantId);
        if (merchant == null) {
            return;
        }
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (payway != null && provider != null && StringUtil.empty(agentName)) {
            throw new CoreInvalidParameterException("当payway不为null, provider不为null时，必须设置agentName值");
        }
        if (merchantConfig == null) {
            initMerchantConfig(merchantId, payway);
            merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        }
        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PROVIDER, provider
        );
        if (payway == null) {
            updateInfo.put(MerchantConfig.B2C_AGENT_NAME, agentName);
            updateInfo.put(MerchantConfig.C2B_AGENT_NAME, agentName);
            updateInfo.put(MerchantConfig.WAP_AGENT_NAME, agentName);
            updateInfo.put(MerchantConfig.MINI_AGENT_NAME, agentName);

        } else {
            //如果正式，不替换对应的agentName
            for (String subPaywayColumn : subPaywayAgentNameColName.keySet()) {
                String agentNameColumn = subPaywayAgentNameColName.get(subPaywayColumn);
                String formalColumn = subPaywayFormalColName.get(subPaywayColumn);
                boolean formal = BeanUtil.getPropBoolean(merchantConfig, formalColumn, false);
                if (!formal) {
                    updateInfo.put(agentNameColumn, agentName);
                }
            }
        }
        //清空设置到大商户下面的子商户号配置
        Map<String, String> providerTradeParamsKey = metaProviderBiz.getTradeParamKeys();
        Map<String, Object> paramsUpdate = new HashMap<String, Object>();
        if (payway != null) {
            if (!StringUtil.empty(agentName) && provider != null) {
                String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
                Map<String, Object> tradeParams = (Map) BeanUtil.getNestedProperty(getAgentByName(agentName), String.format("%s.%s", Agent.PARAMS, getTradeParamsKeyByProvider(provider)));
                if (tradeParams != null) {
                    String tradeParamKey = providerTradeParamsKey.get(String.valueOf(provider));
                    Map<String, Object> merchantTradeParams = null;
                    if (provider == PROVIDER_NUCC) {
                        merchantTradeParams = getNuccMerchantTradeParamsFromMerchantContractService(merchantSn, payway, tradeParams, subAppid, subAppsecret, miniSubAppid);
                    } else if (provider == PROVIDER_UNIONPAY) {
                        merchantTradeParams = getUnionPayMerchantTradeParamsFromMerchantContractService(merchantSn, payway, tradeParams, subAppid, subAppsecret, miniSubAppid);
                    } else {
                        merchantTradeParams = getWftMerchantTradeParamsFromMerchantContractService(merchantId, merchantSn, provider, payway, tradeParams, subAppid, subAppsecret);
                    }
                    if (merchantTradeParams != null) {
                        paramsUpdate.put(tradeParamKey, merchantTradeParams);
                    }
                }
            }
        }
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            if (providerTradeParamsKey != null) {
                for (String providerKey : providerTradeParamsKey.keySet()) {
                    //lakala 比较特殊，它还负责结算
                    if (!providerKey.equals(PROVIDER_LAKALA + "")) {
                        merchantConfigParams.remove(providerTradeParamsKey.get(providerKey));
                    }
                }
            }
            merchantConfigParams.putAll(paramsUpdate);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(updateInfo, paramsChangeFunc);
    }


    private Map<String, Object> getWftMerchantTradeParamsFromMerchantContractService(String merchantId, String merchantSn, Integer provider, Integer payway, Map<String, Object> tradeParams, String subAppid, String subAppsecret) {
        // 如果设置了受理商，此受理商配置了大商户号或者渠道号，如果此商户已经入网到了此大商户下面或者渠道下面。 则设置入网的商户信息到params里面
        String groupNo = null;
        String agentNo = null;
        String agentDefaultMchId = null;
        String agentWeixinSubAppid = null;
        String mchIdKey = null;
        for (String key : tradeParams.keySet()) {
            if (key.endsWith(TransactionParam.SWIFTPASS_GROUP_NO_SUFFIX)) {
                groupNo = BeanUtil.getPropString(tradeParams, key);
            } else if (key.endsWith(TransactionParam.SWIFTPASS_AGENT_NO_SUFFIX)) {
                agentNo = BeanUtil.getPropString(tradeParams, key);
            } else if (key.endsWith(TransactionParam.SWIFTPASS_MCH_ID_SUFFIX)) {
                mchIdKey = key;
                agentDefaultMchId = BeanUtil.getPropString(tradeParams, key);
            } else if (key.equals(TransactionParam.WEIXIN_SUB_APP_ID)) {
                agentWeixinSubAppid = BeanUtil.getPropString(tradeParams, key);
            }
        }
        if (agentNo != null || groupNo != null) {
            String mchId = null;
            if (payway == PAYWAY_WEIXIN && StringUtil.empty(agentWeixinSubAppid) && StringUtil.empty(agentDefaultMchId) && !StringUtil.empty(subAppid) && !StringUtil.empty(subAppsecret)) {
                //同一个商户有可能在同一个渠道报备了多次，区别在于报备的公众号不一样
                mchId = providerTradeParamsService.getProviderMerchantIdWithWeixinAppId(merchantSn, agentNo, subAppid);
                if (!StringUtil.empty(mchId)) {
                    return CollectionUtil.hashMap(
                            mchIdKey, mchId,
                            TransactionParam.WEIXIN_SUB_APP_ID, subAppid,
                            TransactionParam.WEIXIN_SUB_APP_SECRET, subAppsecret
                    );

                }
            }

            if (StringUtil.empty(mchId)) {
                mchId = providerTradeParamsService.getProviderMerchantId(merchantSn, agentNo, groupNo);
                //为了容错，如果从入网服务里面获取不到商户号，那么从数据库中查询一遍，兼容入网服务不迁移老数据的情况
                if (StringUtil.empty(mchId) && !StringUtil.empty(groupNo)) {
                    Map merchantTradeParams = getTradeParamsInMerchantEnrolledInfo(merchantId, provider, groupNo);
                    if (merchantTradeParams != null && !merchantTradeParams.isEmpty()) {
                        mchId = (String) BeanUtil.getNestedProperty(merchantTradeParams, providerTradeParamsKey.get(String.valueOf(provider)) + "." + mchIdKey);
                    }
                }
                //如果agent里面的子商户号为空，那么必须要配置商户入网后的商户号
                if (StringUtil.empty(agentDefaultMchId) && StringUtil.empty(mchId)) {
                    throw new InvalidParameterException("子商户号不能为空");
                }
                //兼容只报备支付宝渠道--商户--门店模式，微信使用默认商户号交易。只有在拥有该渠道下银行商户号才在payway = 3的params中写入银行商户号
                if (!StringUtil.empty(mchId)) {
                    return CollectionUtil.hashMap(
                            mchIdKey, mchId
                    );
                }
            }

        }
        return null;
    }

    private Map<String, Object> getNuccMerchantTradeParamsFromMerchantContractService(String merchantSn, Integer payway, Map<String, Object> tradeParams, String subAppid, String subAppsecret, String miniSubAppid) {
        if (payway == PAYWAY_ALIPAY2) {
            //todo 支付宝参数获取待定
            String alipayPid = BeanUtil.getPropString(tradeParams, TransactionParam.NUCC_ALIPAY_PID);
            Map<String, Object> providerTradeParams = providerTradeParamsService.getProviderTradeParamsWithWeixinAppid(merchantSn, alipayPid, null);
            String alipayMchId = BeanUtil.getPropString(providerTradeParams, MerchantProviderTradeParams.ALIPAY_MERCHANT_ID);
            if (alipayMchId != null) {
                return CollectionUtil.hashMap(
                        TransactionParam.NUCC_ALIPAY_SUB_MCH_ID, alipayMchId
                );
            }
        } else if (payway == PAYWAY_WEIXIN) {
            String channelId = BeanUtil.getPropString(tradeParams, TransactionParam.NUCC_CHANNEL_ID);
            String mchId = BeanUtil.getPropString(tradeParams, TransactionParam.NUCC_WEIXIN_MCH_ID);
            Map<String, Object> providerTradeParams = providerTradeParamsService.getProviderTradeParamsByParams(merchantSn, channelId, mchId, subAppid, miniSubAppid, null);
            String subMchId = BeanUtil.getPropString(providerTradeParams, MerchantProviderTradeParams.WEIXIN_MERCHANT_ID);
            if (subMchId != null) {
                return CollectionUtil.hashMap(
                        TransactionParam.NUCC_WEIXIN_SUB_MCH_ID, subMchId,
                        TransactionParam.NUCC_WEIXIN_SUB_APP_ID, subAppid,
                        TransactionParam.NUCC_WEIXIN_SUB_APP_SECRET, subAppsecret
                );
            }
        }
        return null;
    }

    private Map<String, Object> getUnionPayMerchantTradeParamsFromMerchantContractService(String merchantSn, Integer payway, Map<String, Object> tradeParams, String subAppid, String subAppsecret, String miniSubAppid) {
        if (payway == PAYWAY_ALIPAY2) {
            //todo 支付宝参数获取待定
            String alipayAppid = BeanUtil.getPropString(tradeParams, TransactionParam.UNION_PAY_ALIPAY_APP_ID);
            Map<String, Object> providerTradeParams = providerTradeParamsService.getProviderTradeParamsWithWeixinAppid(merchantSn, alipayAppid, null);
            String alipayMchId = BeanUtil.getPropString(providerTradeParams, MerchantProviderTradeParams.ALIPAY_MERCHANT_ID);
            if (alipayMchId != null) {
                return CollectionUtil.hashMap(
                        TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID, alipayMchId
                );
            }
        } else if (payway == PAYWAY_WEIXIN) {
            String channelId = BeanUtil.getPropString(tradeParams, TransactionParam.UNION_PAY_CHANNEL_ID);
            String mchId = BeanUtil.getPropString(tradeParams, TransactionParam.UNION_PAY_WEIXIN_MCH_ID);
            Map<String, Object> providerTradeParams = providerTradeParamsService.getProviderTradeParamsByParams(merchantSn, channelId, mchId, subAppid, miniSubAppid, null);
            String subMchId = BeanUtil.getPropString(providerTradeParams, MerchantProviderTradeParams.WEIXIN_MERCHANT_ID);
            if (subMchId != null) {
                return CollectionUtil.hashMap(
                        TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, subMchId,
                        TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID, subAppid,
                        TransactionParam.UNION_PAY_WEIXIN_SUB_APP_SECRET, subAppsecret
                );
            }
        }
        return null;
    }

    /**
     * 根据大商户号获取注册入网信息
     *
     * @param merchantId
     * @param groupNo
     * @return
     */
    private Map<String, Object> getTradeParamsInMerchantEnrolledInfo(String merchantId, Integer provider, String groupNo) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map params = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        String key = String.format("%s.%s.%s", TransactionParam.PROVIDER_ENROLLED_INFO, provider + "", groupNo);
        return (Map<String, Object>) BeanUtil.getNestedProperty(params, key);

    }

    @Override
    @SuppressWarnings("unchecked")
    public void updateSolicitorConfigProvider(String solicitorId, Integer payway, Integer provider) {
        Map solicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
        if (solicitorConfig == null) {
            initSolicitorConfig(solicitorId, payway);
            solicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
        }
        this.solicitorConfigDao.updatePart(CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(solicitorConfig, DaoConstants.ID),
                SolicitorConfig.PROVIDER, provider
        ));

    }

    @Override
    @SuppressWarnings("unchecked")
    public void updateSolicitorConfigProvider(String solicitorId, Integer payway, Integer provider, String agentName) {
        Map solicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
        if (solicitorConfig == null) {
            initSolicitorConfig(solicitorId, payway);
            solicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
        }
        this.solicitorConfigDao.updatePart(CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(solicitorConfig, DaoConstants.ID),
                SolicitorConfig.PROVIDER, provider,
                SolicitorConfig.B2C_AGENT_NAME, agentName,
                SolicitorConfig.C2B_AGENT_NAME, agentName,
                SolicitorConfig.WAP_AGENT_NAME, agentName,
                SolicitorConfig.MINI_AGENT_NAME, agentName
        ));
    }


    @Override
    @SuppressWarnings("unchecked")
    public void addProviderEnrolledInfo(String merchantId, Integer provider, String groupNo, Map tradeParams) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) {
            initMerchantConfig(merchantId, null);
            merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        }
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID);
        String key = String.format("%s.%s.%s", TransactionParam.PROVIDER_ENROLLED_INFO, provider + "", groupNo);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            BeanUtil.setNestedProperty(merchantConfigParams, key, tradeParams);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
    }

    @Override
    public List getAnalyzedMerchantConfigs(String merchantId) {
        return getAnalyzedMerchantConfigsByPayWayList(merchantId, DEFAULT_PAYWAYS);
    }

    private List getAnalyzedMerchantConfigsByPayWayListAndSubpayway(String merchantId, int[] payWayList, Integer subpayway, String tradeApp) {
        List merchantConfigs = new ArrayList();
        Map defaultMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map defaultParams = (Map) BeanUtil.getProperty(defaultMerchantConfig, MerchantConfig.PARAMS);
        for (int payway : payWayList) {
            Map merchantConfig = getMerchantConfigTemplate();
            Map currentMerchantConfig = getMerchantConfigByMerchantIdAndPaywayAndTradeApp(merchantId, payway, tradeApp);
            if (currentMerchantConfig == null && !isBasicPay(tradeApp)) {
                continue;
            }
            Map currentParams = (Map) BeanUtil.getProperty(currentMerchantConfig, MerchantConfig.PARAMS);
            if (currentParams == null) {
                currentParams = new HashMap();
            }
            for (String col : subpaywayCloNames.get(subpayway)) {
                replaceTradeParamValueByKey(col, merchantConfig, defaultMerchantConfig);
                replaceTradeParamValueByKey(col, merchantConfig, currentMerchantConfig);
            }
            //设置agentName, 如果是正式，则不获取payway为null的agentName,如果不是正式，那么获取payway为null的agentName
            for (String col : specialCols.get(subpayway).keySet()) {
                String formalCol = specialCols.get(subpayway).get(col);
                boolean formal = BeanUtil.getPropBoolean(currentMerchantConfig, formalCol);
                if (!formal) {
                    replaceTradeParamValueByKey(col, merchantConfig, defaultMerchantConfig);
                }
                replaceTradeParamValueByKey(col, merchantConfig, currentMerchantConfig);

            }
            merchantConfig.put(MerchantConfig.PAYWAY, payway);
            merchantConfig.put(MerchantConfig.MERCHANT_ID, merchantId);

            //设置银行交易参数
            for (String tradeParamsKey : tradeParamsTemplates.keySet()) {
                Map tradeParams = getMergedTradeParams(tradeParamsKey, defaultParams, currentParams);
                if (tradeParams != null) {
                    currentParams.put(tradeParamsKey, tradeParams);
                }
            }

            //设置其他威富通对接的银行的交易参数
            List<Map<String, Object>> metaProviders = metaProviderBiz.getAllMetaProviders();
            if (metaProviders != null) {
                for (Map<String, Object> metaProvider : metaProviders) {
                    int provider = com.wosai.pantheon.util.MapUtil.getIntValue(metaProvider, DaoConstants.ID);
                    if (ApolloConfigurationCenterUtil.getWftProviders().contains(provider)) {
                        String tradeKey = com.wosai.pantheon.util.MapUtil.getString(metaProvider, MetaProvider.TRADE_PARAMS_KEY);
                        Map swiftParams = getMergedSwiftPassTradeParams(defaultParams, currentParams, tradeKey);
                        if (swiftParams != null) {
                            currentParams.put(tradeKey, swiftParams);
                        }
                    }
                }
            }
            //添加交易限额等参数
            if (defaultParams != null) {
                for (String key : tradeValidateParamKeys) {
                    if (defaultParams.containsKey(key)) {
                        currentParams.put(key, defaultParams.get(key));
                    }
                }
                if (!currentParams.containsKey(TransactionParam.ALLOW_CREDIT_PAY)) {
                    currentParams.put(TransactionParam.ALLOW_CREDIT_PAY, defaultParams.get(TransactionParam.ALLOW_CREDIT_PAY));
                }
                if (!currentParams.containsKey(TransactionParam.USE_CLIENT_STORE_SN)) {
                    currentParams.put(TransactionParam.USE_CLIENT_STORE_SN, defaultParams.get(TransactionParam.USE_CLIENT_STORE_SN));
                }
                    //存在默认参数时，不存在IS_SENT_STORE_ID则赋予默认值true
                currentParams.put(TransactionParam.IS_SENT_STORE_ID
                        , defaultParams.getOrDefault(TransactionParam.IS_SENT_STORE_ID, true));
                currentParams.put(TransactionParam.CLEARANCE_PROVIDER, defaultParams.get(TransactionParam.CLEARANCE_PROVIDER));
                currentParams.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, defaultParams.get(TransactionParam.IS_NEED_REFUND_FEE_FLAG));
            }
            //默认设置由wosai清算的时候走拉卡拉进行清算
            if (StringUtil.empty(BeanUtil.getPropString(currentParams, TransactionParam.CLEARANCE_PROVIDER))) {
                currentParams.put(TransactionParam.CLEARANCE_PROVIDER, CLEARANCE_PROVIDER_LKL);
            }
            String feeType = com.wosai.pantheon.util.MapUtil.getString(currentParams, MerchantConfig.FEE_RATE_TYPE);
            if (!StringUtil.empty(feeType)) {
                // 新版本费率配置
                if (MerchantConfig.FEE_RATE_TYPE_LADDER.equals(feeType)) {
                    merchantConfig.put(TransactionParam.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.LADDER_FEE_RATES));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL.equals(feeType)) {
                    merchantConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER.equals(feeType)) {
                    merchantConfig.put(TransactionParam.CHANNEL_LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.CHANNEL_LADDER_FEE_RATES));
                }
                merchantConfig.put(MerchantConfig.FEE_RATE_TYPE, feeType);
            } else {
                // 老版本费率配置
                // 添加分级费率配置，未配置ladder_status的交易参数，返回不允许开通，当前payway对应的交易参数未配置时，取payway=null的这条配置
                if (!StringUtil.empty(BeanUtil.getPropString(currentParams, MerchantConfig.LADDER_STATUS))) {
                    merchantConfig.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(currentParams, MerchantConfig.LADDER_STATUS));
                    merchantConfig.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, MerchantConfig.LADDER_FEE_RATES));
                    merchantConfig.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subpayway, currentParams));

                } else if (!StringUtil.empty(BeanUtil.getPropString(defaultMerchantConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)))) {
                    merchantConfig.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(defaultMerchantConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)));
                    merchantConfig.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getNestedProperty(defaultMerchantConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_FEE_RATES)));
                    merchantConfig.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subpayway, (Map) BeanUtil.getProperty(defaultMerchantConfig, MerchantConfig.PARAMS)));
                } else {
                    merchantConfig.put(MerchantConfig.LADDER_STATUS, MerchantConfig.LADDER_STATUS_NOT_ALLOW);
                }
                if (payway == Payway.BANKCARD.getCode()) {
                    // 添加资金渠道费率相关配置，未配置bankcard_fee的交易参数，返回不允许开通，当前payway对应的交易参数未配置时，取payway=null的这条配置
                    if (isNotEmptyChannelFeeRate(currentParams, null)) {
                        merchantConfig.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(currentParams, null));
                        merchantConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                        merchantConfig.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subpayway, currentParams));
                    } else if (isNotEmptyChannelFeeRate(defaultMerchantConfig, MerchantConfig.PARAMS)) {
                        merchantConfig.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(defaultMerchantConfig, MerchantConfig.PARAMS));
                        merchantConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getNestedProperty(defaultMerchantConfig, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.PARAMS_BANKCARD_FEE)));
                        merchantConfig.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subpayway, (Map) BeanUtil.getProperty(defaultMerchantConfig, MerchantConfig.PARAMS)));
                    } else {
                        merchantConfig.put(MerchantConfig.CHANNEL_STATUS, MerchantConfig.LADDER_STATUS_NOT_ALLOW);
                    }
                }
            }
            if (payway == PAYWAY_ALIPAY2) {
                int huabeiStatus = BeanUtil.getPropInt(currentParams, TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_CLOSED);
                int huabeiLimit = BeanUtil.getPropInt(currentParams, TransactionParam.ALIPAY_HUABEI_LIMIT, ApolloConfigurationCenterUtil.getHuabeiDefaultMinLimit());
                int fitnessStatus = BeanUtil.getPropInt(currentParams, TransactionParam.FITNESS_STATUS, MerchantConfig.STATUS_CLOSED);
                merchantConfig.put(TransactionParam.ALIPAY_HUABEI_STATUS, huabeiStatus);
                merchantConfig.put(TransactionParam.ALIPAY_HUABEI_LIMIT, huabeiLimit);
                merchantConfig.put(TransactionParam.FITNESS_STATUS, fitnessStatus);
            }

            if (payway == PAYWAY_WEIXIN) {
                if (MapUtils.isNotEmpty(defaultParams) && defaultParams.containsKey(TransactionParam.WEIXIN_INSTALLMENT_STATUS)) {
                    merchantConfig.put(TransactionParam.WEIXIN_INSTALLMENT_STATUS, defaultParams.get(TransactionParam.WEIXIN_INSTALLMENT_STATUS));
                }
            }
            if (payway == PAYWAY_JD){
                int status = BeanUtil.getPropInt(defaultParams, TransactionParam.JD_BAITIAO_STATUS, ApolloConfigurationCenterUtil.getBaitiaoDefaultStatus());
                int limit = BeanUtil.getPropInt(defaultParams, TransactionParam.JD_BAITIAO_LIMIT, ApolloConfigurationCenterUtil.getBaitiaoDefaultLimit());
                merchantConfig.put(TransactionParam.JD_BAITIAO_STATUS, status);
                merchantConfig.put(TransactionParam.JD_BAITIAO_LIMIT, limit);
            }

            Map feeRateTag = (Map) BeanUtil.getProperty(currentParams, TransactionParam.FEE_RATE_TAG);
            if (MapUtils.isNotEmpty(feeRateTag)) {
                merchantConfig.put(TransactionParam.FEE_RATE_TAG, buildFeeRateTag(subpayway, currentParams));
            }

            merchantConfig.put(MerchantConfig.PARAMS, currentParams);
            merchantConfigs.add(merchantConfig);
        }
        return merchantConfigs;
    }

    private boolean isNotEmptyChannelFeeRate(Map params, String level) {
        level = StringUtil.empty(level) ? "" : level + ".";
        return Objects.nonNull(BeanUtil.getNestedProperty(params, level + TransactionParam.PARAMS_BANKCARD_FEE))
                || !StringUtil.empty(BeanUtil.getPropString(params, level + TransactionParam.CHANNEL_STATUS));
    }

    private int getChannelStatus(Map params, String level) {
        level = StringUtil.empty(level) ? "" : level + ".";
        Integer status = MapUtils.getInteger(params, level + MerchantConfig.CHANNEL_STATUS);
        if (Objects.nonNull(status)) {
            return status;
        }
        //兼容处理
        return Objects.nonNull(BeanUtil.getNestedProperty(params, level + TransactionParam.PARAMS_BANKCARD_FEE))
                ? MerchantConfig.STATUS_OPENED : MerchantConfig.STATUS_CLOSED;
    }

    public List getAnalyzedStoreConfigsByPayWayList(String storeId, int[] payWayList, Integer subpayway) {
        List storeConfigs = new ArrayList();
        Map defaultStoreConfig = getStoreConfigByStoreIdAndPayway(storeId, null);
        defaultStoreConfig = (null == defaultStoreConfig) ? new HashMap() : defaultStoreConfig;
        Map defaultParams = (Map) BeanUtil.getProperty(defaultStoreConfig, MerchantConfig.PARAMS);
        for (int payway : payWayList) {
            Map storeConfig = getStoreConfigTemplate();
            Map currentStoreConfig = getStoreConfigByStoreIdAndPayway(storeId, payway);
            Map currentParams = (Map) BeanUtil.getProperty(currentStoreConfig, MerchantConfig.PARAMS);
            if (currentParams == null) {
                currentParams = new HashMap();
            }
            for (String col : subpaywayCloNames.get(subpayway)) {
                replaceTradeParamValueByKey(col, storeConfig, defaultStoreConfig);
                replaceTradeParamValueByKey(col, storeConfig, currentStoreConfig);
            }
            //设置agentName, 如果是正式，则不获取payway为null的agentName,如果不是正式，那么获取payway为null的agentName
            for (String col : specialCols.get(subpayway).keySet()) {
                String formalCol = specialCols.get(subpayway).get(col);
                boolean formal = BeanUtil.getPropBoolean(currentStoreConfig, formalCol);
                if (!formal) {
                    replaceTradeParamValueByKey(col, storeConfig, defaultStoreConfig);
                }
                replaceTradeParamValueByKey(col, storeConfig, currentStoreConfig);

            }

            //设置银行交易参数
            for (String tradeParamsKey : tradeParamsTemplates.keySet()) {
                Map tradeParams = getMergedTradeParams(tradeParamsKey, defaultParams, currentParams);
                if (tradeParams != null) {
                    currentParams.put(tradeParamsKey, tradeParams);
                }
            }

            //设置其他威富通对接的银行的交易参数
            List<Map<String, Object>> metaProviders = metaProviderBiz.getAllMetaProviders();
            if (metaProviders != null) {
                for (Map<String, Object> metaProvider : metaProviders) {
                    int provider = com.wosai.pantheon.util.MapUtil.getIntValue(metaProvider, DaoConstants.ID);
                    if (ApolloConfigurationCenterUtil.getWftProviders().contains(provider)) {
                        String tradeKey = com.wosai.pantheon.util.MapUtil.getString(metaProvider, MetaProvider.TRADE_PARAMS_KEY);
                        Map swiftParams = getMergedSwiftPassTradeParams(defaultParams, currentParams, tradeKey);
                        if (swiftParams != null) {
                            currentParams.put(tradeKey, swiftParams);
                        }
                    }
                }
            }
            //添加交易限额等参数
            if (defaultParams != null) {
                for (String key : tradeValidateParamKeys) {
                    if (defaultParams.containsKey(key)) {
                        currentParams.put(key, defaultParams.get(key));
                    }
                }
                if (!currentParams.containsKey(TransactionParam.ALLOW_CREDIT_PAY)) {
                    currentParams.put(TransactionParam.ALLOW_CREDIT_PAY, defaultParams.get(TransactionParam.ALLOW_CREDIT_PAY));
                }
                if (!currentParams.containsKey(TransactionParam.USE_CLIENT_STORE_SN)) {
                    currentParams.put(TransactionParam.USE_CLIENT_STORE_SN, defaultParams.get(TransactionParam.USE_CLIENT_STORE_SN));
                }
                if (!currentParams.containsKey(TransactionParam.IS_SENT_STORE_ID)) {
                    currentParams.put(TransactionParam.IS_SENT_STORE_ID, defaultParams.get(TransactionParam.IS_SENT_STORE_ID));
                }
                if (!currentParams.containsKey(TransactionParam.CLEARANCE_PROVIDER)) {
                    currentParams.put(TransactionParam.CLEARANCE_PROVIDER, defaultParams.get(TransactionParam.CLEARANCE_PROVIDER));
                }
                if (!currentParams.containsKey(TransactionParam.IS_NEED_REFUND_FEE_FLAG)) {
                    currentParams.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, defaultParams.get(TransactionParam.IS_NEED_REFUND_FEE_FLAG));
                }
            }
            String feeType = com.wosai.pantheon.util.MapUtil.getString(currentParams, MerchantConfig.FEE_RATE_TYPE);
            if (!StringUtil.empty(feeType)) {
                // 新版本费率配置
                if (MerchantConfig.FEE_RATE_TYPE_LADDER.equals(feeType)) {
                    storeConfig.put(TransactionParam.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.LADDER_FEE_RATES));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL.equals(feeType)) {
                    storeConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER.equals(feeType)) {
                    storeConfig.put(TransactionParam.CHANNEL_LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.CHANNEL_LADDER_FEE_RATES));
                }
                storeConfig.put(MerchantConfig.FEE_RATE_TYPE, feeType);
            } else {
                // 老版本费率配置
                // 添加分级费率配置，未配置ladder_status的交易参数，返回不允许开通，当前payway对应的交易参数未配置时，取payway=null的这条配置
                if (!StringUtil.empty(BeanUtil.getPropString(currentParams, MerchantConfig.LADDER_STATUS))) {
                    storeConfig.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(currentParams, MerchantConfig.LADDER_STATUS));
                    storeConfig.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, MerchantConfig.LADDER_FEE_RATES));
                    storeConfig.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subpayway, currentParams));
                } else if (!StringUtil.empty(BeanUtil.getPropString(defaultStoreConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)))) {
                    storeConfig.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(defaultStoreConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)));
                    storeConfig.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getNestedProperty(defaultStoreConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_FEE_RATES)));
                    storeConfig.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subpayway, (Map) BeanUtil.getProperty(defaultStoreConfig, MerchantConfig.PARAMS)));
                }
                if (payway == Payway.BANKCARD.getCode()) {
                    // 添加资金渠道费率相关配置，未配置bankcard_fee的交易参数，返回不允许开通，当前payway对应的交易参数未配置时，取payway=null的这条配置
                    if (!StringUtil.empty(BeanUtil.getPropString(currentParams, MerchantConfig.CHANNEL_STATUS))) {
                        storeConfig.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(currentParams, null));
                        storeConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                        storeConfig.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subpayway, currentParams));
                    } else if (!StringUtil.empty(BeanUtil.getPropString(defaultStoreConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.CHANNEL_STATUS)))) {
                        storeConfig.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(defaultStoreConfig, MerchantConfig.PARAMS));
                        storeConfig.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getNestedProperty(storeConfig, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.PARAMS_BANKCARD_FEE)));
                        storeConfig.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subpayway, (Map) BeanUtil.getProperty(defaultStoreConfig, MerchantConfig.PARAMS)));
                    }
                }
            }
            if (payway == PAYWAY_ALIPAY2) {
                if(!StringUtil.empty(BeanUtil.getPropString(currentParams, TransactionParam.ALIPAY_HUABEI_STATUS))) {
                    int huabeiStatus = BeanUtil.getPropInt(currentParams, TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_CLOSED);
                    int huabeiLimit = BeanUtil.getPropInt(currentParams, TransactionParam.ALIPAY_HUABEI_LIMIT, ApolloConfigurationCenterUtil.getHuabeiDefaultMinLimit());
                    storeConfig.put(TransactionParam.ALIPAY_HUABEI_STATUS, huabeiStatus);
                    storeConfig.put(TransactionParam.ALIPAY_HUABEI_LIMIT, huabeiLimit);
                }
                if(!StringUtil.empty(BeanUtil.getPropString(currentParams, TransactionParam.FITNESS_STATUS))) {
                    int fitnessStatus = BeanUtil.getPropInt(currentParams, TransactionParam.FITNESS_STATUS, MerchantConfig.STATUS_CLOSED);
                    storeConfig.put(TransactionParam.FITNESS_STATUS, fitnessStatus);
                }
                if(currentParams.containsKey(TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT)) {
                    storeConfig.put(TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT, currentParams.get(TransactionParam.ALIPAY_HUABEI_SELLER_DISCOUNT));
                }
            }

            Map feeRateTag = (Map) BeanUtil.getProperty(currentParams, TransactionParam.FEE_RATE_TAG);
            if (MapUtils.isNotEmpty(feeRateTag) || !StringUtils.isEmpty(getFeeRate(currentStoreConfig, subpayway))) {
                storeConfig.put(TransactionParam.FEE_RATE_TAG, buildFeeRateTag(subpayway, currentParams));
            }

            storeConfig.put(DaoConstants.ID, BeanUtil.getPropString(currentStoreConfig, DaoConstants.ID));
            storeConfig.put(MerchantConfig.PARAMS, currentParams);
            storeConfigs.add(storeConfig);
        }
        return storeConfigs;
    }

    public List getAnalyzedTerminalConfigsByPayWayListAndSubpayway(String terminalId, int[] payWayList
            , Integer subpayway) {
        List terminalConfigs = new ArrayList();
        Map defaultTerminalConfig = getTerminalConfigByTerminalIdAndPayway(terminalId, null);
        defaultTerminalConfig = (null == defaultTerminalConfig) ? new HashMap() : defaultTerminalConfig;
        Map defaultParams = (Map) BeanUtil.getProperty(defaultTerminalConfig, TerminalConfig.PARAMS);
        for (int payway : payWayList) {
            Map terminalConfigTemplate = getTerminalConfigTemplate();
            Map currentTerminalConfig = getTerminalConfigByTerminalIdAndPayway(terminalId, payway);
            Map currentParams = (Map) BeanUtil.getProperty(currentTerminalConfig, TerminalConfig.PARAMS);
            if (currentParams == null) {
                currentParams = new HashMap();
            }
            for (String col : subpaywayCloNames.get(subpayway)) {
                replaceTradeParamValueByKey(col, terminalConfigTemplate, defaultTerminalConfig);
                replaceTradeParamValueByKey(col, terminalConfigTemplate, currentTerminalConfig);
            }
            //设置agentName, 如果是正式，则不获取payway为null的agentName,如果不是正式，那么获取payway为null的agentName
            for (String col : specialCols.get(subpayway).keySet()) {
                String formalCol = specialCols.get(subpayway).get(col);
                boolean formal = BeanUtil.getPropBoolean(currentTerminalConfig, formalCol);
                if (!formal) {
                    replaceTradeParamValueByKey(col, terminalConfigTemplate, defaultTerminalConfig);
                }
                replaceTradeParamValueByKey(col, terminalConfigTemplate, currentTerminalConfig);

            }
            terminalConfigTemplate.put(TerminalConfig.PAYWAY, payway);
            terminalConfigTemplate.put(TerminalConfig.TERMINAL_ID, terminalId);

            //设置银行交易参数
            for (String tradeParamsKey : tradeParamsTemplates.keySet()) {
                Map tradeParams = getMergedTradeParams(tradeParamsKey, defaultParams, currentParams);
                if (tradeParams != null) {
                    currentParams.put(tradeParamsKey, tradeParams);
                }
            }

            //设置其他威富通对接的银行的交易参数
            List<Map<String, Object>> metaProviders = metaProviderBiz.getAllMetaProviders();
            if (metaProviders != null) {
                for (Map<String, Object> metaProvider : metaProviders) {
                    int provider = com.wosai.pantheon.util.MapUtil.getIntValue(metaProvider, DaoConstants.ID);
                    if (ApolloConfigurationCenterUtil.getWftProviders().contains(provider)) {
                        String tradeKey = com.wosai.pantheon.util.MapUtil.getString(metaProvider, MetaProvider.TRADE_PARAMS_KEY);
                        Map swiftParams = getMergedSwiftPassTradeParams(defaultParams, currentParams, tradeKey);
                        if (swiftParams != null) {
                            currentParams.put(tradeKey, swiftParams);
                        }
                    }
                }
            }
            //添加交易限额等参数
            if (defaultParams != null) {
                for (String key : tradeValidateParamKeys) {
                    if (defaultParams.containsKey(key)) {
                        currentParams.put(key, defaultParams.get(key));
                    }
                }
                if (!currentParams.containsKey(TransactionParam.ALLOW_CREDIT_PAY)) {
                    currentParams.put(TransactionParam.ALLOW_CREDIT_PAY
                            , defaultParams.get(TransactionParam.ALLOW_CREDIT_PAY));
                }
                if (!currentParams.containsKey(TransactionParam.USE_CLIENT_STORE_SN)) {
                    currentParams.put(TransactionParam.USE_CLIENT_STORE_SN
                            , defaultParams.get(TransactionParam.USE_CLIENT_STORE_SN));
                }
                if (!currentParams.containsKey(TransactionParam.IS_SENT_STORE_ID)) {
                    currentParams.put(TransactionParam.IS_SENT_STORE_ID
                            , defaultParams.get(TransactionParam.IS_SENT_STORE_ID));
                }
                if (!currentParams.containsKey(TransactionParam.IS_SENT_STORE_ID)) {
                    currentParams.put(TransactionParam.CLEARANCE_PROVIDER, defaultParams.get(TransactionParam.CLEARANCE_PROVIDER));
                }
                if (!currentParams.containsKey(TransactionParam.IS_SENT_STORE_ID)) {
                    currentParams.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, defaultParams.get(TransactionParam.IS_NEED_REFUND_FEE_FLAG));
                }
            }
            String feeType = com.wosai.pantheon.util.MapUtil.getString(currentParams, MerchantConfig.FEE_RATE_TYPE);
            if (!StringUtil.empty(feeType)) {
                // 新版本费率配置
                if (MerchantConfig.FEE_RATE_TYPE_LADDER.equals(feeType)) {
                    terminalConfigTemplate.put(TransactionParam.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.LADDER_FEE_RATES));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL.equals(feeType)) {
                    terminalConfigTemplate.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                } else if (MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER.equals(feeType)) {
                    terminalConfigTemplate.put(TransactionParam.CHANNEL_LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, TransactionParam.CHANNEL_LADDER_FEE_RATES));
                }
                terminalConfigTemplate.put(MerchantConfig.FEE_RATE_TYPE, feeType);
            } else {
                // 老版本费率配置
                // 添加分级费率配置，未配置ladder_status的交易参数，返回不允许开通，当前payway对应的交易参数未配置时，取payway=null的这条配置
                if (!StringUtil.empty(BeanUtil.getPropString(currentParams, MerchantConfig.LADDER_STATUS))) {
                    terminalConfigTemplate.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(currentParams, MerchantConfig.LADDER_STATUS));
                    terminalConfigTemplate.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getProperty(currentParams, MerchantConfig.LADDER_FEE_RATES));

                    terminalConfigTemplate.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subpayway, currentParams));
                } else if (!StringUtil.empty(BeanUtil.getPropString(defaultTerminalConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)))) {
                    terminalConfigTemplate.put(MerchantConfig.LADDER_STATUS, BeanUtil.getPropInt(defaultTerminalConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_STATUS)));
                    terminalConfigTemplate.put(MerchantConfig.LADDER_FEE_RATES, BeanUtil.getNestedProperty(defaultTerminalConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.LADDER_FEE_RATES)));
                    terminalConfigTemplate.put(MerchantConfig.LADDER_FEE_RATE_TAG, buildLadderFeeRateTag(subpayway, (Map) BeanUtil.getProperty(defaultTerminalConfig, MerchantConfig.PARAMS)));
                }
                if (payway == Payway.BANKCARD.getCode()) {
                    // 添加资金渠道费率相关配置，未配置bankcard_fee的交易参数，返回不允许开通，当前payway对应的交易参数未配置时，取payway=null的这条配置
                    if (!StringUtil.empty(BeanUtil.getPropString(currentParams, MerchantConfig.CHANNEL_STATUS))) {
                        terminalConfigTemplate.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(currentParams, null));
                        terminalConfigTemplate.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getProperty(currentParams, TransactionParam.PARAMS_BANKCARD_FEE));
                        terminalConfigTemplate.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subpayway, currentParams));
                    } else if (!StringUtil.empty(BeanUtil.getPropString(defaultTerminalConfig, String.format("%s.%s", MerchantConfig.PARAMS, MerchantConfig.CHANNEL_STATUS)))) {
                        terminalConfigTemplate.put(MerchantConfig.CHANNEL_STATUS, getChannelStatus(defaultTerminalConfig, MerchantConfig.PARAMS));
                        terminalConfigTemplate.put(TransactionParam.PARAMS_BANKCARD_FEE, BeanUtil.getNestedProperty(defaultTerminalConfig, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.PARAMS_BANKCARD_FEE)));
                        terminalConfigTemplate.put(MerchantConfig.CHANNEL_FEE_RATE_TAG, buildChannelFeeRateTag(subpayway, (Map) BeanUtil.getProperty(defaultTerminalConfig, MerchantConfig.PARAMS)));
                    }
                }
            }
            if (payway == PAYWAY_ALIPAY2 && !StringUtil.empty(BeanUtil.getPropString(currentParams, TransactionParam.ALIPAY_HUABEI_STATUS))) {
                int huabeiStatus = BeanUtil.getPropInt(currentParams, TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_CLOSED);
                int huabeiLimit = BeanUtil.getPropInt(currentParams, TransactionParam.ALIPAY_HUABEI_LIMIT, ApolloConfigurationCenterUtil.getHuabeiDefaultMinLimit());
                terminalConfigTemplate.put(TransactionParam.ALIPAY_HUABEI_STATUS, huabeiStatus);
                terminalConfigTemplate.put(TransactionParam.ALIPAY_HUABEI_LIMIT, huabeiLimit);
            }
            if (payway == PAYWAY_ALIPAY2 && !StringUtil.empty(BeanUtil.getPropString(currentParams, TransactionParam.FITNESS_STATUS))) {
                int fitnessStatus = BeanUtil.getPropInt(currentParams, TransactionParam.FITNESS_STATUS, MerchantConfig.STATUS_CLOSED);
                terminalConfigTemplate.put(TransactionParam.ALIPAY_HUABEI_STATUS, fitnessStatus);
            }

            Map feeRateTag = (Map) BeanUtil.getProperty(currentParams, TransactionParam.FEE_RATE_TAG);
            if (MapUtils.isNotEmpty(feeRateTag) || !StringUtils.isEmpty(getFeeRate(currentTerminalConfig, subpayway))) {
                terminalConfigTemplate.put(TransactionParam.FEE_RATE_TAG, buildFeeRateTag(subpayway, currentParams));
            }

            terminalConfigTemplate.put(DaoConstants.ID, BeanUtil.getPropString(currentTerminalConfig
                    , DaoConstants.ID));
            terminalConfigTemplate.put(TerminalConfig.PARAMS, currentParams);
            terminalConfigs.add(terminalConfigTemplate);
        }

        return terminalConfigs;
    }

    @Override
    public List getAnalyzedMerchantConfigsByPayWayList(String merchantId, int[] payWayList) {
        return getAnalyzedMerchantConfigsByPayWayListAndSubpayway(merchantId, payWayList, null, TransactionParam.TRADE_APP_BASIC_PAY);
    }

    /**
     * 获取商户解析的配置列表
     * 针对　getAnalyzedMerchantConfigsByPayWayList 查询条件扩展延伸
     *
     * @param request 入参
     * @return
     * @see TradeConfigService#getAnalyzedMerchantConfigsByPayWayList(String, int[])
     */
    @Override
    public List getAnalyzedMerchantConfigList(GetAnalyzedMerchantAppConfigRequest request) {
        List<String> appIdList = request.getAppIdList();
        //未传入多业务ID时，获取商户当前所有多业务ID
        if (CollectionUtils.isEmpty(appIdList)) {
            List list = getMerchantAppConfigsByMerchantId(request.getMerchantId());
            appIdList = (List<String>) list.stream().map(l->BeanUtil.getPropString(l, MerchantAppConfig.APP_ID)
            ).distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(appIdList)) {
            logger.info("getAnalyzedMerchantConfigList 当前商户未配置多业务. merchantId:{}", request.getMerchantId());
            return Collections.emptyList();
        }
        List result = Lists.newArrayList();
        for (String appId : appIdList) {
            List list = getAnalyzedMerchantConfigsByPayWayListAndSubpayway(request.getMerchantId(), request.payWayListToArray(), request.getSubPayWay(), appId);
            result.addAll(list);
        }
        return result;
    }

    private Map getMergedTradeParams(String tradeParamsKey, Map defaultParams, Map currentPaywayParams) {
        Map<String, Object> templateTradeParams = (Map<String, Object>) tradeParamsTemplates.get(tradeParamsKey).get("template");
        if (MapUtils.isEmpty(templateTradeParams)) {
            return null;
        }
        Map defaultTradeParams = (Map) BeanUtil.getProperty(defaultParams, tradeParamsKey);
        Map currentTradeParams = (Map) BeanUtil.getProperty(currentPaywayParams, tradeParamsKey);
        if (null == defaultTradeParams && null == currentTradeParams) {
            return null;
        }
        Map mergeConfig = new HashMap(templateTradeParams);
        if (defaultTradeParams != null) {
            for (String key : templateTradeParams.keySet()) {
                replaceTradeParamValueByKey(key, mergeConfig, defaultTradeParams);
            }
        }
        if (currentTradeParams != null) {
            for (String key : templateTradeParams.keySet()) {
                replaceTradeParamValueByKey(key, mergeConfig, currentTradeParams);
            }
        }
        if (checkTradeParamNotEmpty(mergeConfig, tradeParamsKey)) {
            return mergeConfig;
        } else {
            return null;
        }
    }

    /**
     * 检查交易参数中的必填参数是否存在值（不允许空字符串）
     *
     * @param tradeParam
     * @param tradeParamsKey
     * @return
     */
    private boolean checkTradeParamNotEmpty(Map tradeParam, String tradeParamsKey) {
        Map<String, Object> template = tradeParamsTemplates.get(tradeParamsKey);
        if (template == null) {
            return true;
        }
        Set<String> notEmptys = (Set<String>) template.get("notEmpty");
        boolean check = (null == notEmptys || notEmptys.size() == 0) ? true : false;
        if (null != notEmptys && notEmptys.size() > 0) {
            for (String key : notEmptys) {
                if (!StringUtil.empty(BeanUtil.getPropString(tradeParam, key))) {
                    check = true;
                    break;
                }
            }
        }
        return check;
    }

    /**
     * 获取威富通的交易参数
     *
     * @param defaultParams
     * @param currentPaywayParams
     * @return
     */
    private Map getMergedSwiftPassTradeParams(Map defaultParams, Map currentPaywayParams, String tradeKey) {
        Map defaultTradeParams = (Map) BeanUtil.getProperty(defaultParams, tradeKey);
        Map currentTradeParams = (Map) BeanUtil.getProperty(currentPaywayParams, tradeKey);
        if (null == defaultParams && null == currentTradeParams) {
            return null;
        }
        Map<String, Object> templateTradeParams = getSwiftPassTradeParamsTemplate();
        if (defaultTradeParams != null) {
            for (String key : templateTradeParams.keySet()) {
                replaceTradeParamValueByKey(key, templateTradeParams, defaultTradeParams);
            }
        }
        if (currentTradeParams != null) {
            for (String key : templateTradeParams.keySet()) {
                replaceTradeParamValueByKey(key, templateTradeParams, currentTradeParams);
            }
        }
        if (templateTradeParams.get(TransactionParam.SWIFTPASS_MCH_ID) == null) {
            return null;
        } else {
            return templateTradeParams;
        }
    }

    @Override
    public Map getSolicitorTradeValidateParams(String solicitorId) {
        Map solicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, null);
        Map solicitorParams = (Map) BeanUtil.getProperty(solicitorConfig, SolicitorConfig.PARAMS);
        if (solicitorParams == null) {
            solicitorParams = new HashMap();
        }
        Map validateParams = new HashMap();
        for (String key : tradeValidateParamKeys) {
            validateParams.put(key, solicitorParams.get(key) != null ? solicitorParams.get(key) : solicitorParams.get(key));
        }
        return validateParams;
    }

    @Override
    public void updateSolicitorTradeValidateParams(String solicitorId, Map validateParams) {
        Map solicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, null);
        if (solicitorConfig == null) {
            solicitorConfig = initSolicitorConfig(solicitorId, null);
        }
        Map solicitorParams = (Map) BeanUtil.getProperty(solicitorConfig, MerchantConfig.PARAMS);
        if (solicitorParams == null) {
            solicitorParams = new HashMap();
            solicitorConfig.put(MerchantConfig.PARAMS, solicitorParams);
        }

        for (String key : tradeValidateParamKeys) {
            if (validateParams.containsKey(key)) {
                Object value = BeanUtil.getProperty(validateParams, key);
                //如果值为""字符串或者空map,则设置为null
                if (value instanceof String && StringUtils.isEmpty((String) value)) {
                    value = null;
                } else if (value instanceof Map && ((Map) value).isEmpty()) {
                    value = null;
                }
                solicitorParams.put(key, value);
            }
        }
        solicitorConfigDao.updatePart(solicitorConfig);
    }

    @Override
    public List getAnalyzedSolicitorConfigs(String solicitorId) {
        List solicitorConfigs = new ArrayList();
        Map defaultSolicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, null);
        for (int payway : new int[]{PAYWAY_ALIPAY, PAYWAY_ALIPAY2, PAYWAY_WEIXIN, PAYWAY_BAIFUBAO, PAYWAY_JD, PAYWAY_QQWALLET, PAYWAY_APPLEPAY, PAYWAY_LAKALAWALLET, PAYWAY_LKL_UNIONPAY, PAYWAY_BESTPAY, PAYWAY_WEIXIN_HK}) {
            Map solicitorTemplate = getSolicitorConfigTemplate(solicitorId);
            Map currentSolicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
            Map currentParams = (Map) BeanUtil.getProperty(currentSolicitorConfig, SolicitorConfig.PARAMS);
            String cols[] = {
                    SolicitorConfig.B2C_FEE_RATE, SolicitorConfig.B2C_FORMAL, SolicitorConfig.B2C_STATUS,
                    SolicitorConfig.C2B_FEE_RATE, SolicitorConfig.C2B_FORMAL, SolicitorConfig.C2B_STATUS,
                    SolicitorConfig.WAP_FEE_RATE, SolicitorConfig.WAP_FORMAL, SolicitorConfig.WAP_STATUS,
                    SolicitorConfig.MINI_FEE_RATE, SolicitorConfig.MINI_FORMAL, SolicitorConfig.MINI_STATUS
            };
            for (String col : cols) {
                replaceTradeParamValueByKey(col, solicitorTemplate, defaultSolicitorConfig);
                replaceTradeParamValueByKey(col, solicitorTemplate, currentSolicitorConfig);
            }
            solicitorTemplate.put(MerchantConfig.PARAMS, currentParams);
            solicitorTemplate.put(SolicitorConfig.PAYWAY, payway);
            solicitorConfigs.add(solicitorTemplate);
        }
        return solicitorConfigs;
    }

    @Override
    public void updateSolicitorConfigStatusAndFeeRate(String solicitorId, Map solicitorConfig) {
        if (solicitorService.getSolicitor(solicitorId) == null) {
            throw new CoreSolicitorNotExistsException("推广者不存在");
        }
        checkSolicitorConfig(solicitorConfig);
        solicitorConfig.remove(SolicitorConfig.PARAMS);
        int payway = BeanUtil.getPropInt(solicitorConfig, SolicitorConfig.PAYWAY);
        Map existsSolicitorConfig = getSolicitorConfigsBySolicitorIdAndPayway(solicitorId, payway);
        if (existsSolicitorConfig != null) {
            solicitorConfig.put(DaoConstants.ID, BeanUtil.getPropString(existsSolicitorConfig, DaoConstants.ID));
            this.solicitorConfigDao.updatePart(solicitorConfig);
        } else {
            solicitorConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
            solicitorConfig.put(SolicitorConfig.SOLICITOR_ID, solicitorId);
            solicitorConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
            this.solicitorConfigDao.save(solicitorConfig);
        }
    }


    @Override
    public String getAlipayV2AppAuthToken(String authAppId) {
        String key = REDIS_KEY_ALIPAY_APP_AUTH_TOKEN_PREFIX + authAppId;
        String appAuthToken = redisTemplate.boundValueOps(key).get();
        if (StringUtil.empty(appAuthToken)) {
            try {
                return alipayStoreService.getAuthTokenByAppId(authAppId);
            } catch (ExceptionBase exceptionBase) {
                throw new CoreIOException("获取appAuthToken失败: " + exceptionBase.getMessage(), exceptionBase);
            }
        } else {
            return appAuthToken;
        }
    }

    @Override
    public Map getAlipayV2AppAuthInfo(String authAppId, String storeId) {
        Map<String, String> info = new HashMap();
        try {
            if(!FakeRequestUtil.isFakeRequest()) {
                Map result = alipayStoreService.getAuthTokenAndShopId(authAppId, storeId);
                info.put(TransactionParam.APP_AUTH_TOKEN, BeanUtil.getPropString(result, "appAuthToken"));
                info.put(TransactionParam.APP_AUTH_USER_ID, BeanUtil.getPropString(result, "appAuthUserId"));
                info.put(TransactionParam.APP_AUTH_STORE_ID, BeanUtil.getPropString(result, "storeId"));
                info.put(TransactionParam.APP_AUTH_SHOP_ID, BeanUtil.getPropString(result, "shopId"));
            }else {
                // 全链路压测时，alipay-authinto服务抗不住fake请求，mock返回
                info.put(TransactionParam.APP_AUTH_TOKEN, "app_auth_token");
                info.put(TransactionParam.APP_AUTH_USER_ID, "app_auth_user_id");
                info.put(TransactionParam.APP_AUTH_STORE_ID, "app_auth_store_id");
                info.put(TransactionParam.APP_AUTH_SHOP_ID, "app_auth_shop_id");
            }
            return info;
        } catch (Exception ex) {
            throw new CoreIOException("获取AuthTokenAndShopId失败: " + ex.getMessage(), ex);
        }
    }

    /**
     * 获取终端类型
     *
     * @param terminalId
     * @return
     */
    private String getTerminalType(String terminalId) {
        Map<String, Object> terminal = dataRepository.getTerminalDao().get(terminalId);
        String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        String terminalType = TERMINAL_APP_ID_TYPE.get(vendorAppAppid);
        if (terminalType == null || terminalType.length() == 0) {
            return null;
        }
        return terminalType;
    }

    /**
     * 获取merchantConfig默认值
     *
     * @return
     */
    private Map getMerchantConfigTemplate() {
        Map merchantConfig = CollectionUtil.hashMap(
                MerchantConfig.PAYWAY, null,
                MerchantConfig.MERCHANT_ID, null,
                MerchantConfig.B2C_FEE_RATE, "0.6",
                MerchantConfig.B2C_FORMAL, false,
                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                MerchantConfig.B2C_AGENT_NAME, null,
                MerchantConfig.C2B_FEE_RATE, "0.6",
                MerchantConfig.C2B_FORMAL, false,
                MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                MerchantConfig.C2B_AGENT_NAME, null,
                MerchantConfig.WAP_FEE_RATE, "0.6",
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                MerchantConfig.WAP_AGENT_NAME, null,
                MerchantConfig.MINI_FEE_RATE, "0.6",
                MerchantConfig.MINI_FORMAL, false,
                MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                MerchantConfig.MINI_AGENT_NAME, null,
                MerchantConfig.H5_FORMAL, false,
                MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                MerchantConfig.H5_AGENT_NAME, null,
                MerchantConfig.PROVIDER, null,
                MerchantConfig.PARAMS, new HashMap<>()
        );
        return merchantConfig;
    }

    /**
     * 获取merchantConfig默认值
     *
     * @return
     */
    private Map getStoreConfigTemplate() {
        //注意不要修改下面字段的默认值
        Map storeConfig = CollectionUtil.hashMap(
                StoreConfig.PAYWAY, null,
                StoreConfig.STORE_ID, null,
                StoreConfig.B2C_FEE_RATE, null,
                StoreConfig.B2C_FORMAL, null,
                StoreConfig.B2C_STATUS, null,
                StoreConfig.B2C_AGENT_NAME, null,
                StoreConfig.C2B_FEE_RATE, null,
                StoreConfig.C2B_FORMAL, null,
                StoreConfig.C2B_STATUS, null,
                StoreConfig.C2B_AGENT_NAME, null,
                StoreConfig.WAP_FEE_RATE, null,
                StoreConfig.WAP_FORMAL, null,
                StoreConfig.WAP_STATUS, null,
                StoreConfig.WAP_AGENT_NAME, null,
                StoreConfig.MINI_FEE_RATE, null,
                StoreConfig.MINI_FORMAL, null,
                StoreConfig.MINI_STATUS, null,
                StoreConfig.MINI_AGENT_NAME, null,
                StoreConfig.H5_FORMAL, null,
                StoreConfig.H5_STATUS, null,
                StoreConfig.H5_AGENT_NAME, null,
                StoreConfig.PROVIDER, null,
                StoreConfig.PARAMS, new HashMap<>()
        );
        return storeConfig;
    }

    /**
     * 获取terminalConfig默认值
     *
     * @return
     */
    private Map getTerminalConfigTemplate() {
        //注意不要修改下面字段的默认值
        Map terminalConfig = CollectionUtil.hashMap(
                TerminalConfig.TERMINAL_ID, null,
                TerminalConfig.PAYWAY, null,
                TerminalConfig.B2C_FEE_RATE, null,
                TerminalConfig.B2C_FORMAL, null,
                TerminalConfig.B2C_STATUS, null,
                TerminalConfig.B2C_AGENT_NAME, null,
                TerminalConfig.C2B_FEE_RATE, null,
                TerminalConfig.C2B_FORMAL, null,
                TerminalConfig.C2B_STATUS, null,
                TerminalConfig.C2B_AGENT_NAME, null,
                TerminalConfig.WAP_FEE_RATE, null,
                TerminalConfig.WAP_FORMAL, null,
                TerminalConfig.WAP_STATUS, null,
                TerminalConfig.WAP_AGENT_NAME, null,
                TerminalConfig.MINI_FEE_RATE, null,
                TerminalConfig.MINI_FORMAL, null,
                TerminalConfig.MINI_STATUS, null,
                TerminalConfig.MINI_AGENT_NAME, null,
                TerminalConfig.H5_FORMAL, null,
                TerminalConfig.H5_STATUS, null,
                TerminalConfig.H5_AGENT_NAME, null,
                TerminalConfig.PROVIDER, null,
                TerminalConfig.PARAMS, new HashMap<>()
        );

        return terminalConfig;
    }

    /**
     * 获取默认的solicitorConfig参数结构
     *
     * @param solicitorId
     * @return
     */
    private Map getSolicitorConfigTemplate(String solicitorId) {
        Map solicitorConfig = CollectionUtil.hashMap(
                SolicitorConfig.SOLICITOR_ID, solicitorId,
                SolicitorConfig.B2C_FEE_RATE, null,
                SolicitorConfig.B2C_FORMAL, false,
                SolicitorConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                SolicitorConfig.B2C_AGENT_NAME, null,
                SolicitorConfig.C2B_FEE_RATE, null,
                SolicitorConfig.C2B_FORMAL, false,
                SolicitorConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                SolicitorConfig.C2B_AGENT_NAME, null,
                SolicitorConfig.WAP_FEE_RATE, null,
                SolicitorConfig.WAP_FORMAL, false,
                SolicitorConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                SolicitorConfig.WAP_AGENT_NAME, null,
                SolicitorConfig.MINI_FEE_RATE, null,
                SolicitorConfig.MINI_FORMAL, false,
                SolicitorConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                SolicitorConfig.MINI_AGENT_NAME, null,
                SolicitorConfig.PROVIDER, null,
                SolicitorConfig.PARAMS, new HashMap<>()
        );
        return solicitorConfig;
    }

    private Map getSwiftPassTradeParamsTemplate() {
        return CollectionUtil.hashMap(
                TransactionParam.SWIFTPASS_MCH_ID, null,
                TransactionParam.SWIFTPASS_MCH_KEY, null,
                TransactionParam.SWIFTPASS_PRIVATE_KEY, null,
                TransactionParam.SWIFTPASS_PUBLIC_KEY, null,
                TransactionParam.SWIFTPASS_WEIXIN_SUB_APP_ID, null,
                TransactionParam.SWIFTPASS_WEIXIN_SUB_APP_SECRET, null,
                TransactionParam.SWIFTPASS_WEIXIN_MINI_SUB_APP_ID, null,
                TransactionParam.SWIFTPASS_WEIXIN_MINI_SUB_APP_SECRET, null
        );
    }

    /**
     * 获取交易限额参数模板
     *
     * @return
     */
    private Map getTradeValidateParamsTemplate() {
        return CollectionUtil.hashMap(
                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, "2500",
                TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS, "2500",
                TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, null,
                TransactionParam.ALLOW_CREDIT_PAY, TransactionParam.CREDIT_PAY_ENABLE,
                TransactionParam.USE_CLIENT_STORE_SN, TransactionParam.USE_CLIENT_STORE_SN_NO
        );
    }


    /**
     * 对异常配置做处理
     *
     * @param params
     */
    private void formatParams(Map params) {
        if (params == null) {
            return;
        }
        //如果费率为空，那么置为0.0
        String keys[] = {
                TransactionParam.ALIPAY_V1_TRADE_PARAMS, TransactionParam.ALIPAY_WAP_TRADE_PARAMS,
                TransactionParam.ALIPAY_V2_TRADE_PARAMS,
                TransactionParam.WEIXIN_TRADE_PARAMS, TransactionParam.WEIXIN_WAP_TRADE_PARAMS,
                TransactionParam.JD_TRADE_PARAMS, TransactionParam.JD_WAP_TRADE_PARAMS,
                TransactionParam.BAIFUBAO_TRADE_PARAMS, TransactionParam.QQ_TRADE_PARAMS,
                TransactionParam.NFC_TRADE_PARAMS, TransactionParam.CIBBANK_TRADE_PARAMS,
                TransactionParam.CMCC_TRADE_PARAMS, TransactionParam.NUCC_TRADE_PARAMS,
                TransactionParam.UNION_PAY_TRADE_PARAMS, TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS

        };
        for (String key : keys) {
            Map map = (Map) params.get(key);
            if (map != null) {
                String feeRate = BeanUtil.getPropString(map, TransactionParam.FEE_RATE, "0.0");
                if (feeRate.trim().isEmpty()) {
                    map.put(TransactionParam.FEE_RATE, "0.0");
                } else {
                    map.put(TransactionParam.FEE_RATE, feeRate.trim());
                }
            }
        }

        //add other
    }

    /**
     * @param tradeParam
     * @param context
     */
    private void specialDealWeixinTradeParam(Map tradeParam, Map context, Integer provider, int payway, int subPayway) {
        //custom goods_tag
        String goodsTag = getMerchantConfigCustomValue(context, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG, subPayway);
        if (StringUtil.empty(goodsTag)) {
            String city = BeanUtil.getPropString(context, TransactionParam.STORE_CITY, "");
            goodsTag = getWeixinGoodsTagByCity(city, tradeParam);
        }
        if (StringUtil.empty(goodsTag)) {
            return;
        }
        Map param = (Map) tradeParam.get(paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway));
        if (param != null) {
            param.put(TransactionParam.GOODS_TAG, goodsTag);
        }
        if (provider != null) {
            Map providerParam = (Map) tradeParam.get(getTradeParamsKeyByProvider(provider));
            if (providerParam != null) {
                providerParam.put(TransactionParam.GOODS_TAG, goodsTag);
            }
        }

    }

    /**
     * @param tradeParam
     * @param context
     */
    @Deprecated
    private void specialDealJDTradeParams(Map tradeParam, Map context, int payway, int subPayway) {
        Map param = (Map) tradeParam.get(paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway));
        if (param == null) {
            return;
        }
        Map<String, Object> customTradeParams = getMerchantConfigCustomTradeParams(context, MerchantConfigCustom.TYPE_JD_MERCHANT_NO, subPayway);
        Map<String, Object> customParams = (Map<String, Object>) BeanUtil.getProperty(customTradeParams, paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway));
        if (customParams == null) {
            return;
        }
        for (String key : customParams.keySet()) {
            if (!TransactionParam.FEE_RATE.equals(key)) {
                replaceTradeParamValueByKey(key, param, customParams);
            }
        }
    }

    /**
     * 设置app_auth_token, seller_id
     *
     * @param tradeParam
     * @param context
     * @param provider
     * @param payway
     * @param subPayway
     */
    private void specialDealAlipayV2TradeParams(Map tradeParam, Map context, Integer provider,  int payway, int subPayway) {
        Map param = (Map) tradeParam.get(paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway));
        if (param == null) {
            //间连
            if (provider != null) {
                Map providerParam = (Map) tradeParam.get(getTradeParamsKeyByProvider(provider));
                if (providerParam != null) {
                    // set edu school id
                    String eduSchoolId = getMerchantConfigCustomValue(context, MerchantConfigCustom.TYPE_ALIPAY_EDU_SCHOOL_ID, subPayway);
                    if (!StringUtils.isEmpty(eduSchoolId)) {
                        providerParam.put(TransactionParam.ALIPAY_EDU_SCHOOL_ID, eduSchoolId);
                    }
                }
            }
        } else {
            //直连
            //set token
            String authAppId = BeanUtil.getPropString(param, TransactionParam.ALIPAY_AUTH_APP_ID);
            if (!StringUtil.empty(authAppId)) {
                String storeId = BeanUtil.getPropString(context, ConstantUtil.KEY_STORE_ID);
                Map info = getAlipayV2AppAuthInfo(authAppId, storeId);
                String appAuthToken = BeanUtil.getPropString(info, TransactionParam.APP_AUTH_TOKEN);
                String authUserId = BeanUtil.getPropString(info, TransactionParam.APP_AUTH_USER_ID);
                String authStoreId = BeanUtil.getPropString(info, TransactionParam.APP_AUTH_STORE_ID);
                String authShopId = BeanUtil.getPropString(info, TransactionParam.APP_AUTH_SHOP_ID);
                if (StringUtil.empty(appAuthToken)) {
                    throw new CoreUnknownException("支付宝授权token获取失败");
                } else {
                    param.put(TransactionParam.APP_AUTH_TOKEN, appAuthToken);
                    param.put(TransactionParam.APP_AUTH_USER_ID, authUserId);
                    param.put(TransactionParam.APP_AUTH_STORE_ID, authStoreId);
                    param.put(TransactionParam.APP_AUTH_SHOP_ID, authShopId);
                }
            }
            Map<Integer, String> typeCustomValues = getMerchantConfigCustomValueByTypes(context,
                    Arrays.asList(MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                            MerchantConfigCustom.TYPE_ALIPAY_EDU_SCHOOL_ID), subPayway);
            //set seller_id (收款账户集功能)
            String sellerId = typeCustomValues.get(MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID);
            if (!StringUtil.empty(sellerId)) {
                param.put(TransactionParam.ALIPAY_SELLER_ID, sellerId);
            }
            //set alipay_store_id
            String alipayStoreId = typeCustomValues.get(MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
            if (!StringUtils.isEmpty(alipayStoreId)) {
                param.put(TransactionParam.ALIPAY_STORE_ID, alipayStoreId);
            }
            // set edu school id
            String eduSchoolId = typeCustomValues.get(MerchantConfigCustom.TYPE_ALIPAY_EDU_SCHOOL_ID);
            if (!StringUtils.isEmpty(eduSchoolId)) {
                param.put(TransactionParam.ALIPAY_EDU_SCHOOL_ID, eduSchoolId);
            }
        }
    }

    /**
     * 支付宝交易获取小程序交易参数时，用的是门店码的参数，需要把相关配置的sub_payway改写为小程序
     * @param tradeParam
     * @param subPayway
     * @param originSubPayway
     */
    private void specialDealFeeRateTag(Map tradeParam, int subPayway, int originSubPayway){
        if (!(originSubPayway == SUB_PAYWAY_MINI && subPayway == SUB_PAYWAY_WAP)) {
            return;
        }
        Map feeRateTag = com.wosai.pantheon.util.MapUtil.getMap(tradeParam, TransactionParam.FEE_RATE_TAG);
        if(feeRateTag != null){
            Object value = feeRateTag.get(subPayway + "");
            if(value != null){
                feeRateTag.put(originSubPayway + "", value);
                feeRateTag.remove(subPayway + "");
            }
        }
        Map ladderFeeRateTag = com.wosai.pantheon.util.MapUtil.getMap(tradeParam, TransactionParam.LADDER_FEE_RATE_TAG);
        if(ladderFeeRateTag != null){
            Object value = ladderFeeRateTag.get(subPayway + "");
            if(value != null){
                ladderFeeRateTag.put(originSubPayway + "", value);
                ladderFeeRateTag.remove(subPayway + "");
            }
        }
    }

    /**
     * 设置兴业银行支付通道活动参数
     *
     * @param tradeParam
     * @param context
     * @param payway
     * @param subPayway
     */
    private void specialDealCIBBankTradeParams(Map tradeParam, Map context, int payway, int subPayway) {
        Map param = (Map) tradeParam.get(getTradeParamsKeyByProvider(PROVIDER_CIBBANK));
        if (param == null) {
            return;
        } else if (payway == PAYWAY_WEIXIN || payway == PAYWAY_ALIPAY || payway == PAYWAY_ALIPAY2) {
            int type = (payway == PAYWAY_WEIXIN ? MerchantConfigCustom.TYPE_CIBBANK_MCH_ID_WEIXIN : MerchantConfigCustom.TYPE_CIBBANK_MCH_ID_ALIPAY);
            Map<String, Object> customTradeParams = getMerchantConfigCustomTradeParams(context, type, subPayway);
            Map<String, Object> customParams = (Map<String, Object>) BeanUtil.getProperty(customTradeParams, getTradeParamsKeyByProvider(PROVIDER_CIBBANK));
            if (customParams == null) {
                return;
            }
            for (String key : customParams.keySet()) {
                if (!TransactionParam.FEE_RATE.equals(key)) {
                    replaceTradeParamValueByKey(key, param, customParams);
                }
            }
        }

    }

    /**
     * 设置中信银行支付通道活动参数
     *
     * @param tradeParam
     * @param context
     * @param payway
     * @param subPayway
     */
    private void specialDealCITICBankTradeParams(Map tradeParam, Map context, int payway, int subPayway) {
        Map param = (Map) tradeParam.get(getTradeParamsKeyByProvider(PROVIDER_CITICBANK));
        if (param == null) {
            return;
        } else if (payway == PAYWAY_WEIXIN || payway == PAYWAY_ALIPAY || payway == PAYWAY_ALIPAY2) {
            int type = (payway == PAYWAY_WEIXIN ? MerchantConfigCustom.TYPE_CITICBANK_MCH_ID_WEIXIN : MerchantConfigCustom.TYPE_CITICBANK_MCH_ID_ALIPAY);
            Map<String, Object> customTradeParams = getMerchantConfigCustomTradeParams(context, type, subPayway);
            Map<String, Object> customParams = (Map<String, Object>) BeanUtil.getProperty(customTradeParams, getTradeParamsKeyByProvider(PROVIDER_CITICBANK));
            if (customParams == null) {
                return;
            }
            for (String key : customParams.keySet()) {
                if (!TransactionParam.FEE_RATE.equals(key)) {
                    replaceTradeParamValueByKey(key, param, customParams);
                }
            }
        }

    }

    private void specialDealZJTLCBTradeParams(Map<String, Object> defaultTradeParam, Map context) {

        Map tradeParams = (Map) defaultTradeParam.get(getTradeParamsKeyByProvider(PROVIDER_ZJTLCB));
        if(Objects.isNull(tradeParams)) {
            return;
        }
        String clientSn =   com.wosai.pantheon.util.MapUtil.getString(context, TransactionParam.STORE_CLIENT_SN);
        //优先取
        if(!StringUtil.empty(clientSn)) {
            tradeParams.put(TransactionParam.PROVIDER_MCH_ID, clientSn);
        }
    }

    /**
     * 添加江苏银行扩展交易信息
     *
     * @param context
     */
    private void addJSBExtraParams(Map context) {
        String terminalSn = MapUtils.getString(context, TransactionParam.TERMINAL_SN);
        Map<String, Object> terminalInfo = businssCommonService.getTerminalMinimalInfoBySn(terminalSn);
        //江苏银行交易时要上送设备指纹
        context.put(TransactionParam.TERMINAL_DEVICE_FINGERPRINT, MapUtils.getString(terminalInfo, Terminal.DEVICE_FINGERPRINT));
    }


    private void specialDealLakalaBankcardParams(Map tradeParam, Map mergerConfig, String tradeAppId) {
        if(tradeParam != null) {
            if (BeanUtil.getPropInt(mergerConfig, MerchantConfig.CHANNEL_STATUS) != MerchantConfig.STATUS_OPENED
                    && !Objects.equals(BeanUtil.getPropString(mergerConfig, MerchantConfig.FEE_RATE_TYPE), MerchantConfig.FEE_RATE_TYPE_CHANNEL)) {
                throw new CoreMerchantConfigAbnormalException("资金渠道费率未打开");
            }
            String mercId = BeanUtil.getPropString(tradeParam, TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID);
            String termId = BeanUtil.getPropString(tradeParam, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID);
            String termNo = BeanUtil.getPropString(tradeParam, TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO);
            if (Objects.equals(ApolloConfigurationCenterUtil.getPhonePosTradeApp(), tradeAppId)) {
                if (StringUtils.isEmpty(mercId) || StringUtils.isEmpty(termNo)) {
                    throw new CoreMerchantConfigAbnormalException("手机外卡交易参数配置异常");
                }
            } else {
                if (StringUtils.isEmpty(mercId) || StringUtils.isEmpty(termId) || StringUtils.isEmpty(termNo)) {
                    throw new CoreMerchantConfigAbnormalException("银行卡交易参数配置异常");
                }
            }
        }
    }

    /**
     * 先获取门店的，如果门店没有配置，再获取商户的配置
     *
     * @param context
     * @param type
     * @param subPayway
     * @return
     */
    private Map<String, Object> getMerchantConfigCustomTradeParams(Map context, int type, int subPayway) {
        String merchantId = BeanUtil.getPropString(context, TransactionParam.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(context, TransactionParam.STORE_ID);
        Map storeCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, storeId, type);
        String agentName = BeanUtil.getPropString(storeCustom, subPaywayAgentNameColName.get(subPayway + ""));
        if (StringUtil.empty(agentName)) {
            Map merchantCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null, type);
            agentName = BeanUtil.getPropString(merchantCustom, subPaywayAgentNameColName.get(subPayway + ""));
        }
        if (StringUtil.empty(agentName)) {
            return null;
        } else {
            Map<String, Object> agent = getAgentByName(agentName);
            return (Map<String, Object>) BeanUtil.getProperty(agent, Agent.PARAMS);
        }
    }


    /**
     * 先获取门店的，如果门店没有配置，再获取商户的配置
     *
     * @param context
     * @param type
     * @param subPayway
     * @return
     */
    private String getMerchantConfigCustomValue(Map context, int type, int subPayway) {
        String merchantId = BeanUtil.getPropString(context, TransactionParam.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(context, TransactionParam.STORE_ID);
        Map storeCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, storeId, type);
        String value = BeanUtil.getPropString(storeCustom, subPaywaysValueColName.get(subPayway + ""));
        if (StringUtil.empty(value)) {
            Map merchantCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null, type);
            value = BeanUtil.getPropString(merchantCustom, subPaywaysValueColName.get(subPayway + ""));
        }
        return value;
    }

    /**
     * 先获取门店的，如果门店没有配置，再获取商户的配置
     *
     * @param context
     * @param types
     * @param subPayway
     * @return
     */
    private Map<Integer,String> getMerchantConfigCustomValueByTypes(Map context, List<Integer> types, int subPayway) {
        Map<Integer,String> map = new LinkedHashMap<>();
        String merchantId = BeanUtil.getPropString(context, TransactionParam.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(context, TransactionParam.STORE_ID);
        List<Map<String,Object>> allCustom = getMerchantConfigCustomsByMerchantIdAndStoreIdAndTypes(merchantId, storeId, types);
        Map<String, Map<String, Object>> typeStoreIdMap = allCustom.stream().collect(Collectors.toMap(r -> BeanUtil.getPropString(r, MerchantConfigCustom.TYPE) + BeanUtil.getPropString(r, MerchantConfigCustom.STORE_ID, ""), Function.identity()));
        for (Integer type : types) {
            Map storeCustom = typeStoreIdMap.get(type + "" + storeId);
            String value = BeanUtil.getPropString(storeCustom, subPaywaysValueColName.get(subPayway + ""));
            if (StringUtil.empty(value)) {
                Map merchantCustom = typeStoreIdMap.get(type + "");
                value = BeanUtil.getPropString(merchantCustom, subPaywaysValueColName.get(subPayway + ""));
            }
            map.put(type, value);
        }
        return map;
    }

    private List<Map<String,Object>> getMerchantConfigCustomsByMerchantIdAndStoreIdAndTypes(String merchantId, String storeId, List<Integer> types) {
        if (Objects.isNull(merchantId)) {
            throw new CoreInvalidParameterException("商户id不能为空");
        }
        Criteria criteria = Criteria.where(MerchantConfigCustom.MERCHANT_ID).is(merchantId);
        criteria.withOr(Criteria.where(MerchantConfigCustom.STORE_ID).is(null), Criteria.where(MerchantConfigCustom.STORE_ID).is(storeId));
        criteria.with(MerchantConfigCustom.TYPE).in(types);
        return CollectionUtil.iterator2list(merchantConfigCustomDao.filter(criteria).fetchAll());
    }


    /**
     * 如果商户或者门店的参数值不为null 则替换掉默认参数
     *
     * @param key
     */
    private void replaceTradeParamValueByKey(String key, Map defaultTradeParam, Map tradeParam) {
        Object value = defaultTradeParam.get(key);
        if (value instanceof Map) {
            for (Object nestkey : ((Map) value).keySet()) {
                replaceTradeParamValueByKey(key + "." + nestkey.toString(), defaultTradeParam, tradeParam);
            }
        } else {
            Object replaceValue = BeanUtil.getNestedProperty(tradeParam, key);
            if (replaceValue != null) {
                BeanUtil.setNestedProperty(defaultTradeParam, key, replaceValue);
            }
        }
    }

    /**
     * 根据城市获取微信的子商户号
     *
     * @param city
     * @param tradeParam
     * @return
     */
    private String getWeixinSubMchIdForTrailStoreByCity(String city, Map tradeParam) {
        if (StringUtil.empty(city)) {
            return null;
        }
        Map citySubMchId = (Map) BeanUtil.getProperty(tradeParam, TransactionParam.WEXIN_CITY_SUB_MCH_ID);
        if (citySubMchId != null) {
            Iterator iterator = citySubMchId.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next() + "";
                if (city.contains(key)) {
                    return citySubMchId.get(key) + "";
                }
            }
        }
        return null;
    }

    /**
     * 获取微信城市活动参数
     *
     * @param city
     * @param tradeParam
     * @return
     */
    private String getWeixinGoodsTagByCity(String city, Map tradeParam) {
        if (StringUtil.empty(city)) {
            return "";
        }
        Map cityGoodsTag = systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_WEIXIN_CITY_GOODS_TAG);
        if (cityGoodsTag != null) {
            Iterator iterator = cityGoodsTag.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next() + "";
                if (city.contains(key)) {
                    return cityGoodsTag.get(key) + "";
                }
            }
        }
        return "";
    }

    /**
     * 初始化merchantConfig
     *
     * @param merchantId
     */
    private Map initMerchantConfig(String merchantId, Integer payway) {
        Map merchantConfig = CollectionUtil.hashMap(
                DaoConstants.ID, uuidGenerator.nextUuid(),
                MerchantConfig.PAYWAY, payway,
                MerchantConfig.MERCHANT_ID, merchantId,
                MerchantConfig.B2C_FORMAL, false,
                MerchantConfig.C2B_FORMAL, false,
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.MINI_FORMAL, false,
                MerchantConfig.H5_FORMAL, false,
                MerchantConfig.APP_FORMAL, false,
                MerchantConfig.PARAMS, new HashMap<>()
        );
        createMerchantConfig(merchantConfig);
        return merchantConfigDao.get(BeanUtil.getPropString(merchantConfig, DaoConstants.ID));
    }

    /**
     * 初始化storeConfig
     *
     * @param storeId
     */
    private Map initStoreConfig(String storeId, Integer payway) {
        Map storeConfig = CollectionUtil.hashMap(
                DaoConstants.ID, uuidGenerator.nextUuid(),
                StoreConfig.PAYWAY, payway,
                StoreConfig.STORE_ID, storeId,
                StoreConfig.PARAMS, new HashMap<>()
        );
        storeConfigDao.save(storeConfig);
        return storeConfigDao.get(BeanUtil.getPropString(storeConfig, DaoConstants.ID));
    }

    /**
     * 初始化solicitorConfig
     *
     * @param solicitorId
     */
    private Map initSolicitorConfig(String solicitorId, Integer payway) {
        Map solicitorConfig = CollectionUtil.hashMap(
                DaoConstants.ID, uuidGenerator.nextUuid(),
                SolicitorConfig.PAYWAY, payway,
                SolicitorConfig.SOLICITOR_ID, solicitorId,
                SolicitorConfig.B2C_FORMAL, false,
                SolicitorConfig.C2B_FORMAL, false,
                SolicitorConfig.WAP_FORMAL, false,
                SolicitorConfig.MINI_FORMAL, false,
                SolicitorConfig.PARAMS, new HashMap<>()
        );
        solicitorConfigDao.save(solicitorConfig);
        return solicitorConfig;
    }

    /**
     * 解析商户交易参数配置
     *
     * @param analyzedMerchantConfig
     * @param payway
     * @param subPayway
     * @return
     */
    private Map getAnalyzedTradeParams(Map analyzedMerchantConfig, int payway, int subPayway) {
        Map params = new HashMap();
        Boolean formal = BeanUtil.getPropBoolean(analyzedMerchantConfig, subPaywayFormalColName.get(subPayway + ""), false);
        String feeRate = (String) analyzedMerchantConfig.get(subPaywayFeeRateColName.get(subPayway + ""));
        Integer provider = BeanUtil.getPropString(analyzedMerchantConfig, MerchantConfig.PROVIDER) != null ? BeanUtil.getPropInt(analyzedMerchantConfig, MerchantConfig.PROVIDER) : null;
        //防止feeRate为空字符串场景情况，赋null，让后续操作走default值
        if (StringUtil.empty(feeRate)) {
            feeRate = null;
            logger.error("解析商户交易参数feeRate为空. merchantId:{}, feeRateKey:{}", BeanUtil.getPropString(analyzedMerchantConfig, MerchantConfig.MERCHANT_ID), subPaywayFeeRateColName.get(subPayway + ""));
        }
        //设置交易参数信息
        if (formal == true) {
            String key = paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway);
            Map tradeParams = (Map) ((Map) analyzedMerchantConfig.get(MerchantConfig.PARAMS)).get(key);
            if (tradeParams == null) {
                throw new CoreMerchantConfigAbnormalException("商户正式交易参数配置错误");
            }
            tradeParams.put(TransactionParam.LIQUIDATION_NEXT_DAY, false);
            if (feeRate != null) {
                tradeParams.put(TransactionParam.FEE_RATE, feeRate);
            }
            params.put(key, tradeParams);
        } else {
            if (provider != null && provider != payway && isThisProviderSupport(provider, payway, subPayway)) {
                String tradeParamsKey = getTradeParamsKeyByProvider(provider);
                Map providerTradeParams = (Map) BeanUtil.getNestedProperty(analyzedMerchantConfig, MerchantConfig.PARAMS + "." + tradeParamsKey);
                if (providerTradeParams == null) {
                    providerTradeParams = new HashMap();
                }
                if (feeRate != null) {
                    providerTradeParams.put(TransactionParam.FEE_RATE, feeRate);
                }
                params.put(tradeParamsKey, providerTradeParams);
            }
            String key = paywaySubpaywayTradeParamsKey.get(payway + "" + subPayway);
            Map tradeParams = new HashMap();
            tradeParams.put(TransactionParam.LIQUIDATION_NEXT_DAY, true);
            if (feeRate != null) {
                tradeParams.put(TransactionParam.FEE_RATE, feeRate);
            }
            params.put(key, tradeParams);
        }
        //设置限额等信息
        for (String key : tradeValidateParamKeys) {
            params.put(key, BeanUtil.getProperty(BeanUtil.getProperty(analyzedMerchantConfig, MerchantConfig.PARAMS), key));
        }
        params.put(TransactionParam.ALLOW_CREDIT_PAY, BeanUtil.getProperty(BeanUtil.getProperty(analyzedMerchantConfig, MerchantConfig.PARAMS), TransactionParam.ALLOW_CREDIT_PAY));
        params.put(TransactionParam.USE_CLIENT_STORE_SN, BeanUtil.getProperty(BeanUtil.getProperty(analyzedMerchantConfig, MerchantConfig.PARAMS), TransactionParam.USE_CLIENT_STORE_SN));
        params.put(TransactionParam.IS_SENT_STORE_ID, BeanUtil.getProperty(BeanUtil.getProperty(analyzedMerchantConfig, MerchantConfig.PARAMS), TransactionParam.IS_SENT_STORE_ID));
        params.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, BeanUtil.getProperty(BeanUtil.getProperty(analyzedMerchantConfig, MerchantConfig.PARAMS), TransactionParam.IS_NEED_REFUND_FEE_FLAG));
        return params;
    }

    /**
     * 检查商户交易配置是否合法
     *
     * @param config
     */
    private void checkConfig(Map config) {

        if (config != null) {
            //校验费率是否合法
            List<String> feeRateKeys = Arrays.asList(MerchantConfig.B2C_FEE_RATE, MerchantConfig.C2B_FEE_RATE, MerchantConfig.WAP_FEE_RATE, MerchantConfig.MINI_FEE_RATE, MerchantConfig.H5_FEE_RATE, MerchantConfig.APP_FEE_RATE);
            String[] feeRateKeyStrs;
            String feeRateKeyStr;
            for (String feeRateKey : feeRateKeys) {
                feeRateKeyStr = BeanUtil.getPropString(config, feeRateKey);
                if (!StringUtil.empty(feeRateKeyStr)) {
                    feeRateKeyStrs = feeRateKeyStr.split("\\.");
                    if (feeRateKeyStrs.length == 2 && feeRateKeyStrs[1].length() > 2) {
                        throw new CoreInvalidParameterException("商户费率小数点后最多2位");
                    }
                    double feeRate = Double.parseDouble(feeRateKeyStr);
                    if (feeRate < ConstantUtil.MIN_FEE_RATE || feeRate > ConstantUtil.MAX_FEE_RATE) {
                        throw new CoreInvalidParameterException("商户费率超出限额");
                    }
                } else {
                    // 存在空字符串则输出日志
                    if (Objects.nonNull(feeRateKeyStr) && org.apache.commons.lang3.StringUtils.EMPTY.equals(feeRateKeyStr.trim())) {
                        logger.error("检测出feeRate值为空. merchantId:{} feeRateKey:{}",
                                BeanUtil.getPropString(config, MerchantConfig.MERCHANT_ID), feeRateKey);
                    }
                }
            }
        }
    }

    /**
     * 检查推广者配置是否合法
     */

    private void checkSolicitorConfig(Map solicitorConfig) {
        if (solicitorConfig != null) {
            //校验费率是否合法
            List<String> feeRateKeys = Arrays.asList(SolicitorConfig.B2C_FEE_RATE, SolicitorConfig.C2B_FEE_RATE, SolicitorConfig.WAP_FEE_RATE, SolicitorConfig.MINI_FEE_RATE);
            for (String feeRateKey : feeRateKeys) {
                if (solicitorConfig.get(feeRateKey) != null) {
                    double feeRate = Double.parseDouble(BeanUtil.getPropString(solicitorConfig, feeRateKey));
                    if (feeRate < ConstantUtil.MIN_FEE_RATE || feeRate > ConstantUtil.MAX_FEE_RATE) {
                        throw new CoreInvalidParameterException("商户费率超出限额");
                    }
                }
            }
        }
    }

    /**
     * 如果payway为支付宝1.0并且不为wap支付， 则按照一定规则判断是否走1.0 还是2.0, 其他返回本身的payway
     *
     * @param merchantId
     * @param payway
     * @param subPayway
     * @return
     */
    private Object[] guessAlipayNewPayway(String merchantId, int payway, int subPayway, String currency, String tradeApp) {
        if (payway != PAYWAY_ALIPAY) {
            return new Object[]{payway};
        }

        List<Map<String, Object>> merchantConfigs = getAnalyzedMerchantConfigsByPayWayListAndSubpayway(merchantId, new int[]{PAYWAY_ALIPAY, PAYWAY_ALIPAY2, PAYWAY_ALIPAY_INTL}, subPayway, tradeApp);
        Map<String, Object> alipayV1MerchantConfig = null;
        Map<String, Object> alipayV2MerchantConfig = null;
        Map<String, Object> alipayIntlMerchantConfig = null;
        for (Map merchantConfig : merchantConfigs) {
            int thisPayway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (thisPayway == PAYWAY_ALIPAY) {
                alipayV1MerchantConfig = merchantConfig;
            } else if (thisPayway == PAYWAY_ALIPAY2) {
                alipayV2MerchantConfig = merchantConfig;
            } else if (thisPayway == PAYWAY_ALIPAY_INTL) {
                alipayIntlMerchantConfig = merchantConfig;
            }
        }
        boolean v1Formal = BeanUtil.getPropBoolean(alipayV1MerchantConfig, subPaywayFormalColName.get(subPayway + ""));
        boolean v2Formal = BeanUtil.getPropBoolean(alipayV2MerchantConfig, subPaywayFormalColName.get(subPayway + ""));
        boolean intlFormal = BeanUtil.getPropBoolean(alipayIntlMerchantConfig, subPaywayFormalColName.get(subPayway + ""));
        int v1Status = BeanUtil.getPropInt(alipayV1MerchantConfig, subPaywayStatusColName.get(subPayway + ""));
        int v2Status = BeanUtil.getPropInt(alipayV2MerchantConfig, subPaywayStatusColName.get(subPayway + ""));
        int intlStatus = BeanUtil.getPropInt(alipayIntlMerchantConfig, subPaywayStatusColName.get(subPayway + ""));
        int bestPayway = 0;
        Map bestPaywayConfig = null;
        if (intlFormal && intlStatus == MerchantConfig.STATUS_OPENED && !TransactionParam.UPAY_DEFAULT_CURRENCY_CNY.equals(currency)) {
            bestPayway = PAYWAY_ALIPAY_INTL;
            bestPaywayConfig = alipayIntlMerchantConfig;
        } else {
            if (v2Status == MerchantConfig.STATUS_CLOSED) {
                bestPayway = PAYWAY_ALIPAY;
                bestPaywayConfig = alipayV1MerchantConfig;
            } else {
                if (v1Formal && !v2Formal) {
                    bestPayway = PAYWAY_ALIPAY;
                    bestPaywayConfig = alipayV1MerchantConfig;
                } else {
                    bestPayway = PAYWAY_ALIPAY2;
                    bestPaywayConfig = alipayV2MerchantConfig;
                }
            }
        }
        return new Object[]{bestPayway, bestPaywayConfig};
    }


    /**
     * 检查收款通道是否被关闭，以及某些收款通道只允许正式商户才能交易
     *
     * @param merchantConfig
     * @param payway
     * @param subPayway
     */
    private void checkStatusAndFormal(Map merchantConfig, int payway, int subPayway) {
        Integer provider = BeanUtil.getPropString(merchantConfig, MerchantConfig.PROVIDER) != null ? BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER) : null;
        Boolean formal = BeanUtil.getPropBoolean(merchantConfig, subPaywayFormalColName.get(subPayway + ""), false);
        int status = BeanUtil.getPropInt(merchantConfig, subPaywayStatusColName.get(subPayway + ""), MerchantConfig.STATUS_CLOSED);
        if (status != MerchantConfig.STATUS_OPENED) {
            throw new CoreMerchantConfigAbnormalException("此收款通道已被关闭，请换用其他收款通道");
        }
        if (!formal) {
            boolean isSupport = isThisProviderSupport(provider, payway, subPayway);
            if (!isSupport || (isSupport && (provider == null || provider == payway))) {
                Map<String, Object> forbiddens = systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_INFORMAL_FORBIDDEN_PAYWAYS);
                if (forbiddens != null && !forbiddens.isEmpty()) {
                    String errorMessage = BeanUtil.getPropString(forbiddens, String.format("%s:%s", payway, subPayway));
                    if (errorMessage == null) {
                        errorMessage = BeanUtil.getPropString(forbiddens, String.format("%s", payway));
                    }
                    if (!StringUtil.empty(errorMessage)) {
                        throw new CoreMerchantConfigAbnormalException(errorMessage);
                    }
                }
            }
            String merchantId = BeanUtil.getPropString(merchantConfig, MerchantConfig.MERCHANT_ID);
            if (provider != null && PROVIDER_UNIONPAY_TL == provider) {
                Map tlParams = getProviderTradeParamsByKey(merchantId, TransactionParam.TL_TRADE_PARAMS);
                if (StringUtil.empty(BeanUtil.getPropString(tlParams, TransactionParam.TL_MCH_ID))) {
                    throw new CoreMerchantConfigAbnormalException("无通联商户号，不可发生代结算交易");
                }
            }else if (provider != null && PROVIDER_CHINAUMS == provider) {
                String agentName = com.wosai.pantheon.util.MapUtil.getString(merchantConfig, subPaywayAgentNameColName.get(subPayway + ""));
                Map<String, Object> agent = getAgentByName(agentName);
                Map<String, Object> params = com.wosai.pantheon.util.MapUtil.getMap(agent, Agent.PARAMS);
                Map<String, Object> tradeParams = com.wosai.pantheon.util.MapUtil.getMap(params, TransactionParam.CHINAUMS_TRADE_PARAMS);
                boolean nextday = com.wosai.pantheon.util.MapUtil.getBooleanValue(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, true);
                if(nextday) {
                    Map ysParams = getProviderTradeParamsByKey(merchantId, TransactionParam.YS_TRADE_PARAMS);
                    if ((subPayway == SUB_PAYWAY_BARCODE && StringUtil.empty(BeanUtil.getPropString(ysParams, TransactionParam.YS_MCH_CODE)))
                            || (subPayway != SUB_PAYWAY_BARCODE && StringUtil.empty(BeanUtil.getPropString(ysParams, TransactionParam.YS_CSB_MCH_CODE)))) {
                        throw new CoreMerchantConfigAbnormalException("无银商商户号，不可发生代结算交易");
                    }
                }
            } else if (provider != null && PROVIDER_HAIKE_UNION_PAY == provider) {
                //海科都是间连
                Map hkParams = getProviderTradeParamsByKey(merchantId, TransactionParam.HK_TRADE_PARAMS);
                if (StringUtil.empty(BeanUtil.getPropString(hkParams, TransactionParam.HK_MCH_ID))) {
                    throw new CoreMerchantConfigAbnormalException("无海科商户号，不可发生代结算交易");
                }
            } else if (provider != null && PROVIDER_GUOTONG == provider) {
                //国通都是间连
                Map gtParams = getProviderTradeParamsByKey(merchantId, TransactionParam.GUOTONG_TRADE_PARAMS);
                if (StringUtil.empty(BeanUtil.getPropString(gtParams, TransactionParam.GUOTONG_MCH_ID))) {
                    throw new CoreMerchantConfigAbnormalException("无国通商户号，不可发生代结算交易");
                }
            } else if (provider != null && PROVIDER_FUYOU == provider) {
                //富友通道跳过校验逻辑
            } else if (provider != null && PROVIDER_ZTKX == provider) {
                //中投通道暂时跳过校验逻辑。中投目前是直连，本期上线后（20250714），会将存量的商户全部切到间连
            } else {
                if (!metaProviderBiz.isIndirect(provider)) {
                    // 不是间连直清通道，则默认走拉卡拉结算，如果商户没有拉卡拉商户号，则不能做待结算交易
                    Map lakalaParams = getProviderTradeParams(merchantId, PROVIDER_LAKALA);
                    if (StringUtil.empty(BeanUtil.getPropString(lakalaParams, TransactionParam.LAKALA_MERC_ID))) {
                        throw new CoreMerchantConfigAbnormalException("无拉卡拉商户号，不可发生代结算交易");
                    }
                }
            }
            //如果走威富通的交易， 并且支付宝报备没有成功，那么不允许交易
            if ((payway == PAYWAY_ALIPAY || payway == PAYWAY_ALIPAY2) && provider != null && ApolloConfigurationCenterUtil.getWftProviders().contains(provider)) {
                String tradeParamsKey = getTradeParamsKeyByProvider(provider);
                boolean hasMchId = true;
                Map<String, Object> tradeParams = (Map<String, Object>) BeanUtil.getNestedProperty(merchantConfig, "params." + tradeParamsKey);
                if (tradeParams == null || tradeParams.isEmpty()) {
                    hasMchId = false;
                } else {
                    String mchId = null;
                    for (String key : tradeParams.keySet()) {
                        if (key.endsWith(TransactionParam.SWIFTPASS_MCH_ID_SUFFIX)) {
                            mchId = BeanUtil.getPropString(tradeParams, key);
                        }
                    }
                    if (StringUtil.empty(mchId)) {
                        hasMchId = false;
                    }
                }
                if (!hasMchId) {
                    throw new CoreMerchantConfigAbnormalException("支付宝商户报备中，请耐心等待");
                }

            }
        }

    }

    /**
     * 获取服务商交易参数
     * 所有的服务商交易参数都一样，故现在返回服务商为收钱吧的vendorConfig
     * (线上vendorConfig创建时间最早的一条就是收钱吧服务商)
     *
     * @return
     */
    @Override
    public Map getDefaultVendorConfig() {
        Criteria criteria = new Criteria();
        criteria.with(VendorConfig.PARAMS).ne(null);
        Filter<Map<String, Object>> filter = vendorConfigDao.filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.ASC);
        return filter.fetchOne();
    }


    /**
     * 查看某支付通道是否支持此收款通道的交易模式
     *
     * @param provider
     * @param payway
     * @param subPayway
     * @return
     */
    private boolean isThisProviderSupport(Integer provider, int payway, int subPayway) {
        return metaProviderBiz.isThisProviderSupport(provider, payway, subPayway);
    }

    /**
     * 获取受理商的交易参数
     *
     * @param merchantConfig
     * @param subPayway
     * @return
     */
    private Map<String, Object> getAgentTradeParams(Map<String, Object> merchantConfig, int subPayway) {
        Map<String, Object> tradeParams = new HashMap<>();
        //设置交易限额
        Map content = systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_DEFAULT_MERCHANT_TRADE_VALIDATE_PARAMS);
        tradeParams.putAll(content);
        int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
        boolean formal = BeanUtil.getPropBoolean(merchantConfig, subPaywayFormalColName.get(subPayway + ""));
        Long provider = BeanUtil.getPropString(merchantConfig, MerchantConfig.PROVIDER) != null ? BeanUtil.getPropLong(merchantConfig, MerchantConfig.PROVIDER) : null;
        String agentName = BeanUtil.getPropString(merchantConfig, subPaywayAgentNameColName.get(subPayway + ""));

        //直连试用
        Criteria criteria = Criteria.where(Agent.PROVIDER).is(null).with(Agent.PAYWAY).is(payway).with(Agent.FORMAL).is(false).with(Agent.IS_PRIMARY).is(true);
        Map<String, Object> agent = agentDao.filter(criteria).fetchOne();
        addTradeParams(tradeParams, BeanUtil.getPropString(agent, Agent.NAME));

        if (!StringUtil.empty(agentName)) {
            addTradeParams(tradeParams, agentName);
        } else {
            if (formal) {
                criteria = Criteria.where(Agent.PROVIDER).is(null).with(Agent.PAYWAY).is(payway).with(Agent.SUB_PAYWAY).is(subPayway).with(Agent.FORMAL).is(true).with(Agent.IS_PRIMARY).is(true);
                agent = agentDao.filter(criteria).fetchOne();
                if (agent == null) {
                    agent = agentDao.filter(Criteria.where(Agent.PROVIDER).is(null).with(Agent.PAYWAY).is(payway).with(Agent.SUB_PAYWAY).is(null).with(Agent.FORMAL).is(true).with(Agent.IS_PRIMARY).is(true)).fetchOne();
                }
                addTradeParams(tradeParams, BeanUtil.getPropString(agent, Agent.NAME));
            } else {
                //银行试用
                if (provider != null) {
                    criteria = Criteria.where(Agent.PROVIDER).is(provider).with(Agent.PAYWAY).is(payway).with(Agent.IS_PRIMARY).is(true).with(Agent.FORMAL).is(false);
                    agent = agentDao.filter(criteria).fetchOne();
                    if (agent == null) {
                        criteria = Criteria.where(Agent.PROVIDER).is(provider).with(Agent.PAYWAY).is(null).with(Agent.IS_PRIMARY).is(true).with(Agent.FORMAL).is(false);
                        agent = agentDao.filter(criteria).fetchOne();
                    }
                    addTradeParams(tradeParams, BeanUtil.getPropString(agent, Agent.NAME));
                }
            }
        }
        return tradeParams;
    }


    private void addTradeParams(Map params, String agentName) {
        if (!StringUtil.empty(agentName)) {
            Map<String, Object> agent = getAgentByName(agentName);
            if (agent != null && agent.get(Agent.PARAMS) != null) {
                params.putAll((Map<String, Object>) agent.get(Agent.PARAMS));
            }
        }
    }

    /**
     * @param provider
     * @return
     */
    private String getTradeParamsKeyByProvider(Integer provider) {
        return metaProviderBiz.getTradeParamsKey(provider);
    }

    @Override
    public Map<String, Object> getSystemConfigContentByName(String name) {
        return systemConfigService.getSystemConfigContentByName(name);
    }

    @Override
    public Map<String, Object> updateMerchantSingleMaxOfTran(String merchantSn, Map config) {
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(BeanUtil.getPropString(merchant, DaoConstants.ID))
                .with(MerchantConfig.PAYWAY).is(null)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            if (null == config) {
                merchantConfigParams.remove(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN);
            } else {
                merchantConfigParams.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, config);
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        return merchantConfigDao.get(id);
    }

    @Override
    public Map<String, Object> updateMerchantSingleMaxOfTranAndLog(String merchantSn, Map config, OpLogCreateRequest opLogCreateRequest) {
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(BeanUtil.getPropString(merchant, DaoConstants.ID))
                .with(MerchantConfig.PAYWAY).is(null)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> before = merchantConfigDao.filter(criteria).fetchOne();
        Map<String, Object> beforeMerchantConfigParams = MapUtils.getMap(before, MerchantConfig.PARAMS);
        if (null == before) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        Map<String, Object> after = updateMerchantSingleMaxOfTran(merchantSn, config);
        Map<String, Object> AfterMerchantConfigParams = MapUtils.getMap(after, MerchantConfig.PARAMS);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, BeanUtil.getPropString(merchant, DaoConstants.ID), null, OpLog.MERCHANT_CONFIG_PARAMS_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_PARAMS_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_PARAMS_KEY_LIST, OpLog.MERCHANT_CONFIG_PARAMS_CHANGE_KEY_LIST, new HashMap(), beforeMerchantConfigParams, AfterMerchantConfigParams);
        return after;
    }

    @Override
    public void saveMerchantLadderInfoByPayWay(String merchantId, Integer payWay, Map<String, Object> ladderInfo) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        //初始化
        if (Objects.isNull(before)) {
            initMerchantConfig(merchantId, payWay);
        }
        updateMerchantLadderInfoByPayWay(merchantId, payWay, ladderInfo);
    }

    @Override
    public void updateMerchantLadderInfoByPayWay(String merchantId, Integer payWay, Map<String, Object> ladderInfo) {
        updateLadderInternal(merchantId, Lists.newArrayList(payWay),
                BeanUtil.getPropInt(ladderInfo, MerchantConfig.LADDER_STATUS),
                (List<Map>) BeanUtil.getProperty(ladderInfo, MerchantConfig.LADDER_FEE_RATES)
                , (Map<String, String>) BeanUtil.getProperty(ladderInfo, MerchantConfig.LADDER_FEE_RATE_TAG));
    }


    private void updateLadderInternal(String merchantId, List<Integer> paywayList, int ladderStatus, List<Map> ladderFeeRates, Map<String, String> ladderFeeRatesTag) {
        Map<String, Object> merchant = merchantService.getMerchant(merchantId);
        if (null == merchant) {
            throw new CoreInvalidParameterException("商户不存在");
        }
        if (ladderStatus != MerchantConfig.STATUS_OPENED) {
            ladderFeeRates = null;
        }
        List<Map> existsMerchantConfigs = new ArrayList();
        // 开通特优商户费率时，商户如有有使用直连通道时，不允许开通
        for (Integer payway : paywayList) {
            Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
            if (null != merchantConfig) {
                if (ladderStatus == MerchantConfig.STATUS_OPENED) {
                    for (String formal : subPaywayFormalColName.values()) {
                        if (BeanUtil.getPropBoolean(merchantConfig, formal, false)) {
                            throw new CoreMerchantConfigAbnormalException("商户已使用直连通道，不允许开通特优费率");
                        }
                    }
                }
                existsMerchantConfigs.add(merchantConfig);
            }
        }

        List<Map> historyMerchantConfig = new ArrayList<>();
        List<Map> finalLadderFeeRates = ladderFeeRates;
        for (Map existsMerchantConfig : existsMerchantConfigs) {
            historyMerchantConfig.add(existsMerchantConfig);
            Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
                if (ladderStatus == MerchantConfig.STATUS_CLOSED) {
                    merchantConfigParams.remove(MerchantConfig.LADDER_STATUS);
                    merchantConfigParams.remove(MerchantConfig.LADDER_FEE_RATES);
                    merchantConfigParams.remove(MerchantConfig.LADDER_FEE_RATE_TAG);
                } else {
                    merchantConfigParams.put(MerchantConfig.LADDER_STATUS, MerchantConfig.STATUS_OPENED);
                    merchantConfigParams.put(MerchantConfig.LADDER_FEE_RATES, finalLadderFeeRates);
                    merchantConfigParams.put(MerchantConfig.LADDER_FEE_RATE_TAG, ladderFeeRatesTag);
                }
                return merchantConfigParams;
            };
            updateMerchantConfigWithRetry(BeanUtil.getPropString(existsMerchantConfig, DaoConstants.ID), paramsChangeFunc);
            int payway = BeanUtil.getPropInt(existsMerchantConfig, MerchantConfig.PAYWAY);
            try {
                merchantConfigDataBusBiz.feeRateChange(existsMerchantConfig == null ? new HashMap() : existsMerchantConfig, getMerchantConfigByMerchantIdAndPayway(merchantId, payway));
            } catch (Exception e) {
                logger.error("{} {} 费率变更写入事件表失败", merchantId, payway, e);
            }
        }
        if (historyMerchantConfig.size() > 0) {
            // 更新费率成功，清空商户交易参数缓存
            redisTemplate.delete(PublicConstants.UPAY_BASIC_PARAM + BeanUtil.getPropString(merchant, Merchant.SN));
            redisTemplate.delete(PublicConstants.UPAY_ALL_PARAM + BeanUtil.getPropString(merchant, Merchant.SN));
        }

    }

    public Map<String, Object> updateUseClientStoreSn(String merchantSn, int useClientSn) {
        if ((useClientSn != 0) && (useClientSn != TransactionParam.USE_CLIENT_STORE_SN_YES) && (useClientSn != TransactionParam.USE_CLIENT_STORE_SN_NO)) {
            throw new CoreMerchantConfigAbnormalException("参数错误，配置失败");
        }
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(BeanUtil.getPropString(merchant, DaoConstants.ID))
                .with(MerchantConfig.PAYWAY).is(null)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            if (useClientSn == 0) {
                merchantConfigParams.remove(TransactionParam.USE_CLIENT_STORE_SN);
            } else {
                merchantConfigParams.put(TransactionParam.USE_CLIENT_STORE_SN, useClientSn);
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        return merchantConfigDao.get(id);
    }

    @Override
    public void updateHuabeiParamsBySn(String merchantSn, Map<String, Object> params) {
        if (StringUtil.empty(merchantSn)) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        updateHuabeiParamsById(BeanUtil.getPropString(merchant, DaoConstants.ID), params);
    }

    @Override
    public void updateHuabeiParamsById(String merchantId, Map<String, Object> huabeiParams) {
        if (StringUtil.empty(merchantId)) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(PAYWAY_ALIPAY2)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (merchantConfig == null) {
            throw new CoreInvalidParameterException("商户未设置交易参数，配置失败");
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            if (huabeiParams.containsKey(TransactionParam.ALIPAY_HUABEI_STATUS)) {
                merchantConfigParams.put(TransactionParam.ALIPAY_HUABEI_STATUS, huabeiParams.get(TransactionParam.ALIPAY_HUABEI_STATUS));
            }
            if (huabeiParams.containsKey(TransactionParam.ALIPAY_HUABEI_LIMIT)) {
                merchantConfigParams.put(TransactionParam.ALIPAY_HUABEI_LIMIT, huabeiParams.get(TransactionParam.ALIPAY_HUABEI_LIMIT));
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        Map<String, Object> merchant = merchantDao.filter(Criteria.where(DaoConstants.ID).is(merchantId)).fetchOne();
        redisService.removeCachedHuabeiParams(merchantId);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
    }

    @Override
    public Map<String, Object> getHuabeiParamsBySn(String merchantSn) {
        if (StringUtil.empty(merchantSn)) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        return getHuabeiParamsById(BeanUtil.getPropString(merchant, DaoConstants.ID));
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getHuabeiParamsById(String merchantId) {
        BoundValueOperations operations = redisTemplate.boundValueOps(PublicConstants.HUABEI_PARAM + merchantId);
        Object value = operations.get();
        if (value != null && com.wosai.pantheon.util.StringUtil.isNotEmpty(String.valueOf(value))) {
            try {
                Map<String, Object> map = JSONObject.parseObject(String.valueOf(value));
                if (map != null && !map.isEmpty()) {
                    return map;
                }
            } catch (Exception ex) {
                logger.info("value:{}", value);
            }
        }
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(PAYWAY_ALIPAY2)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (merchantConfig == null){
            merchantConfig = new HashMap<>();
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> params = (Map<String, Object>) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap<>();
            merchantConfig.put(MerchantConfig.PARAMS, params);
        }
        Map<String, Object> result = new HashMap<>();
        result.put(TransactionParam.ALIPAY_HUABEI_STATUS, BeanUtil.getPropInt(params, TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_CLOSED));
        result.put(TransactionParam.ALIPAY_HUABEI_LIMIT, BeanUtil.getPropInt(params, TransactionParam.ALIPAY_HUABEI_LIMIT, ApolloConfigurationCenterUtil.getHuabeiDefaultMinLimit()));
        result.put(TransactionParam.ALIPAY_HUABEI_PARAMS, ApolloConfigurationCenterUtil.getHuabeiConfigParams());
        result.put(MerchantConfig.B2C_FORMAL, merchantConfig.get(MerchantConfig.B2C_FORMAL));
        result.put(MerchantConfig.C2B_FORMAL, merchantConfig.get(MerchantConfig.C2B_FORMAL));
        result.put(MerchantConfig.WAP_FORMAL, merchantConfig.get(MerchantConfig.WAP_FORMAL));
        result.put(MerchantConfig.PROVIDER, merchantConfig.get(MerchantConfig.PROVIDER));
        operations.set(JSONObject.toJSONString(result), DateTimeUtil.ONE_HOUR_MILLIS, TimeUnit.MILLISECONDS);
        return result;
    }

    @Override
    public void updateMerchantConfigFeeRateTag(String merchantId, int payway, long version, Map<String, Object> feeRateTag, Map<String, Object> ladderFeeRateTag) {
        Map<String, Object> merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数");
        }
        if(feeRateTag != null){
            BeanUtil.setNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + TransactionParam.FEE_RATE_TAG, feeRateTag);
        }
        if(ladderFeeRateTag != null){
            BeanUtil.setNestedProperty(merchantConfig, MerchantConfig.PARAMS + "." + MerchantConfig.LADDER_FEE_RATE_TAG, ladderFeeRateTag);
        }
        merchantConfigDao.updatePart(com.wosai.pantheon.util.MapUtil.hashMap(
                DaoConstants.ID, merchantConfig.get(DaoConstants.ID),
                MerchantConfig.PARAMS, merchantConfig.get(MerchantConfig.PARAMS),
                DaoConstants.VERSION, version
        ));
    }

    @Override
    public Map<String, Object> updateMerchantHistoryTradeRefundFlag(String merchantId, int flagStatus) {
        if (flagStatus != TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_CLOSE && flagStatus != TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN) {
            throw new CoreMerchantConfigAbnormalException("参数错误，配置失败");
        }
        Map<String, Object> merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, flagStatus);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        return merchantConfigDao.get(id);
    }

    @Override
    public Map<String, Object> updateMerchantHistoryTradeRefundFlagAndLog(String merchantId, int flagStatus, OpLogCreateRequestV2 opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map<String, Object> result = updateMerchantHistoryTradeRefundFlag(merchantId, flagStatus);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map beforeParam = new HashMap();
        Map afterParam = new HashMap();
        // 设置修改状态
        if (null != before) {
            int historyFlag = BeanUtil.getPropInt(before, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG));
            beforeParam.put(TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, historyFlag == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN ? "同支付源" : "默认");
            BeanUtil.setNestedProperty(before,
                    String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG),
                    historyFlag == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN ? "同支付源" : "默认");
        }

        if (null != after) {
            int historyFlag = BeanUtil.getPropInt(after, String.format("%s.%s", MerchantConfig.PARAMS, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG));
            afterParam.put(TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, historyFlag == TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN ? "同支付源" : "默认");
        }
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, BeanUtil.getPropString(before, MerchantConfig.MERCHANT_ID), null, OpLog.HISTORY_TRADE_REFUND_FLAG_TEMPLATE_CODE, OpLog.HISTORY_TRADE_REFUND_FLAG_TABLE_NAME, OpLog.FIXED_HISTORY_TRADE_REFUND_FLAG_KEY_LIST, OpLog.HISTORY_TRADE_REFUND_FLAG_CHANGE_KEY_LIST, new HashMap(), beforeParam, afterParam);
        return result;
    }

    @Override
    public List getAnalyzedStoreConfigs(String storeId) {
        return getAnalyzedStoreConfigsByPayWayList(storeId, DEFAULT_PAYWAYS);
    }

    @Override
    public List getAnalyzedTerminalConfigs(String terminalId) {
        return getAnalyzedTerminalConfigsByPayWayList(terminalId, DEFAULT_PAYWAYS);
    }


    @Override
    public List getAnalyzedStoreConfigsByPayWayList(String storeId, int[] payWayList) {
        return getAnalyzedStoreConfigsByPayWayList(storeId, payWayList, null);
    }

    @Override
    public List getAnalyzedTerminalConfigsByPayWayList(String terminalId, int[] payWayList) {
        return getAnalyzedTerminalConfigsByPayWayListAndSubpayway(terminalId, payWayList, null);
    }

    @Override
    public Map<String, Object> updateMerchantDeposit(String merchantId, Map depositConfig) {
        Map<String, Object> merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            if (depositConfig == null) {
                merchantConfigParams.remove(TransactionParam.DEPOSIT);
            } else {
                merchantConfigParams.put(TransactionParam.DEPOSIT, depositConfig);
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        return merchantConfigDao.get(id);
    }

    @Override
    public Map<String, Object> updateMerchantDepositAndLog(String merchantId, Map depositConfig, OpLogCreateRequestV2 opLogCreateRequest) {
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map<String, Object> result = updateMerchantDeposit(merchantId, depositConfig);
        Map after = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, BeanUtil.getPropString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_PARAMS_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_PARAMS_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_PARAMS_KEY_LIST, OpLog.MERCHANT_CONFIG_PARAMS_CHANGE_KEY_LIST, new HashMap(), before, after);
        return result;
    }

    @Override
    public Boolean getMerchantIsSentStoreId(String merchantId) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);

        if (MapUtils.isNotEmpty(merchantParams)) {
            return (Boolean) merchantParams.getOrDefault(TransactionParam.IS_SENT_STORE_ID, true);
        }

        return true;
    }

    @Override
    public void updateMerchantIsSentStoreId(String merchantId, boolean isSentStoreId) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (merchantConfig == null) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (merchantParams == null) {
            merchantParams = new HashMap();
            merchantConfig.put(MerchantConfig.PARAMS, merchantParams);
        }
        merchantParams.put(TransactionParam.IS_SENT_STORE_ID, isSentStoreId);
        saveMerchantConfig(merchantConfig);
    }

    @Override
    public void updateGiftCardParams(String merchantId, boolean open) {
        Map<String, Object> config = merchantConfigDao.filter(new Criteria().with(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_GIFT_CARD)).fetchOne();
        if (config == null || config.isEmpty()) {
            config = new HashMap<String, Object>() {{
                put(DaoConstants.ID, CrudUtil.randomUuid());
                put(MerchantConfig.MERCHANT_ID, merchantId);
                put(MerchantConfig.PAYWAY, PAYWAY_GIFT_CARD);
                put(MerchantConfig.B2C_FORMAL, true);
                put(MerchantConfig.B2C_STATUS, open);
                put(MerchantConfig.B2C_FEE_RATE, 0);
                put(MerchantConfig.PARAMS, new HashMap<String, Object>() {{
                    put(TransactionParam.GIFT_CARD_TRADE_PARAMS, new HashMap<>());
                }});
            }};
            saveMerchantConfig(config);
        } else {
            config.put(MerchantConfig.B2C_STATUS, open);
            config.put(MerchantConfig.B2C_FORMAL, true);
            config.put(MerchantConfig.B2C_FEE_RATE, 0);
            config.put(MerchantConfig.PARAMS, new HashMap<String, Object>() {{
                put(TransactionParam.GIFT_CARD_TRADE_PARAMS, new HashMap<>());
            }});
            updateMerchantConfigWithRetry(config);
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, ConstantUtil.KEY_SN));
    }

    @Override
    public boolean getGiftCardStatus(String merchantId) {
        Map config = merchantConfigDao.filter(new Criteria().with(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_GIFT_CARD)).fetchOne();
        return BeanUtil.getPropBoolean(config, MerchantConfig.B2C_STATUS, false);
    }

    @Override
    public void updateWelfareCardParams(String merchantId, boolean open) {
        Map<String, Object> config = merchantConfigDao.filter(new Criteria().with(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_WELFARE_CARD)).fetchOne();
        if (config == null || config.isEmpty()) {
            config = new HashMap<String, Object>() {{
                put(DaoConstants.ID, CrudUtil.randomUuid());
                put(MerchantConfig.MERCHANT_ID, merchantId);
                put(MerchantConfig.PAYWAY, PAYWAY_WELFARE_CARD);
                put(MerchantConfig.B2C_FORMAL, true);
                put(MerchantConfig.B2C_STATUS, open);
                put(MerchantConfig.B2C_FEE_RATE, 0);
                put(MerchantConfig.PARAMS, new HashMap<String, Object>() {{
                    put(TransactionParam.GIFT_CARD_TRADE_PARAMS, new HashMap<>());
                }});
            }};
            saveMerchantConfig(config);
        } else {
            config.put(MerchantConfig.B2C_STATUS, open);
            config.put(MerchantConfig.B2C_FORMAL, true);
            config.put(MerchantConfig.B2C_FEE_RATE, 0);
            config.put(MerchantConfig.PARAMS, new HashMap<String, Object>() {{
                put(TransactionParam.GIFT_CARD_TRADE_PARAMS, new HashMap<>());
            }});
            updateMerchantConfigWithRetry(config);
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, ConstantUtil.KEY_SN));
    }

    @Override
    public boolean getWelfareCardStatus(String merchantId) {
        Map config = merchantConfigDao.filter(new Criteria().with(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(PAYWAY_WELFARE_CARD)).fetchOne();
        return BeanUtil.getPropBoolean(config, MerchantConfig.B2C_STATUS, false);
    }


    @Override
    public void configureChinaumsTradeParams(Map<String, Object> params) {
        String merchantSn = BeanUtil.getPropString(params, TradeParamConfigureConstants.MERCHANT_SN);
        String storeSn = BeanUtil.getPropString(params, TradeParamConfigureConstants.STORE_SN);
        String appKey = BeanUtil.getPropString(params, TradeParamConfigureConstants.APP_KEY);
        String appId = BeanUtil.getPropString(params, TradeParamConfigureConstants.APP_ID);
        String mchCode = BeanUtil.getPropString(params, TradeParamConfigureConstants.MCH_CODE);
        String termCode = BeanUtil.getPropString(params, TradeParamConfigureConstants.TERM_CODE);
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(storeSn)
                || StringUtils.isEmpty(appKey) || StringUtils.isEmpty(appId)
                || StringUtils.isEmpty(mchCode) || StringUtils.isEmpty(termCode)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }

        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (StringUtils.isEmpty(merchantId)) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        Map store = storeService.getStoreByStoreSn(storeSn);
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        if (StringUtils.isEmpty(storeId)) {
            throw new CoreStoreNotExistsException("门店不存在");
        }

        String chinaumsAgent = "1018_17_*_false_false_0246";
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_LKL_UNIONPAY);
        if (MapUtils.isEmpty(merchantConfig)) {
            merchantConfig = CollectionUtil.hashMap(
                    MerchantConfig.MERCHANT_ID, merchantId,
                    MerchantConfig.PAYWAY, PAYWAY_LKL_UNIONPAY,
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_AGENT_NAME, chinaumsAgent,
                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.C2B_AGENT_NAME, chinaumsAgent,
                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.WAP_AGENT_NAME, chinaumsAgent,
                    MerchantConfig.PROVIDER, PROVIDER_CHINAUMS,
                    MerchantConfig.PARAMS, com.wosai.pantheon.util.MapUtil.hashMap(TransactionParam.CHINAUMS_TRADE_PARAMS, com.wosai.pantheon.util.MapUtil.hashMap(
                            TransactionParam.CHINAUMS_APP_ID, appId,
                            TransactionParam.CHINAUMS_APP_KEY, appKey
                    ))
            );
            createMerchantConfig(merchantConfig);
        } else {
            Map<String, Object> merchantUpdatePart = com.wosai.pantheon.util.MapUtil.hashMap(DaoConstants.ID, com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID),
                        MerchantConfig.B2C_AGENT_NAME, chinaumsAgent,
                        MerchantConfig.C2B_AGENT_NAME, chinaumsAgent,
                        MerchantConfig.WAP_AGENT_NAME, chinaumsAgent,
                        MerchantConfig.PROVIDER, PROVIDER_CHINAUMS
                    );

            Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
                BeanUtil.setNestedProperty(merchantConfigParams
                        , TransactionParam.CHINAUMS_TRADE_PARAMS + "." + TransactionParam.CHINAUMS_APP_ID
                        , appId);

                BeanUtil.setNestedProperty(merchantConfigParams
                        , TransactionParam.CHINAUMS_TRADE_PARAMS + "." + TransactionParam.CHINAUMS_APP_KEY
                        , appKey);
                return merchantConfigParams;
            };
            updateMerchantConfigWithRetry(merchantUpdatePart, paramsChangeFunc);
        }

        Map storeConfig = getStoreConfigByStoreIdAndPayway(storeId, PAYWAY_LKL_UNIONPAY);
        if (MapUtils.isEmpty(storeConfig)) {
            storeConfig = getStoreConfigTemplate();
            storeConfig.put(StoreConfig.STORE_ID, storeId);
            storeConfig.put(StoreConfig.PAYWAY, PAYWAY_LKL_UNIONPAY);
            storeConfig.put(StoreConfig.B2C_AGENT_NAME, chinaumsAgent);
            storeConfig.put(StoreConfig.C2B_AGENT_NAME, chinaumsAgent);
            storeConfig.put(StoreConfig.WAP_AGENT_NAME, chinaumsAgent);
            storeConfig.put(StoreConfig.PROVIDER, PROVIDER_CHINAUMS);
            storeConfig.put(StoreConfig.PARAMS, com.wosai.pantheon.util.MapUtil.hashMap(TransactionParam.CHINAUMS_TRADE_PARAMS, com.wosai.pantheon.util.MapUtil.hashMap(
                        TransactionParam.CHINAUMS_MCH_CODE, mchCode,
                        TransactionParam.CHINAUMS_TERM_CODE, termCode
                    )));

            createStoreConfig(storeConfig);
        } else {
            storeConfig.put(StoreConfig.B2C_AGENT_NAME, chinaumsAgent);
            storeConfig.put(StoreConfig.C2B_AGENT_NAME, chinaumsAgent);
            storeConfig.put(StoreConfig.WAP_AGENT_NAME, chinaumsAgent);
            storeConfig.put(StoreConfig.PROVIDER, PROVIDER_CHINAUMS);
            BeanUtil.setNestedProperty(storeConfig
                    , StoreConfig.PARAMS + "." + TransactionParam.CHINAUMS_TRADE_PARAMS + "." + TransactionParam.CHINAUMS_MCH_CODE
                    , mchCode);

            BeanUtil.setNestedProperty(storeConfig
                    , StoreConfig.PARAMS + "." + TransactionParam.CHINAUMS_TRADE_PARAMS + "." + TransactionParam.CHINAUMS_TERM_CODE
                    , termCode);

            storeConfigDao.updatePart(storeConfig);
        }
    }

    @Override
    public void configureBestpayStoreTradeParams(Map<String, Object> params) {
        String merchantSn = BeanUtil.getPropString(params, TradeParamConfigureConstants.MERCHANT_SN);
        String storeSn = BeanUtil.getPropString(params, TradeParamConfigureConstants.STORE_SN);
        String bestpayStoreId = BeanUtil.getPropString(params, TradeParamConfigureConstants.STORE_ID);
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(storeSn)
                || StringUtils.isEmpty(bestpayStoreId)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }

        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (StringUtils.isEmpty(merchantId)) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_BESTPAY);
        if (MapUtils.isEmpty(merchantConfig)) {
            throw new CoreMerchantConfigAbnormalException("商户配置不存在");
        }

        Map bestpayTradeParams = (Map) BeanUtil.getNestedProperty(merchantConfig
                , MerchantConfig.PARAMS + "." + TransactionParam.BESTPAY_TRADE_PARAMS);
        if (MapUtils.isEmpty(bestpayTradeParams)) {
            throw new CoreMerchantConfigAbnormalException("翼支付直连交易参数不存在");
        }
        if (!bestpayTradeParams.containsKey(TransactionParam.BESTPAY_STORE_ID)) {
            bestpayTradeParams.put(TransactionParam.BESTPAY_STORE_ID, null);
            Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
                merchantConfigParams.put(TransactionParam.BESTPAY_TRADE_PARAMS, bestpayTradeParams);
                return merchantConfigParams;
            };
            updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), paramsChangeFunc);
        }

        Map store = storeService.getStoreByStoreSn(storeSn);
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        if (StringUtils.isEmpty(storeId)) {
            throw new CoreStoreNotExistsException("门店不存在");
        }
        String bestpayAgent = "18_*_true_true_0001";
        Map storeConfigTradeParams = CollectionUtil.hashMap();
        storeConfigTradeParams.put(TransactionParam.BESTPAY_STORE_ID, bestpayStoreId);

        Map storeConfig = getStoreConfigByStoreIdAndPayway(storeId, PAYWAY_BESTPAY);
        if (MapUtils.isEmpty(storeConfig)) {
            storeConfig = getStoreConfigTemplate();
            storeConfig.put(StoreConfig.STORE_ID, storeId);
            storeConfig.put(StoreConfig.PAYWAY, PAYWAY_BESTPAY);
            storeConfig.put(StoreConfig.B2C_FORMAL, true);
            storeConfig.put(StoreConfig.B2C_AGENT_NAME, bestpayAgent);
            storeConfig.put(StoreConfig.C2B_FORMAL, true);
            storeConfig.put(StoreConfig.C2B_AGENT_NAME, bestpayAgent);
            storeConfig.put(StoreConfig.WAP_FORMAL, true);
            storeConfig.put(StoreConfig.WAP_AGENT_NAME, bestpayAgent);
            storeConfig.put(StoreConfig.PARAMS, storeConfigTradeParams);

            createStoreConfig(storeConfig);
        } else {
            storeConfig.put(StoreConfig.STORE_ID, storeId);
            storeConfig.put(StoreConfig.PAYWAY, PAYWAY_BESTPAY);
            storeConfig.put(StoreConfig.B2C_FORMAL, true);
            storeConfig.put(StoreConfig.B2C_AGENT_NAME, bestpayAgent);
            storeConfig.put(StoreConfig.C2B_FORMAL, true);
            storeConfig.put(StoreConfig.C2B_AGENT_NAME, bestpayAgent);
            storeConfig.put(StoreConfig.WAP_FORMAL, true);
            storeConfig.put(StoreConfig.WAP_AGENT_NAME, bestpayAgent);
            storeConfig.put(StoreConfig.PARAMS, storeConfigTradeParams);

            storeConfigDao.updatePart(storeConfig);
        }
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void configureWxGoodsTag(Map<String, Object> params) {
        String merchantSn = BeanUtil.getPropString(params, TradeParamConfigureConstants.MERCHANT_SN);
        String storeSn = BeanUtil.getPropString(params, TradeParamConfigureConstants.STORE_SN);
        String goodsTag = BeanUtil.getPropString(params, TradeParamConfigureConstants.GOODS_TAG);
        boolean isNeedDelStoreConfig = BeanUtil.getPropBoolean(params
                , TradeParamConfigureConstants.IS_NEED_DEL_STORE_CONFIG);
        if (StringUtils.isEmpty(merchantSn)
                || StringUtils.isEmpty(goodsTag)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }

        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        if (StringUtils.isEmpty(merchantId)) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }
        Map store = storeService.getStoreByStoreSn(storeSn);
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);

        if (isNeedDelStoreConfig) {
            Criteria criteria = new Criteria();
            criteria.with(MerchantConfigCustom.MERCHANT_ID).is(merchantId);
            Filter filter = merchantConfigCustomDao.filter(criteria);
            List merchantConfigCustomList = CollectionUtil.iterator2list(filter.fetchAll());
            if (CollectionUtils.isNotEmpty(merchantConfigCustomList)) {
                for (Object merchantConfigCustom : merchantConfigCustomList) {
                    merchantConfigCustomDao.delete(BeanUtil.getPropString(merchantConfigCustom
                            , DaoConstants.ID));
                }
            }
        }

        Map merchantConfigCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId
                , storeId, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG);
        if (MapUtils.isEmpty(merchantConfigCustom)) {
            merchantConfigCustom = CollectionUtil.hashMap(
                    MerchantConfigCustom.MERCHANT_ID, merchantId,
                    MerchantConfigCustom.STORE_ID, storeId,
                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                    MerchantConfigCustom.B2C_VALUE, goodsTag,
                    MerchantConfigCustom.C2B_VALUE, goodsTag,
                    MerchantConfigCustom.WAP_VALUE, goodsTag,
                    MerchantConfigCustom.MINI_VALUE, goodsTag
            );

            createMerchantConfigCustom(merchantConfigCustom);
            return;
        }
        merchantConfigCustom.put(MerchantConfigCustom.B2C_VALUE, goodsTag);
        merchantConfigCustom.put(MerchantConfigCustom.C2B_VALUE, goodsTag);
        merchantConfigCustom.put(MerchantConfigCustom.WAP_VALUE, goodsTag);
        merchantConfigCustom.put(MerchantConfigCustom.MINI_VALUE, goodsTag);
        updateMerchantConfigCustom(merchantConfigCustom);
    }

    @Override
    public Integer queryStatus(String merchantId, String switchKey) {
        if (!TransactionParam.switchList.contains(switchKey)) {
            throw new CoreMerchantNotExistsException("查询的状态权限不存在");
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map merchant = merchantDao.filter(criteria).fetchOne();

        if (merchant == null) {
            throw new CoreMerchantNotExistsException("该商户不存在");
        }
        criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(null);
        Map merchantConfig = merchantConfigDao.filter(criteria).fetchOne();

        if (merchantConfig == null || !merchantConfig.containsKey(MerchantConfig.PARAMS)) {
            return null;
        }
        Map merchantConfigParams = (Map) merchantConfig.get(MerchantConfig.PARAMS);
        Map paramsSwitches = (Map) merchantConfigParams.get(TransactionParam.SWITCHES);
        //如果merchantConfig中不存在switchs字段
        if (paramsSwitches == null || !paramsSwitches.containsKey(switchKey)) {
            return null;
        }

        return BeanUtil.getPropInt(paramsSwitches, switchKey);
    }

    @Override
    public Map<String, Object> getSwitches(String merchantId) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(null);
        Map merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (merchantConfig == null || !merchantConfig.containsKey(MerchantConfig.PARAMS)) {
            return null;
        }
        Map merchantConfigParams = (Map) merchantConfig.get(MerchantConfig.PARAMS);
        Map paramsSwitches = (Map) merchantConfigParams.get(TransactionParam.SWITCHES);
        return paramsSwitches;
    }

    @Override
    public Map<String, Object> getStoreSwitches(String storeId) {

        Criteria criteria = Criteria.where(DaoConstants.ID).is(storeId);
        Map store = storeDao.filter(criteria).fetchOne();

        if (store == null) {
            throw new CoreStoreNotExistsException("该门店不存在");
        }
        criteria = Criteria.where(StoreConfig.STORE_ID).is(storeId).with(MerchantConfig.PAYWAY).is(null);
        Map storeConfig = storeConfigDao.filter(criteria).fetchOne();
        if (storeConfig == null || !storeConfig.containsKey(StoreConfig.PARAMS)) {
            return null;
        }
        Map storeConfigParams = (Map) storeConfig.get(MerchantConfig.PARAMS);
        Map paramsSwitches = (Map) storeConfigParams.get(TransactionParam.SWITCHES);
        return paramsSwitches;
    }

    @Override
    public void switchStoreStatus(String storeId, String switchKey, int status) {
        if (!TransactionParam.storeSwitchList.contains(switchKey)) {
            throw new CoreStoreNotExistsException("修改的状态权限不存在");
        }
        if (!TransactionParam.statusList.contains(status)) {
            throw new CoreStoreNotExistsException("status状态不存在");
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(storeId);
        Map store = storeDao.filter(criteria).fetchOne();

        if (store == null) {
            throw new CoreStoreNotExistsException("该门店不存在");
        }
        criteria = Criteria.where(StoreConfig.STORE_ID).is(storeId).with(MerchantConfig.PAYWAY).is(null);
        Map storeConfig = storeConfigDao.filter(criteria).fetchOne();

        //当storeConfig不存在时，创建payway为null的storeConfig
        if (storeConfig == null) {
            storeConfig = new HashMap();
            storeConfig.put(StoreConfig.STORE_ID, storeId);
            createStoreConfig(storeConfig);
            storeConfig = storeConfigDao.filter(criteria).fetchOne();
        }

        Map storeConfigParams = (Map) storeConfig.get(StoreConfig.PARAMS);
        if (storeConfigParams == null) {
            storeConfigParams = new HashMap();
        }

        Map paramsSwitchs = (Map) storeConfigParams.get(TransactionParam.SWITCHES);
        if (paramsSwitchs == null) {
            paramsSwitchs = new HashMap();
        }
        paramsSwitchs.put(switchKey, status);

        //在paramsSwitch里面添加pay_status的参数设置，即开启/关闭该statusTag状态
        storeConfigParams.put(TransactionParam.SWITCHES, paramsSwitchs);
        storeConfig.put(MerchantConfig.PARAMS, storeConfigParams);

        this.storeConfigDao.updatePart(storeConfig);

        //删除缓存
        Map merchant = merchantService.getMerchant(MapUtils.getString(store, Store.MERCHANT_ID));
        redisService.removeCachedParams(MapUtils.getString(merchant, Merchant.SN));
    }

    @Override
    public Integer queryStoreStatus(String storeId, String switchKey) {
        if (!TransactionParam.storeSwitchList.contains(switchKey)) {
            throw new CoreStoreNotExistsException("查询的状态权限不存在");
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(storeId);
        Map store = storeDao.filter(criteria).fetchOne();

        if (store == null) {
            throw new CoreStoreNotExistsException("该门店不存在");
        }
        criteria = Criteria.where(StoreConfig.STORE_ID).is(storeId).with(MerchantConfig.PAYWAY).is(null);
        Map storeConfig = storeConfigDao.filter(criteria).fetchOne();

        if (storeConfig == null || !storeConfig.containsKey(MerchantConfig.PARAMS)) {
            return null;
        }
        Map storeConfigParams = (Map) storeConfig.get(MerchantConfig.PARAMS);
        Map paramsSwitches = (Map) storeConfigParams.get(TransactionParam.SWITCHES);
        //如果merchantConfig中不存在switchs字段
        if (paramsSwitches == null || !paramsSwitches.containsKey(switchKey)) {
            return null;
        }

        return MapUtils.getInteger(paramsSwitches, switchKey);
    }

    @Override
    public void switchStatus(String merchantId, String switchKey, int status) {
        if (!TransactionParam.switchList.contains(switchKey)) {
            throw new CoreMerchantNotExistsException("修改的状态权限不存在");
        }
        if (!TransactionParam.statusList.contains(status)) {
            throw new CoreMerchantNotExistsException("status状态不存在");
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map merchant = merchantDao.filter(criteria).fetchOne();

        if (merchant == null) {
            throw new CoreMerchantNotExistsException("该商户不存在");
        }
        criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(null);
        Map merchantConfig = merchantConfigDao.filter(criteria).fetchOne();

        //当merchantConfig不存在时，创建payway为null的merchantConfig
        if (merchantConfig == null) {
            merchantConfig = new HashMap();
            merchantConfig.put(MerchantConfig.MERCHANT_ID, merchantId);
            createMerchantConfig(merchantConfig);
            merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        }
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID);
        //在paramsSwitch里面添加pay_status的参数设置，即开启/关闭该statusTag状态
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            Map<String, Object> paramsSwitchs = (Map) merchantConfigParams.get(TransactionParam.SWITCHES);
            if (paramsSwitchs == null) {
                paramsSwitchs = new HashMap<>();
                merchantConfigParams.put(TransactionParam.SWITCHES, paramsSwitchs);
            }
            paramsSwitchs.put(switchKey, status);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);

        //删除缓存
        redisService.removeCachedParams(MapUtils.getString(merchant, Merchant.SN));
    }

    @Override
    public Map<String, Object> updateAlipaySellerId(Map<String, Object> params) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfigCustom.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(params, MerchantConfigCustom.STORE_ID);
        String value = BeanUtil.getPropString(params, CoreCommonConstants.MERCHANT_CONFIG_CUSTOM_VALUE);
        if (StringUtils.isEmpty(merchantId)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }
        Map merchantConfigCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, storeId
                , MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID);
        if (MapUtils.isEmpty(merchantConfigCustom)) {
            merchantConfigCustom = CollectionUtil.hashMap(
                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID,
                    MerchantConfigCustom.MERCHANT_ID, merchantId,
                    MerchantConfigCustom.STORE_ID, storeId,
                    MerchantConfigCustom.B2C_VALUE, value,
                    MerchantConfigCustom.C2B_VALUE, value,
                    MerchantConfigCustom.WAP_VALUE, value,
                    MerchantConfigCustom.MINI_VALUE, value
            );

            createMerchantConfigCustom(merchantConfigCustom);
        } else {
            merchantConfigCustom.put(MerchantConfigCustom.MERCHANT_ID, merchantId);
            merchantConfigCustom.put(MerchantConfigCustom.STORE_ID, storeId);
            merchantConfigCustom.put(MerchantConfigCustom.B2C_VALUE, value);
            merchantConfigCustom.put(MerchantConfigCustom.C2B_VALUE, value);
            merchantConfigCustom.put(MerchantConfigCustom.WAP_VALUE, value);
            merchantConfigCustom.put(MerchantConfigCustom.MINI_VALUE, value);

            updateMerchantConfigCustom(merchantConfigCustom);
        }

        return getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, storeId
                , MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID);
    }

    @Override
    public Map<String, Object> updateAlipaySellerIdAndLog(Map<String, Object> params, OpLogCreateRequestV2 opLogCreateRequest) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfigCustom.MERCHANT_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(merchantId)) {
            throw new CoreInvalidParameterException("商户id不能为空");
        }
        Map before = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null
                , MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID);
        Map<String, Object> result = updateAlipaySellerId(params);
        Map after = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null
                , MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_CUSTOM_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_CUSTOM_TABLE_NAME, new ArrayList<>(), OpLog.MERCHANT_CONFIG_CUSTOM_CHANGE_KEY_LIST, new HashMap<>(), before, after);
        return result;
    }

    @Override
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public void batchUpdateAlipayStoreId(Map<String, Object> params) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfigCustom.MERCHANT_ID);
        Map<String, String> storeIdMap = (Map<String, String>) BeanUtil
                .getProperty(params, CoreCommonConstants.SQB_STORE_ALIPAY_STORE_MAP);
        if (StringUtils.isEmpty(merchantId)
                || MapUtils.isEmpty(storeIdMap)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }
        Set<Map.Entry<String, String>> entries = storeIdMap.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            String storeSn = entry.getKey();
            String alipayStoreId = entry.getValue();

            Map store = storeService.getStoreByStoreSn(storeSn);
            if (MapUtils.isEmpty(store)) {
                throw new CoreStoreNotExistsException("门店不存在");
            }
            String storeId = BeanUtil.getPropString(store, DaoConstants.ID);

            Map merchantConfigCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, storeId
                    , MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
            if (MapUtils.isEmpty(merchantConfigCustom)) {

                merchantConfigCustom = CollectionUtil.hashMap(
                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                        MerchantConfigCustom.MERCHANT_ID, merchantId,
                        MerchantConfigCustom.STORE_ID, storeId,
                        MerchantConfigCustom.B2C_VALUE, alipayStoreId,
                        MerchantConfigCustom.C2B_VALUE, alipayStoreId,
                        MerchantConfigCustom.WAP_VALUE, alipayStoreId,
                        MerchantConfigCustom.MINI_VALUE, alipayStoreId
                );

                createMerchantConfigCustom(merchantConfigCustom);
            } else {
                merchantConfigCustom.put(MerchantConfigCustom.MERCHANT_ID, merchantId);
                merchantConfigCustom.put(MerchantConfigCustom.STORE_ID, storeId);
                merchantConfigCustom.put(MerchantConfigCustom.B2C_VALUE, alipayStoreId);
                merchantConfigCustom.put(MerchantConfigCustom.C2B_VALUE, alipayStoreId);
                merchantConfigCustom.put(MerchantConfigCustom.WAP_VALUE, alipayStoreId);
                merchantConfigCustom.put(MerchantConfigCustom.MINI_VALUE, alipayStoreId);

                updateMerchantConfigCustom(merchantConfigCustom);
            }

        }
    }

    @Override
    public Map<String, Object> updateRefundFeeFlag(String merchantSn, Map config) {
        if (MapUtils.isEmpty(config)) {
            throw new CoreInvalidParameterException("入参配置不能为空");
        }

        Map merchant = merchantService.getMerchantBySn(merchantSn);
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(
                BeanUtil.getPropString(merchant, DaoConstants.ID), null);
        if (Objects.isNull(merchantConfig)) {
            throw new CoreMerchantConfigAbnormalException("商户交易配置不存在");
        }
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            merchantConfigParams.put(TransactionParam.IS_NEED_REFUND_FEE_FLAG, config);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        return merchantConfigDao.get(id);
    }

    @Override
    public Map createMerchantAppConfig(Map merchantAppConfig) {
        checkConfig(merchantAppConfig);
        if (merchantAppConfig.get(DaoConstants.ID) == null) {
            merchantAppConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        merchantAppConfigDao.save(merchantAppConfig);
        return merchantAppConfig;
    }

    @Override
    public Map getMerchantAppConfig(String merchantAppConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantAppConfigId);
        return merchantAppConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public List getMerchantAppConfigsByMerchantId(String merchantId) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId);
        return CollectionUtil.iterator2list(merchantAppConfigDao.filter(criteria).fetchAll());
    }

    //是否存在三方通道(间连间清通道)
    private boolean existThirdClearanceProvider(List<Map> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return false;
        }

        List<Map<String,Object>> metaProviderList = metaProviderBiz.getAllMetaProviders();
        Map<Integer, Boolean> providerFlagMap = new HashMap<>(metaProviderList.size());
        for(Map<String,Object> metaProvider : metaProviderList) {
            Integer provider = MapUtils.getInteger(metaProvider, DaoConstants.ID);
            Integer flag = MapUtils.getInteger(metaProvider, MetaProductFlag.FLAG);
            if (null != provider && null != flag) {
                providerFlagMap.put(provider, MetaProviderFlagEnum.isWithdrawFlag(flag));
            }
        }

        for(Map config : configList) {
            Integer provider = MapUtils.getInteger(config, MerchantConfig.PROVIDER);
            if (null == provider) {
                continue;
            }
            Integer payWay = MapUtils.getInteger(config, MerchantConfig.PAYWAY);
            if (!PayWayConstant.isNeedCheckPayWayOfThirdClearanceProvider(payWay)) {
                continue;
            }
            boolean isThirdClearanceProvider = MapUtils.getBooleanValue(providerFlagMap, provider);
            if (isThirdClearanceProvider) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean existThirdClearanceProvider(String merchantId) {
        //查询基础业务的清算通道
        List<Map> merchantConfigList = getMerchantConfigsByMerchantId(merchantId);
        //是否存在间连间清通道
        boolean isThirdClearanceProvider = existThirdClearanceProvider(merchantConfigList);
        if (isThirdClearanceProvider) {
            return true;
        }

        //查询多业务的清算通道
        List<Map> appMerchantConfigs = getMerchantAppConfigsByMerchantId(merchantId);
        return existThirdClearanceProvider(appMerchantConfigs);
    }

    @Override
    public Map updateMerchantAppConfig(Map merchantAppConfig) {
        Map<String, Object> oldMerchantAppConfig = getMerchantAppConfig(MapUtils.getString(merchantAppConfig, DaoConstants.ID));
        if (Objects.isNull(oldMerchantAppConfig)) {
            logger.warn("业务方配置不存在, request={}", JsonUtil.toJsonStr(merchantAppConfig));
            throw new CoreInvalidParameterException("业务方配置不存在");
        }

        checkConfig(merchantAppConfig);

        merchantAppConfigDao.updatePart(merchantAppConfig);
        //发送merchantAppConfig变更事件
        sendMerchantAppConfigChangeEvent(oldMerchantAppConfig);
        removeCachedParamsByMerchantId(MapUtils.getString(oldMerchantAppConfig, MerchantAppConfig.MERCHANT_ID));
        return merchantAppConfig;
    }

    @Override
    public Map updateMerchantAppConfigAndLog(Map merchantAppConfig, OpLogCreateRequest opLogCreateRequest) {
        String merchantId = MapUtils.getString(merchantAppConfig, MerchantAppConfig.MERCHANT_ID);
        Integer payway = MapUtils.getInteger(merchantAppConfig, MerchantAppConfig.PAYWAY);
        String appId = MapUtils.getString(merchantAppConfig, MerchantAppConfig.APP_ID);
        Map before = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, appId);
        Map result = updateMerchantAppConfig(merchantAppConfig);
        Map after = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, appId);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_APP_CONFIG_TEMPLATE_CODE, OpLog.MERCHANT_APP_CONFIG_TABLE_NAME, OpLog.FIXED_MERCHANT__APP_CONFIG_KEY_LIST, OpLog.MERCHANT__APP_CONFIG_CHANGE_KEY_LIST, OpLog.MERCHANT_APP_CONFIG_DESC_MAP, before, after);
        return result;
    }

    @Override
    public void deleteMerchantAppConfig(String merchantAppConfigId) {
        Map<String, Object> merchantAppConfig = getMerchantAppConfig(merchantAppConfigId);
        if (Objects.nonNull(merchantAppConfig)) {
            sendMerchantAppConfigDeleteEvent(merchantAppConfig);
            merchantAppConfigDao.delete(merchantAppConfigId);
            removeCachedParamsByMerchantId(MapUtils.getString(merchantAppConfig, MerchantAppConfig.MERCHANT_ID));
        }
    }

    @Override
    public Map createStoreAppConfig(Map storeAppConfig) {
        if (storeAppConfig.get(DaoConstants.ID) == null) {
            storeAppConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        checkConfig(storeAppConfig);
        storeAppConfigDao.save(storeAppConfig);
        return storeAppConfig;
    }

    @Override
    public Map getStoreAppConfigByStoreIdAndPaywayAndApp(String storeId, Integer payway, String appId) {
        Criteria criteria = Criteria.where(StoreAppConfig.STORE_ID).is(storeId).with(StoreAppConfig.PAYWAY).is(payway);
        criteria = criteria.with(MerchantAppConfig.APP_ID).is(appId);
        return storeAppConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public List<Map<String, Object>> getStoreAppConfigByStoreIdAndApp(String storeId, String appId) {
        Criteria criteria = Criteria.where(StoreAppConfig.STORE_ID).is(storeId).with(StoreAppConfig.APP_ID).is(appId);
        return CollectionUtil.iterator2list(storeAppConfigDao.filter(criteria).fetchAll());
    }

    @Override
    public Map updateStoreAppConfig(Map storeAppConfig) {
        String id = MapUtils.getString(storeAppConfig, DaoConstants.ID);
        Map<String, Object> oldStoreAppConfig = getStoreAppConfig(id);
        if (Objects.isNull(oldStoreAppConfig)) {
            throw new CoreInvalidParameterException("业务方配置不存在");
        }
        checkConfig(storeAppConfig);
        storeAppConfigDao.updatePart(storeAppConfig);
        return getStoreAppConfig(id);
    }


    @Override
    public void deleteStoreAppConfig(String storeAppConfigId) {
        storeAppConfigDao.delete(storeAppConfigId);
    }

    private Map<String,Object> getStoreAppConfig(String id){
        return storeAppConfigDao.get(id);
    }

    @Override
    public Map getStoreTradeValidateParams(String storeId) {
        Map defaultValidateParams = systemConfigService.getSystemConfigContentByName(CoreConstant.SYSTEM_CONFIG_NAME_DEFAULT_MERCHANT_TRADE_VALIDATE_PARAMS);
        if (defaultValidateParams == null) {
            defaultValidateParams = getTradeValidateParamsTemplate();
        }
        Map storeConfig = getStoreConfigByStoreIdAndPayway(storeId, null);
        Map storeParams = (Map) BeanUtil.getProperty(storeConfig, StoreConfig.PARAMS);
        if (storeParams == null) {
            storeParams = new HashMap();
        }
        Map validateParams = new HashMap();
        for (String key : storeParamKeys) {
            validateParams.put(key, storeParams.get(key) != null ? storeParams.get(key) : defaultValidateParams.get(key));
        }
        return validateParams;
    }

    @Override
    public void updateStoreTradeValidateParams(String storeId, Map validateParams) {
        Map storeConfig = getStoreConfigByStoreIdAndPayway(storeId, null);
        if (storeConfig == null) {
            storeConfig = initStoreConfig(storeId, null);
        }
        Map storeParams = (Map) BeanUtil.getProperty(storeConfig, MerchantConfig.PARAMS);
        if (storeParams == null) {
            storeParams = new HashMap();
            storeConfig.put(MerchantConfig.PARAMS, storeParams);
        }

        for (String key : storeParamKeys) {
            if (validateParams.containsKey(key)) {
                Object value = BeanUtil.getProperty(validateParams, key);
                //如果值为""字符串或者空map,则设置为null
                if (value instanceof String && StringUtils.isEmpty((String) value)) {
                    value = null;
                } else if (value instanceof Map && ((Map) value).isEmpty()) {
                    value = null;
                }
                storeParams.put(key, value);

            }
        }
        storeConfigDao.updatePart(storeConfig);
    }

    @Override
    public void closeStoreTradeQuota(String storeId) {
        Map storeConfig = getStoreConfigByStoreIdAndPayway(storeId, null);
        if (storeConfig == null) {
            return;
        }
        Map storeParams = (Map) BeanUtil.getProperty(storeConfig, MerchantConfig.PARAMS);
        if (storeParams == null) {
            return;
        }

        if (storeParams.containsKey(TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS)) {
            storeParams.remove(TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS);
            storeConfigDao.updatePart(storeConfig);
        }
    }

    private void removeCachedParamsByMerchantId(String merchantId) {
        Map<String, Object> merchant = merchantService.getMerchant(merchantId);
        if (Objects.nonNull(merchant)) {
            redisService.removeCachedParams(MapUtils.getString(merchant, Merchant.SN));
        }
    }

    @Override
    public Map getMerchantAppConfigByMerchantIdAndPaywayAndApp(String merchantId, Integer payway, String appId) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(payway);
        criteria = criteria.with(MerchantAppConfig.APP_ID).is(appId);
        return merchantAppConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public List<Map<String, Object>> getMerchantAppConfigByMerchantIdAndProvider(String merchantId, Integer provider) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantAppConfig.PROVIDER).is(provider);
        return CollectionUtil.iterator2list(merchantAppConfigDao.filter(criteria).fetchAll());
    }

    private Map<String, Object> getMerchantConfigByMerchantIdAndPaywayAndTradeApp(String merchantId, Integer
            payway, String tradeApp) {
        if (isBasicPay(tradeApp)) {
            return getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        } else {
            return getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payway, tradeApp);
        }
    }

    private boolean isBasicPay(String tradeApp) {
        return Objects.isNull(tradeApp) || TransactionParam.TRADE_APP_BASIC_PAY.equals(tradeApp);
    }

    public Map updateAlipayStoreIdOnMerchant(Map<String, Object> params) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfigCustom.MERCHANT_ID);
        String alipayStoreId = BeanUtil.getPropString(params, CoreCommonConstants.ALIPAY_STORE_ID);

        Map merchantConfigCustom = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null
                , MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
        if (MapUtils.isEmpty(merchantConfigCustom)) {

            merchantConfigCustom = CollectionUtil.hashMap(
                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                    MerchantConfigCustom.MERCHANT_ID, merchantId,
                    MerchantConfigCustom.B2C_VALUE, alipayStoreId,
                    MerchantConfigCustom.C2B_VALUE, alipayStoreId,
                    MerchantConfigCustom.WAP_VALUE, alipayStoreId,
                    MerchantConfigCustom.MINI_VALUE, alipayStoreId
            );

            createMerchantConfigCustom(merchantConfigCustom);
        } else {
            merchantConfigCustom.put(MerchantConfigCustom.MERCHANT_ID, merchantId);
            merchantConfigCustom.put(MerchantConfigCustom.B2C_VALUE, alipayStoreId);
            merchantConfigCustom.put(MerchantConfigCustom.C2B_VALUE, alipayStoreId);
            merchantConfigCustom.put(MerchantConfigCustom.WAP_VALUE, alipayStoreId);
            merchantConfigCustom.put(MerchantConfigCustom.MINI_VALUE, alipayStoreId);

            updateMerchantConfigCustom(merchantConfigCustom);
        }

        return getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null
                , MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
    }

    @Override
    public Map updateAlipayStoreIdOnMerchantAndLog(Map<String, Object> params, OpLogCreateRequestV2 opLogCreateRequest) {
        String merchantId = BeanUtil.getPropString(params, MerchantConfigCustom.MERCHANT_ID);
        if (StringUtils.isEmpty(merchantId)) {
            throw new CoreInvalidParameterException("商户ID不能为空");
        }
        Map before = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
        Map result = updateAlipayStoreIdOnMerchant(params);
        Map after = getMerchantConfigCustomByMerchantIdAndStoreIdAndType(merchantId, null, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, MapUtils.getString(before, MerchantConfig.MERCHANT_ID), null, OpLog.MERCHANT_CONFIG_CUSTOM_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_CUSTOM_TABLE_NAME, new ArrayList<>(), OpLog.MERCHANT_CONFIG_CUSTOM_CHANGE_KEY_LIST, new HashMap<>(), before, after);
        return result;
    }


    @Override
    public List<Map<String, Object>> getMerchantAppConfigByMerchantIdAndApp(String merchantId, String appId) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId);
        if (!StringUtil.empty(appId)) {
            criteria = criteria.with(MerchantAppConfig.APP_ID).is(appId);
        }
        return CollectionUtil.iterator2list(merchantAppConfigDao.filter(criteria).fetchAll());
    }

    @Override
    public void configCommonSwitch(Map<String, Object> request) {
        String merchantId = com.wosai.pantheon.util.MapUtil.getString(request, MerchantConfig.MERCHANT_ID);
        Integer type = com.wosai.pantheon.util.MapUtil.getInteger(request, CoreCommonConstants.KEY_COMMON_SWITCH_TYPE);
        Integer status = com.wosai.pantheon.util.MapUtil.getInteger(request, CoreCommonConstants.KEY_COMMON_SWITCH_STATUS);
        if (StringUtils.isEmpty(merchantId) || Objects.isNull(type)
                || Objects.isNull(status) || !TransactionParam.statusList.contains(status)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }
        if (!ApolloConfigurationCenterUtil.isLegalType(type)) {
            throw new CoreInvalidParameterException("类型参数未定义");
        }

        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map merchant = merchantDao.filter(criteria).fetchOne();
        if (merchant == null) {
            throw new CoreMerchantNotExistsException("该商户不存在");
        }
        criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(null);
        Map merchantConfig = merchantConfigDao.filter(criteria).fetchOne();

        //当merchantConfig不存在时，创建payway为null的merchantConfig
        if (merchantConfig == null) {
            merchantConfig = new HashMap();
            merchantConfig.put(MerchantConfig.MERCHANT_ID, merchantId);
            createMerchantConfig(merchantConfig);
            merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        }

        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            Map switches = com.wosai.pantheon.util.MapUtil.getMap(merchantConfigParams, TransactionParam.SWITCHES);
            if (switches == null) {
                switches = new HashMap();
                merchantConfigParams.put(TransactionParam.SWITCHES, switches);
            }
            String commonSwitch = com.wosai.pantheon.util.MapUtil.getString(switches, TransactionParam.COMMON_SWITCH);
            if (StringUtils.isEmpty(commonSwitch)) {
                commonSwitch = new StringBuilder(COMMON_SWITCH_BASE)
                        .replace(type, type + 1, status.toString())
                        .toString();
            } else {
                commonSwitch = new StringBuilder(commonSwitch)
                        .replace(type, type + 1, status.toString())
                        .toString();
            }
            switches.put(TransactionParam.COMMON_SWITCH, commonSwitch);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(com.wosai.pantheon.util.MapUtil.getString(merchantConfig, DaoConstants.ID), paramsChangeFunc);
    }

    @Override
    public void configCommonSwitchAndLog(Map<String, Object> request, OpLogCreateRequestV2 opLogCreateRequestV2) {
        int status = MapUtils.getIntValue(request, CoreCommonConstants.KEY_COMMON_SWITCH_STATUS);

        Map preValue = new HashMap();
        preValue.put("account_phone", "");
        if (status == 0) {
            preValue.put("status", "开启");
        } else {
            preValue.put("status", "关闭");
        }
        dataRepository.doMerchantRelatedTransaction(() -> {
            configCommonSwitch(request);
        });

        Map afterValue = new HashMap();
        if (status == 0) {
            afterValue.put("status", "关闭");
        } else {
            afterValue.put("status", "开启");
        }
        Map<String, Object> extra = opLogCreateRequestV2.getExtra();
        String accountPhone = "";
        if (extra != null) {
            accountPhone = MapUtils.getString(extra, "account_phone");
        }
        afterValue.put("account_phone", accountPhone);
        String merchantId = MapUtils.getString(request, "merchant_id");
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequestV2, merchantId, null, OpLog.COMMON_SWITCH_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_COMMON_SWITCH_TABLE_NAME, new ArrayList<>(), OpLog.MERCHANT_CONFIG_COMMON_SWITCH_CHANGE_KEY_LIST, new HashMap<>(), preValue, afterValue);
    }

    @Override
    public int getCommonSwitchStatus(Map<String, Object> request) {
        String merchantId = com.wosai.pantheon.util.MapUtil.getString(request, MerchantConfig.MERCHANT_ID);
        Integer type = com.wosai.pantheon.util.MapUtil.getInteger(request, CoreCommonConstants.KEY_COMMON_SWITCH_TYPE);
        if (StringUtils.isEmpty(merchantId) || Objects.isNull(type)) {
            throw new CoreInvalidParameterException("缺少必要参数");
        }
        if (!ApolloConfigurationCenterUtil.isLegalType(type)) {
            throw new CoreInvalidParameterException("类型参数未定义");
        }

        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map merchant = merchantDao.filter(criteria).fetchOne();
        if (merchant == null) {
            throw new CoreMerchantNotExistsException("该商户不存在");
        }
        criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId).with(MerchantConfig.PAYWAY).is(null);
        Map merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        return judgeCommonSwitchStatus(merchantConfig, type);
    }

    @Override
    public void updatePrepaidTradeParams(String merchantId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map merchant = merchantDao.filter(criteria).fetchOne();
        if (MapUtils.isEmpty(merchant)) {
            throw new CoreMerchantNotExistsException("该商户不存在");
        }
        Map<String, Object> config = merchantConfigDao.filter(new Criteria()
                .with(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(PAYWAY_PREPAID_CARD))
                .fetchOne();
        if (MapUtils.isEmpty(config)) {
            config = CollectionUtil.hashMap(
                    DaoConstants.ID, CrudUtil.randomUuid(),
                    MerchantConfig.MERCHANT_ID, merchantId,
                    MerchantConfig.PAYWAY, PAYWAY_PREPAID_CARD,
                    MerchantConfig.B2C_FORMAL, true,
                    MerchantConfig.B2C_STATUS, true,
                    MerchantConfig.B2C_FEE_RATE, "0",
                    MerchantConfig.C2B_FORMAL, true,
                    MerchantConfig.C2B_STATUS, true,
                    MerchantConfig.C2B_FEE_RATE, "0",
                    MerchantConfig.WAP_FORMAL, true,
                    MerchantConfig.WAP_STATUS, true,
                    MerchantConfig.WAP_FEE_RATE, "0",
                    MerchantConfig.MINI_FORMAL, true,
                    MerchantConfig.MINI_STATUS, true,
                    MerchantConfig.MINI_FEE_RATE, "0",
                    MerchantConfig.H5_FORMAL, true,
                    MerchantConfig.H5_STATUS, true,
                    MerchantConfig.H5_FEE_RATE, "0",
                    MerchantConfig.PARAMS, CollectionUtil.hashMap(
                            TransactionParam.PREPAID_CARD_TRADE_PARAMS, new HashMap<>()
                    )
            );
            saveMerchantConfig(config);
        } else {
            config.put(MerchantConfig.B2C_FORMAL, true);
            config.put(MerchantConfig.B2C_STATUS, true);
            config.put(MerchantConfig.B2C_FEE_RATE, "0");
            config.put(MerchantConfig.C2B_FORMAL, true);
            config.put(MerchantConfig.C2B_STATUS, true);
            config.put(MerchantConfig.C2B_FEE_RATE, "0");
            config.put(MerchantConfig.WAP_FORMAL, true);
            config.put(MerchantConfig.WAP_STATUS, true);
            config.put(MerchantConfig.WAP_FEE_RATE, "0");
            config.put(MerchantConfig.MINI_FORMAL, true);
            config.put(MerchantConfig.MINI_STATUS, true);
            config.put(MerchantConfig.MINI_FEE_RATE, "0");
            config.put(MerchantConfig.H5_FORMAL, true);
            config.put(MerchantConfig.H5_STATUS, true);
            config.put(MerchantConfig.H5_FEE_RATE, "0");
            config.put(MerchantConfig.PARAMS, CollectionUtil.hashMap(
                    TransactionParam.PREPAID_CARD_TRADE_PARAMS, new HashMap<>()
            ));
            updateMerchantConfigWithRetry(config);
        }
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, ConstantUtil.KEY_SN));
    }

    @Override
    public Map<String, Object> updateCategoryMerchantSingleMax(String merchantSn, Long quota, String category) {
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(BeanUtil.getPropString(merchant, DaoConstants.ID), null);
        if (null == merchantConfig) {
            throw new CoreMerchantConfigAbnormalException("商户未设置交易参数，配置失败");
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            Map<String, Object> categorySingleMax = (Map<String, Object>) merchantConfigParams.computeIfAbsent(TransactionParam.CATEGORY_MERCHANT_SINGLE_MAX_OF_TRAN, k -> Maps.newHashMap());
            if (null == quota) {
                categorySingleMax.remove(category);
            } else {
                categorySingleMax.put(category, com.wosai.mpay.util.StringUtils.cents2yuan(quota));
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, ConstantUtil.KEY_SN));
        return merchantConfigDao.get(id);
    }

    @Override
    public void createTradeExtConfig(TradeExtConfigCreateRequest request) {
        request.check();
        Map config = JsonUtil.convertToObject(request, Map.class);
        config.put(DaoConstants.ID, uuidGenerator.nextUuid());
        tradeExtConfigDao.save(config);
    }

    @Override
    public void updateTradeExtConfig(TradeExtConfigUpdateRequest request) {
        request.check();
        TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest(request.getSn()
                , request.getSnType(), request.getProvider());
        TradeExtConfigQueryResponse queryResponse = queryTradeExtConfig(queryRequest);
        if (Objects.isNull(queryResponse)) {
            throw new RuntimeException("配置不存在");
        }
        TradeExtConfigContentModel reqContent = request.getContent();
        if (Objects.isNull(reqContent)) {
            return;
        }
        TermInfo termInfo = reqContent.getTermInfo();
        String termId = reqContent.getTermId();
        if(termInfo != null && termId != null){
            throw new RuntimeException("termInfo 与 termId 不能同时存在");
        }
        TradeExtConfigContentModel currContent = queryResponse.getContent();
        // term_info 与 term_id 正常只应该存在一个，并且有了其中一个，就不能有另外一个
        if (Objects.nonNull(termInfo)) {
            currContent.setTermInfo(termInfo);
            currContent.setTermId(null);
        }
        if (Objects.nonNull(termId)) {
            currContent.setTermId(termId);
            currContent.setTermInfo(null);
        }

        Boolean limitPayer = reqContent.getLimitPayer();
        if (Objects.nonNull(limitPayer)) {
            currContent.setLimitPayer(limitPayer);
        }
        Boolean storeSceneSwitch = reqContent.getStoreSceneSwitch();
        if (Objects.nonNull(storeSceneSwitch)) {
            currContent.setStoreSceneSwitch(storeSceneSwitch);
        }

        queryResponse.setContent(currContent);
        Map<String, Object> map = JsonUtil.convertToObject(queryResponse, Map.class);
        Map<String, Object> result = removeNullValues(map);
        tradeExtConfigDao.updatePart(result);
    }

    @Override
    public TradeExtConfigQueryResponse queryTradeExtConfig(TradeExtConfigQueryRequest request) {
        request.check();
        Criteria criteria = Criteria
                .where(TradeExtConfigQueryRequest.SN).is(request.getSn())
                .with(TradeExtConfigQueryRequest.SN_TYPE).is(request.getSnType())
                .with(TradeExtConfigQueryRequest.PROVIDER).is(request.getProvider());
        Map<String, Object> config = tradeExtConfigDao.filter(criteria).fetchOne();
        if (MapUtils.isEmpty(config)) {
            return null;
        }
        return JsonUtil.convertToObject(config, TradeExtConfigQueryResponse.class);

    }

    @Override
    public void updateMerchantCreditLimit(String merchantId, int payway, int creditType, String quota) {
        Map merchantTradeValidateParams = getMerchantTradeValidateParams(merchantId);
        String key = null;
        if(creditType == CoreCommonConstants.CREDIT_TYPE_DAY){
            key =  TransactionParam.PAYWAY_DAY_CREDIT_LIMITS;
        } else if(creditType == CoreCommonConstants.CREDIT_TYPE_MONTH) {
            key = TransactionParam.PAYWAY_MONTH_CREDIT_LIMITS;
        } else {
            return ;
        }

        Map creditParams = (Map) BeanUtil.getProperty(merchantTradeValidateParams,  key);
        if(Objects.isNull(creditParams)){
            creditParams = new HashMap();
        }
        //支付宝两个值，统一取2
        if (payway == SupportService.PAYWAY_ALIPAY || payway == SupportService.PAYWAY_ALIPAY2){
            payway = SupportService.PAYWAY_ALIPAY2;
        }
        creditParams.put(payway, quota);
        Map creditMapParams = new HashMap<>();
        creditMapParams.put(key, creditParams);

        updateMerchantTradeValidateParams(merchantId, creditMapParams);
        String merchantSn = businssCommonService.getMerchantSnById(merchantId);
        SupportService support = SpringContextHolder.getBean(SupportService.class);
        //removeCachedParams
        support.removeCachedParams(merchantSn);
    }


    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    @Override
    public void updateCmbLifeAppConfig(String merchantId, Map<String, String> config) {
        try {
            if (StringUtil.empty(merchantId)) {
                throw new CoreInvalidParameterException("商户号为空");
            }
            //保存
            Map<String, Object> merchantConfigDto = getMerchantConfigByMerchantIdAndPayway(merchantId, Payway.CMB_APP.getCode());
            Map<String, Object> cmbTradeParam = MapUtils.getMap(MapUtils.getMap(merchantConfigDto, MerchantConfig.PARAMS),
                    TransactionParam.CMB_APP_TRADE_PARAMS, Maps.newHashMapWithExpectedSize(4));
            for (String key : ImmutableList.of("mid", "aid", "mer_no", "str_no")) {
                String value = config.get(key);
                if (Objects.nonNull(value)) {
                    cmbTradeParam.put(key, value);
                }
            }
            String priKey = MapUtils.getString(config, "mer_private_key");
            if (!StringUtil.empty(priKey)) {
                String priRsaId = generateRsaKeyAndGetId("招行生活私钥" + merchantId, priKey);
                cmbTradeParam.put("mer_private_key_id", priRsaId);
            }
            //支付参数为空，直接退出
            if (MapUtils.isEmpty(cmbTradeParam)) {
                return;
            }
            if (MapUtils.isNotEmpty(merchantConfigDto)) {
                Map<String, Object> merchantConfigParam = (Map<String, Object>) merchantConfigDto
                        .computeIfAbsent(MerchantConfig.PARAMS, key -> Maps.newHashMap());
                merchantConfigParam.put(TransactionParam.CMB_APP_TRADE_PARAMS, cmbTradeParam);
                updateMerchantConfig(merchantConfigDto);
                return;
            }

            merchantConfigDto = CollectionUtil.hashMap(
                    MerchantConfig.MERCHANT_ID, merchantId,
                    MerchantConfig.PAYWAY, Payway.CMB_APP.getCode(),
                    MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_FEE_RATE, MapUtils.getString(config, MerchantConfig.B2C_FEE_RATE),
                    MerchantConfig.PARAMS, CollectionUtil.hashMap(TransactionParam.CMB_APP_TRADE_PARAMS, cmbTradeParam)
            );
            createMerchantConfig(merchantConfigDto);
        } finally {
            //执行完清理缓存
            removeCachedParamsByMerchantId(merchantId);
        }
    }

    private String generateRsaKeyAndGetId(String name, String data) {
        //以md5查询
        String digest = StringUtil.md5(data);
        Map<String, Object> rsaKey = rsaKeyService.getRsaKeyByDigest(digest);
        if (!MapUtils.isEmpty(rsaKey)) {
            return (String) MapUtil.getPropValue(rsaKey, DaoConstants.ID);
        }
        //以名称查询
        rsaKey = rsaKeyService.getRsaKeyByName(name);
        //不存在 新增
        if (MapUtils.isEmpty(rsaKey)) {
            rsaKey = CollectionUtil.hashMap(RsaKey.NAME, name, RsaKey.DATA, data);
            rsaKey = rsaKeyService.create(rsaKey);
        }
        //存在 更新
        else {
            rsaKey.put(RsaKey.DATA, data);
            rsaKeyService.update(rsaKey);
        }
        return (String) MapUtil.getPropValue(rsaKey, DaoConstants.ID);
    }

    @Override
    public Map updateBankCardMerchantConfigFeeRate(Map merchantConfig) {
        int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY, -1);
        if(payway != PAYWAY_BANKCARD){
            throw new CoreInvalidParameterException("收款通道有误");
        }
        String merchantId = BeanUtil.getPropString(merchantConfig, MerchantConfig.MERCHANT_ID);
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (before == null) {
            //开通商户银行卡通道
            before = initMerchantConfig(merchantId, payway);
        }
        checkConfig(merchantConfig);
        List<String> updateParamsKeys = ImmutableList.of(MerchantConfig.CHANNEL_STATUS,
                TransactionParam.PARAMS_BANKCARD_FEE, MerchantConfig.CHANNEL_FEE_RATE_TAG);
        Map<String, Object> overParams = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
        Integer channelStatus = MapUtils.getInteger(overParams, MerchantConfig.CHANNEL_STATUS, MerchantConfig.STATUS_CLOSED);
        String id = BeanUtil.getPropString(before, DaoConstants.ID);
        merchantConfig.put(DaoConstants.ID, id);
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            if (Objects.equals(channelStatus, MerchantConfig.STATUS_OPENED)) {
                for (String key : updateParamsKeys) {
                    Object overParam = MapUtils.getObject(overParams, key);
                    if (Objects.nonNull(overParam)) {
                        merchantConfigParams.put(key, overParam);
                    }
                }
            } else {
                for (String key : updateParamsKeys) {
                    merchantConfigParams.remove(key);
                }
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, merchantConfig,  updateParamsFunc);
        Map updateResult = getMerchantConfig(BeanUtil.getPropString(before, DaoConstants.ID));
        try {
            merchantConfigDataBusBiz.feeRateChange(before, updateResult);
        } catch (Exception e) {
            logger.error("{} 写入事件表失败", BeanUtil.getPropString(before, DaoConstants.ID), e);
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
        return updateResult;
    }

    @Override
    public void closeMerchantConfigFeeRate(String merchantId, Integer payWay, List<String> feeRateStatusKeys) {
        Map merchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        if (MapUtils.isEmpty(merchantConfig)) {
            throw new CoreInvalidParameterException("商户配置不存在");
        }
        for (String feeRateStatusKey : feeRateStatusKeys) {
            Map merchantConfigParams = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
            Integer status = MapUtils.getInteger(merchantConfigParams, feeRateStatusKey);
            if (Objects.equals(status, MerchantConfig.STATUS_CLOSED)) {
                continue;
            }
            if (Objects.equals(feeRateStatusKey, MerchantConfig.LADDER_STATUS)) {
                Map<String, Object> ladderInfo = com.wosai.pantheon.util.MapUtil.hashMap(MerchantConfig.LADDER_STATUS, MerchantConfig.STATUS_CLOSED);
                updateMerchantLadderInfoByPayWay(merchantId, payWay, ladderInfo);
            } else if (
                    // 只操作银行卡payWay
                    Objects.equals(Payway.BANKCARD.getCode(), payWay)
                    && Objects.equals(feeRateStatusKey, MerchantConfig.CHANNEL_STATUS)) {
                Map<String, Object> updateMerchantConfig = com.wosai.pantheon.util.MapUtil.hashMap(
                        MerchantConfig.MERCHANT_ID, merchantId,
                        MerchantConfig.PAYWAY, payWay,
                        MerchantConfig.PARAMS, com.wosai.pantheon.util.MapUtil.hashMap(MerchantConfig.CHANNEL_STATUS, MerchantConfig.STATUS_CLOSED)
                );
                updateBankCardMerchantConfigFeeRate(updateMerchantConfig);
            }
        }
    }

    @Override
    public Map updateLklOpenMerchantConfig(Map merchantConfig) {
        return updateBankCardMerchantConfig(merchantConfig);
    }

    @Override
    public Map updateLklOpenTerminalConfig(Map terminalConfig) {
        return updateBankCardTerminalConfig(terminalConfig);
    }

    @Override
    public List<Map> getTerminalLaklaConfigBatch(String merchantId ,List<String> terminalIds) {
        List<Map>result = new ArrayList<>();
        if(StringUtil.empty(merchantId) || terminalIds == null || terminalIds.isEmpty()){
            throw new CoreInvalidParameterException("必传参数不能为空");
        }
        Map config = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_BANKCARD);
        if(config == null){
            throw new CoreInvalidParameterException("商户号配置不存在");
        }
        String mercId = BeanUtil.getPropString(config, LAKALA_MCHR_ID_KEY);
        if(!StringUtil.empty(mercId)) {
            Iterator<Map<String, Object>> all = terminalConfigDao.filter(Criteria.where(TerminalConfig.TERMINAL_ID).in(terminalIds).with(TerminalConfig.PAYWAY).is(PAYWAY_BANKCARD)).fetchAll();
            List<Map<String,Object>> terminalConfigs = CollectionUtil.iterator2list(all);
            for (Map<String, Object> map : terminalConfigs) {
                Map<String,Object> record = new HashMap<>();
                String terminalId = BeanUtil.getPropString(map,TerminalConfig.TERMINAL_ID);
                String termId = BeanUtil.getPropString(map, LAKALA_TERM_ID_KEY);
                String termNo = BeanUtil.getPropString(map, LAKALA_TERM_NO_KEY);
                if(!StringUtil.empty(termId)){
                    record.put(DaoConstants.ID, terminalId);
                    record.put(TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID, termNo);
                    record.put(TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID, mercId);
                }
                result.add(record);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> getBankcardFee(String merchantSn, Integer payway) {
        Map<String,Object>result = null;
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if(merchant == null){
            throw new CoreInvalidParameterException("商户号不存在");
        }
        String merchantId = BeanUtil.getPropString(merchant,DaoConstants.ID);
        Map config = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if(MapUtils.isNotEmpty(config)){
            Object feeConfig =  BeanUtil.getNestedProperty(config,MerchantConfig.PARAMS + "." + TransactionParam.PARAMS_BANKCARD_FEE);
            if(feeConfig!=null && feeConfig instanceof Map){
                result = (Map)feeConfig;
            }
        }
        return result;
    }

    @Override
    public List<MerchantAvailablePaywaysQueryResult> queryMerchantAvailablePayways(MerchantAvailablePaywaysQueryRequest request) {
        String merchantId = request.getMerchantId();
        Integer subPayway = request.getSubPayway();
        if (Objects.isNull(merchantId)) {
            throw new CoreInvalidParameterException("商户ID不能为空");
        }
        if (Objects.isNull(subPayway)) {
            throw new CoreInvalidParameterException("子支付方式不能为空");
        }

        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .withAnd(Criteria.where(subPaywayStatusColName.get(Integer.toString(subPayway)))
                        .is(TransactionParam.STATUS_OPENED));
        List<Map<String, Object>> configs = CollectionUtil.iterator2list(merchantConfigDao.filter(criteria).fetchAll());
        List<MerchantAvailablePaywaysQueryResult> resultList;
        if (CollectionUtils.isEmpty(configs)) {
            resultList = Lists.newArrayListWithCapacity(0);
        } else {
            resultList = Lists.newArrayListWithCapacity(configs.size());
            MerchantAvailablePaywaysQueryResult queryResult;
            for (Map<String, Object> config : configs) {
                queryResult = new MerchantAvailablePaywaysQueryResult();
                queryResult.setPayway(MapUtils.getInteger(config, MerchantConfig.PAYWAY));
                queryResult.setProvider(MapUtils.getInteger(config, MerchantConfig.PROVIDER));
                queryResult.setParams(MapUtils.getMap(config, MerchantConfig.PARAMS));
                resultList.add(queryResult);
            }
        }

        return resultList;
    }

    @Override
    public Map<String, Object> updateFitnessMerchantAppConfig(Map params) {
        String appId = ApolloConfigurationCenterUtil.getFitnessAppId();
        String merchantId = MapUtils.getString(params, MerchantAppConfig.MERCHANT_ID);
        Map<String, Object> config = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, PAYWAY_ALIPAY2, appId);

        String shopId = MapUtils.getString(params, TransactionParam.FITNESS_PARAMS_ALIPAY_SHOP_ID);
        String settleFeeRate = MapUtils.getString(params, TransactionParam.FEE_RATE);
        String merchantPid = MapUtils.getString(params, TransactionParam.FITNESS_PARAMS_ALIPAY_MERCHANT_PID);
        Map<String, Object> fitnessParams = new HashMap<>(ApolloConfigurationCenterUtil.getDefaultFitnessParams());
        fitnessParams.put(TransactionParam.FITNESS_PARAMS_ALIPAY_PID, merchantPid);
        fitnessParams.put(TransactionParam.FITNESS_PARAMS_ALIPAY_SHOP_ID, shopId);
        fitnessParams.put(TransactionParam.FITNESS_PARAMS_ALIPAY_MERCHANT_PID, merchantPid);
        fitnessParams.put(TransactionParam.FITNESS_PARAMS_SETTLE_FEE_RATE, settleFeeRate);
        if (config != null) {
            Map<String, Object> upsertParams = new HashMap<>();
            Map beforeParams = MapUtils.getMap(config, MerchantAppConfig.PARAMS, new HashMap());
            Map beforeFitnessParams = MapUtils.getMap(beforeParams, TransactionParam.FITNESS_PARAMS, new HashMap());
            beforeFitnessParams.putAll(fitnessParams);
            beforeParams.put(TransactionParam.FITNESS_PARAMS, beforeFitnessParams);
            upsertParams.put(MerchantAppConfig.PARAMS, beforeParams);
            upsertParams.put(DaoConstants.ID, MapUtils.getString(config, DaoConstants.ID));
            return updateMerchantAppConfig(upsertParams);
        } else {
            Map<String, Object> insertParams = new HashMap<>();
            insertParams.put(MerchantAppConfig.WAP_STATUS, 1);
            insertParams.put(MerchantAppConfig.MINI_STATUS, 1);
            insertParams.put(MerchantAppConfig.PAYWAY, PAYWAY_ALIPAY2);
            insertParams.put(MerchantAppConfig.MERCHANT_ID, merchantId);
            insertParams.put(MerchantAppConfig.APP_ID, appId);
            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put(TransactionParam.FITNESS_PARAMS, fitnessParams);
            insertParams.put(MerchantAppConfig.PARAMS, paramsMap);
            Map result = createMerchantAppConfig(insertParams);
            updateFitnessMerchantSwitch(merchantId, 1);
            return result;
        }
    }

    @Override
    public boolean updateFitnessShopId(Map params) {
        String appId = ApolloConfigurationCenterUtil.getFitnessAppId();
        String merchantId = MapUtils.getString(params, MerchantAppConfig.MERCHANT_ID);
        Map<String, Object> config = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, PAYWAY_ALIPAY2, appId);
        String shopId = MapUtils.getString(params, TransactionParam.FITNESS_PARAMS_ALIPAY_SHOP_ID);
        if (config == null) {
            return false;
        } else {
            Map configParams = MapUtils.getMap(config, MerchantAppConfig.PARAMS);
            if (configParams == null) {
                return false;
            }
            Map fitnessParams = MapUtils.getMap(configParams, TransactionParam.FITNESS_PARAMS);
            if (fitnessParams == null) {
                return false;
            }
            fitnessParams.put(TransactionParam.FITNESS_PARAMS_ALIPAY_SHOP_ID, shopId);
            Map<String, Object> upsertParams = new HashMap<>();
            upsertParams.put(DaoConstants.ID, MapUtils.getString(config, DaoConstants.ID));
            upsertParams.put(MerchantAppConfig.PARAMS, configParams);
            updateMerchantAppConfig(upsertParams);
            return true;
        }
    }

    @Override
    public Map<String, Object> updateFitnessMerchantSwitch(String merchantId, int status) {
        String appId = ApolloConfigurationCenterUtil.getFitnessAppId();
        Map<String, Object> baseConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_ALIPAY2);
        Map<String, Object> merchantAppConfig = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, PAYWAY_ALIPAY2, appId);
        if (baseConfig == null || merchantAppConfig == null) {
            throw new CoreInvalidParameterException("商户参数异常");
        }
        Map params = MapUtils.getMap(baseConfig, MerchantAppConfig.PARAMS, new HashMap());
        params.put(TransactionParam.FITNESS_STATUS, status);
        Map<String,Object> update = new HashMap<>();
        update.put(DaoConstants.ID, MapUtils.getString(baseConfig, DaoConstants.ID));
        update.put(MerchantAppConfig.PARAMS, params);
        return updateMerchantConfig(update);
    }

    @Override
    public void updateFitnessProducts(String merchantId, List<Map> products, boolean exclusive) {
        Map fitnessParams = new HashMap();
        fitnessParams.put(TransactionParam.FITNESS_PARAMS_PRODUCTS, products);
        fitnessParams.put(TransactionParam.FITNESS_PARAMS_EXCLUSIVE, exclusive);
        upsertFitnessParams(merchantId, fitnessParams);
    }

    @Override
    public void upsertFitnessParams(String merchantId, Map upsertParams) {
        String appId = ApolloConfigurationCenterUtil.getFitnessAppId();
        Map<String, Object> merchantAppConfig = getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, PAYWAY_ALIPAY2, appId);
        if (merchantAppConfig == null) {
            throw new CoreInvalidParameterException("商户未开通先享后付");
        }
        Map params = MapUtils.getMap(merchantAppConfig, MerchantAppConfig.PARAMS);
        if (params == null) {
            throw new CoreInvalidParameterException("商户参数异常");
        }
        Map fitnessParams = MapUtils.getMap(params, TransactionParam.FITNESS_PARAMS);
        if (fitnessParams == null) {
            throw new CoreInvalidParameterException("商户参数异常");
        }
        fitnessParams.putAll(upsertParams);
        Map<String, Object> update = new HashMap<>();
        update.put(MerchantAppConfig.PARAMS, params);
        update.put(DaoConstants.ID, MapUtils.getString(merchantAppConfig, DaoConstants.ID));
        updateMerchantAppConfig(update);
    }

    @Override
    public void createTradeAppConfig(TradeAppConfigCreateRequest request) {
        Map<String,Object> model = new LinkedHashMap<>();
        model.put(DaoConstants.ID, uuidGenerator.nextUuid());
        model.put(TradeAppConfig.KEY_TRADE_APP, request.getTradeApp());
        model.put(TradeAppConfig.KEY_BIZ_MODELS, com.wosai.mpay.util.JsonUtil.toJsonStr(request.getBizModels()));
        model.put(TradeAppConfig.KEY_PAY_PATHS, com.wosai.mpay.util.JsonUtil.toJsonStr(request.getPayPaths()));
        model.put(TradeAppConfig.KEY_BIZ_MODEL_TRADE_APP, com.wosai.mpay.util.JsonUtil.toJsonStr(request.getBizModelTradeApp()));
        model.put(TradeAppConfig.KEY_SUBJECT_RULE_TYPE, request.getSubjectRuleType());
        model.put(TradeAppConfig.KEY_SUBJECT_RULE, com.wosai.mpay.util.JsonUtil.toJsonStr(request.getSubjectRule()));
        model.put(TradeAppConfig.KEY_TRANSACTION_JUMP_URL, request.getTransactionJumpUrl());
        model.put(DaoConstants.CTIME, System.currentTimeMillis());
        dataRepository.getTradeAppConfigDao().save(model);
    }

    @Override
    public List<TradeAppConfig> getAllTradeAppConfig() {
        List<Map<String, Object>> records = CollectionUtil.iterator2list(dataRepository.getTradeAppConfigDao().filter(Criteria.where(DaoConstants.DELETED).is(0)).fetchAll());
        return records.stream().map(record -> {
            TradeAppConfig config = new TradeAppConfig();
            config.setTradeApp(BeanUtil.getPropInt(record, TradeAppConfig.KEY_TRADE_APP));
            config.setBizModels(com.wosai.mpay.util.JsonUtil.jsonStrToObject(BeanUtil.getPropString(record, TradeAppConfig.KEY_BIZ_MODELS), Set.class));
            config.setPayPaths(com.wosai.mpay.util.JsonUtil.jsonStrToObject(BeanUtil.getPropString(record, TradeAppConfig.KEY_PAY_PATHS), Set.class));
            config.setBizModelTradeApp(com.wosai.mpay.util.JsonUtil.jsonStrToObject(BeanUtil.getPropString(record, TradeAppConfig.KEY_BIZ_MODEL_TRADE_APP), Map.class));
            config.setSubjectRuleType(BeanUtil.getPropInt(record, TradeAppConfig.KEY_SUBJECT_RULE_TYPE));
            config.setSubjectRule(com.wosai.mpay.util.JsonUtil.jsonStrToObject(BeanUtil.getPropString(record, TradeAppConfig.KEY_SUBJECT_RULE), Map.class));
            config.setTransactionJumpUrl(BeanUtil.getPropString(record, TradeAppConfig.KEY_TRANSACTION_JUMP_URL));
            return config;
        }).collect(Collectors.toList());
    }

    @Override
    public Map updateBankCardMerchantConfig(Map merchantConfig) {
        int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY, PAYWAY_BANKCARD);
        if (payway != PAYWAY_BANKCARD) {
            throw new CoreInvalidParameterException("收款通道有误");
        }
        String merchantId = BeanUtil.getPropString(merchantConfig, MerchantConfig.MERCHANT_ID);
        Map before = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        if (before == null) {
            //开通商户银行卡通道
            before = initMerchantConfig(merchantId, payway);
        }
        checkConfig(merchantConfig);
        Map<String, Object> afterParams = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
        String id = BeanUtil.getPropString(before, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> updateParamsFunc = (merchantConfigParams) -> {
            mergeParams(merchantConfigParams, afterParams, false, true);
            merchantConfigParams.putAll(afterParams);
            return merchantConfigParams;
        };
        MapUtil.removeKeys(merchantConfig, new String[]{DaoConstants.DELETED, DaoConstants.VERSION, DaoConstants.MTIME, MerchantConfig.PARAMS});
        updateMerchantConfigWithRetry(id, merchantConfig, updateParamsFunc);
        Map updateResult = getMerchantConfig(id);
        try {
            merchantConfigDataBusBiz.feeRateChange(before, updateResult);
        } catch (Exception e) {
            logger.error("{} 写入事件表失败", BeanUtil.getPropString(merchantConfig, DaoConstants.ID), e);
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
        return updateResult;
    }

    @Override
    public Map updateBankCardTerminalConfig(Map terminalConfig) {
        int payway = BeanUtil.getPropInt(terminalConfig, MerchantConfig.PAYWAY, PAYWAY_BANKCARD);
        if(payway != PAYWAY_BANKCARD){
            throw new CoreInvalidParameterException("收款通道有误");
        }
        String terminalId = BeanUtil.getPropString(terminalConfig, TerminalConfig.TERMINAL_ID);
        if(StringUtil.empty(terminalId)){
            throw new CoreInvalidParameterException("terminal_id must not null");
        }
        Map config = getTerminalConfigByTerminalIdAndPayway(terminalId, payway);
        if(config == null){
            config = createTerminalConfig(terminalConfig);
        }else {
            terminalConfig.put(DaoConstants.ID, BeanUtil.getPropString(config, DaoConstants.ID));
            config = updateTerminalConfig(terminalConfig);
        }
        Map<String, Object> terminal = dataRepository.getTerminalDao().get(terminalId);
        if(terminal != null){
            String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
            Map merchant = merchantService.getMerchantByMerchantId(merchantId);
            redisService.removeCachedParams(BeanUtil.getPropString(merchant,Merchant.SN));
        }
        return config;
    }

    @Override
    public List<Map> listTerminalConfigByTerminalIds(String merchantId, List<String> terminalIds) {
        List<Map> result = new ArrayList<>();
        if (StringUtil.empty(merchantId) || terminalIds == null || terminalIds.isEmpty()) {
            throw new CoreInvalidParameterException("必传参数不能为空");
        }
        Map config = getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY_BANKCARD);
        if (config == null) {
            throw new CoreInvalidParameterException("商户号配置不存在");
        }
        String mchIdKey = null;
        String termIdKey = null;
        Integer provider = MapUtils.getInteger(config, MerchantConfig.PROVIDER);
        if (Objects.equals(Provider.FUYOU.getCode(), provider)) {
            mchIdKey = FUYOU_MCHR_ID_KEY;
            termIdKey = FUYOU_TERM_ID_KEY;
        } else if (Objects.equals(Provider.LAKALA_UNION_PAY_V3.getCode(), provider)) {
            mchIdKey = LAKALA_MCHR_ID_KEY;
            //拉卡拉测交易和退款用的分别是终端号和终端id，spa测需要展示的是term_no
            termIdKey = LAKALA_TERM_NO_KEY;
        } else if (Objects.equals(Provider.TL_SYB.getCode(), provider)) {
            mchIdKey = SYB_MCHR_ID_KEY;
            termIdKey = SYB_TERM_ID_KEY;
        }
        if(!StringUtil.empty(mchIdKey)){
            String mchId = BeanUtil.getPropString(config, mchIdKey);
            if(!StringUtil.empty(mchId)) {
                Iterator<Map<String, Object>> all = terminalConfigDao.filter(Criteria.where(TerminalConfig.TERMINAL_ID).in(terminalIds).with(TerminalConfig.PAYWAY).is(PAYWAY_BANKCARD)).fetchAll();
                List<Map<String, Object>> terminalConfigs = CollectionUtil.iterator2list(all);
                Map<String, String> terminalIdMapping = new HashMap<>();
                if (!StringUtil.empty(termIdKey) && CollectionUtils.isNotEmpty(terminalConfigs)) {
                    String finalTermIdKey = termIdKey;
                    terminalIdMapping = terminalConfigs.stream().collect(
                            Collectors.toMap(
                                    terminalConfig -> MapUtils.getString(terminalConfig, TerminalConfig.TERMINAL_ID),
                                    terminalConfig -> BeanUtil.getPropString(terminalConfig, finalTermIdKey)));
                }
                for (String terminalId : terminalIds) {
                    Map<String, Object> record = new HashMap<>();
                    record.put(DaoConstants.ID, terminalId);
                    record.put(TransactionParam.PROVIDER_MCH_ID, mchId);
                    record.put(MerchantConfig.PROVIDER, provider);
                    String termId = null;
                    if (terminalIdMapping != null) {
                        termId = terminalIdMapping.get(terminalId);
                    }
                    record.put(TransactionParam.TRADE_EXT_TERM_INFO_TERM_ID, termId);
                    result.add(record);
                }
            }
        }
        return result;
    }

    @Override
    public Map queryMerchantPaywayLimit(String merchantId) {

        Map<String, Object> result = new HashMap<>();

        Map<String, String> merchantSingleTranLimitMap = new HashMap();

        Map<String, String> merchantDayTranLimitMap = new HashMap();


        Map merchantTradeValidateParams = getMerchantTradeValidateParams(merchantId);

        //云闪付境外钱包
        String unionOverSeasWalletSingleTranLimit = com.wosai.pantheon.util.MapUtil.getString(merchantTradeValidateParams, TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT);
        if (!Objects.isNull(unionOverSeasWalletSingleTranLimit)) {
            merchantSingleTranLimitMap.put(TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT, unionOverSeasWalletSingleTranLimit);

        }
        String unionOverSeasWalletDayTranLimit = com.wosai.pantheon.util.MapUtil.getString(merchantTradeValidateParams, TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT);
        if (!Objects.isNull(unionOverSeasWalletDayTranLimit)) {
            merchantDayTranLimitMap.put(TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT, unionOverSeasWalletDayTranLimit);
        }

        Map<String, Object> merchantSingleMaxOfTran = com.wosai.pantheon.util.MapUtil.getMap(merchantTradeValidateParams, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN);
        //单笔，
        if (!MapUtils.isEmpty(merchantSingleMaxOfTran)) {
            for (Map.Entry<String, Object> entry : merchantSingleMaxOfTran.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                //校验类型
                if (value instanceof String) {
                    merchantSingleTranLimitMap.put(key, (String) value);
                } else if (value instanceof Map) {
                    merchantSingleTranLimitMap.put(key, com.wosai.pantheon.util.MapUtil.getString((Map) value, SubPayway.BARCODE.getCode() + ""));
                }
            }

        }
        Map<String, Object> merchantDailyPaywayMaxSumOfTrans = com.wosai.pantheon.util.MapUtil.getMap(merchantTradeValidateParams, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);
        if (!MapUtils.isEmpty(merchantDailyPaywayMaxSumOfTrans)) {
            for (Map.Entry<String, Object> entry : merchantDailyPaywayMaxSumOfTrans.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                //key 为 "" 就是默认，不区分到二级支付方式
                merchantDayTranLimitMap.put(key, com.wosai.pantheon.util.MapUtil.getString((Map) value, ""));
            }

        }

        result.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, merchantSingleTranLimitMap);
        result.put(TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, merchantDayTranLimitMap);

        return result;
    }



    private Map<String, Object> makePayWayTranLimitMapTemplate(String payway, Object quota) {
        return CollectionUtil.hashMap(payway, new HashMap() {
            {
                put(SubPayway.BARCODE.getCode() + "", quota);
                put(SubPayway.QRCODE.getCode() + "", quota);
                put(SubPayway.WAP.getCode() + "", quota);
                put(SubPayway.MINI.getCode() + "", quota);
            }
        });

    }


    @Override
    public void updateMerchantPaywayLimit(String merchantId, String key, int type, Integer quota) {

        Map newMerchantTradeValidateParams = new HashMap();

        Map merchantTradeValidateParams = getMerchantTradeValidateParams(merchantId);
        //云闪付境外特殊处理
        if (Objects.equals(key, TransactionParam.UNION_OVER_SEAS_WALLET_SINGLE_TRAN_LIMIT) || Objects.equals(key, TransactionParam.UNION_OVER_SEAS_WALLET_DAY_TRAN_LIMIT)) {
            newMerchantTradeValidateParams.put(key, quota);
        } else {
            //支付源限额
            String limit = quota + "";
            if(quota == null){
                limit = null;
            }
            //payway单笔
            if (type == CoreCommonConstants.SINGLE) {
                Map<String, Object> paywaySingleLimit = null;
                Object merchantSingleMaxOfTran = BeanUtil.getProperty(merchantTradeValidateParams, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN);
                if (merchantSingleMaxOfTran == null) {
                    //新增配置 默认四个二级支付方式
                    paywaySingleLimit = makePayWayTranLimitMapTemplate(key, quota);
                } else {
                    if (merchantSingleMaxOfTran instanceof String) {
                        //旧版本string全部转换成map，单笔限额转换为payway 2,3,17 及子支付方式为1,2,3,4的限额 旧版本转新版本，默认三个限额
                        paywaySingleLimit = makePayWayTranLimitMapTemplate(Payway.ALIPAY2.getCode() + "" , merchantSingleMaxOfTran);
                        paywaySingleLimit.putAll(makePayWayTranLimitMapTemplate(Payway.WEIXIN.getCode() + "", merchantSingleMaxOfTran));
                        paywaySingleLimit.putAll(makePayWayTranLimitMapTemplate(Payway.UNIONPAY.getCode() + "", merchantSingleMaxOfTran));
                        //更新
                        paywaySingleLimit.putAll(makePayWayTranLimitMapTemplate(key, quota));

                    } else if (merchantSingleMaxOfTran instanceof Map) {
                        //直接update ,相当于原所有payway下，subpayway的也全部覆盖了
                        ((Map<String, Object>) merchantSingleMaxOfTran).putAll(makePayWayTranLimitMapTemplate(key,quota));
                        paywaySingleLimit = (Map<String, Object>) merchantSingleMaxOfTran;
                    }
                }
                newMerchantTradeValidateParams.put(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, paywaySingleLimit);
                //单日
            } else if (type == CoreCommonConstants.DAY) {
                Map paywayDayTranLimit = (Map) BeanUtil.getProperty(merchantTradeValidateParams, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);
                if (Objects.isNull(paywayDayTranLimit)) {
                    paywayDayTranLimit = new HashMap();
                }
                newMerchantTradeValidateParams.put(TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, paywayDayTranLimit);
                //更新，就是全部覆盖
                Map<String, Object> paywayDayLimitMap = new HashMap<>();
                paywayDayTranLimit.put(key, paywayDayLimitMap);
                //""是取payway限额，其他 1,2等Key 是其他二级方式限额
                paywayDayLimitMap.put("", limit);
            } else {
                return;
            }

        }

        updateMerchantTradeValidateParams(merchantId, newMerchantTradeValidateParams);
        String merchantSn = businssCommonService.getMerchantSnById(merchantId);
        SupportService support = SpringContextHolder.getBean(SupportService.class);
        //removeCachedParams
        support.removeCachedParams(merchantSn);

    }

    @Override
    public void updateMerchantPaywayLimitAndLog(String merchantId, String key, int type, Integer quota, OpLogCreateRequest opLogCreateRequest) {
        Map map = queryMerchantPaywayLimit(merchantId);
        Integer originalQuota = null;
        boolean empty = false;
        if (MapUtils.isEmpty(map) || !map.containsKey(TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN) || null == MapUtils.getObject(MapUtils.getMap(map, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN), key)) {
            //如果原先没有设置限额，则默认为无限额
            originalQuota = Integer.MAX_VALUE;
            empty = true;
        } else {
            originalQuota = MapUtils.getIntValue(MapUtils.getMap(map, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN), key);
        }

        Map<String, Object> before = new HashMap<>();
        Map<String, Object> after = new HashMap<>();
        before.put(OpLog.SINGLE_TRANSACTION_LIMIT, empty ? "" : originalQuota);
        updateMerchantPaywayLimit(merchantId, key, type, quota);
        after.put(OpLog.SINGLE_TRANSACTION_LIMIT, quota);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, merchantId, null, OpLog.MERCHANT_CONFIG_PARAMS_TEMPLATE_CODE, OpLog.MERCHANT_CONFIG_PARAMS_TABLE_NAME, OpLog.FIXED_MERCHANT_CONFIG_PARAMS_KEY_LIST, OpLog.MERCHANT_CONFIG_PARAMS_CHANGE_KEY_LIST, new HashMap(), before, after);
    }


    /**
     * 合并map
     * @param originalMap
     * @param mergeOnMap
     * @param override 如果值不同是否覆盖
     * @param classDiffOverride 类型不同的时候是否覆盖
     */
    private void mergeParams(Map<String,Object> originalMap,Map<String,Object>mergeOnMap,boolean override,boolean classDiffOverride){
        originalMap.entrySet().forEach(original->{
            String key = original.getKey();
            Object value = original.getValue();
            if(mergeOnMap.containsKey(key)){
                Object o = mergeOnMap.get(key);
                if (value instanceof Map) {
                    if(o instanceof Map){
                        mergeParams((Map) value, (Map) o, override, classDiffOverride);
                    }else {
                        if(classDiffOverride){
                            mergeOnMap.put(key,value);
                        }
                    }
                } else {
                    if(override) {
                        if (value.getClass().equals(o.getClass()) || classDiffOverride) {
                            mergeOnMap.put(key, value);
                        }
                    }
                }
            }else {
                mergeOnMap.put(key,value);
            }
        });
    }


    private int judgeCommonSwitchStatus(Map merchantConfig, int type) {
        String commonSwitch = (String) BeanUtil.getNestedProperty(merchantConfig, COMMON_SWITCH_FORMATTER);
        if (StringUtils.isEmpty(commonSwitch)) {
            return ApolloConfigurationCenterUtil.getDefaultCommonSwitchStatus(type);
        }
        char currentStatus = commonSwitch.charAt(type);
        switch (currentStatus) {
            case '0':
                return TransactionParam.STATUS_CLOSED;
            case '1':
                return TransactionParam.STATUS_OPENED;
            default:
                return ApolloConfigurationCenterUtil.getDefaultCommonSwitchStatus(type);
        }
    }

    private Map<String, String> buildFeeRateTag(Integer subPayway, Map config) {
        if (Objects.isNull(subPayway)) {
            return null;
        }
        String subPaywayTagKey = TransactionParam.FEE_RATE_TAG + "." + subPayway;
        Object subPaywayTag = BeanUtil.getNestedProperty(config, subPaywayTagKey);
        if (Objects.isNull(subPaywayTag)) {
            subPaywayTag = "";
        }
        return CollectionUtil.hashMap(
                subPayway.toString(), subPaywayTag
        );
    }

    private Map<String, String> buildLadderFeeRateTag(Integer subPayway
            , Map config) {
        if (Objects.isNull(subPayway)) {
            return null;
        }
        String subPaywayTagKey = MerchantConfig.LADDER_FEE_RATE_TAG + "." + subPayway;
        Object subPaywayTag = BeanUtil.getNestedProperty(config, subPaywayTagKey);
        if (Objects.isNull(subPaywayTag)) {
            subPaywayTag = "";
        }
        return CollectionUtil.hashMap(
                    subPayway.toString(), subPaywayTag
                );
    }

    private Map<String, String> buildChannelFeeRateTag(Integer subPayway
            , Map config) {
        if (Objects.isNull(subPayway)) {
            return null;
        }
        String subPaywayTagKey = MerchantConfig.CHANNEL_FEE_RATE_TAG + "." + subPayway;
        Object subPaywayTag = BeanUtil.getNestedProperty(config, subPaywayTagKey);
        if (Objects.isNull(subPaywayTag)) {
            subPaywayTag = "";
        }
        return CollectionUtil.hashMap(
                subPayway.toString(), subPaywayTag
        );
    }

    private String getFeeRate(Map config, Integer subPayway) {
        if (Objects.isNull(subPayway)) {
            return null;
        }
        if (subPayway == SUB_PAYWAY_BARCODE) {
            return BeanUtil.getPropString(config, TerminalConfig.B2C_FEE_RATE);
        } else if (subPayway == SUB_PAYWAY_QRCODE) {
            return BeanUtil.getPropString(config, TerminalConfig.C2B_FEE_RATE);
        } else if (subPayway == SUB_PAYWAY_WAP) {
            return BeanUtil.getPropString(config, TerminalConfig.WAP_FEE_RATE);
        } else if (subPayway == SUB_PAYWAY_MINI) {
            return BeanUtil.getPropString(config, TerminalConfig.MINI_FEE_RATE);
        }
        return null;
    }

    private void saveMerchantConfig(Map<String,Object> merchantConfig){
        //由于有的场景，更新也会用save方法来实现，故此处也需要查询修改之前的数据
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Map<String, Object> before = merchantConfigDao.get(id);
        merchantConfigDao.save(merchantConfig);
        sendMerchantConfigChangeEvent(before, id);
    }

    private void updateMerchantConfigWithRetry(String id, Map<String, Object> basicChange, Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc){
        if (com.wosai.pantheon.util.MapUtil.isNotEmpty(basicChange)) {
            basicChange.remove(MerchantConfig.PARAMS);
            basicChange.remove(DaoConstants.VERSION);
            basicChange.remove(DaoConstants.ID);
            basicChange.remove(DaoConstants.MTIME);
        } else if (basicChange == null) {
            basicChange = Collections.emptyMap();
        }
        for (int i = 0; i< 3; i++) {
            try {
                Map<String, Object> before = merchantConfigDao.get(id);
                if (before == null) {
                    throw new CoreDataObjectNotExistsException("配置不存在");
                }
                Map<String, Object> updatePart = com.wosai.pantheon.util.MapUtil.copyInclusive(before, DaoConstants.ID, DaoConstants.VERSION);
                updatePart.putAll(basicChange);
                if (paramsChangeFunc != null) {
                    Map<String, Object> copyBeforeParams = com.wosai.pantheon.util.MapUtil.getMap(before, MerchantConfig.PARAMS);
                    if (copyBeforeParams == null) {
                        copyBeforeParams = new HashMap<String, Object>();
                    } else {
                        copyBeforeParams = JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(copyBeforeParams), Map.class);
                        if (copyBeforeParams == null) {
                            copyBeforeParams = com.wosai.pantheon.util.MapUtil.getMap(before, MerchantConfig.PARAMS);
                        }
                    }
                    updatePart.put(MerchantConfig.PARAMS, paramsChangeFunc.apply(copyBeforeParams));
                }
                merchantConfigDao.updatePart(updatePart);
                sendMerchantConfigChangeEvent(before, id);
                bizLogFacade.safeSendMerchantConfigParamsUpdate(com.wosai.pantheon.util.MapUtil.getMap(before, MerchantConfig.PARAMS), id);
                return;
            } catch (DaoVersionMismatchException e) {
                continue;
            }
        }
        throw new DaoVersionMismatchException("incorrect version.");
    }

    private void updateMerchantConfigWithRetry(String id, Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc){
        updateMerchantConfigWithRetry(id, Collections.emptyMap(), paramsChangeFunc);
    }

    private void updateMerchantConfigWithRetry(Map<String, Object> merchantConfigUpdate, Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc){
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfigUpdate, DaoConstants.ID);
        updateMerchantConfigWithRetry(id, merchantConfigUpdate, paramsChangeFunc);
    }

    private void updateMerchantConfigWithRetry(Map<String, Object> merchantConfigUpdate){
        String id = com.wosai.pantheon.util.MapUtil.getString(merchantConfigUpdate, DaoConstants.ID);
        Map<String, Object> params = (Map<String, Object>) merchantConfigUpdate.remove(MerchantConfig.PARAMS);
        updateMerchantConfigWithRetry(id, merchantConfigUpdate, params != null ? (_params) -> params : null);
    }

    private void sendMerchantConfigChangeEvent(Map<String,Object> before, String merchantConfigId){
        Map<String, Object> after = merchantConfigDao.get(merchantConfigId);
        try{
            merchantConfigDataBusBiz.merchantConfigChange(before, after);
        }catch (Exception e){
            logger.error("{} 写入事件表失败", merchantConfigId, e);
        }
    }

    /**
     * 发送商户的多业务配置删除事件
     *
     * @param needDeleteMerchantAppConfig 需要删除的多业务的商户配置
     */
    private void sendMerchantAppConfigDeleteEvent(Map<String,Object> needDeleteMerchantAppConfig) {
        String merchantId = BeanUtil.getPropString(needDeleteMerchantAppConfig, MerchantConfig.MERCHANT_ID);
        Integer payWay = BeanUtil.getPropInt(needDeleteMerchantAppConfig, MerchantConfig.PAYWAY);
        //商户的多业务配置删除后，使用基础业务的配置
        Map<String, Object> baseMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        try{
            merchantConfigDataBusBiz.merchantAppConfigChange(needDeleteMerchantAppConfig, baseMerchantConfig);
        }catch (Exception e){
            logger.error("发送多业务商户配置删除事件失败, needDeleteMerchantAppConfig={}, baseMerchantConfig={}",
                    JsonUtil.toJsonStr(needDeleteMerchantAppConfig), JsonUtil.toJsonStr(baseMerchantConfig), e);
        }
    }

    /**
     * 发送多业务的商户配置变更事件
     *
     * @param before
     */
    private void sendMerchantAppConfigChangeEvent(Map<String,Object> before) {
        String merchantAppConfigId = BeanUtil.getPropString(before, DaoConstants.ID);
        Map<String, Object> after = merchantAppConfigDao.get(merchantAppConfigId);
        try{
            merchantConfigDataBusBiz.merchantAppConfigChange(before, after);
        }catch (Exception e){
            logger.error("发送多业务商户配置变更事件失败, before={}, after={}",
                    JsonUtil.toJsonStr(before), JsonUtil.toJsonStr(after), e);
        }
    }

    /**
     * 根据配置重置并获取payway
     *
     * @param payway     支付源
     * @param merchantId 商户ID
     * @param storeId 门店ID
     * @return
     */
    private int resetUnionPayway(int payway, String merchantId, String storeId) {
        //支付源编码不在转换列表内，忽略转换
        List<Integer> paywayList = Lists.newArrayList(Payway.CCB_APP.getCode(), Payway.CMB_APP.getCode());
        if (!paywayList.contains(payway)) {
            return payway;
        }
        //先查询门店层级的配置，没有则查询商户层级的配置
        Map config = getStoreConfigByStoreIdAndPayway(storeId, payway);
        if(MapUtils.isEmpty(config)){
            config = getMerchantConfigByMerchantIdAndPayway(merchantId, payway);
        }
        //支付源配置不存在，转换为云闪付
        if (MapUtils.isEmpty(config)) {
            payway = TradeConfigService.PAYWAY_UNIONPAY;
        }
        return payway;
    }

    /**
     * 获取命中payway
     *
     * @param payway 当前payway
     * @param originPayway 原始payway
     * @return
     */
    private String getHitPayway(int payway, int originPayway) {
        //当前payway是从建行或招行转换为云闪付，才返回命中payway给上游
        if (Payway.UNIONPAY.getCode().equals(payway)
                && (Payway.CCB_APP.getCode().equals(originPayway)
                || Payway.CMB_APP.getCode().equals(originPayway))) {
            return payway + "";
        }
        return null;
    }

    public static Map<String, Object> removeNullValues(Map<String, Object> map) {
        map.entrySet().removeIf(entry -> {
            Object value = entry.getValue();
            if (value == null) {
                return true;
            } else if (value instanceof Map) {
                Map<?, ?> subMap = (Map<?, ?>) value;
                subMap = removeNullValues((Map<String, Object>) subMap);
                entry.setValue(subMap);
                return subMap.isEmpty();
            } else {
                return false;
            }
        });
        return map;
    }

    @Override
    public AllMerchantConfigResponse queryAllMerchantConfigs(String merchantSn) {
        Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
        if (null == merchant) {
            throw new CoreMerchantNotExistsException("商户不存在");
        }

        AllMerchantConfigResponse response = new AllMerchantConfigResponse();

        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        //查询基础业务的商户配置
        List baseMerchantConfigs = getMerchantConfigsByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(baseMerchantConfigs)) {
            throw new CoreMerchantConfigAbnormalException("未查到商户基础业务的费率配置");
        }
        //填充清算通道
        fillWithClearanceProvider(baseMerchantConfigs);
        response.setBaseMerchantConfigs(baseMerchantConfigs);

        //查询多业务的商户配置
        List appMerchantConfigs = getMerchantAppConfigsByMerchantId(merchantId);
        if (CollectionUtils.isNotEmpty(appMerchantConfigs)) {
            //填充清算通道
            fillWithClearanceProvider(appMerchantConfigs);
            response.setAppMerchantConfigs(appMerchantConfigs);
        }
        return response;
    }

    /**
     * 填充清算通道
     */
    private void fillWithClearanceProvider(List<Map> merchantConfigs) {
        merchantConfigs.forEach(merchantConfig -> {
            Integer provider = MapUtils.getInteger(merchantConfig, MerchantConfig.PROVIDER);
            Integer payWay = MapUtils.getInteger(merchantConfig, MerchantConfig.PAYWAY);
            if(null == provider) {
                return;
            }
            Integer clearanceProvider = cacheService.getClearanceProviderWithCache(ProviderAbilityRequest.builder()
                            .provider(provider)
                            .payway(payWay)
                            .build());
            merchantConfig.put(ProviderAbility.CLEARANCE_PROVIDER, clearanceProvider);
        });
    }

    @Override
    public void updateMerchantSFTBrandInfo(UpdateMerchantSFTBrandInfoRequest request) {
        if (request == null || StringUtil.empty(request.getMerchantSn())) {
            throw new CoreInvalidParameterException("merchant_sn 不能为空");
        }
        String merchantId = businssCommonService.getMerchantIdBySn(request.getMerchantSn());
        Map<String, Object> defaultMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        Map<String, Object> params = com.wosai.pantheon.util.MapUtil.getMap(defaultMerchantConfig, MerchantConfig.PARAMS);
        if (defaultMerchantConfig == null || params == null) {
            throw new CoreMerchantConfigAbnormalException("商户交易参数异常");
        }
        params.put(TransactionParam.SFT_BRAND_ID, request.getBrandId());
        if (StringUtil.empty(request.getBrandId())) {
            params.remove(TransactionParam.SFT_BRAND_ID);
        }
        updateMerchantConfigWithoutMessage(com.wosai.pantheon.util.MapUtil.hashMap(DaoConstants.ID, defaultMerchantConfig.get(DaoConstants.ID),
                MerchantConfig.PARAMS, params,
                DaoConstants.VERSION, defaultMerchantConfig.get(DaoConstants.VERSION)));
        removeCachedParamsByMerchantId(merchantId);
    }

    @Override
    public void updateMerchantSwitchMchTime(String merchantId, Integer clearanceProvider, long successSwitchTime) {
        if (StringUtil.empty(merchantId) || clearanceProvider == null) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(null)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (merchantConfig == null) {
            throw new CoreInvalidParameterException("商户交易参数有误，配置失败");
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);

        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            Map<String, Long> switchMchTime = new HashMap();
            if (merchantConfigParams.containsKey(TransactionParam.PARAMS_SWITCH_MCH_TIME)) {
                switchMchTime = MapUtils.getMap(merchantConfigParams, TransactionParam.PARAMS_SWITCH_MCH_TIME, new HashMap());
            }
            switchMchTime.put(String.valueOf(clearanceProvider), successSwitchTime);
            merchantConfigParams.put(TransactionParam.PARAMS_SWITCH_MCH_TIME, switchMchTime);
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        Map<String, Object> merchant = merchantDao.filter(Criteria.where(DaoConstants.ID).is(merchantId)).fetchOne();
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
        redisService.removeCacheSwitchMchTime(merchantId);
    }

    @Override
    public Map getMerchantSwitchMchTime(String merchantId) {
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(null)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (merchantConfig == null) {
            merchantConfig = new HashMap<>();
        }
        @SuppressWarnings("unchecked")
        Map<String, Object> params = (Map<String, Object>) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap<>();
            merchantConfig.put(MerchantConfig.PARAMS, params);
        }
        Map result = MapUtils.getMap(params, TransactionParam.PARAMS_SWITCH_MCH_TIME, new HashMap());
        return result;
    }

    @Override
    public void deleteTradeExtConfig(TradeExtConfigRemoveRequest request) {
        String merchantSn = request.getMerchantSn();
        Integer provider = request.getProvider();
        List<Integer> snTypes = request.getSnTypes();
        if (StringUtils.isEmpty(merchantSn) || provider == null || CollectionUtils.isEmpty(snTypes)) {
            throw new CoreInvalidParameterException("必填参数不能为空");
        }
        String providerMchId = request.getProviderMchId();
        if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_PROVIDER_MCH) || snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_STORE_SUB_MCH) || snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_MERCHANT_SUB_MCH)) {
            if (StringUtils.isEmpty(providerMchId)) {
                throw new CoreInvalidParameterException("必填参数不能为空");
            }
        }
        List<String> querySns = new ArrayList<>();
        String merchantId = businssCommonService.getMerchantIdBySn(merchantSn);
        if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_MERCHANT)) {
            querySns.add(merchantSn);
        }

        if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_MERCHANT_SUB_MCH)) {
            querySns.add(String.format("%s:%s", merchantSn, providerMchId));
        }

        final int limit = ApolloConfigurationCenterUtil.getDeleteTradeExtConfigThreshold(800);
        if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_STORE) || snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_STORE_SUB_MCH)) {
            List<String> storeSns = new LinkedList<>();
            String storeSn = request.getStoreSn();
            if (!StringUtils.isEmpty(storeSn)) {
                storeSns.add(storeSn);
            } else {
                //提供给小微升级商户用 小微商户门店正常不会多
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPage(1);
                pageInfo.setPageSize(limit);
                Criteria criteria = Criteria.where(Store.MERCHANT_ID).is(merchantId);
                SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
                SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
                Filter filter = storeDao.filter(criteria, Arrays.asList(Store.SN));
                PageInfoUtil.pagination(pageInfo, filter);
                List<Map> storeIdList = CollectionUtil.iterator2list(filter.fetchAll());
                if (WosaiCollectionUtils.isNotEmpty(storeIdList)) {
                    storeSns = storeIdList.stream().map(store -> MapUtils.getString(store, Store.SN)).distinct().collect(Collectors.toList());
                    if (storeSns.size() >= limit) {
                        throw new CoreInvalidParameterException(String.format("终端删除失败 商户%s门店超过%d个", request.getMerchantSn(), limit));
                    }
                }
            }

            if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_STORE) && CollectionUtils.isNotEmpty(storeSns)) {
                querySns.addAll(storeSns);
            }
            if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_STORE_SUB_MCH) && CollectionUtils.isNotEmpty(storeSns)) {
                for (String sn : storeSns) {
                    querySns.add(String.format("%s:%s", sn, providerMchId));
                }
            }
        }
        if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_TERMINAL)) {
            Criteria criteria = Criteria.where(Terminal.MERCHANT_ID).is(merchantId);
            Filter<Map<String, Object>> filter = dataRepository.getTerminalDao().filter(criteria, Arrays.asList(Terminal.SN));
            filter.limit(limit);
            List<Map<String, Object>> terminalList = CollectionUtil.iterator2list(filter.fetchAll());
            List<String> terminalSns = terminalList.stream().map(o -> com.wosai.pantheon.util.MapUtil.getString(o, Terminal.SN)).collect(Collectors.toList());
            if (terminalSns.size() >= limit) {
                throw new CoreInvalidParameterException(String.format("终端删除失败 商户%s终端超%d个", request.getMerchantSn(), limit));
            }
            if (CollectionUtils.isNotEmpty(terminalSns)) {
                querySns.addAll(terminalSns);
            }
        }
        if (snTypes.contains(TradeExtConfigRemoveRequest.SN_TYPE_PROVIDER_MCH)) {
            querySns.add(providerMchId);
        }

        List<Map<String, Object>> tradeExtConfigList = CollectionUtil.iterator2list(tradeExtConfigDao.filter(Criteria.where(TradeExtConfigRemoveRequest.SN).in(querySns), Arrays.asList(DaoConstants.ID, TradeExtConfigRemoveRequest.SN, TradeExtConfigRemoveRequest.SN_TYPE, TradeExtConfigRemoveRequest.PROVIDER)).fetchAll());

        List<String> deleteIds = tradeExtConfigList.stream().filter(o ->
                snTypes.contains(MapUtils.getIntValue(o, TradeExtConfigRemoveRequest.SN_TYPE, -1))
                        && Objects.equals(MapUtils.getIntValue(o, TradeExtConfigRemoveRequest.PROVIDER, -1), provider))
                .map(o -> MapUtils.getString(o, DaoConstants.ID)).collect(Collectors.toList());
        dataRepository.doInTransaction(()->{
            for (String id : deleteIds) {
                tradeExtConfigDao.delete(id);
            }
        });
        redisService.removeCachedParams(merchantSn);
    }

    @Override
    public void updateMerchantCustomBusinessData(Map<String, Object> request) {
        String merchantId = MapUtils.getString(request, MerchantConfig.MERCHANT_ID);
        List<Map<String, Object>> data = (List<Map<String, Object>>) MapUtils.getObject(request, MerchantConfig.BUSINESS_DATA);
        if (StringUtil.empty(merchantId) || CollectionUtils.isEmpty(data)) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        Map<String, Object> defaultMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (defaultMerchantConfig == null) {
            throw new CoreInvalidParameterException("商户交易参数有误，配置失败");
        }

        Map<String, Object> params = MapUtils.getMap(defaultMerchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap<>();
            defaultMerchantConfig.put(MerchantConfig.PARAMS, params);
        }

        data.forEach(item -> {
            long dataId = MapUtils.getLongValue(item, MerchantConfig.BUSINESS_DATA_ID);
            Map<String, Object> payData = operationHomePageService.getPayData(dataId);
            item.put(MerchantConfig.BUSINESS_DATA_NAME, MapUtils.getString(payData, OperationPayData.NAME));
            item.put(MerchantConfig.BUSINESS_DATA_SYMBOL, MapUtils.getString(payData, OperationPayData.SYMBOL));
        });

        params.put(MerchantConfig.BUSINESS_DATA, data);
        updateMerchantConfig(defaultMerchantConfig);
    }

    @Override
    public List<Map<String, Object>> getMerchantCustomBusinessData(String merchantId) {

        Map defaultMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (defaultMerchantConfig == null) {
            defaultMerchantConfig = new HashMap<>();
        }

        Map<String, Object> params = MapUtils.getMap(defaultMerchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap<>();
        }

        return (List<Map<String, Object>>) MapUtils.getObject(params, MerchantConfig.BUSINESS_DATA);
    }

    @Override
    public void removeMerchantCustomBusinessData(String merchantId) {

        Map defaultMerchantConfig = getMerchantConfigByMerchantIdAndPayway(merchantId, null);
        if (defaultMerchantConfig == null) {
            defaultMerchantConfig = new HashMap<>();
        }

        Map<String, Object> params = MapUtils.getMap(defaultMerchantConfig, MerchantConfig.PARAMS);
        if (params == null) {
            params = new HashMap<>();
        }

        params.remove(MerchantConfig.BUSINESS_DATA);
        updateMerchantConfig(defaultMerchantConfig);
    }


    @Override
    public boolean isCanFqTradeWithPaywayAndTradeApp(String merchantId, Integer payway, String tradeApp) {
        BoundValueOperations operations = redisTemplate.boundValueOps(String.format(PublicConstants.FQ_STATUS_FORMAT, merchantId, payway));
        Object value = operations.get();
        if (value != null && value instanceof String) {
            return Boolean.parseBoolean(value.toString());
        }
        boolean status = false;
        boolean existTradeParams = false;
        //目前还未定义分期的多业务应用,接口支持传tradeApp 但底层先不实现
        //基础配置下查询是否有交易参数
        Map<String, Object> merchantTradeConfig = merchantConfigDao.filter(Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(payway)
                .with(DaoConstants.DELETED).is(false)).fetchOne();
        if (merchantTradeConfig != null && MapUtils.isNotEmpty(MapUtils.getMap(merchantTradeConfig, MerchantAppConfig.PARAMS))) {
            Integer provider = MapUtils.getInteger(merchantTradeConfig, MerchantConfig.PROVIDER);
            if (provider != null && (Provider.FUYOU.getCode().intValue() == provider.intValue() || Provider.LAKALA_UNION_PAY_V3.getCode().intValue() == provider.intValue())) {
                existTradeParams = true;
            }
        }
        //查询分期开关的状态
        if (existTradeParams) {
            Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                    .with(MerchantConfig.PAYWAY).is(null)
                    .with(DaoConstants.DELETED).is(false);
            Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
            if (merchantConfig != null) {
                Map params = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
                String key = TransactionParam.FQ_STATUS_MAPPING.get(payway);
                if (params != null && !StringUtils.isEmpty(key) && params.containsKey(key)) {
                    status = MapUtils.getBooleanValue(params, key);
                } else {
                    status = ApolloConfigurationCenterUtil.getDefaultStatusByPayway(payway) == 0 ? false : true;
                }
            }
        }
        operations.set(Boolean.toString(status), 1, TimeUnit.HOURS);
        return status;
    }

    @Override
    public void updateFqParamsById(String merchantId, Integer payway, Map<String, Object> params) {
        if (StringUtil.empty(merchantId)) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        Criteria criteria = Criteria.where(MerchantConfig.MERCHANT_ID).is(merchantId)
                .with(MerchantConfig.PAYWAY).is(null)
                .with(DaoConstants.DELETED).is(false);
        Map<String, Object> merchantConfig = merchantConfigDao.filter(criteria).fetchOne();
        if (merchantConfig == null) {
            throw new CoreInvalidParameterException("参数错误,配置失败");
        }
        List<String> allowFields = TransactionParam.FQ_PARAMS_ALLOW_FILEDS.getOrDefault(payway, new ArrayList<>());
        if(com.wosai.pantheon.util.CollectionUtil.isEmpty(allowFields)){
            return;
        }
        String id = BeanUtil.getPropString(merchantConfig, DaoConstants.ID);
        Function<Map<String, Object>, Map<String, Object>> paramsChangeFunc = (merchantConfigParams) -> {
            for (String allowField : allowFields) {
                if (params.containsKey(allowField)) {
                    merchantConfigParams.put(allowField, params.get(allowField));
                }
            }
            return merchantConfigParams;
        };
        updateMerchantConfigWithRetry(id, paramsChangeFunc);
        Map<String, Object> merchant = merchantDao.filter(Criteria.where(DaoConstants.ID).is(merchantId)).fetchOne();
        redisService.removeCachedFqParams(merchantId, payway);
        redisService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));
    }

    @Override
    public void updateExternalExtraConfig(Map externalExtraConfig) {
        String sn = MapUtils.getString(externalExtraConfig, ExternalExtraConfig.SN);
        Integer snType = MapUtils.getInteger(externalExtraConfig, ExternalExtraConfig.SN_TYPE);
        Integer bizType = MapUtils.getInteger(externalExtraConfig, ExternalExtraConfig.BIZ_TYPE);
        if (null == bizType) {
            bizType = ExternalExtraConfig.BIZ_TYPE_TRADE;
            externalExtraConfig.put(ExternalExtraConfig.BIZ_TYPE, bizType);
        } else {
            if (!ExternalExtraConfig.ALLOW_BIZ_TYPE.contains(bizType)) {
                throw new CoreInvalidParameterException("输入类型bizType不合法");
            }
        }
        Integer provider = MapUtils.getInteger(externalExtraConfig, ExternalExtraConfig.PROVIDER);
        if (provider != null) {
            provider = LAKALA_V3_COMPATIBILITY_PROVIDER.contains(provider) ? LAKALA_V3_PROVIDER : provider;
            externalExtraConfig.put(ExternalExtraConfig.PROVIDER, provider);
        }
        if (!ExternalExtraConfig.ALLOW_SN_TYPE.contains(snType)) {
            throw new CoreInvalidParameterException("输入类型不合法");
        }
        Integer status = MapUtils.getInteger(externalExtraConfig, ExternalExtraConfig.STATUS);
        if (!Objects.equals(ExternalExtraConfig.STATUS_CLOSED, status) && !Objects.equals(ExternalExtraConfig.STATUS_OPENED, status)) {
            throw new CoreInvalidParameterException("不合法的状态");
        }
        Map<String, Object> oriExternalExtraConfig = externalExtraConfigDao.filter(Criteria.where(ExternalExtraConfig.SN).is(sn).with(ExternalExtraConfig.SN_TYPE).is(snType).with(ExternalExtraConfig.PROVIDER).is(provider).with(ExternalExtraConfig.BIZ_TYPE).is(bizType)).fetchOne();
        if (oriExternalExtraConfig == null) {
            Map copyExternalExtraConfig = com.wosai.pantheon.util.MapUtil.copyInclusive(externalExtraConfig, ExternalExtraConfig.SN, ExternalExtraConfig.SN_TYPE, ExternalExtraConfig.STATUS, ExternalExtraConfig.PROVIDER, ExternalExtraConfig.BIZ_TYPE);
            copyExternalExtraConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
            externalExtraConfigDao.save(copyExternalExtraConfig);
        } else {
            Map update = com.wosai.pantheon.util.MapUtil.copyInclusive(oriExternalExtraConfig, DaoConstants.ID, DaoConstants.VERSION);
            update.put(ExternalExtraConfig.STATUS, status);
            externalExtraConfigDao.updatePart(update);
        }
        String merchantSn = MapUtils.getString(externalExtraConfig, TransactionParam.MERCHANT_SN);
        if (!StringUtils.isEmpty(merchantSn)) {
            redisService.removeCachedParams(merchantSn);
        }
    }

    @Override
    public Map getProviderBizStatus(Integer provider, String providerMchId) {
        provider = TradeConfigServiceImpl.LAKALA_V3_COMPATIBILITY_PROVIDER.contains(provider) ? TradeConfigServiceImpl.LAKALA_V3_PROVIDER : provider;
        Criteria criteria = Criteria.where(ExternalExtraConfig.SN).is(providerMchId)
                .with(ExternalExtraConfig.SN_TYPE).is(ExternalExtraConfig.SN_TYPE_PROVIDER_MCH)
                .with(ExternalExtraConfig.PROVIDER).is(provider);
        List<Map<String, Object>> externalExtraConfigList = CollectionUtil.iterator2list(externalExtraConfigDao.filter(criteria).fetchAll());
        if (CollectionUtils.isEmpty(externalExtraConfigList)) {
            return Collections.emptyMap();
        }
        Map<Integer, Object> context = new HashMap<>();
        externalExtraConfigList.forEach(extraConfig -> {
            context.put(MapUtils.getInteger(extraConfig, ExternalExtraConfig.BIZ_TYPE), MapUtils.getInteger(extraConfig, ExternalExtraConfig.STATUS));
        });
        return context;
    }
}

