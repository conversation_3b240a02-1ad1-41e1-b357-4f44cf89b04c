package com.wosai.upay.core.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.bean.request.OpLogCreateRequest;
import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.core.exception.CoreOnlyStatusDisabledCouldEnableException;
import com.wosai.upay.core.exception.CoreVendorDeveloperNotExistsException;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.user.VendorUser;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.util.CoreUtil;
import com.wosai.upay.user.api.service.UserService;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class VendorServiceImpl implements VendorService {

    @Autowired
    private KeyGenerator keyGenerator;
    @Autowired
    private SnGenerator snGenerator;
    @Autowired
    private UuidGenerator uuidGenerator;
    @Autowired
    private BusinssCommonService businssCommonService;


    @Autowired
    private UserService userService;
    @Autowired
    private CacheService cacheService;

    @Autowired
    BizLogFacade bizLogFacade;

    private Dao<Map<String, Object>> vendorDao;
    private Dao<Map<String, Object>> vendorAppDao;
    private Dao<Map<String, Object>> vendorConfigDao;
    private Dao<Map<String, Object>> vendorDeveloperDao;

    @Autowired
    public VendorServiceImpl(DataRepository repository) {
        this.vendorDao = repository.getVendorDao();
        this.vendorAppDao = repository.getVendorAppDao();
        this.vendorConfigDao = repository.getVendorConfigDao();
        this.vendorDeveloperDao = repository.getVendorDeveloperDao();
    }

    @Override
    @Transactional(value = "transactionManager")
    public Map createVendorComplete(Map request) {
        VendorService vendorService = SpringContextHolder.getBean(VendorService.class);

        // 关联account
        String accountId = null;
        if (request.get("account_id") != null) {
            accountId = (String) request.get("account_id");
            request.remove("account_id");
        }

        Map vendor = vendorService.createVendor(request);

        String vendorId = BeanUtil.getPropString(vendor, DaoConstants.ID);
        // 默认创建config
        vendor.put("vendor_config", vendorService.createVendorConfig(CollectionUtil.hashMap(VendorConfig.VENDOR_ID, vendorId)));
        // 默认创建developer
        vendor.put("vendor_developer", vendorService.createVendorDeveloper(CollectionUtil.hashMap(VendorConfig.VENDOR_ID, vendorId)));

        if (accountId != null) {
            Map vendorUser = new HashMap();
            vendorUser.put(VendorUser.ACCOUNT_ID, accountId);
            vendorUser.put(VendorUser.VENDOR_ID, vendorId);
            vendor.put("account", userService.createVendorUser(vendorUser));
        }
        return vendor;
    }

    @Override
    public Map createVendor(Map vendor) {
        CrudUtil.ignoreForCreate(vendor);
        if (vendor.get(DaoConstants.ID) == null) {
            vendor.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        if (vendor.get(Vendor.SN) == null){
            vendor.put(Vendor.SN, snGenerator.nextVendorSn());
        }
        vendor.put(Vendor.STATUS, Vendor.STATUS_ENABLED);
        vendorDao.save(vendor);
        return vendor;
    }


    @Override
    public void deleteVendor(String vendorId) {
        vendorDao.softDelete(vendorId);
    }

    @Override
    public void deleteVendorBySn(String vendorSn) {
        Map vendor = getVendorBySn(vendorSn);
        if (vendor != null) {
            deleteVendor(BeanUtil.getPropString(vendor, DaoConstants.ID));
        }
    }

    @Override
    public Map updateVendor(Map vendor) {
        CrudUtil.ignoreForUpdate(vendor, new String[]{Vendor.SN, Vendor.STATUS});
        vendorDao.updatePart(vendor);
        Map updateRs = getVendor(BeanUtil.getPropString(vendor, DaoConstants.ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR, updateRs);
        return updateRs;
    }

    @Override
    public Map getVendor(String vendorId) {
        if(StringUtil.empty(vendorId)){
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(vendorId);
        return vendorDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getVendorBySn(String vendorSn) {
        if(StringUtil.empty(vendorSn)){
            return null;
        }
        Criteria criteria = Criteria.where(Vendor.SN).is(vendorSn);
        return vendorDao.filter(criteria).fetchOne();
    }

    @Override
    public void enableVendor(String vendorId) {
        Map vendorMininfo = businssCommonService.getVendorMinimalInfoById(vendorId);
        if(BeanUtil.getPropInt(vendorMininfo, ConstantUtil.KEY_STATUS, -1) != Vendor.STATUS_DISABLED){
            throw new CoreOnlyStatusDisabledCouldEnableException(CoreException.getCodeDesc(CoreException.CODE_ONLY_STATUS_DISABLED_COULD_ENABLE));
        }
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, vendorId,
                Vendor.STATUS, Vendor.STATUS_ENABLED
        );
        vendorDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR, vendorMininfo);
    }

    @Override
    public void enableVendorAndLog(String vendorId, OpLogCreateRequest opLogCreateRequest) {
        Map before = getVendor(vendorId);
        enableVendor(vendorId);
        Map after = getVendor(vendorId);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, vendorId, null, OpLog.VENDOR_TEMPLATE_CODE, OpLog.VENDOR_TABLE_NAME, new ArrayList<>(), OpLog.VENDOR_CHANGE_KEY_LIST, OpLog.VENDOR_DESC_MAP, before, after);
    }

    @Override
    public void disableVendor(String vendorId) {
        Map vendorMininfo = businssCommonService.getVendorMinimalInfoById(vendorId);
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(vendorMininfo, DaoConstants.ID),
                Vendor.STATUS, Vendor.STATUS_DISABLED
        );
        vendorDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR, vendorMininfo);
    }

    @Override
    public void disableVendorAndLog(String vendorId, OpLogCreateRequest opLogCreateRequest) {
        Map before = getVendor(vendorId);
        disableVendor(vendorId);
        Map after = getVendor(vendorId);
        bizLogFacade.safeSendTradeCommonParamsLog(opLogCreateRequest, vendorId, null, OpLog.VENDOR_TEMPLATE_CODE, OpLog.VENDOR_TABLE_NAME, new ArrayList<>(), OpLog.VENDOR_CHANGE_KEY_LIST, OpLog.VENDOR_DESC_MAP, before, after);
    }

    @Override
    public void closeVendor(String vendorId) {
        Map vendorMininfo = businssCommonService.getVendorMinimalInfoById(vendorId);
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(vendorMininfo, DaoConstants.ID),
                Vendor.STATUS, Vendor.STATUS_CLOSED
        );
        vendorDao.updatePart(update);
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR, vendorMininfo);
    }

    @Override
    public ListResult findVendors(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        String name = BeanUtil.getPropString(queryFilter, Vendor.NAME);
        if(!StringUtil.empty(name)){
            criteria.with(Vendor.NAME).like("%" + name + "%");
        }
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(Vendor.SN, Vendor.SN);
//            put(Vendor.CELLPHONE, Vendor.CELLPHONE);
//            put(Vendor.CONTACT_NAME, Vendor.CONTACT_NAME);
//            put(Vendor.CONTACT_PHONE, Vendor.CONTACT_PHONE);
//            put(Vendor.CONTACT_CELLPHONE, Vendor.CONTACT_CELLPHONE);
//            put(Vendor.CONTACT_EMAIL, Vendor.CONTACT_EMAIL);
            put(Vendor.RANK, Vendor.RANK);
            put(Vendor.STATUS, Vendor.STATUS);
//            put(Vendor.PROVINCE, Vendor.PROVINCE);
//            put(Vendor.CITY, Vendor.CITY);
//            put(Vendor.DISTRICT, Vendor.DISTRICT);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});

        if (queryFilter.get(Vendor.CONTACT_PHONE) != null && queryFilter.get(Vendor.CONTACT_CELLPHONE) != null) {
            if (criteria.chain().size() == 1 && criteria.chain().get(0).key() == null) {
                criteria = Criteria.or(Criteria.where(Vendor.CONTACT_PHONE).is(queryFilter.get(Vendor.CONTACT_PHONE)),
                                       Criteria.where(Vendor.CONTACT_CELLPHONE).is(queryFilter.get(Vendor.CONTACT_CELLPHONE)));
            } else {
                criteria.withOr(Criteria.where(Vendor.CONTACT_PHONE).is(queryFilter.get(Vendor.CONTACT_PHONE)),
                                       Criteria.where(Vendor.CONTACT_CELLPHONE).is(queryFilter.get(Vendor.CONTACT_CELLPHONE)));
            }
        } else if (queryFilter.get(Vendor.CONTACT_PHONE) != null) {
            criteria.with(Vendor.CONTACT_PHONE).is(queryFilter.get(Vendor.CONTACT_PHONE));
        } else if (queryFilter.get(Vendor.CONTACT_CELLPHONE) != null) {
            criteria.with(Vendor.CONTACT_CELLPHONE).is(queryFilter.get(Vendor.CONTACT_CELLPHONE));
        }

        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = vendorDao.filter(criteria).count();
        Filter filter = vendorDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }
    
    @Override
    public String resetAppKey(String vendorSn) {
        String key = keyGenerator.nextKey(vendorSn);
        updateVendorAppKey(vendorSn, key);
        return key;
    }

    @Override
    public String getAppKey(String vendorSn) {
        return findVendorAppKey(vendorSn);
    }



    @Override
    public Map createVendorApp(Map vendorApp) {
        businssCommonService.checkVendorStatus(BeanUtil.getPropString(vendorApp, ConstantUtil.KEY_VENDOR_ID), null);
        CrudUtil.ignoreForCreate(vendorApp);
        if (vendorApp.get(DaoConstants.ID) == null) {
            vendorApp.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        vendorApp.put(VendorApp.APPID, snGenerator.nextVendorAppid());
        vendorApp.put(VendorApp.APPKEY, keyGenerator.nextKey(""));
        vendorAppDao.save(vendorApp);
        return vendorApp;
    }

    @Override
    public void deleteVendorApp(String vendorAppId) {
        vendorAppDao.delete(vendorAppId);
    }

    @Override
    public Map updateVendorApp(Map vendorApp) {
        businssCommonService.checkVendorStatus(BeanUtil.getPropString(vendorApp, ConstantUtil.KEY_VENDOR_ID), null);
        CrudUtil.ignoreForUpdate(vendorApp, new String[]{VendorApp.APPID, VendorApp.VENDOR_ID});
        vendorAppDao.updatePart(vendorApp);
        Map updateRs = getVendorApp(BeanUtil.getPropString(vendorApp, DaoConstants.ID));
        cacheService.deleteCache(ConstantUtil.BUSINESS_OBJECT_TYPE_VENDOR_APP, updateRs);
        return updateRs;
    }

    @Override
    public Map getVendorApp(String vendorAppId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(vendorAppId);
        return vendorAppDao.filter(criteria).fetchOne();
    }

    @Override
    public String getAppKeyByVendorAppAppid(String vendorAppAppid) {
        Criteria criteria = Criteria.where(VendorApp.APPID).is(vendorAppAppid);
        Map<String,Object> vendorApp = vendorAppDao.filter(criteria, Arrays.asList(VendorApp.APPKEY)).fetchOne();
        return MapUtil.getString(vendorApp, VendorApp.APPKEY);
    }

    @Override
    public ListResult findVendorApps(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put("vendor_id", VendorApp.VENDOR_ID);
            put("appid", VendorApp.APPID);
            put("type", VendorApp.TYPE);
        }});
        String vendorAppName = BeanUtil.getPropString(queryFilter, "name");
        if (!StringUtil.empty(vendorAppName)) {
            criteria.with(VendorApp.NAME).like("%" + vendorAppName + "%");
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = vendorAppDao.filter(criteria).count();
        Filter filter = vendorAppDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }


    @SuppressWarnings("unchecked")
    private void updateVendorAppKey(String vendorSn, String key) {
        Map<String, Object> vendorDeveloper = vendorDeveloperDao.filter(Criteria.where(VendorDeveloper.VENDOR_SN).is(vendorSn),
                                                                        CollectionUtil.hashSet(DaoConstants.ID,
                                                                                               VendorDeveloper.APP_KEY)).fetchOne();
        if (vendorDeveloper == null) {
            Map<String, Object> vendor = businssCommonService.getVendorMinimalInfoBySn(vendorSn);
            vendorDeveloper = CollectionUtil.hashMap(DaoConstants.ID, uuidGenerator.nextUuid(),
                                                     VendorDeveloper.VENDOR_ID, vendor.get(DaoConstants.ID),
                                                     VendorDeveloper.VENDOR_SN, vendorSn,
                                                     VendorDeveloper.APP_KEY, key);
            vendorDeveloperDao.save(vendorDeveloper);
        } else {
            vendorDeveloper.put(VendorDeveloper.APP_KEY, key);
            vendorDeveloperDao.updatePart(vendorDeveloper);
        }
    }
    
    private String findVendorAppKey(String vendorSn) {
        Map<String, Object> vendorDeveloper = vendorDeveloperDao.filter(Criteria.where(VendorDeveloper.VENDOR_SN).is(vendorSn),
                                                                        Collections.singleton(VendorDeveloper.APP_KEY)).fetchOne();
        if (vendorDeveloper == null) {
            throw new CoreVendorDeveloperNotExistsException("服务商没有设置开发者账户");
        }
        return BeanUtil.getPropString(vendorDeveloper, VendorDeveloper.APP_KEY);
    }

    @Override
    public Map createVendorConfig(Map vendorConfig) {
        CrudUtil.ignoreForCreate(vendorConfig);
        if (vendorConfig.get(DaoConstants.ID) == null) {
            vendorConfig.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        vendorConfigDao.save(vendorConfig);
        return vendorConfig;
    }

    @Override
    public Map updateVendorConfig(Map vendorConfig) {
        CrudUtil.ignoreForUpdate(vendorConfig, new String[]{VendorConfig.VENDOR_ID});
        vendorConfigDao.updatePart(vendorConfig);
        return getVendorConfig(BeanUtil.getPropString(vendorConfig, DaoConstants.ID));
    }

    private Map getVendorConfig(String vendorConfigId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(vendorConfigId);
        return vendorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getVendorConfigByVendorId(String vendorId) {
        Criteria criteria = Criteria.where(VendorConfig.VENDOR_ID).is(vendorId);
        return vendorConfigDao.filter(criteria).fetchOne();
    }

    @Override
    public ListResult findVendorConfigs(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(VendorConfig.VENDOR_ID, VendorConfig.VENDOR_ID);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = vendorConfigDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = vendorConfigDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public Map createVendorDeveloper(Map vendorDeveloper) {
        CrudUtil.ignoreForCreate(vendorDeveloper, new String[]{VendorDeveloper.PUBLIC_KEY});
        if (vendorDeveloper.get(DaoConstants.ID) == null) {
            vendorDeveloper.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        vendorDeveloper.put(VendorDeveloper.VENDOR_SN, businssCommonService.getVendorSnById(BeanUtil.getPropString(vendorDeveloper, VendorDeveloper
                .VENDOR_ID)));
        vendorDeveloper.put(VendorDeveloper.APP_KEY, keyGenerator.nextKey(""));
        vendorDeveloperDao.save(vendorDeveloper);
        return vendorDeveloper;
    }

    @Override
    public Map updateVendorDeveloper(Map vendorDeveloper) {
        CrudUtil.ignoreForUpdate(vendorDeveloper, new String[]{VendorDeveloper.VENDOR_ID, VendorDeveloper.VENDOR_SN});
        vendorDeveloperDao.updatePart(vendorDeveloper);
        return getVendorDeveloper(BeanUtil.getPropString(vendorDeveloper, DaoConstants.ID));
    }

    private Map getVendorDeveloper(String vendorDeveloperId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(vendorDeveloperId);
        return vendorDeveloperDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getVendorDeveloperByVendorId(String vendorId) {
        Criteria criteria = Criteria.where(VendorDeveloper.VENDOR_ID).is(vendorId);
        return vendorDeveloperDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getVendorDeveloperByVendorSn(String vendorSn) {
        Criteria criteria = Criteria.where(VendorDeveloper.VENDOR_SN).is(vendorSn);
        return vendorDeveloperDao.filter(criteria).fetchOne();
    }

    @Override
    public String getVendorAppKeyByVendorSn(String vendorSn) {
        return BeanUtil.getPropString(vendorDeveloperDao.filter(Criteria.where(VendorDeveloper.VENDOR_SN).is(vendorSn), CollectionUtil.hashSet(VendorDeveloper.APP_KEY)).fetchOne(), VendorDeveloper.APP_KEY);
    }

    @Override
    public ListResult findVendorDevelopers(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(VendorDeveloper.VENDOR_ID, VendorDeveloper.VENDOR_ID);
            put(VendorDeveloper.VENDOR_SN, VendorDeveloper.VENDOR_SN);
//            put(VendorDeveloper.APP_KEY, VendorDeveloper.APP_KEY);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = vendorDeveloperDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = vendorDeveloperDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    public Map<Integer, String> getVendorAppTypesAndDesc() {
        Map types = new HashMap<Integer, String>();
        types.put(VendorApp.TYPE_DESKTOP , "WINDOWS桌面应用");
        types.put(VendorApp.TYPE_ANDROID ,"android");
        types.put(VendorApp.TYPE_IOS , "ios");
        types.put(VendorApp.TYPE_DEDICATED_DEVICE , "专有设备");
        types.put(VendorApp.TYPE_WAP , "门店码");
        types.put(VendorApp.TYPE_SERVICE , "支付服务");
        types.put(VendorApp.TYPE_INVOICE , "电子发票应用");
        types.put(VendorApp.TYPE_CARD , "卡券核销应用");
        return types;
    }

    @Override
    public Map<String, Object> getVendorAppTranslateInfoById(final String vendorAppId) {
        return new HashMap<String, Object>() {{
            put(ConstantUtil.KEY_VENDOR_APP_ID, vendorAppId);
        }};
    }

    @Override
    public Map<String, Object> getVendorAppTranslateInfoByAppId(final String vendorAppAppId) {
        return new HashMap<String, Object>() {{
            put(ConstantUtil.KEY_VENDOR_APP_APPID, vendorAppAppId);
        }};
    }

    @Override
    public Map<String, Object> getVendorTranslateInfoById(final String vendorId) {
        return new HashMap<String, Object>() {{
            put(ConstantUtil.KEY_VENDOR_ID, vendorId);
        }};
    }

    @Override
    public Map<String, Object> getVendorTranslateInfoBySn(final String vendorSn) {
        return new HashMap<String, Object>() {{
            put(ConstantUtil.KEY_VENDOR_SN, vendorSn);
        }};
    }

}
