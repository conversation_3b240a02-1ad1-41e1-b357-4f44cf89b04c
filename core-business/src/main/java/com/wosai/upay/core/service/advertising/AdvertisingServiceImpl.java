package com.wosai.upay.core.service.advertising;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.log.LogstashMarkerAppendFileds;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.bean.response.MerchantResponse;
import com.wosai.upay.core.bean.response.StoreResponse;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.CacheService;
import com.wosai.upay.core.service.biz.BankInfoBiz;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@AutoJsonRpcServiceImpl
@NoArgsConstructor
public class AdvertisingServiceImpl implements AdvertisingService {

    private static final Logger logger = LoggerFactory.getLogger(AdvertisingServiceImpl.class);

    /**
     * 商户查询字段常量列表
     */
    private static final List<String> MERCHANT_QUERY_FIELDS = Collections.unmodifiableList(Arrays.asList(
            DaoConstants.ID,
            Merchant.SN,
            Merchant.NAME,
            Merchant.INDUSTRY,
            Merchant.PROVINCE,
            Merchant.CITY,
            Merchant.DISTRICT,
            Merchant.LONGITUDE,
            Merchant.LATITUDE
    ));

    /**
     * 门店查询字段常量列表
     */
    private static final List<String> STORE_QUERY_FIELDS = Collections.unmodifiableList(Arrays.asList(
            DaoConstants.ID,
            Store.SN,
            Store.NAME,
            Store.INDUSTRY,
            Store.PROVINCE,
            Store.CITY,
            Store.DISTRICT,
            Store.LONGITUDE,
            Store.LATITUDE,
            Store.MERCHANT_ID
    ));

    @Autowired
    private BankInfoBiz bankInfoBiz;

    @Autowired
    private CacheService cacheService;

    private Dao<Map<String, Object>> merchantDao;

    private Dao<Map<String, Object>> storeDao;

    @Autowired
    public AdvertisingServiceImpl(DataRepository repository) {
        this.merchantDao = repository.getMerchantDao();
        this.storeDao = repository.getStoreDao();
    }

    @Override
    public MerchantResponse getMerchantByMerchantId(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantId);
        Map<String, Object> merchant = merchantDao.filter(criteria, MERCHANT_QUERY_FIELDS).fetchOne();
        bankInfoBiz.resolveDistrict(merchant);
        return buildMerchantResponse(merchant);
    }

    private MerchantResponse buildMerchantResponse(Map<String, Object> merchant) {
        if (merchant == null) {
            return null;
        }
        MerchantResponse response = new MerchantResponse();
        response.setId(BeanUtil.getPropString(merchant, DaoConstants.ID));
        response.setSn(BeanUtil.getPropString(merchant, Merchant.SN));
        response.setName(BeanUtil.getPropString(merchant, Merchant.NAME));
        response.setIndustry(BeanUtil.getPropString(merchant, Merchant.INDUSTRY));
        response.setProvince(BeanUtil.getPropString(merchant, Merchant.PROVINCE));
        response.setCity(BeanUtil.getPropString(merchant, Merchant.CITY));
        response.setDistrict(BeanUtil.getPropString(merchant, Merchant.DISTRICT));
        response.setLongitude(BeanUtil.getPropString(merchant, Merchant.LONGITUDE));
        response.setLatitude(BeanUtil.getPropString(merchant, Merchant.LATITUDE));
        return response;
    }

    @Override
    public StoreResponse getStoreByStoreSn(String storeSn) {
        if (StringUtil.empty(storeSn)) {
            return null;
        }
        Criteria criteria = Criteria.where(Store.SN).is(storeSn);
        Map<String, Object> store = storeDao.filter(criteria, STORE_QUERY_FIELDS).fetchOne();
        bankInfoBiz.resolveDistrict(store);
        StoreResponse storeResponse = buildStoreResponse(store);
        setMerchantInfo(BeanUtil.getPropString(store, ConstantUtil.KEY_MERCHANT_ID), storeResponse);
        return storeResponse;
    }

    private StoreResponse buildStoreResponse(Map<String, Object> store) {
        if (store == null) {
            return null;
        }
        StoreResponse response = new StoreResponse();
        response.setId(BeanUtil.getPropString(store, DaoConstants.ID));
        response.setSn(BeanUtil.getPropString(store, Store.SN));
        response.setName(BeanUtil.getPropString(store, Store.NAME));
        response.setIndustry(BeanUtil.getPropString(store, Store.INDUSTRY));
        response.setProvince(BeanUtil.getPropString(store, Store.PROVINCE));
        response.setCity(BeanUtil.getPropString(store, Store.CITY));
        response.setDistrict(BeanUtil.getPropString(store, Store.DISTRICT));
        response.setLongitude(BeanUtil.getPropString(store, Store.LONGITUDE));
        response.setLatitude(BeanUtil.getPropString(store, Store.LATITUDE));
        response.setMerchantId(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        return response;
    }

    private void setMerchantInfo(String merchantId, StoreResponse storeResponse) {
        try {
            Map<String, Object> minimalInfo = cacheService.getMerchantMinimalInfo(merchantId, null);
            if (minimalInfo != null) {
                storeResponse.setMerchantId(BeanUtil.getPropString(minimalInfo, ConstantUtil.KEY_ID));
                storeResponse.setMerchantSn(BeanUtil.getPropString(minimalInfo, ConstantUtil.KEY_SN));
                storeResponse.setMerchantName(BeanUtil.getPropString(minimalInfo, ConstantUtil.KEY_NAME));
            }
        } catch (Exception e) {
            logger.error(LogstashMarkerAppendFileds.append(e), "error when getStoreByStoreSn > setMerchantInfo. {}", e.getMessage());
            throw e;
        }
    }
}
