package com.wosai.upay.core.service.biz;

import com.alibaba.fastjson.JSON;
import com.aliyun.core.utils.StringUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.info.api.model.BankInfo;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.model.Merchant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-06-18
 */
@Component
public class BankInfoBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(BankInfoBiz.class);

    @Autowired
    private BankInfoService bankInfoService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    private static Config config = ConfigService.getAppConfig();

    private static final String KEY = "append-bank-clearing-number";

    private static final String DISTRICT_KEY = "bank-info-districts_";

    private static final List<String> BANK_ACCOUNT_NOT_CHANGE_KEY = Lists.newArrayList("merchant_id");
    /**
     * 卡变更流水表涉及到的字段
     */
    private static final List<String> BANK_ACCOUNT_CHANGE_ALL_KEY = Lists.newArrayList("merchant_id", "type", "holder", "identity", "number", "bank_name", "branch_name", "clearing_number", "opening_number");
    /**
     * 主要变更字段，都变更了才去记录流水表记录
     */
    private static final List<String> BANK_ACCOUNT_CHANGE_MAIN_KEY = Lists.newArrayList("type", "holder", "identity", "number");
    /**
     * 银行名称、分支行名称 => 开户行号、清算行号
     *
     * @param bankAccount
     */
    public void appendOpeningClearingNum(Map<String, Object> bankAccount) {
        if (WosaiMapUtils.isEmpty(bankAccount)) {
            return;
        }
        String branchName = WosaiMapUtils.getString(bankAccount, BankInfo.BRANCH_NAME);
        String bankName = WosaiMapUtils.getString(bankAccount, BankInfo.BANK_NAME);
        boolean hasBankBranchName = WosaiStringUtils.isNotEmpty(branchName);

        String openingNum = WosaiMapUtils.getString(bankAccount, BankInfo.OPENING_NUMBER);
        String clearingNum = WosaiMapUtils.getString(bankAccount, BankInfo.CLEARING_NUMBER);
        boolean emptyOpeningClearingNum = WosaiStringUtils.isEmpty(openingNum) || WosaiStringUtils.isEmpty(clearingNum);


        if (hasBankBranchName && emptyOpeningClearingNum) {
            Map bankInfo = null;
            try {
                // 精确匹配
                bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                        BankInfo.BANK_NAME_IS, bankName,
                        BankInfo.BRANCH_NAME_IS, branchName
                ));

                // 查询不到再模糊匹配一次
                if (WosaiMapUtils.isEmpty(bankInfo)) {
                    bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                            BankInfo.BANK_NAME, bankName,
                            BankInfo.BRANCH_NAME, branchName
                    ));
                }
                if (WosaiMapUtils.isNotEmpty(bankInfo)) {
                    bankAccount.put(BankInfo.OPENING_NUMBER, WosaiMapUtils.getString(bankInfo, BankInfo.OPENING_NUMBER));
                    bankAccount.put(BankInfo.CLEARING_NUMBER, WosaiMapUtils.getString(bankInfo, BankInfo.CLEARING_NUMBER));
                } else {
                    throw new CoreInvalidParameterException("银行名称或分支行名称不正确");
                }
            } catch (Exception e) {
                LOGGER.warn("getBankInfo fail", e);
            }
        }
    }

    /**
     * 开户行号 => 银行名称、分支行名称
     *
     * @param bankAccount
     */
    public void appendBankBranchName(Map<String, Object> bankAccount) {
        if (!config.getBooleanProperty(KEY, false) || WosaiMapUtils.isEmpty(bankAccount)) {
            return;
        }
        String openingNum = WosaiMapUtils.getString(bankAccount, BankInfo.OPENING_NUMBER);
        if (WosaiStringUtils.isNotEmpty(openingNum)) {
            String[] bankInfo = getBankInfo(openingNum);
            if (bankInfo.length == 4) {
                bankAccount.put(BankInfo.BANK_NAME, bankInfo[0]);
                bankAccount.put(BankInfo.BRANCH_NAME, bankInfo[1]);
                bankAccount.put(BankInfo.CLEARING_NUMBER, bankInfo[2]);
                bankAccount.put(BankInfo.CITY, bankInfo[3]);
            }
        }
    }

    private String[] getBankInfo(String openingNum) {
        String key = String.format("cb-bi-v3-%s", openingNum);
        String value = (String) redisTemplate.opsForValue().get(key);
        if (value == null) {
            List<Map> bankInfos = bankInfoService.getBankInfos(new PageInfo(1, 5), CollectionUtil.hashMap(
                    BankInfo.OPENING_NUMBER, openingNum
            )).getRecords();

            if (!bankInfos.isEmpty()) {
                Map bankInfo = bankInfos.get(0);
                value = WosaiMapUtils.getString(bankInfo, BankInfo.BANK_NAME)
                        + "," + WosaiMapUtils.getString(bankInfo, BankInfo.BRANCH_NAME)
                        + "," + WosaiMapUtils.getString(bankInfo, BankInfo.CLEARING_NUMBER)
                        + "," + WosaiMapUtils.getString(bankInfo, BankInfo.CITY);

                redisTemplate.opsForValue().set(key, value, 1, TimeUnit.HOURS);
            } else {
                value = "";
            }
        }

        return value.split(",");
    }

    /**
     * 补充检查省市区信息
     *
     * @param merchantOrStore
     * @param insert
     */
    public void appendDistrictCode(Map merchantOrStore, boolean insert) {
        String districtCode = (String) merchantOrStore.get(Merchant.DISTRICT_CODE);
        if (WosaiStringUtils.isNotBlank(districtCode)) {
            District district = getDistrictByCode(districtCode);
            districtValidCheck(district, "地区码不正确");

            merchantOrStore.put(Merchant.PROVINCE, district.getProvince_name());
            merchantOrStore.put(Merchant.CITY, district.getCity_name());
            merchantOrStore.put(Merchant.DISTRICT, district.getName());
        } else {
            String province = (String) merchantOrStore.get(Merchant.PROVINCE);
            String city = (String) merchantOrStore.get(Merchant.CITY);
            String district = (String) merchantOrStore.get(Merchant.DISTRICT);
            //都不为空才处理
            if (WosaiStringUtils.isNotBlank(province) && WosaiStringUtils.isNotBlank(city) && WosaiStringUtils.isNotBlank(district)) {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(province).append(" ").append(city).append(" ").append(district);
                District districtFull = districtsServiceV2.getCodeByName(stringBuffer.toString());
                districtValidCheck(districtFull, "省市区信息不正确");
                merchantOrStore.put(Merchant.DISTRICT_CODE, districtFull.getCode());
            } else if (insert) {
                // 先打日志，不报错
                LOGGER.warn("省市区信息缺失");
//                    throw new CoreInvalidParameterException("省市区信息不正确");
            }
        }
    }

    private void districtValidCheck(District district, String errMsg) {

        if (Objects.isNull(district)) {
            throw new CoreInvalidParameterException(errMsg);
        }

        if (Objects.equals(district.getStatus(), 0)) {
            throw new CoreInvalidParameterException("失效地区码");
        }

    }

    public void resolveDistrict(Map map) {
        District district = getDistrictByCode(BeanUtil.getPropString(map, Merchant.DISTRICT_CODE));
        if (Objects.nonNull(district)) {
            map.put(Merchant.PROVINCE, district.getProvince_name());
            map.put(Merchant.CITY, district.getCity_name());
            map.put(Merchant.DISTRICT, district.getName());
        }
    }

    private District getDistrictByCode(String districtCode) {
        if (WosaiStringUtils.isBlank(districtCode)) {
            return null;
        }
        String key = DISTRICT_KEY + districtCode;
        String dis = (String) redisTemplate.opsForValue().get(key);
        District district = JSON.parseObject(dis, District.class);

        if (Objects.isNull(district)) {
            district = districtsServiceV2.getDistrict(new HashMap() {{
                put("code", districtCode);
            }});
            if (Objects.nonNull(district)) {
                redisTemplate.opsForValue().set(key, JSON.toJSONString(district), 10, TimeUnit.DAYS);
            }
        }
        return district;
    }

    public Map getMerchantBankAccountChangeLog(Map<String, Object> original, Map<String, Object> updateRequest) {
        if (!checkKeyFieldDifferent(original, updateRequest)) {
            return null;
        }
        Map merchantBankAccountChangeLog = new HashMap();
        if (WosaiMapUtils.isNotEmpty(updateRequest)) {
            updateRequest.forEach((key, value) -> {
                if (BANK_ACCOUNT_CHANGE_ALL_KEY.contains(key)) {
                    if (BANK_ACCOUNT_NOT_CHANGE_KEY.contains(key)) {
                        merchantBankAccountChangeLog.put(key, value);
                    } else {
                        merchantBankAccountChangeLog.put("new_" + key, value);
                    }
                }
            });
        }

        if (WosaiMapUtils.isNotEmpty(original)) {
            original.forEach((key, value) -> {
                if (BANK_ACCOUNT_CHANGE_ALL_KEY.contains(key)) {
                    if (BANK_ACCOUNT_NOT_CHANGE_KEY.contains(key)) {
                        merchantBankAccountChangeLog.put(key, value);
                    } else {
                        merchantBankAccountChangeLog.put("old_" + key, value);
                    }
                }
            });
        }

        if (WosaiMapUtils.isEmpty(merchantBankAccountChangeLog)) {
            return null;
        }

        String bankAccountId = BeanUtil.getPropString(original, DaoConstants.ID);
        if (StringUtils.isEmpty(bankAccountId)) {
            bankAccountId = BeanUtil.getPropString(updateRequest, DaoConstants.ID);
        }

        merchantBankAccountChangeLog.put("bank_account_id", bankAccountId);
        merchantBankAccountChangeLog.put("ctime", System.currentTimeMillis());
        merchantBankAccountChangeLog.put("mtime", System.currentTimeMillis());
        merchantBankAccountChangeLog.put("change_time", System.currentTimeMillis());
        return merchantBankAccountChangeLog;
    }

    private boolean checkKeyFieldDifferent(Map<String, Object> original, Map<String, Object> updateRequest) {
        for (String element : BANK_ACCOUNT_CHANGE_MAIN_KEY) {
            if (!Objects.equals(WosaiMapUtils.getString(original, element),
                    WosaiMapUtils.getString(updateRequest, element))) {
                return true;
            }
        }
        return false;
    }
}
