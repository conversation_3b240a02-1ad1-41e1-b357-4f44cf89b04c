package com.wosai.upay.core.service.biz;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.enums.MetaProviderFlagEnum;
import com.wosai.upay.core.model.MetaProvider;
import com.wosai.upay.core.service.BusinssCommonService;

@Component
public class MetaProviderBiz {
    @Autowired
    BusinssCommonService businssCommonService;

    /**
     * 获取所有通道的交易参数key
     * 
     */
    public Map<String, String> getTradeParamKeys() {
        Map<String, String> result = new HashMap<String, String>();
        List<Map<String, Object>> metaProviders = getAllMetaProviders();
        for (Map<String, Object> metaProvider : metaProviders) {
            String provider = MapUtil.getString(metaProvider, DaoConstants.ID);
            String tradeParamsKey = MapUtil.getString(metaProvider, MetaProvider.TRADE_PARAMS_KEY);
            if (!StringUtil.empty(provider) && !StringUtil.empty(tradeParamsKey)) {
                result.put(provider, tradeParamsKey);
            }
        }
        return result;
    }

    /**
     * 获取通道的交易key
     * 
     * @param provider
     * @return
     */
    public String getTradeParamsKey(Integer provider) {
        if (provider != null) {
            return businssCommonService.getTradeParamsKey(provider);
        }
        return null;
    }

    /**
     * 间连通道是否为间连直清
     * 
     * @param provider
     * @return
     */
    public boolean isIndirect(Integer provider) {
        if (provider != null) {
            int flag = com.wosai.pantheon.util.MapUtil.getIntValue(businssCommonService.getMetaProviderById(provider), MetaProvider.FLAG, MetaProviderFlagEnum.INVALID.getType());
            if(flag == MetaProviderFlagEnum.INDIRECT.getType() || flag == MetaProviderFlagEnum.INDIRECT_OR_WITHDRAW.getType()){
                return true;
            }
        }
        return false;
    }

    /**
     * 获取所有交易通道
     * 
     * @return
     */
    public List<Map<String, Object>> getAllMetaProviders() {
        return businssCommonService.getAllMetaProviders();
    }

    /**
     * 通道是否支持交易
     * 
     * @param provider
     * @param payway
     * @param subPayway
     * @return
     */
    public boolean isThisProviderSupport(Integer provider, int payway, int subPayway) {
        if (provider == null || provider == payway) {
            return true;
        }
        Map providerPayways = com.wosai.pantheon.util.MapUtil.getMap(businssCommonService.getMetaProviderById(provider), MetaProvider.SUPPORT_PAYWAYS);
        if (providerPayways == null) {
            return false;
        } else {
            List subPayways = (List) providerPayways.get(payway + "");
            if (subPayways == null) {
                return false;
            } else {
                if (subPayways.contains(subPayway + "")) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    }
}
