package com.wosai.upay.core.service.cache;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.base.Stopwatch;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.breaker.BreakerMetrics;
import com.wosai.upay.breaker.BreakerMetricsManager;
import com.wosai.upay.breaker.BreakerProperty;
import com.wosai.upay.breaker.BreakerStatus;
import com.wosai.upay.core.constant.CoreConstant;
import com.wosai.upay.core.constant.PublicConstants;
import com.wosai.upay.core.model.MerchantConfigBypassStrategy;
import com.wosai.upay.core.model.MetaProvider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.core.service.SystemConfigService;
import com.wosai.upay.core.service.biz.MetaProviderBiz;
import com.wosai.upay.core.service.redis.RedisService;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
import com.wosai.upay.exception.RequestRejectedException;

@Service
public class ParamCacheService {
    private static final Logger logger = LoggerFactory.getLogger(ParamCacheService.class);
    private boolean usedCachedParams = true; //是否启用交易参数缓存
    private static final String REDIS_METRICS = "redis";
    private Semaphore semaphore;
    private BreakerMetrics redisBm;
    Cache<String, Map<String, Object>> memoryCache;
    private volatile int semaphoreTimeoutMs = 500;
    private Config config = ConfigService.getAppConfig();

    @Autowired
    private RedisService redisService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private BusinssCommonService businssCommonService;
    @Autowired
    MetaProviderBiz metaProviderBiz;

    @PostConstruct
    public void init() {
        // 初始化redis监控
        redisBm = new BreakerMetrics(REDIS_METRICS, new BreakerProperty());
        BreakerMetricsManager.getInstance().register(REDIS_METRICS, redisBm);

        // 初始化缓存、限流
        loadCacheConfig();
        loadSemaphoreConfig();

        // 添加apollo变更任务
        config.addChangeListener(cl -> {
            if (cl.isChanged(PublicConstants.GET_PARAMS_SEMAPHORE_KEY)) {
                loadSemaphoreConfig();
            } else if(cl.isChanged(PublicConstants.GET_PARAMS_MEMORY_CACHE_KEY)){
                loadCacheConfig();
            }
        });
    }

    private void loadSemaphoreConfig() {
        // 初始化DB限流配置
        String semaphoreConfigStr = config.getProperty(PublicConstants.GET_PARAMS_SEMAPHORE_KEY, "{}");
        Map<String, Object> changeSemaphoreConfig = JsonUtil.jsonStrToObject(semaphoreConfigStr, Map.class);
        semaphore = new Semaphore(MapUtil.getIntValue(changeSemaphoreConfig,  PublicConstants.SEMAPHORE_THRESHOLD, 300));
        semaphoreTimeoutMs = MapUtil.getIntValue(changeSemaphoreConfig, PublicConstants.SEMAPHORE_TIMEOUT, 100);
    }

    private void loadCacheConfig() {
        // 初始化本地缓存配置
        String cacheConfigStr = config.getProperty(PublicConstants.GET_PARAMS_MEMORY_CACHE_KEY, "{}");
        Map<String, Object> changeCacheConfig = JsonUtil.jsonStrToObject(cacheConfigStr, Map.class);
        Cache<String, Map<String, Object>> changeMemoryCache = Caffeine
                                                                .newBuilder()
                                                                .maximumSize(MapUtil.getIntValue(changeCacheConfig, PublicConstants.MEMORY_CACHE_MAX_SIZE, 1000000))
                                                                .expireAfterAccess(MapUtil.getIntValue(changeCacheConfig, PublicConstants.MEMORY_CACHE_EXPIRE_SECONDS, 600), TimeUnit.SECONDS)
                                                                .executor(Executors.newFixedThreadPool(8))
                                                                .build();
        Cache<String, Map<String, Object>> oldMemoryCache = memoryCache;
        memoryCache = changeMemoryCache;
        if (oldMemoryCache != null) {
            oldMemoryCache.invalidateAll();
            oldMemoryCache = null;
        }
    }
    
    public Map<String, Object> loadParams(String cacheKey, String fieldKey, Supplier<Map<String, Object>> sp) {
        // 不使用缓存
        if (!usedCachedParams) {
            return sp.get();
        }
        // 数据缓存策略
        Consumer<Map<String, Object>> cacheConsumer;
        if (redisBm.isOpen()) {
            // 已触发redis熔断，使用本地缓存
            Map<String, Object> result = loadParamFromMemory(cacheKey, fieldKey);
            if (result != null) {
                return result;
            }
            cacheConsumer = (dbValue) -> {
                putMemoryCache(cacheKey, fieldKey, dbValue);
            };
        } else {
            // 未触发熔断，使用redis缓存
            Map<String, Object> result = loadParamFromRedis(cacheKey, fieldKey);
            if (result != null) {
                return result;
            }
            cacheConsumer = (dbValue) -> {
                putRedisCache(cacheKey, fieldKey, dbValue);
            };
        }
        // 未获取到缓存数据，查询数据库
        Map<String, Object> dbValue = loadParamFromDb(cacheKey, sp);
        if (dbValue != null) {
            cacheConsumer.accept(dbValue);
        }
        return dbValue;
    }

    private Map<String, Object> loadParamFromRedis(String cacheKey, String fieldKey){
        // redis 读取失败不报错，执行后续处理逻辑
        try {
            Map<String, Object> result = redisService.getMapValueFromHash(cacheKey, fieldKey);
            redisBm.increment(BreakerStatus.SUCCESS);
            if (redisBm.inTestPhase()) {
                redisBm.singleTestPass(true);
            }
            return result;
        } catch (Exception e) {
            logger.info("get redis cache fail", e);
            redisBm.increment(BreakerStatus.ERROR);
            if (redisBm.inTestPhase()){
                redisBm.singleTestPass(false);
            }
        }
        return null;
    }

    private Map<String, Object> loadParamFromMemory(String cacheKey, String fieldKey) {
        return memoryCache.getIfPresent(cacheKey + fieldKey);
    }

    private Map<String, Object> loadParamFromDb(String cacheKey, Supplier<Map<String, Object>> sp){
        boolean needReject = false;
        try {
            Stopwatch stopwatch = Stopwatch.createStarted();
            if(!semaphore.tryAcquire(semaphoreTimeoutMs, TimeUnit.MILLISECONDS)){
                logger.info("缓存未命中,查询db获取许可失败,key->{},耗时->{}ms", cacheKey, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
                needReject = true;
            }
        }catch (InterruptedException e) {
            needReject = true;
        }
        if (needReject) {
            throw new RequestRejectedException("在限定时间未拿到许可,被限流,请稍后重试!");
        }
        try {
            return sp.get();
        } finally {
            semaphore.release();
        }
    }

    private void putRedisCache(String cacheKey, String fieldKey, Map<String, Object> value){
        // redis写入失败不影响返回
        try {
            redisService.setMapValueToHash(cacheKey, fieldKey, value);
            if (redisBm.inTestPhase()) {
                redisBm.singleTestPass(true);
            }
        }catch (Exception e) {
            redisBm.increment(BreakerStatus.ERROR);
            if (redisBm.inTestPhase()) {
                redisBm.singleTestPass(false);
            }
            logger.info("put redis cache fail", e);
        }
    }

    private void putMemoryCache(String cacheKey, String fieldKey, Map<String, Object> value) {
        memoryCache.put(cacheKey + fieldKey, value);
    }

    public void setUsedCachedParams(boolean usedCachedParams) {
        this.usedCachedParams = usedCachedParams;
    }

    public void loadMerchantBypassConfig(Integer payway, Integer subPayway, String cacheKey, String fieldsKey, Map<String, Object> context, Supplier<Map<String, Object>> sp) {
        // 备用通道校验
        MerchantConfigBypassStrategy bypassStrategy = ApolloConfigurationCenterUtil.getMerchantConfigBypassStrategy();
        if (bypassStrategy == null 
                || bypassStrategy.getWeight() <= 0 
                || bypassStrategy.getChangeRules() == null
                || bypassStrategy.getChangeRules().isEmpty()) {
            return;
        }

        // 是否满足切通道比例
        String merchantId = MapUtil.getString(context, TransactionParam.MERCHANT_ID);
        if (bypassStrategy.getWeight() < 100 
                && bypassStrategy.getWeight() < Math.abs(merchantId.hashCode() % 100)) {
            return;
        }

        // 是否满足切通道规则
        Integer provider = MapUtil.getInteger(context, TransactionParam.PROVIDER);
        if (!bypassStrategy.useBypass(provider, payway, subPayway)) {
            return;
        }

        try {
            String batchNoFieldsKey = StringUtil.join("_", null, null, fieldsKey, bypassStrategy.getBatchNo());
            Map<String, Object> bypassTradeParams = loadParamFromRedis(cacheKey, batchNoFieldsKey);
            if (bypassTradeParams != null) {
                if (MapUtil.getBooleanValue(bypassTradeParams, PublicConstants.SWITCH_MERCHANT_BYPASS, true)) {
                    mergeByPassTradeParams(context, bypassTradeParams);
                }
                return;
            }
            // 不存在缓存，进行查询
            bypassTradeParams = sp.get();
            if (bypassTradeParams == null) {
                bypassTradeParams = MapUtil.hashMap(PublicConstants.SWITCH_MERCHANT_BYPASS, false);
            } else {
                bypassTradeParams.put(TransactionParam.BYPASS_BATCH_NO, bypassStrategy.getBatchNo());
                byPassMergeTradeParams(context, bypassTradeParams);
                mergeByPassTradeParams(context, bypassTradeParams);
            }
            putRedisCache(cacheKey, batchNoFieldsKey, bypassTradeParams);
        }catch (Exception e) {
            logger.warn("load merchant bypass config fail", e);
        }
    }

    /**
     * 备用通道和主通道参数合并
     * 
     * @param context
     * @param bypassTradeParams
     */
    private void mergeByPassTradeParams(Map<String, Object> context, Map<String, Object> bypassTradeParams) {
        Integer provider = MapUtil.getInteger(context, TransactionParam.PROVIDER);
        String sourceTradeParamsKey = metaProviderBiz.getTradeParamsKey(provider);
        // 原始流水中不存在provider配置或新老参数使用同一通道时，不使用备用通道 
        if(StringUtil.isEmpty(sourceTradeParamsKey) || bypassTradeParams.containsKey(sourceTradeParamsKey)) {
            return;
        }
        context.remove(sourceTradeParamsKey);
        // 由于部分结算通道在core-b中未定义结算通道，需要使用原始的交易通道
        if (!ApolloConfigurationCenterUtil.getMerchantConfigBypassUseClearanceProviders().contains(provider + "")) {
            context.put(TransactionParam.SOURCE_ORGANIZATION, provider + "");
        } else {
            context.put(TransactionParam.SOURCE_ORGANIZATION, MapUtil.getInteger(context, TransactionParam.CLEARANCE_PROVIDER));
        }
        // 移除主通道特有的参数
        TransactionParam.BYPASS_REMOVE_TRADE_EXT.forEach(key -> context.remove(key));
        // 使用备用通道参数覆盖主通道参数
        context.putAll(bypassTradeParams);
    }

    /**
     * 备用通道合并正式通道的部分字段
     * 
     * @param context
     * @param bypassTradeParams
     */
    private void byPassMergeTradeParams(Map<String, Object> context, Map<String, Object> bypassTradeParams) {
        String sourceTradeParamsKey = metaProviderBiz.getTradeParamsKey(MapUtil.getInteger(context, TransactionParam.PROVIDER));
        // 原始流水中不存在provider配置，不使用备用通道
        if (StringUtil.isEmpty(sourceTradeParamsKey)){
            return;
        }
        Map<String, Object> sourceTradeParams = MapUtil.getMap(context, sourceTradeParamsKey);
        String replaceTradeParamsKey = metaProviderBiz.getTradeParamsKey(MapUtil.getInteger(bypassTradeParams, TransactionParam.PROVIDER));
        Map<String, Object> replaceTradeParams = MapUtil.getMap(bypassTradeParams, replaceTradeParamsKey);
        CoreConstant.BYPASS_USE_FIELDS.forEach(field -> {
            if (sourceTradeParams.get(field) != null) {
                replaceTradeParams.put(field, sourceTradeParams.get(field));
            }
        });
    }
}
