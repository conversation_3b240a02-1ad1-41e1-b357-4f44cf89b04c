package com.wosai.upay.core.service.department;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.StoreService;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by lihebin on 07/03/2018.
 */
@Service
@AutoJsonRpcServiceImpl
@Deprecated
@NoArgsConstructor
public class DepartmentServiceImpl implements DepartmentService {
    @Autowired
    private StoreService storeService;

    @Autowired
    JdbcTemplate userJdbcTemplate;


    private Dao<Map<String, Object>> departmentStoreDao;

    @Autowired
    public DepartmentServiceImpl(DataRepository repository) {
        this.departmentStoreDao = repository.getDepartmentStoreDao();

    }


    @Override
    public List<Map> getDepartmentStoreByDepartmentSn(String departmentSn) {
        if (StringUtil.empty(departmentSn)) {
            return null;
        }
        List<Map> result = new ArrayList<>();
        Criteria criteria = Criteria.where(Department.DEPARTMENT_SN).is(departmentSn);
        List<Map<String, Object>> departmentStores = CollectionUtil.iterator2list(departmentStoreDao.filter(criteria).fetchAll());
        for (Map<String, Object> departmentStore : departmentStores) {
            result.add(storeService.getStore(BeanUtil.getPropString(departmentStore, Department.STORE_ID)));
        }
        return result;
    }


}
