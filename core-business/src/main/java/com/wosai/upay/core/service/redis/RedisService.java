package com.wosai.upay.core.service.redis;

import com.google.common.collect.Maps;
import com.wosai.data.util.CollectionUtil;
import com.wosai.redis.constant.CommonConstant;
import com.wosai.upay.core.constant.CoreConstant;
import com.wosai.upay.core.constant.PublicConstants;
import com.wosai.upay.core.service.RMQService;
import com.wosai.upay.core.util.JsonUtil;
import com.wosai.upay.util.DateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class RedisService {

    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RMQService rmqService;

    public Map<String, Object> getTerminalSecret(String terminalSn){
        Map<String, Object> terminalSecret = null;
        try{
            BoundValueOperations op = redisTemplate.boundValueOps(CoreConstant.TERMINAL_SECRET_REDIS_KEY + terminalSn);
            if (op.persist()) {
                terminalSecret = JsonUtil.jsonStrToObject((String)op.get(), Map.class);
            }
        }catch (Exception ex){
            logger.error("redis error", ex);
            throw ex;
        }
        return terminalSecret;
    }

    public void putTerminalSecret(String terminalSn, Map config){
        try{
            BoundValueOperations op = redisTemplate.boundValueOps(CoreConstant.TERMINAL_SECRET_REDIS_KEY + terminalSn);
            op.set(JsonUtil.toJsonStr(config));
            op.expire(4, TimeUnit.HOURS);
        }catch (Exception ex){
            logger.error("redis error", ex);
            throw ex;
        }
    }

    public void removeTerminalSecret(String terminalSn){
        try{
            redisTemplate.delete(CoreConstant.TERMINAL_SECRET_REDIS_KEY + terminalSn);
        }catch (Exception ex){
            logger.error("redis error", ex);
            throw ex;
        }
    }

    public Map getMapValueFromHash(String key, String field){
        String content;
        try{
            content = (String)redisTemplate.boundHashOps(key).get(field);
            if(!Objects.isNull(content)){
                Map map = JsonUtil.jsonStrToObject(content, Map.class);
                return map != null && map.isEmpty() ? null : map;
            }
        }catch (Exception ex){
            logger.error("redis error", ex);
            throw ex;
        }
        return null;
    }

    public Boolean getMerchantIsFormalFromHash(String key, String field){
        try{
            String flag = (String)redisTemplate.boundHashOps(key).get(field);

            if(flag != null) {
                return "1".equals(flag) ? true: false;
            }else {
                return null;
            }
        }catch (Exception ex){
            logger.error("redis error", ex);
            throw ex;
        }
    }

    public Map getMerchantUpdateMchTime(String merchantId) {
        BoundValueOperations operations = redisTemplate.boundValueOps(PublicConstants.REDIS_CACHE_SWITCH_MCH_TIME + merchantId);
        Object value = operations.get();
        if (value != null && com.wosai.pantheon.util.StringUtil.isNotEmpty(String.valueOf(value))) {
            try {
                Map<String, Object> map = JsonUtil.jsonStrToObject(String.valueOf(value), Map.class);
                if (map != null) {
                    return map;
                }
            } catch (Exception ex) {
                logger.info("redis get error value:{}", value);
            }
        }
        return null;
    }

    public void setMerchantUpdateMchTime(String merchantId, Map mchUpdateTimeMap) {
        BoundValueOperations operations = redisTemplate.boundValueOps(PublicConstants.REDIS_CACHE_SWITCH_MCH_TIME + merchantId);
        operations.set(JsonUtil.toJsonStr(mchUpdateTimeMap), DateTimeUtil.ONE_HOUR_MILLIS, TimeUnit.MILLISECONDS);
    }

    public void setMapValueToHash(String key, String field, Map value){
        try{
            if(CollectionUtils.isEmpty(value)){
                value = Maps.newHashMap();
            }
            redisTemplate.boundHashOps(key).put(field, JsonUtil.toJsonStr(value));
            long time = DateTimeUtil.getOneDayEnd(System.currentTimeMillis());
            long randomTime = new Random().nextInt(7200000); //7200000  2小时的毫秒数 凌晨0-2点随机失效
            redisTemplate.boundHashOps(key).expireAt(new Date(time + randomTime));
        }catch (Exception e){
            logger.error("redis error", e);
            throw e;
        }
    }

    public void setMerchantIsFormalToHash(String key, String field, boolean isFormal){
        try{
            redisTemplate.boundHashOps(key).put(field, isFormal ? "1" : "0");
            long time = DateTimeUtil.getOneDayEnd(System.currentTimeMillis());
            long randomTime = new Random().nextInt(7200000); //7200000  2小时的毫秒数 凌晨0-2点随机失效
            redisTemplate.boundHashOps(key).expireAt(new Date(time + randomTime));
        }catch (Exception e){
            logger.error("redis error", e);
            throw e;
        }
    }


    public Map getProviderBizStatus(Integer provider, String providerMchId) {
        Map<String, Object> map = null;
        try {
            String cacheKey = String.format(PublicConstants.PROVIDER_BIZ_STATUS, provider, providerMchId);
            BoundValueOperations op = redisTemplate.boundValueOps(cacheKey);
            if (op.persist()) {
                map = JsonUtil.jsonStrToObject((String) op.get(), Map.class);
            }
        } catch (Exception ex) {
            logger.error("redis error", ex);
            throw ex;
        }
        return map;
    }

    public void setProviderBizStatus(Integer provider, String providerMchId, Map providerParams) {
        String cacheKey = String.format(PublicConstants.PROVIDER_BIZ_STATUS, provider, providerMchId);
        BoundValueOperations operations = redisTemplate.boundValueOps(cacheKey);
        long time = DateTimeUtil.getOneDayEnd(System.currentTimeMillis());
        long randomTime = new Random().nextInt(7200000); //7200000  2小时的毫秒数 凌晨0-2点随机失效
        Date expireAt = new Date(time + randomTime);
        operations.set(JsonUtil.toJsonStr(providerParams), Duration.between(
                Instant.now(),
                expireAt.toInstant()
        ));
    }

    /**
     * 删除交易参数缓存
     * @param merchantSn
     */
    public void removeCachedParams(String merchantSn) {
        //删除时，保证有值，以便redis同步工具能正常同步redis的删除操作
        String basicKey = PublicConstants.UPAY_BASIC_PARAM + merchantSn;
        String allKey = PublicConstants.UPAY_ALL_PARAM + merchantSn;
        String merchantFormalKey = PublicConstants.UPAY_FORMAL + merchantSn;
        redisTemplate.delete(basicKey);
        redisTemplate.delete(allKey);
        redisTemplate.delete(merchantFormalKey);
        rmqService.writeRedisChange(getDelChangeMessage(basicKey));
        rmqService.writeRedisChange(getDelChangeMessage(allKey));
        rmqService.writeRedisChange(getDelChangeMessage(merchantFormalKey));
    }


    /**
     * 删除花呗分期参数的缓存
     * @param merchantId
     */
    public void removeCachedHuabeiParams(String merchantId){
        String key = PublicConstants.HUABEI_PARAM + merchantId;
        redisTemplate.delete(key);
        rmqService.writeRedisChange(getDelChangeMessage(key));
    }

    public void removeCacheSwitchMchTime(String merchantId){
        String key = PublicConstants.REDIS_CACHE_SWITCH_MCH_TIME + merchantId;
        redisTemplate.delete(key);
        rmqService.writeRedisChange(getDelChangeMessage(key));
    }


    /**
     * 删除分期参数的缓存
     *
     * @param merchantId
     */
    public void removeCachedFqParams(String merchantId, Integer payway) {
        String key = String.format(PublicConstants.FQ_STATUS_FORMAT, merchantId, payway);
        redisTemplate.delete(key);
        rmqService.writeRedisChange(getDelChangeMessage(key));
    }

    public void removeCacheProviderBizStatus(Integer provider, String providerMchId) {
        String key = String.format(PublicConstants.PROVIDER_BIZ_STATUS, provider, providerMchId);
        redisTemplate.delete(key);
        rmqService.writeRedisChange(getDelChangeMessage(key));
    }

    /**
     * 获取redis删除消息
     * @param key
     * @return
     */
    private Map<String,Object> getDelChangeMessage(String key){
        return CollectionUtil.hashMap(
                CommonConstant.KAFKA_REDIS_CHANGE_DATA_NAME, CommonConstant.DATA_NAME_CORE_BUSINESS,
                CommonConstant.KAFKA_REDIS_CHANGE_KEY, key,
                CommonConstant.KAFKA_REDIS_CHANGE_TYPE, CommonConstant.TYPE_DELETE
        );
    }
}
