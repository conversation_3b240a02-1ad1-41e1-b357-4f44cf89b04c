package com.wosai.upay.core.service.user;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.PageInfoUtil;
import com.wosai.upay.common.dao.SimpleConditionUtil;
import com.wosai.upay.common.log.LogstashMarkerAppendFileds;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.department.Department;
import com.wosai.upay.core.model.department.MerchantUserDepartmentAuth;
import com.wosai.upay.core.model.user.*;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.UuidGenerator;
import com.wosai.upay.core.util.CoreUtil;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;

/**
 * Created by jianfree on 22/3/16.
 */
@Service
@AutoJsonRpcServiceImpl
@Deprecated
@NoArgsConstructor
public class UserServiceImpl implements UserService {

    private Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UuidGenerator uuidGenerator;

    private Dao<Map<String, Object>> vendorUserDao;
    private Dao<Map<String, Object>> merchantUserDao;
    private Dao<Map<String, Object>> merchantUserStoreAuthDao;
    private Dao<Map<String, Object>> accountDao;

    private Dao<Map<String, Object>> departmentDao;
    private Dao<Map<String, Object>> merchantUserDepartmentAuthDao;

    @Autowired
    JdbcTemplate userJdbcTemplate;

    private final String SUPER_ADMIN = "super_admin";


    @Autowired
    public UserServiceImpl(DataRepository dataRepository) {
        this.vendorUserDao = dataRepository.getVendorUserDao();
        this.merchantUserDao = dataRepository.getMerchantUserDao();
        this.merchantUserStoreAuthDao = dataRepository.getMerchantUserStoreAuthDao();
        this.accountDao = dataRepository.getAccountDao();
        this.departmentDao = dataRepository.getDepartmentDao();
        this.merchantUserDepartmentAuthDao = dataRepository.getMerchantUserDepartmentAuthDao();
    }

    @Override
    public Map getAccount(String accountId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(accountId);
        return accountDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getAccountByCellphone(String cellphone) {
        Criteria criteria = Criteria.where(Account.CELLPHONE).is(cellphone);
        return accountDao.filter(criteria).fetchOne();
    }

    @Override
    public Map createVendorUser(Map vendorUser) {
        if (vendorUser.get(DaoConstants.ID) == null) {
            vendorUser.put(DaoConstants.ID, uuidGenerator.nextUuid());
        }
        vendorUser.put(VendorUser.STATUS, VendorUser.STATUS_ENABLED);
        vendorUserDao.save(vendorUser);
        return vendorUser;
    }

    @Override
    public void deleteVendorUser(String vendorUserId) {
        vendorUserDao.delete(vendorUserId);
        //todo 删除权限
    }

    @Override
    public Map updateVendorUser(Map vendorUser) {
        vendorUserDao.updatePart(vendorUser);
        return getVendorUser(BeanUtil.getPropString(vendorUser, DaoConstants.ID));
    }

    @Override
    public Map getVendorUser(String vendorUserId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(vendorUserId);
        return vendorUserDao.filter(criteria).fetchOne();
    }

    @Override
    public Map getVendorUserByAccountId(String accountId) {
        Criteria criteria = Criteria.where(VendorUser.ACCOUNT_ID).is(accountId);
        return vendorUserDao.filter(criteria).fetchOne();
    }


    @Override
    public void disableVendorUser(String vendorUserId) {
        Map update = CollectionUtil.hashMap(
                DaoConstants.ID, vendorUserId,
                VendorUser.STATUS, VendorUser.STATUS_DISABLED
        );
        vendorUserDao.updatePart(update);
    }

    @Override
    public ListResult findVendorUsers(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put("vendor_id", VendorUser.VENDOR_ID);
            put("account_id", VendorUser.ACCOUNT_ID);
            put("deleted", DaoConstants.DELETED);
        }});
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = vendorUserDao.filter(criteria).count();
        Filter filter = vendorUserDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }


    @Override
    public void deleteMerchantUser(String merchantUserId) {
        if (StringUtil.empty(merchantUserId)) {
            return;
        }
        Map user = getMerchantUser(merchantUserId);
        logger.info(LogstashMarkerAppendFileds.append(user), "delete merchant user");
        merchantUserDao.delete(merchantUserId);
        userJdbcTemplate.update("delete from merchant_user_department_auth where merchant_user_id = ?", merchantUserId);
    }


    @Override
    public Map getMerchantUser(String merchantUserId) {
        Criteria criteria = Criteria.where(DaoConstants.ID).is(merchantUserId);
        Map userMap = merchantUserDao.filter(criteria).fetchOne();
        if (userMap != null && userMap.get(ConstantUtil.KEY_ACCOUNT_ID) != null) {
            Map account = getAccount(BeanUtil.getPropString(userMap, ConstantUtil.KEY_ACCOUNT_ID));
            if (account != null) {
                userMap.put("cellphone", BeanUtil.getPropString(account, ConstantUtil.KEY_CELLPHONE));
            }
        }
        return userMap;
    }

    @Override
    public Map getMerchantUserByAccountId(String accountId) {
        Criteria criteria = Criteria.where(MerchantUser.ACCOUNT_ID).is(accountId).with(MerchantUser.STATUS).is(MerchantUser.STATUS_ENABLED);
        Filter<Map<String, Object>> filter = merchantUserDao.filter(criteria);
        filter.orderBy(new ArrayList<Filter.OrderByField>() {{
            add(new Filter.OrderByField(DaoConstants.CTIME, Filter.DESC));
        }});
        return filter.fetchOne();
    }


    @Override
    /**
     * 账号数据有点混乱，一个商户存在多个超级管理员，默认获取最近创建的账号
     */
    public Map getMerchantSuperAdminUserAccount(String merchantId) {
        Filter merchantUserFilter =  merchantUserDao
                .filter(Criteria.where(MerchantUser.ROLE)
                .is(SUPER_ADMIN).with(MerchantUser.MERCHANT_ID)
                .is(merchantId)
                , Lists.newArrayList(MerchantUser.ACCOUNT_ID));
        merchantUserFilter.limit(100);
        List<Map<String, Object>> merchantUsers = CollectionUtil.iterator2list(merchantUserFilter.fetchAll());
        if(!CollectionUtils.isEmpty(merchantUsers)){
            Set accountSet = Sets.newHashSet();
            for(Map<String, Object> merchantUser:merchantUsers){
                accountSet.add(merchantUser.get(MerchantUser.ACCOUNT_ID));
            }
           Filter<Map<String, Object>> filter = accountDao
                   .filter(Criteria.where(DaoConstants.ID)
                   .in(accountSet)
                   .with(Account.STATUS)
                   .is(Account.STATUS_ENABLED));
           filter.orderBy(DaoConstants.CTIME, Filter.DESC);
           filter.limit(1);
           return filter.fetchOne();
        }
        return null;
    }

    @Override
    public List<Map> getMerchantUserStoreAuths(Map queryFilter) {
        String accountId = BeanUtil.getPropString(queryFilter, "account_id");
        if (!StringUtil.empty(accountId)) {
            queryFilter.remove("account_id");
            Map<String, Object> merchantUser = getMerchantUserByAccountId(accountId);
            if (merchantUser != null) {
                queryFilter.put(MerchantUserStoreAuth.MERCHANT_USER_ID, BeanUtil.getPropString(merchantUser, DaoConstants.ID));
            } else {
                return new ArrayList<>();
            }
        }
        PageInfo pageInfo = new PageInfo(1, 100000, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        return findMerchantUserStoreAuths(pageInfo, queryFilter).getRecords();
    }

    @Override
    public ListResult findMerchantUserStoreAuths(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(MerchantUserStoreAuth.MERCHANT_ID, MerchantUserStoreAuth.MERCHANT_ID);
            put(MerchantUserStoreAuth.MERCHANT_USER_ID, MerchantUserStoreAuth.MERCHANT_USER_ID);
            put(MerchantUserStoreAuth.STORE_ID, MerchantUserStoreAuth.STORE_ID);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        List<String> storeIds = (List<String>) BeanUtil.getProperty(queryFilter, "store_ids");
        if (storeIds != null && storeIds.size() > 0) {
            criteria.with(MerchantUserStoreAuth.STORE_ID).in(storeIds);
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = merchantUserStoreAuthDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = merchantUserStoreAuthDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List list = CollectionUtil.iterator2list(filter.fetchAll());
        return new ListResult(count, list);
    }

    @Override
    public List<Map> getMerchantUserDepartmentAuths(Map queryFilter) {
        String accountId = BeanUtil.getPropString(queryFilter, "account_id");
        if (!StringUtil.empty(accountId)) {
            queryFilter.remove("account_id");
            Map<String, Object> merchantUser = getMerchantUserByAccountId(accountId);
            if (merchantUser != null) {
                queryFilter.put(MerchantUserDepartmentAuth.MERCHANT_USER_ID, BeanUtil.getPropString(merchantUser, DaoConstants.ID));
            } else {
                return new ArrayList<>();
            }
        }
        PageInfo pageInfo = new PageInfo(1, 100000, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        return findMerchantUserDepartmentAuths(pageInfo, queryFilter).getRecords();
    }


    @Override
    public ListResult findMerchantUserDepartmentAuths(PageInfo pageInfo, Map queryFilter) {
        pageInfo = CoreUtil.formatPageInfo(pageInfo);
        Criteria criteria = new Criteria();
        SimpleConditionUtil.appendCriteria(criteria, queryFilter, new LinkedHashMap<String, String>() {{
            put(MerchantUserDepartmentAuth.MERCHANT_ID, MerchantUserDepartmentAuth.MERCHANT_ID);
            put(MerchantUserDepartmentAuth.MERCHANT_USER_ID, MerchantUserDepartmentAuth.MERCHANT_USER_ID);
            put(MerchantUserDepartmentAuth.DEPARTMENT_ID, MerchantUserDepartmentAuth.DEPARTMENT_ID);
            put(DaoConstants.DELETED, DaoConstants.DELETED);
        }});
        List<String> departmentIds = (List<String>) BeanUtil.getProperty(queryFilter, "department_ids");
        if (departmentIds != null && departmentIds.size() > 0) {
            criteria.with(MerchantUserDepartmentAuth.DEPARTMENT_ID).in(departmentIds);
        }
        SimpleConditionUtil.appendCriteriaDateInfo(criteria, pageInfo);
        long count = merchantUserDepartmentAuthDao.filter(criteria).count();
        SimpleConditionUtil.appendPageInfoCtimeDesc(pageInfo);
        Filter filter = merchantUserDepartmentAuthDao.filter(criteria);
        PageInfoUtil.pagination(pageInfo, filter);
        List<Map> list = CollectionUtil.iterator2list(filter.fetchAll());
        List<Map> listResult = new ArrayList<>();
        for (Map map : list) {
            String departmentId = BeanUtil.getPropString(map, Department.DEPARTMENT_ID);
            Criteria criteriaDepartment = Criteria.where(DaoConstants.ID).is(departmentId);
            Map department = departmentDao.filter(criteriaDepartment).fetchOne();
            if (!(department == null || department.isEmpty())) {
                map.putAll(department);
                listResult.add(map);
            }
        }
        return new ListResult(count, listResult);
    }


}
