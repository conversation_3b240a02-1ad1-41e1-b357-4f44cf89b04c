package com.wosai.upay.core.util;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.constant.ApolloConstants;
import com.wosai.upay.core.datasource.DataSourceConstant;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.meta.Provider;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.core.exception.CoreBizException;
import com.wosai.upay.core.exception.CoreScenesException;
import com.wosai.upay.core.model.MerchantConfigBypassStrategy;
import com.wosai.upay.core.model.TransactionParam;


public class ApolloConfigurationCenterUtil {
	private static Logger logger = LoggerFactory.getLogger(ApolloConfigurationCenterUtil.class);
	
	private static Config config = null;
	private static Config configCoreBase = null;
	public static Map<String, String> defaultGateway;

	private static final String TERMINAL_NAME_VENDOR_APPID_MAPPING = "terminal_name_vendor_appId_mapping";
	private static final String DEFAULT_LADDER_FEE_RATES_KEY = "default_ladder_fee_rates";
	private static final String HUABEI_PARAMS_KEY = "huabei_params";
	private static final String CREDIT_PARAMS_KEY = "credit_params";
	private static final String HUABEI_DEFAULT_STATUS_KEY = "huabei_default_status_key";
	private static final String HUABEI_DEFAULT_MIN_LIMIT_KEY = "huabei_default_min_limit_key";
	private static final String RISK_WHITELIST_KEY = "risk_whitelist";
	private static final String CITY_ACTIVATE_LIMIT_KEY = "city_activate_limit";
	private static final String SHOUQIANBA_VENDOR_IDS_KEY = "shouqianba_vendor_ids";
	private static final String SHOUQIANBA_COOPERATION_APP_IDS_KEY = "shouqianba_cooperation_appids";
	private static final String VENDOR_APP_CATEGORY_CHECK = "vendor_app_category_check"; //终端类目校验
	private static final String QRCODE_VENDOR_APPIDS = "qrcode_vendor_appids";
	private static final String SQB_LKL_CORP_SQB_SPECIAL_VENDOR_APPIDS = "sqb_lkl_corp_sqb_special_vendor_appids"; //收钱吧拉卡拉合作收钱吧特有终端应用
	private static final String PLUGIN_ACTIVE_DISABLE_VENDOR_APPIDS = "plugin.active.disable.vendor.appids"; //插件激活服务禁用vendorAppAppId列表配置项
	private static final String SQB_LKL_CORP_LKL_SPECIAL_VENDOR_APPIDS = "sqb_lkl_corp_lkl_special_vendor_appids"; //收钱吧拉卡拉合作拉卡拉特有终端应用
	private static final String CHECK_FIND_TERMINALS_FLAG = "check_find_terminals_flag"; //是否校验查询终端的查询条件
	private static final String CHECK_LIQUIDATION_NEXT_DAY_CONFIG = "check_liquidation_next_day_config"; //校验是否由喔噻结算字段的配置是否有异常
	private static final String CHANNEL_NAME_CONFIG = "channel_name_config";
	private static final String PROHIBITED_UNBOUND_VENDOR_APP_APPIDS = "prohibited_unbound_vendor_app_appids";
    private static final String MERCHANT_CONFIG_BYPASS_STRATEGY = "merchant_config_bypass_strategy";
    private static final String MERCHANT_CONFIG_BYPASS_USE_CLEARANCE_PROVIDERS = "merchant_config_bypass_use_clearance_providers";
    private static final String CHANGE_SHIFTS_CHECKOUT_INTERVAL = "change_shifts_checkout_interval";    // 重复签退间隔时间
    private static final String CHANGE_TO_APPOINT_DB = "change_to_appoint_db";    // 切换到指定数据库（master/slave）
	private static final String KEY_T9_VENDOR_APP_ID_PROVIDER_MAPPING = "t9_vendor_app_id_provider_mapping";
	private static final String KEY_PHONE_POS_TRADE_APP = "phone_pos_trade_app"; //手机pos专用的trade_app 生产跟测试环境不一致
	private static final String KEY_DEFAULT_PHONE_POS_TRADE_LIMIT = "default_phone_pos_trade_limit";//手机pos默认的限额配置
	private static final String KEY_BAITIAO_PARAMS = "baitiao_params"; //白条分期费率
	private static final String KEY_CHECK_EXTERNAL_EXTRA_CONFIG_FLAG = "check_external_extra_config_flag";//校验收单参数状态
	private static final String KEY_CHECK_EXTERNAL_PROVIDER_MCH_ID_KEY = "check_external_extra_provider_mch_id_key";//校验收单机构子商户号的_trade_params里的key获取位置
	private static final String KEY_INSTALLMENT_24_RATE_CONFIG = "installment_24_rate_config";
	private static final String KEY_WFT_PROVIDERS= "wft_providers";
	private static final String PAY_DISABLE_VENDOR_APPIDS = "pay.disable.vendor.appids"; //支付业务禁用vendorAppAppId列表配置项
	private static final String KEY_DELETE_TRADE_EXT_THRESHOLD = "batch_delete_trade_ext_config_threshold";
	/**
	 * 数据总线直接发到kafka
	 */
	private static final String DATABUS_DIRECT_TO_KAFKA = "databus_direct_to_kafka";

	private static final String KEY_FITNESS_APP_ID = "fitness_app_id";
    private static final String DEFAULT_LADDER_FEE_RATES_VALUE = "[{\"min\":0,\"max\":300,\"b2c_fee_rate\":0,\"c2b_fee_rate\":0,\"wap_fee_rate\":0,\"mini_fee_rate\":0},{\"min\":300,\"max\":null,\"b2c_fee_rate\":0.6,\"c2b_fee_rate\":0.6,\"wap_fee_rate\":0.6,\"mini_fee_rate\":0.6}]";
	private static final int DEFAULT_HUABEI_MIN_LIMIT = 30000;
	private static final int DEFAULT_HUABEI_STATUS = 0;
	private static final int DEFAULT_BAITIAO_MIN_LIMIT = 10000;
	private static final int DEFAULT_BAITIAO_STATUS = 0;

	private static final String DEFAULT_FITNESS_PARAMS = "default_fitness_params";
	private static final String DEFAULT_FQ_PARAMS = "default_fq_params";

	private static volatile Map<String, Map<String, Object>> DEFAULT_COMMON_SWITCH = new HashMap<>();
    // 保存省->区的对应关系
    private static Map<String, Map<String, String>> PROVINCE_AND_CITYS = new HashMap<>();
    private static Set<String> RISK_WHITELIST = null;
    private static Map<String, List<String>> CITY_ACTIVATE_LIMIT = new HashMap<>();
    private static List<String> shouqianbaVendorIds = new ArrayList<>();
    private static List<String> shouqianbaCooperationAppIds = new ArrayList<>();
    private static MerchantConfigBypassStrategy bypassStrategy= null;
    private static List<String> bypassUseClearanceProviders = new ArrayList<>();
    private static volatile String changeToAppointDB = null;
	private static Map<String, List<Integer>> vendorT9ProviderMap = new ConcurrentHashMap<>();

	private static Map<String, Map<String, Object>> fqDefaultParamsMap = new HashMap<>();

	private static Map<String, Object> defaultPhonePosTradeLimitConfig = new HashMap<>();

	private static Map<String, Object> default24InstallmentRateConfig = new HashMap<>();
	private static Set<String> payDisableVendorAppIds = new HashSet<>();

	private static Map<Integer, String> overrideProviderMchIdKey = MapUtil.hashMap(
			Payway.ALIPAY2.getCode(), TransactionParam.ALIPAY_MCH_ID,
			Payway.WEIXIN.getCode(), TransactionParam.WEIXIN_SUB_MCH_ID,
			Provider.UNION_PAY_OPEN.getCode(), TransactionParam.UNION_PAY_OPEN_MCH_ID,
			Provider.CHINA_UMS.getCode(), TransactionParam.CHINAUMS_MCH_CODE,
			Provider.CMB_BANK.getCode(), TransactionParam.MER_ID,
			Provider.FOXCONN.getCode(), TransactionParam.FOXCONN_EQUIPMENT_SN,
			Provider.CCB_BANK.getCode(), TransactionParam.CCB_MERCHANT_ID,
			Provider.CIB_HZ_BANK.getCode(), TransactionParam.NUCC_SP_MCH_ID,
			Provider.ICBC_BANK.getCode(), TransactionParam.MER_ID,
			Provider.LAKALA_UNION_PAY_V3.getCode(), TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID,
			Provider.TL_SYB.getCode(), TransactionParam.TL_SYB_CUS_ID,
			Provider.CCB_GIFT_CARD.getCode(), TransactionParam.CCB_GIFT_CARD_PARAMS_BUSINESS_ID,
			Provider.ABC.getCode(), TransactionParam.ABC_COUNTNO,
			Provider.ENTPAY.getCode(), TransactionParam.ENTPAY_ENT_ID
	);


	//禁止解除绑定的vendor_app_appid集合
    private static volatile Set<String> PROHIBITED_UNBOUND_VENDOR_APP_APPID_SET = Sets.newHashSet();

	public static final Map<Integer, String> FQ_STATUS_MAPPING = com.wosai.pantheon.util.MapUtil.hashMap(
			Payway.JDPAY.getCode(), TransactionParam.JD_BAITIAO_STATUS
	);
	// 威富通渠道
	private static Set<Integer> WFT_PROVIDERS = Collections.emptySet();

	static{
		config = ConfigService.getAppConfig();
		configCoreBase = ConfigService.getConfig("core.base");
		initDefaultCommonSwitch();
		initProvinceAndCitys();
		initRiskWhiteList();
		initCityActivateLimit();
		initShouqianbaVendorIds();
		initShouqianbaCooperationAppIds();
		initProhibitedUnboundVendorAppAppidSet();
		initMerchantConfigBypassStrategy();
		initBypassUseClearanceProviders();
		initChangeToAppointDB();
		initVendorT9ProviderMap();
		initPhonePosTradeLimitConfig();
		initDefaultFqParams();
		initOverrideProviderMchIdKey();
		initInstallment24RateConfig();
		initWftProviders();
		initPayDisableVendorAppIds();
		config.addChangeListener(listener ->{
		    if (listener.isChanged(ApolloConstants.COMMON_SWITCH_KEY)) {
				initDefaultCommonSwitch();
			}
		    if(listener.isChanged(ApolloConstants.UNION_PAY_DISTRICT_CODE_KEY)) {
		        initProvinceAndCitys();
		    }
		    if(listener.isChanged(RISK_WHITELIST_KEY)) {
		        initRiskWhiteList();
		    }
		    if(listener.isChanged(CITY_ACTIVATE_LIMIT_KEY)) {
		        initCityActivateLimit();
		    }
            if(listener.isChanged(SHOUQIANBA_VENDOR_IDS_KEY)){
                initShouqianbaVendorIds();
            }
			if(listener.isChanged(SHOUQIANBA_COOPERATION_APP_IDS_KEY)){
				initShouqianbaCooperationAppIds();
			}
            if (listener.isChanged(PROHIBITED_UNBOUND_VENDOR_APP_APPIDS)) {
            	initProhibitedUnboundVendorAppAppidSet();
			}
            if (listener.isChanged(MERCHANT_CONFIG_BYPASS_STRATEGY)){
                initMerchantConfigBypassStrategy();
            }
            if (listener.isChanged(MERCHANT_CONFIG_BYPASS_USE_CLEARANCE_PROVIDERS)){
                initBypassUseClearanceProviders();
            }
            if (listener.isChanged(CHANGE_TO_APPOINT_DB)) {
                initChangeToAppointDB();
            }
			if (listener.isChanged(DEFAULT_FQ_PARAMS)) {
				initDefaultFqParams();
			}
			if (listener.isChanged(KEY_CHECK_EXTERNAL_PROVIDER_MCH_ID_KEY)) {
				initOverrideProviderMchIdKey();
			}
			if (listener.isChanged(KEY_INSTALLMENT_24_RATE_CONFIG)) {
				initInstallment24RateConfig();
			}
			if (listener.isChanged(KEY_WFT_PROVIDERS)) {
				initWftProviders();
			}
			if (listener.isChanged(PAY_DISABLE_VENDOR_APPIDS)){
				initPayDisableVendorAppIds();
			}
		});
		configCoreBase.addChangeListener(listener -> {
			if (listener.isChanged(KEY_T9_VENDOR_APP_ID_PROVIDER_MAPPING)) {
				initVendorT9ProviderMap();
			}
			if(listener.isChanged(KEY_DEFAULT_PHONE_POS_TRADE_LIMIT)){
				initPhonePosTradeLimitConfig();
			}
		});
	}

	private static void initChangeToAppointDB() {
        String changeValue = config.getProperty(CHANGE_TO_APPOINT_DB, null);
        if (changeValue == null || changeValue.isEmpty()) {
            changeValue = null;
        } else {
            if (!DataSourceConstant.MASTER.equals(changeValue) && !DataSourceConstant.SLAVE.equals(changeValue)) {
                logger.warn("change to appoint db fail, current value: {}, change value: {}", changeToAppointDB, changeValue);
                return;
            }
        }
        changeToAppointDB = changeValue;
    }



    private static void initMerchantConfigBypassStrategy() {
        String bypassStrategyStr = config.getProperty(MERCHANT_CONFIG_BYPASS_STRATEGY, "");
        MerchantConfigBypassStrategy changeBypassStrategy = JsonUtil.jsonStrToObject(bypassStrategyStr, MerchantConfigBypassStrategy.class);
        bypassStrategy = changeBypassStrategy;
    }

    private static void initBypassUseClearanceProviders() {
        String changeBypassClearanceProvidersStr = config.getProperty(MERCHANT_CONFIG_BYPASS_USE_CLEARANCE_PROVIDERS, "");
        List<String> changeBypassClearanceProviders = JsonUtil.jsonStrToObject(changeBypassClearanceProvidersStr, List.class);
        bypassUseClearanceProviders = changeBypassClearanceProviders;

    }
    
    private static void initRiskWhiteList() {
	    String newValue = config.getProperty(RISK_WHITELIST_KEY, "");
	    RISK_WHITELIST = new HashSet<String>(Arrays.asList(newValue.split(",")));
	}

	private static void initShouqianbaVendorIds(){
		String value = config.getProperty(SHOUQIANBA_VENDOR_IDS_KEY, "[]");
		shouqianbaVendorIds = JsonUtil.jsonStrToObject(value, List.class);
	}

	private static void initShouqianbaCooperationAppIds(){
		String value = config.getProperty(SHOUQIANBA_COOPERATION_APP_IDS_KEY, "[]");
		shouqianbaCooperationAppIds = JsonUtil.jsonStrToObject(value, List.class);
	}

	private static void initProhibitedUnboundVendorAppAppidSet() {
		String value = config.getProperty(PROHIBITED_UNBOUND_VENDOR_APP_APPIDS, "[]");
		PROHIBITED_UNBOUND_VENDOR_APP_APPID_SET = JsonUtil.jsonStrToObject(value, Set.class);
	}

	public static List<String> getShouqianbaVendorIds() {
		return shouqianbaVendorIds;
	}

    private static void initCityActivateLimit() {
        Map<String, Object> changeVal = JsonUtil.jsonStrToObject(config.getProperty(CITY_ACTIVATE_LIMIT_KEY, "{}"), Map.class);
        Map<String, List<String>> newVal = new HashMap<String, List<String>>();
        Map<String, String> citys = (Map<String, String>) changeVal.get("citys");
        Map<String, List<String>> combinations = MapUtil.getMap(changeVal, "combinations", new HashMap());
        if(citys != null) {
            citys.forEach((cityName, combination) ->{
                List<String> combinationList = combinations.get(combination);
                if(combinationList == null) {
                    combinationList = new ArrayList<String>();
                }
                newVal.put(cityName, combinationList);
            });
        }
        CITY_ACTIVATE_LIMIT = newVal;
    }

    private static void initProvinceAndCitys() {
        Map<String, Map<String, String>> changeVal = JsonUtil.jsonStrToObject(config.getProperty(ApolloConstants.UNION_PAY_DISTRICT_CODE_KEY, ApolloConstants.UNION_PAY_DISTRICT_CODE_DEFAULT_VALUE), Map.class);
        if(MapUtils.isNotEmpty(changeVal)) {
            PROVINCE_AND_CITYS = changeVal;
        }
    }

	private static void initDefaultCommonSwitch() {
		Map<String, Map<String, Object>> switchObj = JsonUtil.jsonStrToObject(config.getProperty(ApolloConstants.COMMON_SWITCH_KEY, "{}"), Map.class);
		if (MapUtils.isNotEmpty(switchObj)) {
			DEFAULT_COMMON_SWITCH = switchObj;
		}
	}
	
	/**
	 * 
	 * 获取交易错误码
	 * 
	 * @param errorScenes 支付网关场景错误码
	 * 
	 * @return Map
	 * 		key error_code：业务错误码
	 * 		key error_message：返回信息
	 *      key error_code_standard：标准错误码
	 */
	public static void getWosaiErrorDefinition(final CoreScenesException ex) {
		Map info = JsonUtil.jsonStrToObject(config.getProperty(ex.getErrorScenes(), null), Map.class);
		String message = BeanUtil.getPropString(info, "error_message");
		final int code = BeanUtil.getPropInt(info, "error_code", ex.getCode());
		String bizCode = BeanUtil.getPropString(info, "error_code_standard");
		
		if(StringUtil.empty(message)){
			message = CoreErrorScenesConstant.defaultScenesMessage.get(ex.getErrorScenes());
		}
		if(!StringUtil.empty(bizCode)){
			message = String.format("%s[%s]", message, bizCode);
		}
		
		CoreBizException coreException = null;
		try{
			Class newEx = Class.forName(ex.getExceptionName());  
			Constructor c= newEx.getConstructor(String.class);
			coreException = (CoreBizException)c.newInstance(message);
			throw coreException;
		}catch (ClassNotFoundException | NoSuchMethodException 
				|InvocationTargetException | IllegalAccessException
				|InstantiationException e) {
			coreException = new CoreBizException(message) {
				public int getCode() {
					return code;
				}
			};
		}
		throw coreException;
	}

	public static JSONObject getTerminalVendorAppIdMapping(){
		String jsonStr = config.getProperty(TERMINAL_NAME_VENDOR_APPID_MAPPING,"{}");
		return JSONObject.parseObject(jsonStr);
	}

	public static List<String> getQrcodeVendorAppIds(){
		String jsonStr = config.getProperty(QRCODE_VENDOR_APPIDS,"[]");
		return  JSONArray.parseArray(jsonStr).toJavaList(String.class);

	}
	
	public static List<Map> getDefaultLadderFeeRates(){
		String jsonStr = config.getProperty(DEFAULT_LADDER_FEE_RATES_KEY, DEFAULT_LADDER_FEE_RATES_VALUE);
		return JSONArray.parseArray(jsonStr).toJavaList(Map.class);
	}

	public static List<Map> getHuabeiConfigParams(){
		String jsonStr = config.getProperty(HUABEI_PARAMS_KEY, "");
		return JSONArray.parseArray(jsonStr).toJavaList(Map.class);
	}

	public static List<Map> getCreditConfigParams(){
		String jsonStr = config.getProperty(CREDIT_PARAMS_KEY, "");
		return JSONArray.parseArray(jsonStr).toJavaList(Map.class);
	}

	public static int getHuabeiDefaultMinLimit(){
		return config.getIntProperty(HUABEI_DEFAULT_MIN_LIMIT_KEY, DEFAULT_HUABEI_MIN_LIMIT);
	}

	public static int getHuabeiDefaultStatus(){
		return config.getIntProperty(HUABEI_DEFAULT_STATUS_KEY, DEFAULT_HUABEI_STATUS);
	}

	public static List<Map> getBaitiaoConfigParams(){
		String jsonStr = config.getProperty(KEY_BAITIAO_PARAMS, "");
		return JSONArray.parseArray(jsonStr).toJavaList(Map.class);
	}

    public static int getDefaultCommonSwitchStatus(int type) {
		if (MapUtils.isEmpty(DEFAULT_COMMON_SWITCH)) {
			throw new CoreInvalidParameterException("通用开关配置错误");
		}
		Map typeToSwitch = MapUtil.getMap(DEFAULT_COMMON_SWITCH, Integer.toString(type));
		if (MapUtils.isEmpty(typeToSwitch)) {
			throw new CoreInvalidParameterException("通用开关配置错误");
		}

		return MapUtil.getIntValue(typeToSwitch, ApolloConstants.COMMON_SWITCH_DEFAULT_VALUE);
	}

	public static boolean isLegalType(int type) {
		if (MapUtils.isEmpty(DEFAULT_COMMON_SWITCH)) {
			throw new CoreInvalidParameterException("通用开关配置错误");
		}
		Set<String> keySet = DEFAULT_COMMON_SWITCH.keySet();
		return keySet.contains(Integer.toString(type));
	}

	public static boolean checkExternalFlag(boolean defaultValue) {
		return config.getBooleanProperty(KEY_CHECK_EXTERNAL_EXTRA_CONFIG_FLAG, defaultValue);
	}

	public static String replaceCommonSwitchDefaultValue(String oriSwitch) {
		if (StringUtils.isBlank(oriSwitch)) {
			throw new CoreInvalidParameterException("通用开关入参错误");
		}
		StringBuilder oriSwitchBuilder = new StringBuilder(oriSwitch);
		Set<String> typeSet = DEFAULT_COMMON_SWITCH.keySet();
		for (String type : typeSet) {
			int typeNum = Integer.parseInt(type);
			//仅替换未配置的开关
			if (Integer.parseInt(String.valueOf(oriSwitchBuilder.charAt(typeNum))) != TransactionParam.STATUS_NOT_CONFIGURED) {
				continue;
			}
			int defaultValue = MapUtil.getIntValue(MapUtil.getMap(DEFAULT_COMMON_SWITCH, type), ApolloConstants.COMMON_SWITCH_DEFAULT_VALUE);
			oriSwitchBuilder.replace(typeNum, typeNum + 1, Integer.toString(defaultValue));
		}
		return oriSwitchBuilder.toString();
	}
	
    public static String getMerchantDistrictCode(String province, String city) {
        String code = null;
        Map<String, String> info = getValueByLikeKey(PROVINCE_AND_CITYS, province);
        if(null != info) {
            String nCode = getValueByLikeKey(info, city);
            if(!StringUtil.empty(nCode)) {
                code = nCode;
            }
        }
        
        if(code == null) {
            code = ApolloConstants.DEFAULT_UNION_PAY_DISTRICT_CODE;
        }
        
        return ApolloConstants.GB_CHINA + code;
    }

    private static <T> T  getValueByLikeKey(Map<String, T> info, String key) {
        if(info.containsKey(key)) {
            return info.get(key);
        }
        for (String infoKey : info.keySet()) {
            if(infoKey.indexOf(key) >= -1) {
                return info.get(key);
            }
        }
        return null;
    }

    public static boolean inWhitelist(String merchantSn) {
        if(null != RISK_WHITELIST && RISK_WHITELIST.contains(merchantSn)) {
            return true;
        }
        return false;
    }

    public static boolean isShouqianbaVendorId(String vendorId){
		if(shouqianbaVendorIds == null || shouqianbaVendorIds.isEmpty()){
			return false;
		}else{
			return shouqianbaVendorIds.contains(vendorId);
		}
	}

	public static boolean isShouqianbaCooperationAppIds(String appId){
		if(shouqianbaCooperationAppIds == null || shouqianbaCooperationAppIds.isEmpty()){
			return false;
		}else{
			return shouqianbaCooperationAppIds.contains(appId);
		}
	}

	public static boolean vendorAppCategoryCheck(){
		return config.getBooleanProperty(VENDOR_APP_CATEGORY_CHECK, Boolean.FALSE);
	}

	public static List<String> getSqbLklCorpSqbSpecialVendorAppids(){
		return JSONArray.parseArray(config.getProperty(SQB_LKL_CORP_SQB_SPECIAL_VENDOR_APPIDS, "[]"), String.class);
	}

	/**
	 * 获取插件服务禁用的vendor_app_appid列表
	 *
	 * @return
	 */
	public static List<String> getPluginActiveDisableVendorAppids(){
		List<String> list = JSONArray.parseArray(config.getProperty(PLUGIN_ACTIVE_DISABLE_VENDOR_APPIDS, "[]"), String.class);
		return CollectionUtils.isEmpty(list) ? Collections.emptyList() : list;
	}

	public static List<String> getSqbLklCorpLklSpecialVendorAppids(){
		return JSONArray.parseArray(config.getProperty(SQB_LKL_CORP_LKL_SPECIAL_VENDOR_APPIDS, "[]"), String.class);
	}

	public static String getChannelName(Integer provider) {
		Map channelNameMap = JsonUtil.jsonStrToObject(config.getProperty(CHANNEL_NAME_CONFIG
				, "{}"), Map.class);
		if (provider == null) {
			return MapUtils.getString(channelNameMap, StringUtils.EMPTY);
		}
		String channelName = MapUtils.getString(channelNameMap, Integer.toString(provider));
		if (StringUtils.isEmpty(channelName)) {
			channelName = MapUtils.getString(channelNameMap, StringUtils.EMPTY);
		}
		return channelName;
	}

	/**
	 *
	 * @param cityName
	 * @param district
	 * @param vendorSnOrVendorAppId
	 * @return
	 */
	public static boolean activateCityNeedLimit(String cityName, String district, String vendorSnOrVendorAppId) {
		String cityKey = cityName + ".*";
		String districtKey = cityName + "." + district;
		boolean needLimit = false;
		if(CITY_ACTIVATE_LIMIT.containsKey(cityKey)){
			//判断是否全市限制
			List<String> list = CITY_ACTIVATE_LIMIT.get(cityKey);
			if(list == null || !list.contains(vendorSnOrVendorAppId)){
				needLimit = true;
			}
		}else if(CITY_ACTIVATE_LIMIT.containsKey(districtKey)){
			//判断是否区县限制
			List<String> list = CITY_ACTIVATE_LIMIT.get(districtKey);
			if(list == null || !list.contains(vendorSnOrVendorAppId)){
				needLimit = true;
			}
		}
		return needLimit;
	}

	/**
	 * 是否校验查询终端接口的查询条件
	 * @return
	 */
	public static boolean checkFindTerminals(){
		return config.getBooleanProperty(CHECK_FIND_TERMINALS_FLAG, true);
	}

	public static boolean checkLiquidationNextDayConfig(){
		return config.getBooleanProperty(CHECK_LIQUIDATION_NEXT_DAY_CONFIG, false);
	}

	public static boolean isTerminalProhibitedUnbound(String vendorAppAppid) {
		return PROHIBITED_UNBOUND_VENDOR_APP_APPID_SET.contains(vendorAppAppid);
	}

    public static MerchantConfigBypassStrategy getMerchantConfigBypassStrategy() {
        return bypassStrategy;
    }

    public static List<String> getMerchantConfigBypassUseClearanceProviders() {
        return bypassUseClearanceProviders;
    }

    public static int getChangeshiftsCheckoutInterval() {
        return config.getIntProperty(CHANGE_SHIFTS_CHECKOUT_INTERVAL, 60000);
    }

    public static String getChangeToAppointDB() {
        return changeToAppointDB;
    }

	public static boolean databusDirectToKafka(){
		return config.getBooleanProperty(DATABUS_DIRECT_TO_KAFKA, false);
	}
	public static String getFitnessAppId() {
		return configCoreBase.getProperty(KEY_FITNESS_APP_ID,"11");
	}

    public static Map<String, Object> getDefaultFitnessParams() {
        return JsonUtil.jsonStrToObject(config.getProperty(DEFAULT_FITNESS_PARAMS, "{}"), Map.class);
    }

	public static void initDefaultFqParams() {
		Map fqDefaultParams = JsonUtil.jsonStrToObject(configCoreBase.getProperty(DEFAULT_FQ_PARAMS, "{}"), Map.class);
		if (fqDefaultParams == null) {
			fqDefaultParams = new HashMap();
		}
		fqDefaultParamsMap = fqDefaultParams;
	}

	public static int getDefaultStatusByPayway(Integer payway) {
		if (payway == Payway.JDPAY.getCode()) {
			return getBaitiaoDefaultStatus();
		} else {
			return 0;
		}
	}

	public static int getBaitiaoDefaultStatus() {
		Map<String, Object> paywayFqParams = fqDefaultParamsMap.get(Payway.JDPAY.getCode() + "");
		if (paywayFqParams == null) {
			return DEFAULT_BAITIAO_STATUS;
		} else {
			return MapUtil.getIntValue(paywayFqParams, TransactionParam.JD_BAITIAO_STATUS, DEFAULT_BAITIAO_STATUS);
		}
	}

	public static int getBaitiaoDefaultLimit(){
		Map<String, Object> paywayFqParams = fqDefaultParamsMap.get(Payway.JDPAY.getCode() + "");
		if (paywayFqParams == null) {
			return DEFAULT_BAITIAO_MIN_LIMIT;
		} else {
			return MapUtil.getIntValue(paywayFqParams, TransactionParam.JD_BAITIAO_LIMIT, DEFAULT_BAITIAO_MIN_LIMIT);
		}
	}

	private static void initVendorT9ProviderMap(){
		String property = configCoreBase.getProperty(KEY_T9_VENDOR_APP_ID_PROVIDER_MAPPING, "{}");
		vendorT9ProviderMap = JsonUtil.jsonStrToObject(property, Map.class);
	}

	public static Map<String, List<Integer>> getVendorT9ProviderMap() {
		return vendorT9ProviderMap;
	}

	private static void initPhonePosTradeLimitConfig() {
		String property = configCoreBase.getProperty(KEY_DEFAULT_PHONE_POS_TRADE_LIMIT, "{}");
		Map configMap = JsonUtil.jsonStrToObject(property, Map.class);
		//单日限额默认2000元
		if (!configMap.containsKey(TransactionParam.PHONE_POS_DAY_TRAN_LIMIT)) {
			configMap.put(TransactionParam.PHONE_POS_DAY_TRAN_LIMIT, 200000);
		}
		//单笔限额默认900元
		if (!configMap.containsKey(TransactionParam.PHONE_POS_SINGLE_TRAN_LIMIT)) {
			configMap.put(TransactionParam.PHONE_POS_SINGLE_TRAN_LIMIT, 90000);
		}
		defaultPhonePosTradeLimitConfig = configMap;
	}

	private static void initOverrideProviderMchIdKey(){
		String property = config.getProperty(KEY_CHECK_EXTERNAL_PROVIDER_MCH_ID_KEY, "{}");
		Map configMap = JsonUtil.jsonStrToObject(property, Map.class);
		if (MapUtils.isNotEmpty(configMap)) {
			overrideProviderMchIdKey = configMap;
		}
	}

	private static void initInstallment24RateConfig(){
		String property = config.getProperty(KEY_INSTALLMENT_24_RATE_CONFIG, "{}");
		Map configMap = JsonUtil.jsonStrToObject(property, Map.class);
		if (MapUtils.isNotEmpty(configMap)) {
			default24InstallmentRateConfig = configMap;
		}
	}

	public static Map<String, Object> getDefault24InstallmentRateConfig() {
		return new HashMap<>(default24InstallmentRateConfig);
	}

	public static Map<Integer, String> getOverrideProviderMchIdKey() {
		return overrideProviderMchIdKey;
	}

	public static Map<String, Object> getDefaultPhonePosTradeLimitConfig() {
		return defaultPhonePosTradeLimitConfig;
	}

	public static String getPhonePosTradeApp(){
		return configCoreBase.getProperty(KEY_PHONE_POS_TRADE_APP, "8222");
	}

	private static void initWftProviders() {
		String changeValue = config.getProperty(KEY_WFT_PROVIDERS, "[1015,1029]");
		if (StringUtil.empty(changeValue)) {
			WFT_PROVIDERS = Collections.emptySet();
		} else {
			WFT_PROVIDERS = JsonUtil.jsonStrToObject(changeValue, Set.class);
		}
	}

	public static Set<Integer> getWftProviders() {
		return WFT_PROVIDERS;
	}

	private static void initPayDisableVendorAppIds() {
		String changeValue = config.getProperty(PAY_DISABLE_VENDOR_APPIDS, "[]");
		Set<String> newValue = JsonUtil.jsonStrToObject(changeValue, Set.class);
		if (newValue == null) {
			newValue = Collections.emptySet();
		}
		payDisableVendorAppIds = newValue;
	}


	public static int getDeleteTradeExtConfigThreshold(int defaultValue) {
		return config.getIntProperty(KEY_DELETE_TRADE_EXT_THRESHOLD, defaultValue);
	}

	public static Set<String> getPayDisableVendorAppIds(){
		return payDisableVendorAppIds;
	}
}
