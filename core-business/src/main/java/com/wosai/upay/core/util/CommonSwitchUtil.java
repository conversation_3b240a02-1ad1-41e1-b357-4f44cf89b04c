package com.wosai.upay.core.util;

import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.TransactionParam;

public class CommonSwitchUtil {

    private static final char COMMON_SWTICH_OPEN = Character.forDigit(TransactionParam.STATUS_OPENED, 10);

    public static boolean isCommonSwitchOpen(String commonSwitch, int type) {
        if (StringUtils.isEmpty(commonSwitch) || commonSwitch.length() <= type) {
            return false;
        }
        char currentStatus = commonSwitch.charAt(type);
        if (currentStatus == COMMON_SWTICH_OPEN) {
            return true;
        }
        return false;
    }
}
