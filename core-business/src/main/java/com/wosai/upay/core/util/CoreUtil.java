package com.wosai.upay.core.util;

import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;

import java.util.Arrays;

public class CoreUtil {

    /**
     * 获取完整的错误信息
     * @param throwable
     * @return
     */
    public static String getErrorMessage(Throwable throwable){
        String message = throwable.getMessage();
        while(true){
            throwable  = throwable.getCause();
            if(throwable == null){
                break;
            }
            if(StringUtil.empty(message)){
                continue;
            }
            message = message + ";" + throwable.getMessage();
        }
        return message;
    }


    public static PageInfo formatPageInfo(PageInfo pageInfo) {
        if (pageInfo == null) {
            return new PageInfo(1, 10, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        } else {
            if (pageInfo.getPage() == null || pageInfo.getPage() <= 0) {
                pageInfo.setPage(1);
            }
            if (pageInfo.getPageSize() == null) {
                pageInfo.setPageSize(10);
            } else {
                if (pageInfo.getPageSize() <= 0) {
                    pageInfo.setPageSize(10);
                }
                if (pageInfo.getPageSize() > 20000) {
                    pageInfo.setPageSize(20000);
                }

            }
        }
        return pageInfo;
    }


}
