package com.wosai.upay.core.util;

import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.util.DateTimeUtil;
import com.wosai.upay.util.DateUtil;
import org.apache.commons.lang3.time.DateUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class EffectiveTimeUtil {

    public static void checkoutEffectiveTime(String idValidity) {
        if (!idValidity.contains("-")) {
            throw new CoreInvalidParameterException("身份证或执照有效期格式错误");
        }
        try {
            String end = idValidity.split("\\-")[1];
            if (com.wosai.mpay.util.StringUtils.isEmpty(end) || end.length() < 8) {
                throw new CoreInvalidParameterException("身份证或执照有效期格式错误");
            }
            // 校验一下有效期格式是否正确
            DateUtils.parseDate(end, DateUtil.FORMATTER_DATE_INT);
            if (getDiffDay(Integer.valueOf(end)) <= 7) {
                throw new CoreInvalidParameterException("身份证或执照有效期需大于7天");
            }
        } catch (CoreInvalidParameterException e) {
            throw e;
        } catch (Exception e) {
            throw new CoreInvalidParameterException("身份证或执照有效期格式错误");
        }
    }

    private static int getDiffDay(Integer day) {
        LocalDate today = LocalDate.now();
        LocalDate statisticsDay = LocalDate.of(day / 10000, day % 10000 / 100, day % 100);
        return (int) ChronoUnit.DAYS.between(today, statisticsDay);
    }
}
