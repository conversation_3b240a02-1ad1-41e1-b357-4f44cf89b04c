package com.wosai.upay.core.util;

import com.google.common.collect.Sets;
import com.wosai.core.crypto.util.CryptoUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import org.springframework.util.ObjectUtils;

import java.util.*;

public class EncryptArgUtils {

    private final static Set<String> cryptoSet = Sets.newHashSet("contact_cellphone", "street_address", "contact_email", "legal_person_id_number", "legal_person_id_card_front_photo",
            "legal_person_id_card_back_photo", "street_address", "contact_email", "identity", "number", "holder_id_front_photo", "holder_id_back_photo",
            "bank_card_image", "id_validity", "photo");

    /**
     * 手机号脱敏拷贝
     *
     * @param arguments
     * @return
     */
    public static Object[] encrypt(Object... arguments) {
        if (!ObjectUtils.isEmpty(arguments)) {
            Object[] argumentsCopy = new Object[arguments.length];
            int i = 0;
            for (Object argument : arguments) {
                if (argument instanceof Map) {
                    Map<String, Object> map = encryptMap((Map<String, Object>) argument);
                    argumentsCopy[i] = map;
                } else {
                    argumentsCopy[i] = argument;
                }
                i++;
            }
            return argumentsCopy;
        } else {
            return arguments;
        }
    }

    public static Map<String, Object> encryptMap(Map<String, Object> argument) {
        Map<String, Object> map = new HashMap<>(argument);
        for (String key : map.keySet()) {
            String value = BeanUtil.getPropString(map, key, "");
            if (key.toLowerCase().endsWith("phone")) {
                if (!StringUtil.empty(value) && value.length() >= 8 && !CryptoUtil.isMatchEncryptData(value)) {
                    map.put(key, value.replaceAll("(.{3}).*(.{4})", "$1****$2"));
                }
            } else if (cryptoSet.contains(key.toLowerCase())) {
                map.put(key, "***");
            }
        }
        return map;
    }


}
