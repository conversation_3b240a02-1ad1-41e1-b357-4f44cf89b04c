package com.wosai.upay.core.util;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.dao.Criteria;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.config.ElasticsearchConfig;
import com.wosai.upay.core.exception.CoreIOException;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR> by wkx
 * @date 2018/2/11
 **/
public class EsUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(EsUtil.class);

    private static ElasticsearchConfig config= SpringContextHolder.getBean(ElasticsearchConfig.class);

    private static RestHighLevelClient restHighLevelClient = SpringContextHolder.getBean(RestHighLevelClient.class);

    private static final Scroll SCROLL = new Scroll(TimeValue.timeValueMinutes(1L));


    public static Criteria allLikeConvertCriteria(QueryInfo info, String param, String primaryKey) {
        if(StringUtils.isEmpty(primaryKey)) throw new CoreInvalidParameterException("primaryKey 不可为空");
        Set<String> set = new HashSet<>();
        set.add(primaryKey);
        return convertCriteria(queryAllLike(info, param, set),primaryKey);
    }

    public static List<JSONObject> queryAllLike(QueryInfo info, String param, Set<String> columnSet) {
        // 创建 MatchPhraseQueryBuilder
        MatchPhraseQueryBuilder matchPhraseQueryBuilder = QueryBuilders.matchPhraseQuery(info.column, param);
        // 创建 SearchSourceBuilder
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(matchPhraseQueryBuilder);
        // 设置要返回的字段及数量
        String [] columnList = columnSet.toArray(new String[0]);
        searchSourceBuilder.fetchSource(new FetchSourceContext(true, columnList, null));
        searchSourceBuilder.size(1000);
        searchSourceBuilder.sort("_doc");

        try {
            return elasticScrollSearch(info.getIndex(), searchSourceBuilder);
        }catch (IOException ex){
            throw new CoreIOException("elasticSearch访问异常",ex);
        }
    }

    public static Criteria query(QueryInfo queryInfo,SearchSourceBuilder searchSourceBuilder,String primaryKey, String storeNameKey, String storeName){
        searchSourceBuilder.fetchSource(new FetchSourceContext(true, new String[]{primaryKey, storeNameKey}, null));
        searchSourceBuilder.size(1000);
        searchSourceBuilder.sort("_doc");
        try {
            //根据storeName过滤
            List<JSONObject> esResult = elasticScrollSearch(queryInfo.getIndex(),searchSourceBuilder);
            List<JSONObject> objects = new ArrayList<>();
            for (JSONObject obj : esResult){
                if(obj.getString(storeNameKey).contains(storeName)){
                    objects.add(obj);
                }
            }

            return convertCriteria(objects,primaryKey);
        }catch (IOException ex){
            throw new CoreIOException("elasticSearch访问异常",ex);
        }

    }

    public static class QueryInfo {
        private static final String INDEX_CONNECTOR = "_";
        private String dbName;
        private String tableName;
        private String column;

        public QueryInfo(String tableName){
            this.dbName=config.getIndexPrefix();
            this.tableName = tableName;
        }

        public QueryInfo(String tableName, String column){
            this.dbName=config.getIndexPrefix();
            this.tableName = tableName;
            this.column = column;
        }

        public QueryInfo(String dbName, String tableName, String column) {
            this.dbName = dbName;
            this.tableName = tableName;
            this.column = column;
        }

        public QueryInfo instance(String dbName, String tableName, String column) {
            return new QueryInfo(dbName, tableName, column);
        }

        public String getIndex() {
            return dbName + INDEX_CONNECTOR + tableName;
        }
    }

    private static List<JSONObject> elasticScrollSearch(String index, SearchSourceBuilder searchSourceBuilder) throws IOException {
        List<JSONObject> resultList = new ArrayList<>();
        String scrollId = null;
        try {
            // 将 SearchSourceBuilder 添加到 SearchRequest
            SearchRequest searchRequest = new SearchRequest(index);
            searchRequest.source(searchSourceBuilder);
            searchRequest.scroll(SCROLL);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            while (searchHits != null && searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    resultList.add(JSONObject.parseObject(hit.getSourceAsString()));
                    if (resultList.size() >= 2000){
                        return resultList;
                    }
                }

                // 请求下一批查询结果
                searchResponse = restHighLevelClient.scroll(new org.elasticsearch.action.search.SearchScrollRequest(scrollId).scroll(SCROLL), RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
            }
        }catch (Exception e) {
            LOGGER.error("Elasticsearch scroll 查询错误", e);
            throw e;
        } finally {
            if (scrollId != null) {
                try {
                    // 关闭scroll
                    ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                    clearScrollRequest.addScrollId(scrollId);
                    restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                } catch (Exception e) {
                    LOGGER.error("Elasticsearch scroll 清理错误", e);
                }
            }
        }
        return resultList;
    }

    public static Criteria convertCriteria(List<JSONObject> list,String primaryKey) {
        List<Object> values=new ArrayList<>();
        for (Object obj:list){
            if(obj instanceof JSONObject){
                values.add(((JSONObject) obj).get(primaryKey));
            }
        }
        if (values.size() == 0) {
            return null;
        } else if (values.size() == 1) {
            Criteria criteria = new Criteria();
            return criteria.with(primaryKey).is(values.get(0));
        } else {
            Criteria criteria = new Criteria();
            return criteria.with(primaryKey).in(values);
        }
    }
}
