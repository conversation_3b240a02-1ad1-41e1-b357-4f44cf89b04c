package com.wosai.upay.core.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.MerchantBankAccount;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: guozhong
 * @Date: 2019-04-28 07:17
 * @Description:
 */
public class IdCardExtendUtil {

    private static final Logger logger = LoggerFactory.getLogger(IdCardExtendUtil.class);

    public static void getExtend(Map merchantBankAccount, Map oldMerchantBankAccount) {
        //身份证信息写入extend
        int idType = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.ID_TYPE, -1);
        if (idType == -1) {
            idType = BeanUtil.getPropInt(oldMerchantBankAccount, MerchantBankAccount.ID_TYPE, -1);
        }
        String identity = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.IDENTITY);

        //如果是修改身份证信息
        if (idType == 1 && !StringUtils.isBlank(identity) && IdCardUtil.validate(identity)) {
            Map<String, Object> extendMap = null;
            try {
                extendMap = (Map) BeanUtil.getProperty(oldMerchantBankAccount, MerchantBankAccount.EXTEND);
                //身份证户籍编码
                String local = IdCardUtil.getLocalByIdCard(identity);
                //生日
                String birthday = IdCardUtil.getBirthByIdCard(identity);
                //性别
                String sex = IdCardUtil.getGenderByIdCard(identity);
                if (extendMap == null) {
                    extendMap = new HashMap<>();
                }
                extendMap.put("local", local);
                extendMap.put("birthday", birthday);
                extendMap.put("sex", sex);

            } catch (Exception e) {
                logger.error("根据身份证号生成extend信息失败 ", e);
            }

            //插入extend
            merchantBankAccount.put(MerchantBankAccount.EXTEND, extendMap);
        }
    }

    public static void getExtend(Map merchantBankAccount) {
        //身份证信息写入extend
        int idType = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.ID_TYPE, -1);

        String identity = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.IDENTITY);

        //如果是插入身份证信息
        if (idType == 1 && !StringUtils.isBlank(identity) && IdCardUtil.validate(identity)) {
            Map<String, Object> extendMap = new HashMap<>();

            try {
                //身份证户籍编码
                String local = IdCardUtil.getLocalByIdCard(identity);
                //生日
                String birthday = IdCardUtil.getBirthByIdCard(identity);
                //性别
                String sex = IdCardUtil.getGenderByIdCard(identity);
                extendMap.put("local", local);
                extendMap.put("birthday", birthday);
                extendMap.put("sex", sex);

            } catch (Exception e) {
                logger.error("根据身份证号生成extend信息失败 ", e);
            }

            //插入extend
            merchantBankAccount.put(MerchantBankAccount.EXTEND, extendMap);
        }
    }
}
