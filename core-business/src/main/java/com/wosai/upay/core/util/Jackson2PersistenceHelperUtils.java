package com.wosai.upay.core.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import lombok.SneakyThrows;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/11/13.
 */
public class Jackson2PersistenceHelperUtils {
    private static final ObjectMapper om;
    static {
        om = new ObjectMapper();
        om.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
    }

    private Jackson2PersistenceHelperUtils() {
    }

    @SneakyThrows
    public static byte[] toJsonBytes(Object value) {
        return om.writeValueAsBytes(value);

    }

    @SneakyThrows
    public static String toJsonString(Object value) {
        return om.writeValueAsString(value);
    }
}
