package com.wosai.upay.core.util;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;

/**
 * Created by xuchmao on 15/12/11.
 */
public class JsonUtil {
    private static Logger log =  LoggerFactory.getLogger(JsonUtil.class);
    public static ObjectMapper objectMapper;
    static {
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public static <T> T jsonStrToObject(String jsonStr, Class<T> valueType){
        if(jsonStr == null || valueType == null){
            return null;
        }
        T t = null;
        try {
            t = objectMapper.readValue(jsonStr, valueType);
        }catch (Exception ignored){ }
        return t;
    }

    public static <T> String toJsonStr(T obj){
        if(obj == null){
            return null;
        }
        String jsonStr = null;
        try {
            StringWriter sw = new StringWriter();
            JsonGenerator gen = new JsonFactory().createJsonGenerator(sw);
            objectMapper.writeValue(gen, obj);
            gen.close();
            jsonStr = sw.toString();
        }catch (IOException e){
            log.error(e.getMessage());
        }
        return jsonStr;
    }

    public static <T> T convertToObject(Object fromValue, Class<T> toValueType) {
        return objectMapper.convertValue(fromValue, toValueType);
    }
}