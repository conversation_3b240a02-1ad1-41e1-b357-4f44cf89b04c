package com.wosai.upay.core.util;

import com.wosai.common.utils.WosaiMapUtils;

import java.util.HashMap;
import java.util.Map;

public class MapValueUtil {

    public static Map<String, Object> filterNullValues(Map<String, Object> inputMap) {
        if (WosaiMapUtils.isEmpty(inputMap)) return inputMap;

        Map<String, Object> filteredMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : inputMap.entrySet()) {
            if (entry.getValue() != null) {
                filteredMap.put(entry.getKey(), entry.getValue());
            }
        }

        return filteredMap;
    }
}
