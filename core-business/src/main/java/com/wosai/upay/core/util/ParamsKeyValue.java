package com.wosai.upay.core.util;

import java.util.Objects;

public class ParamsKeyValue<F, S, P> {

    private F first;

    private S second;

    private P param;


    public ParamsKeyValue(F first, S second) {
        this.first = first;
        this.second = second;
    }

    public ParamsKeyValue(F first, S second,P param) {
        this.first = first;
        this.second = second;
        this.param = param;

    }

    public ParamsKeyValue() {
    }

    public F getFirst() {
        return this.first;
    }

    public void setFirst(F first) {
        this.first = first;
    }

    public S getSecond() {
        return this.second;
    }

    public void setSecond(S second) {
        this.second = second;

    }

    public P getParam() {
        return param;
    }

    public void setParam(P param) {
        this.param = param;
    }

    @Override
    public String toString() {
        return "ParamsKeyValue{" +
                "first=" + first +
                ", second=" + second +
                ", param=" + param +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ParamsKeyValue<?, ?, ?> that = (ParamsKeyValue<?, ?, ?>) o;
        return Objects.equals(first, that.first) &&
                Objects.equals(second, that.second);
    }

    @Override
    public int hashCode() {

        return Objects.hash(first, second);
    }
}
