package com.wosai.upay.core.util;

import com.wosai.data.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 基于redis实现的简易分布式锁
 * 复杂的实现可参考 http://redis.io/topics/distlock
 */
@Component
public class RedisLock {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private ScheduledExecutorService executorService = Executors.newScheduledThreadPool(2);

    /**
     * redis setNx的集群排它锁
     *
     * @param key
     * @param second
     * @return
     * @see <a href="http://redis.io/commands/setnx">Redis Documentation: SETNX</a>
     */
    public boolean lock(String key, long second) {
        Boolean set = redisTemplate.opsForValue().setIfAbsent(key, System.currentTimeMillis() + second * 1000 + "");
        if (set) {
            redisTemplate.expire(key, second, TimeUnit.SECONDS);
        } else {
            String value = redisTemplate.opsForValue().getAndSet(key, System.currentTimeMillis() + second * 1000 + "");
            if (value != null && Long.parseLong(value) + 1 <= System.currentTimeMillis()) {
                return true;
            }
        }
        return set;
    }

    public boolean tryLock(final String lockKey, final String resourceValue, final long time, final TimeUnit unit){
        redisTemplate.execute(new RedisCallback() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                Boolean set = connection.setNX(lockKey.getBytes(), resourceValue.getBytes());
                if (set && time > 0) {
                    connection.expire(lockKey.getBytes(), time);
                }
                return null;
            }
        });
        String value = (String) redisTemplate.opsForValue().get(lockKey);
        if(resourceValue.equals(value)){
            return true;
        }else{
            return false;
        }
    }

    /**
     * @see http://redis.io/commands/set
     *
     */
    public void unlock(String lockKey, String resourceValue) {
        unlock(lockKey, resourceValue, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 延迟unlock
     * @param delayTime
     * @param unit
     * @return
     */
    public void unlock(final String lockKey, final String resourceValue, long delayTime, TimeUnit unit){
        if(StringUtil.empty(lockKey)){
            return;
        }
        if(delayTime <= 0){
            doUnlock(lockKey, resourceValue);
        }else{
            executorService.schedule(new Runnable() {
                @Override
                public void run() {
                    doUnlock(lockKey, resourceValue);
                }
            }, delayTime, unit);
        }

    }

    private void doUnlock(final String lockKey, final String resourceValue){
        String value = (String) redisTemplate.opsForValue().get(lockKey);
        if(resourceValue.equals(value)){
            redisTemplate.delete(lockKey);
        }
    }

    public String getRedisValue(String lockKey){
        return redisTemplate.opsForValue().get(lockKey);
    }
}
