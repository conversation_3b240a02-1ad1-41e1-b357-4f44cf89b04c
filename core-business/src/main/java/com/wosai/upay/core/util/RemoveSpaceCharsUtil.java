package com.wosai.upay.core.util;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RemoveSpaceCharsUtil {

    public static void removeSpaceChars(Map license, List<String> fields) {
        if (WosaiMapUtils.isEmpty(license)) {
            return;
        }
        for (String field : fields) {
            String value = WosaiMapUtils.getString(license, field);
            if (WosaiStringUtils.isNotEmpty(value)) {
                license.put(field, value.trim());
            }
        }
    }
}
