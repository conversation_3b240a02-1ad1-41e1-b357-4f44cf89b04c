package com.wosai.upay.core.util;

import com.wosai.databus.event.terminal.basic.TerminalBasicUpdateEvent;
import com.wosai.upay.common.util.SpringContextHolder;
import lombok.SneakyThrows;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/8/19.
 */
public class Test {
    @SneakyThrows
    public static void main(String[] args) {




    }


    public static void fun(){
        TerminalBasicUpdateEvent event = new TerminalBasicUpdateEvent();
        System.out.println("event is" + event);
    }

    /**
     * 线程池里面的任务是否已经执行完毕
     * @return
     */
    private  static boolean isExecutorsEmpty(ExecutorService executors){
        if(((ThreadPoolExecutor)executors).getActiveCount() == 0 && ((ThreadPoolExecutor)executors).getQueue().isEmpty()){
            return true;
        }
        return false;
    }

}
