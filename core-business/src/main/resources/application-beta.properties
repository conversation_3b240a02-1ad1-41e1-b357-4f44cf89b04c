#jdbc configuration
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.url=tk-core-business-upay_core-7836?useUnicode=yes&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
master.jdbc.connection.eviction.interval=60000

#jdbc configuration
slave.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
slave.jdbc.url=tk-core-business-upay_core-7836?useUnicode=yes&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
slave.jdbc.connection.eviction.interval=60000

#message
jdbc_message.driverClassName=com.mysql.cj.jdbc.Driver
jdbc_message.url=tk-core-business-upay_message-5467?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
jdbc_message.connection.eviction.interval=60000

#log
jdbc_log.driverClassName=com.mysql.cj.jdbc.Driver
jdbc_log.url=tk-core-business-upay_log-6759?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
jdbc_log.connection.eviction.interval=60000

#user
master.jdbc_user.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc_user.url=tk-core-business-upay_user-6185?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
master.jdbc_user.connection.eviction.interval=60000

slave.jdbc_user.driverClassName=com.mysql.cj.jdbc.Driver
slave.jdbc_user.url=tk-core-business-upay_user-6185?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
slave.jdbc_user.connection.eviction.interval=60000

common_login.driverClassName=com.mysql.cj.jdbc.Driver
common_login.url=tk-core-business-common_login-8653?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
common_login.connection.eviction.interval=60000


#bank-info
master.jdbc.bank-info.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.bank-info.url=tk-core-business-upay_bank_info-7124?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
master.jdbc.bank-info.connection.eviction.interval=60000

slave.jdbc.bank-info.driverClassName=com.mysql.cj.jdbc.Driver
slave.jdbc.bank-info.url=tk-core-business-upay_bank_info-7124?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
slave.jdbc.bank-info.connection.eviction.interval=60000


#redis
redis.url=redis-beta
redis.port=6379
redis.database=2
redis.password=roFXzHwXPY3RnI%5
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info


#db config
db.maxActive=10
db.minIdle=3


#alipay authinto 支付宝2.0授权
alipay.authinto.rpc.server=http://alipay-authinto/

#merchant contract service
merchant.contract.service=http://merchant-contract/

#销售助手
sales.backend.rpc.server=http://sales-system-backend/

#威富通配置信息
wft.feerate=5.5
wft.service.url=http://ccbzj.test.swiftpass.cn/sppay-interface-war/gateway/

#银行信息服务
bankInfo.service=http://bank-info-service/

#商户认证服务
merchant.audit.service=http://merchant-audit-service/

#用户服务
user.service=http://user-service/

#交易风控服务
wosai-preorder-risk.service=http://wosai-preorder-risk/
shouqianba-risk-service=http://shouqianba-risk-service/

#签名服务
signature.service=http://signature-proxy/

#crm 客户关系服务
crm-customer-relation.service=http://crm-customer-relation/

trade-manage.service=http://trade-manage-service/

merchant-user.service=http://merchant-user-service/

aop-backend.service=http://aop-backend/

#lakala config
lkl.comporg.code=WSST
lkl.create.merchant.retUrl=http://upay-api/v2/lklCreateMerchantCallback
lkl.update.merchant.retUrl=http://upay-api/v2/lklUpdateMerchantCallback
lkl.merchant.create=http://120.27.161.217:15023/thirdpartplatform/merchmanage/6001.dor
lkl.merchant.update=http://120.27.161.217:15023/thirdpartplatform/merchmanage/6002.dor
lkl.merchant.terminal.add=http://120.27.161.217:15023/thirdpartplatform/merchmanage/6003.dor
lkl.merchant.bank.query=http://120.27.161.217:15023/thirdpartplatform/merchmanage/6004.dor
lkl.merchant.process.query=http://120.27.161.217:15023/thirdpartplatform/merchmanage/6005.dor
lkl.merchant.query=http://120.27.161.217:15023/thirdpartplatform/merchmanage/6006.dor

# core crypto config
core-crypto.service=http://core-crypto/
core-crypto.access_id=8172747b-00d1-4148-8fcc-c1d1fc3c8d98

#日志服务
business-logstash.service=http://business-logstash.beta.iwosai.com/


#sn config
sn.vendorSnPrefix=2919
sn.solicitorSnPrefix=2519
sn.merchantSnPrefix=2169
sn.storeSnPrefix=2159
sn.terminalSnPrefix=2101
sn.groupSnPrefix=2189
sn.cashDeskSnPrefix=2102
sn.brandSnPrefix=2233

#tracing config
spring.application.name = core-business
spring.application.env=beta
spring.application.rate=1.0f
sn.departmentSnPrefix=2371


#elasticsearch config
#elasticsearch.url=http://es-cn-mp90e2qfh000i3bz8.elasticsearch.aliyuncs.com:9200
elasticsearch.url=http://es-cn-2r42ooguf0005v51a.elasticsearch.aliyuncs.com:9200
elasticsearch.default.index.prefix=upay_core
elasticsearch.instance=es-cn-2r42ooguf0005v51a
elasticsearch.hostname=es-cn-2r42ooguf0005v51a.elasticsearch.aliyuncs.com
elasticsearch.port=9200

#kakfa config
kafka.send.brokers=kafka-beta1:9092,kafka-beta2:9092,kafka-beta3:9092
kafka.send.batch.size=5
kafka.send.acks=1
kafka.send.linger.ms=500
kafka.send.max.block.ms=1000
kafka.send.registry.url=http://kafka-beta1:8081,http://kafka-beta2:8081,http://kafka-beta3:8081

ali.kafka.send.brokers=aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
ali.kafka.send.batch.size=5
ali.kafka.send.acks=1
ali.kafka.send.linger.ms=500
ali.kafka.send.max.block.ms=1200
ali.kafka.send.registry.url=http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081

kafka.databus.brokers=***************:9092,***************:9092,***************:9092
kafka.databus.registry.url=http://***************:8081,http://***************:8081,http://***************:8081

topic.databus.merchant=databus_CUA_merchant_basic_allin
topic.databus.store=databus_CUA_store_basic_allin
topic.databus.terminal=databus_CUA_terminal_basic_allin
topic.databus.merchant_config=databus_PAY_merchant_config_allin

com.wosai.oss.group=pay
com.wosai.oss.internal=false
com.wosai.oss.imgBucket=private-wosai-images
com.wosai.oss.imgBaseUrl=https://private-images.shouqianba.com
com.wosai.oss.staticBucket=private-wosai-statics
com.wosai.oss.staticBaseUrl=https://private-resource.shouqianba.com
