#jdbc configuration
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.url=pk-core-business-upay_core-5820?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
master.jdbc.connection.eviction.interval=60000

slave.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
slave.jdbc.url=pk-core-business-upay_core-5946?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
slave.jdbc.connection.eviction.interval=60000

#message
jdbc_message.driverClassName=com.mysql.cj.jdbc.Driver
jdbc_message.url=pk-core-business-upay_message-1860?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
jdbc_message.connection.eviction.interval=60000

#log
jdbc_log.driverClassName=com.mysql.cj.jdbc.Driver
jdbc_log.url=pk-core-business-upay_log-0738?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
jdbc_log.connection.eviction.interval=60000

#user
master.jdbc_user.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc_user.url=pk-core-business-upay_user-2804?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
master.jdbc_user.connection.eviction.interval=60000

slave.jdbc_user.driverClassName=com.mysql.cj.jdbc.Driver
slave.jdbc_user.url=pk-core-business-upay_user-2804?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
slave.jdbc_user.connection.eviction.interval=60000

common_login.driverClassName=com.mysql.cj.jdbc.Driver
common_login.url=pk-core-business-common_login-9182?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
common_login.connection.eviction.interval=60000

#bank-info
master.jdbc.bank-info.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.bank-info.url=pk-core-business-upay_bank_info-5289?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
master.jdbc.bank-info.connection.eviction.interval=60000

slave.jdbc.bank-info.driverClassName=com.mysql.cj.jdbc.Driver
slave.jdbc.bank-info.url=pk-core-business-upay_bank_info-5289?useUnicode=yes&zeroDateTimeBehavior=convertToNull&socketTimeout=15000
slave.jdbc.bank-info.connection.eviction.interval=60000

#redis
redis.url=r-wz9jf1cetrtmic5fzf.redis.rds.aliyuncs.com
redis.port=6379
redis.database=2
redis.password=WkDfC1aTDW8yelkZ
#cacheService缓存数据的过期时间，单位毫秒
redis.cacheService.expiredTime=86400
#cacheService缓存数据key的前缀
redis.cacheService.keyPrefix=mini_info




#db config
db.maxActive=100
db.minIdle=8

#alipay authinto 支付宝2.0授权
alipay.authinto.rpc.server=http://alipay.authinto.shouqianba.com/

#merchant contract service
merchant.contract.service=http://merchant-contract-internal.shouqianba.com/

#销售助手
sales.backend.rpc.server=http://sales-internal.shouqianba.com/

#威富通配置信息
wft.feerate=5.5
wft.service.url=https://interface.swiftpass.cn/sppay-interface-war/gateway

#银行信息服务
bankInfo.service=http://bank-info-service.internal.shouqianba.com/

#商户认证服务
merchant.audit.service=http://merchant-audit-service.internal.shouqianba.com/

#用户服务
user.service=http://user-service.internal.shouqianba.com/

#交易风控服务
wosai-preorder-risk.service=http://wosai-preorder-risk.internal.shouqianba.com/
shouqianba-risk-service=http://internal.risk.shouqianba.com/

#签名服务
signature.service=http://signature-proxy.shouqianba.com/


#crm 客户关系服务
crm-customer-relation.service=http://crm-customer-relation.internal.shouqianba.com/

trade-manage.service=http://trade-manage-service.internal.shouqianba.com/

merchant-user.service=http://merchant-user-service.internal.shouqianba.com/

aop-backend.service=http://aop-backend.internal.shouqianba.com/

#lkl merchant maanage
#lakala config
lkl.comporg.code=WSJG
lkl.create.merchant.retUrl=https://api.shouqianba.com/v2/lklCreateMerchantCallback
lkl.update.merchant.retUrl=https://api.shouqianba.com/v2/lklUpdateMerchantCallback
lkl.merchant.create=https://api.lakala.com/thirdpartplatform/merchmanage/6001.dor
lkl.merchant.update=https://api.lakala.com/thirdpartplatform/merchmanage/6002.dor
lkl.merchant.terminal.add=https://api.lakala.com/thirdpartplatform/merchmanage/6003.dor
lkl.merchant.bank.query=https://api.lakala.com/thirdpartplatform/merchmanage/6004.dor
lkl.merchant.process.query=https://api.lakala.com/thirdpartplatform/merchmanage/6005.dor
lkl.merchant.query=https://api.lakala.com/thirdpartplatform/merchmanage/6006.dor

# core crypto config
core-crypto.service=http://core-crypto.internal.shouqianba.com/
core-crypto.access_id=9c6c8735-bf58-4286-be63-483ff8f91c91


#日志服务
business-logstash.service=http://osp-gw.vpc.shouqianba.com


#sn config
sn.vendorSnPrefix=918
sn.solicitorSnPrefix=518
sn.merchantSnPrefix=168
sn.storeSnPrefix=158
sn.terminalSnPrefix=100
sn.groupSnPrefix=188
sn.departmentSnPrefix=2370
sn.cashDeskSnPrefix=101
sn.brandSnPrefix=233

#tracing config
spring.application.name = core-business
spring.application.env=prod
spring.application.rate=1.0f

#elasticsearch config
elasticsearch.url=http://es-cn-7mz2rgw2g000fffuy.elasticsearch.aliyuncs.com:9200
elasticsearch.default.index.prefix=upay_core
elasticsearch.instance=es-cn-7mz2rgw2g000fffuy
elasticsearch.hostname=es-cn-7mz2rgw2g000fffuy.elasticsearch.aliyuncs.com
elasticsearch.port=9200

#kakfa config
kafka.send.brokers=conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092 ,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
kafka.send.batch.size=16384
kafka.send.acks=1
kafka.send.linger.ms=500
kafka.send.max.block.ms=1000
kafka.send.registry.url=http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081

ali.kafka.send.brokers=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
ali.kafka.send.batch.size=16384
ali.kafka.send.acks=1
ali.kafka.send.linger.ms=500
ali.kafka.send.max.block.ms=1200
ali.kafka.send.registry.url=http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081

kafka.databus.brokers=conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092 ,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
kafka.databus.registry.url=http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081

topic.databus.merchant=databus.event.merchant.basic.allin
topic.databus.store=databus.event.store.basic.allin
topic.databus.terminal=databus.event.terminal.basic.allin
topic.databus.merchant_config=databus.event.merchant.config.allin

com.wosai.oss.group=pay
com.wosai.oss.internal=true
com.wosai.oss.imgBucket=private-wosai-images
com.wosai.oss.imgBaseUrl=https://private-images.shouqianba.com
com.wosai.oss.staticBucket=private-wosai-statics
com.wosai.oss.staticBaseUrl=https://private-resource.shouqianba.com