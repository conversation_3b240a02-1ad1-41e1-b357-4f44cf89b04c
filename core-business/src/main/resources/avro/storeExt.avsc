{"type": "record", "name": "StoreExtAvro", "namespace": "com.wosai.upay.core.model.kafka.storeExt", "fields": [{"name": "id", "type": ["null", "long"], "default": null}, {"name": "store_id", "type": ["null", "string"], "default": null}, {"name": "before", "type": ["null", {"type": "record", "name": "Digital", "namespace": "com.wosai.upay.core.model.kafka.storeExt", "fields": [{"name": "video", "type": ["null", "string"], "default": null}, {"name": "business_hour", "type": ["null", "string"], "default": null}, {"name": "store_area", "type": ["null", "string"], "default": null}, {"name": "room_count", "type": ["null", "int"], "default": null}, {"name": "table_count", "type": ["null", "int"], "default": null}, {"name": "average_consumption_time", "type": ["null", "string"], "default": null}, {"name": "around_type", "type": ["null", "string"], "default": null}, {"name": "extra", "type": ["null", "string"], "default": null}, {"name": "brand_photo", "type": ["null", {"type": "array", "items": {"type": "record", "name": "PhotoAvro", "namespace": "com.wosai.upay.core.model.kafka.storeExt", "fields": [{"name": "id", "type": ["null", "string"], "default": null}, {"name": "url", "type": ["null", "string"], "default": null}]}}], "default": null}, {"name": "brand_only_scene_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "indoor_material_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "indoor_only_scene_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "outdoor_material_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "outdoor_only_scene_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "other_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "order_price_photo", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "product_price", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}, {"name": "audit_picture", "type": ["null", {"type": "array", "items": "PhotoAvro"}], "default": null}]}], "default": null}, {"name": "after", "type": ["null", "Digital"], "default": null}]}