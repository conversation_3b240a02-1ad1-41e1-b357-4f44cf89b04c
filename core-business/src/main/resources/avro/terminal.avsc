{"namespace": "com.wosai.upay.core.model.kafka", "type": "record", "name": "KafkaTerminal", "fields": [{"name": "id", "type": "string"}, {"name": "sn", "type": "string"}, {"name": "device_fingerprint", "type": ["string", "null"]}, {"name": "type", "type": "int"}, {"name": "status", "type": "int"}, {"name": "code", "type": ["string", "null"]}, {"name": "store_id", "type": ["string", "null"]}, {"name": "merchant_id", "type": ["string", "null"]}, {"name": "vendor_id", "type": ["string", "null"]}, {"name": "vendor_app_id", "type": ["string", "null"]}, {"name": "vendor_app_appid", "type": ["string", "null"]}, {"name": "ctime", "type": "long"}]}