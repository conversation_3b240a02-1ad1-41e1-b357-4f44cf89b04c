{"namespace": "com.wosai.upay.core.model.kafka", "type": "record", "name": "KafkaWithdrawModeChange", "fields": [{"name": "merchant_id", "type": "string"}, {"name": "merchant_sn", "type": "string"}, {"name": "merchant_name", "type": ["null", "string"]}, {"name": "alias", "type": ["null", "string"]}, {"name": "industry", "type": ["null", "string"]}, {"name": "status", "type": ["int"]}, {"name": "withdraw_mode", "type": ["int"]}, {"name": "longitude", "type": ["null", "string"]}, {"name": "latitude", "type": ["null", "string"]}, {"name": "country", "type": ["null", "string"]}, {"name": "province", "type": ["null", "string"]}, {"name": "city", "type": ["null", "string"]}, {"name": "district", "type": ["null", "string"]}, {"name": "platform", "type": ["string"]}, {"name": "operator", "type": ["string"]}, {"name": "operatorId", "type": ["null", "string"]}, {"name": "ctime", "type": "long"}, {"name": "mtime", "type": ["null", "long"]}]}