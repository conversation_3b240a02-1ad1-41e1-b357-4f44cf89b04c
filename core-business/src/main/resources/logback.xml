<?xml version='1.0' encoding='UTF-8'?>
<configuration scan="true" scanPeriod="30 seconds">
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
    <jmxConfigurator/>
    <turboFilter class="com.wosai.upay.core.helper.TraceLogFilter"/>
    <appender class="ch.qos.logback.core.ConsoleAppender" name="_FT_CONSOLE_JSON_SYNC">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>@timestamp</fieldName>
                    <pattern>yyyy-MM-dd HH:mm:ss:SSS</pattern>
                    <timeZone>Asia/Shanghai</timeZone>
                </timestamp>
                <arguments/>
                <stackTrace>
                    <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                        <rootCauseFirst>true</rootCauseFirst>
                        <exclude>^sun\.reflect\..*\.invoke</exclude>
                        <exclude>^net\.sf\.cglib\.proxy\.MethodProxy\.invoke</exclude>
                    </throwableConverter>
                </stackTrace>
                <mdc/>
                <logstashMarkers/>
                <pattern>
                    <pattern>{}</pattern>
                    <omitEmptyFields>true</omitEmptyFields>
                </pattern>
                <callerData>
                    <fieldName>caller</fieldName>
                    <classFieldName>class</classFieldName>
                    <methodFieldName>method</methodFieldName>
                    <fileFieldName>file</fileFieldName>
                    <lineFieldName>line</lineFieldName>
                </callerData>
                <jsonMessage/>
                <message>
                    <fieldName>message</fieldName>
                </message>
                <logLevel>
                    <fieldName>level</fieldName>
                </logLevel>
                <loggerName>
                    <fieldName>logger</fieldName>
                </loggerName>
                <threadName>
                    <fieldName>thread</fieldName>
                </threadName>
            </providers>
        </encoder>
    </appender>
    <appender class="ch.qos.logback.classic.AsyncAppender" name="FT_CONSOLE_JSON">
        <queueSize>8192</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlock>true</neverBlock>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="_FT_CONSOLE_JSON_SYNC"/>
    </appender>
    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="_FT_FILE_SYNC">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir:-.}/main-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>@timestamp</fieldName>
                    <pattern>yyyy-MM-dd HH:mm:ss:SSS</pattern>
                    <timeZone>Asia/Shanghai</timeZone>
                </timestamp>
                <arguments/>
                <stackTrace>
                    <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                        <rootCauseFirst>true</rootCauseFirst>
                        <exclude>^sun\.reflect\..*\.invoke</exclude>
                        <exclude>^net\.sf\.cglib\.proxy\.MethodProxy\.invoke</exclude>
                    </throwableConverter>
                </stackTrace>
                <mdc/>
                <logstashMarkers/>
                <pattern>
                    <pattern>{}</pattern>
                    <omitEmptyFields>true</omitEmptyFields>
                </pattern>
                <callerData>
                    <fieldName>caller</fieldName>
                    <classFieldName>class</classFieldName>
                    <methodFieldName>method</methodFieldName>
                    <fileFieldName>file</fileFieldName>
                    <lineFieldName>line</lineFieldName>
                </callerData>
                <jsonMessage/>
                <message>
                    <fieldName>message</fieldName>
                </message>
                <logLevel>
                    <fieldName>level</fieldName>
                </logLevel>
                <loggerName>
                    <fieldName>logger</fieldName>
                </loggerName>
                <threadName>
                    <fieldName>thread</fieldName>
                </threadName>
            </providers>
        </encoder>
    </appender>
    <appender class="ch.qos.logback.classic.AsyncAppender" name="FT_FILE">
        <queueSize>8192</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlock>true</neverBlock>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="_FT_FILE_SYNC"/>
    </appender>
    <springProfile name="default">
        <root level="INFO">
            <appender-ref ref="FT_CONSOLE_JSON"/>
        </root>
    </springProfile>
    <springProfile name="beta,loadtest,prod,prod-app,prod-upay,prodhuanan,prodhuanan-upay">
        <root level="INFO">
            <appender-ref ref="FT_FILE"/>
        </root>
    </springProfile>
    <logger name="org.springframework.web" level="INFO" additivity="false"/>
    <logger name="org.springframework.remoting.support.RemoteInvocationTraceInterceptor" level="OFF" additivity="true"/>
    <logger name="com.wosai.upay" level="trace" additivity="true"/>
    <logger name="com.googlecode.jsonrpc4j.JsonRpcBasicServer" level="OFF" additivity="true"/>
</configuration>