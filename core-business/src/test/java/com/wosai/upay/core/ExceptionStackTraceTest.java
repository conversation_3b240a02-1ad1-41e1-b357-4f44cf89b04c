package com.wosai.upay.core;

import com.wosai.upay.core.exception.CoreMerchantConfigAbnormalException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 * @date 2024-09-11
 */
@Slf4j
public class ExceptionStackTraceTest {
    private static final int ITERATIONS = 10000;

    @Test
    public void testPerformance1() {
        long startTime, endTime;

        // 测试打印异常栈信息的情况
        startTime = System.currentTimeMillis();
        for (int i = 0; i < ITERATIONS; i++) {
            try {
                throw new CoreMerchantConfigAbnormalException("打印异常栈");
            } catch (CoreMerchantConfigAbnormalException e) {
                log.error("error={}", e.getMessage(), e);
            }
        }
        endTime = System.currentTimeMillis();
        System.out.println("打印异常栈的耗时: " + (endTime - startTime) + " ms");
    }

    @Test
    public void testPerformance2() {
        long startTime, endTime;

        // 测试不打印异常栈信息的情况
        startTime = System.currentTimeMillis();
        for (int i = 0; i < ITERATIONS; i++) {
            try {
                throw new CoreMerchantConfigAbnormalException("不打印异常栈");
            } catch (CoreMerchantConfigAbnormalException e) {
                log.error("error={}", e.getMessage());
            }
        }
        endTime = System.currentTimeMillis();
        System.out.println("不打印异常栈耗时: " + (endTime - startTime) + " ms");
    }
}
