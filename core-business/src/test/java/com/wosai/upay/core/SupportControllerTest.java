package com.wosai.upay.core;

import com.wosai.upay.core.CoreBusinessApplication;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.util.JsonUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = {CoreBusinessApplication.class})
@SpringBootTest
public class SupportControllerTest {



//    @Autowired
//    private SupportService supportService;
////    @Test
//    public void getBasicParams() throws Exception {
//
//        System.out.println(Thread.currentThread().getName());
//
//        List<Map> mapList = new ArrayList<>();
//
//       for(int i = 0 ; i < 10000; i ++ ){
//           try{
//
//
//               mapList.add(supportService.getBasicParams(null, "100000000000684636"));
//
//              // System.out.println("BasicParam->"+JsonUtil.toJsonStr(supportService.getBasicParams("123", "100000000000684636")));
//
//           }catch (Exception ex){
//               ex.printStackTrace();
//           }
//       }
//
//        System.out.println("paramList大小="+Arrays.toString(mapList.toArray()).getBytes().length);
//        TimeUnit.SECONDS.sleep(1000000);
//    }
//
//
//    @Test
//    public void getAllParams() throws Exception {
//
//        for(int i = 0 ; i < 10; i ++ ){
//            List<Map> mapList = new ArrayList<>();
//            try{
//                System.out.println("BasicParam->"+JsonUtil.toJsonStr(supportService.getAllParams(null, "2100000",18,1)));
//            }catch (Exception ex){
//                ex.printStackTrace();
//            }
//        }
//    }


}
