package com.wosai.upay.core.dao;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.core.service.user.UserService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * DbSecurityTransformationTest
 *
 * <AUTHOR>
 * @date 2019-08-12 12:16
 */
public class DbSecurityTransformationTest  extends BaseTest {

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private LogService logService;
    @Autowired
    private UserService userService;
    @Autowired
    private SnGenerator snGenerator;

    @Before
    public void before() {
        System.out.println("------------------------- 开始测试: -------------------------");
    }

    @After
    public void after() {
        System.out.println("------------------------- 测试结束 -------------------------");
    }

    /**
     * upay_core 读操作
     */
    @Test
    public void getTerminal() {
        String terminalId = "0005005e-426f-4666-b46e-1137341e0f55";
        Map result = this.terminalService.getTerminal(terminalId);
        System.out.println(JSON.toJSONString(result));
    }

    /**
     * upay_core 写操作
     */
    @Test
    public void enableTerminal() {
        String terminalId = "0005005e-426f-4666-b46e-1137341e0f55";
//        this.terminalService.disableTerminal(terminalId);
//        this.terminalService.enableTerminal(terminalId);
    }


//    /**
//     * upay_log 读写
//     */
//    @Test
//    public void createOpLog() {
//        Map opLog = new HashMap(5);
//        opLog.put("request_system", 9);
//        opLog.put("deleted", 1);
//        opLog.put("ctime", System.currentTimeMillis());
//        opLog.put("mtime", System.currentTimeMillis());
//        Map result = this.logService.createOpLog(opLog);
//        System.out.println(JSON.toJSONString(result));
//    }

//    /**
//     * ticket 读写
//     */
//    @Test
//    public void nextActivationCode() {
//        String result = this.snGenerator.nextActivationCode();
//        System.out.println(result);
//    }

}