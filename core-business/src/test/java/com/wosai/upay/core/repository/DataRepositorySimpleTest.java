package com.wosai.upay.core.repository;

import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.Filter;
import com.wosai.upay.core.model.MerchantGallery;
import com.wosai.upay.core.service.base.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Simple test class for DataRepository
 * Extends BaseTest to use the existing test infrastructure
 */
@Slf4j
public class DataRepositorySimpleTest extends BaseTest {

    @Autowired
    private List<Dao<Map<String, Object>>> daos;
    @Autowired
    @Qualifier("merchantBusinessLicenseReadDao")
    private Dao<Map<String, Object>> merchantBusinessLicenseReadDao;

    @Test
    public void testQuery() {
        for (Dao<Map<String, Object>> dao : daos){
            try {
                Map<String, Object> result = dao.filter(Criteria.where("id").ne(null)).fetchOne();
                log.info("testQuery: {}", result);
            } catch (Exception e) {
                if (e.getMessage().contains("doesn't exist")){
                    log.warn("testQuery: " + e.getMessage());
                } else {
                    throw e;
                }
            }
        }
    }
}
