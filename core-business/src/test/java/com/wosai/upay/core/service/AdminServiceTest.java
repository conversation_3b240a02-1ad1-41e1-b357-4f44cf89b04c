package com.wosai.upay.core.service;

import com.wosai.data.dao.Criteria;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.core.util.JsonUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Map;

@Ignore
public class AdminServiceTest extends BaseTest {


    @Autowired
    private AdminService adminService;

    @Autowired
    DataRepository dataRepository;


//    @Test //ok
//    public void reSaveMerchantForDbCrypto(){
//        adminService.reSaveMerchantForDbCrypto("00505a45-0bce-4e0e-b684-f905ce4bfa3c");
//    }
//
//
//    @Test //ok
//    public void reSaveMerchantBankAccountForDbCrypto(){
//        adminService.reSaveMerchantBankAccountForDbCrypto("809a509e-2efe-495a-a0a6-e25577545153");
//    }
//
//
//    @Test //ok
//    public void testCriteria(){
//        Map<String,Object> merchant = null;

//                 merchant =  dataRepository.getMerchantDao()
//                .filter(Criteria.where(Merchant.CONTACT_CELLPHONE)
//                        .ne("***********")
//                        , Arrays.asList(Merchant.CONTACT_NAME))
//                .fetchOne();

//                 merchant =  dataRepository.getMerchantDao()
//                .filter(Criteria.where(Merchant.CONTACT_CELLPHONE)
//                                .nin("***********","***********")
//                        , Arrays.asList(Merchant.CONTACT_NAME))
//                .fetchOne();

//                 merchant =  dataRepository.getMerchantDao()
//                .filter(Criteria.where(Merchant.CONTACT_CELLPHONE).is("***********")
//                        , Arrays.asList(Merchant.CONTACT_NAME))
//                .fetchOne();

//                merchant =  dataRepository.getMerchantDao()
//                .filter(Criteria.or(Criteria.where(Merchant.CONTACT_CELLPHONE).is("***********")
//                        , Criteria.where(Merchant.CONTACT_PHONE).is("***********"))
//                        , Arrays.asList(Merchant.CONTACT_NAME))
//                .fetchOne();

//                 merchant =  dataRepository.getMerchantDao()
//                .filter(Criteria.and(Criteria.where(Merchant.CONTACT_CELLPHONE).is("***********")
//                        , Criteria.where(Merchant.CONTACT_CELLPHONE).is("***********"))
//                        , Arrays.asList(Merchant.CONTACT_NAME))
//                .fetchOne();


//        merchant = dataRepository.getMerchantDao().findAndModify(Criteria.where(Merchant.CONTACT_CELLPHONE).is("***********")
//                , CollectionUtil.hashMap(Merchant.CONTACT_CELLPHONE, "***********"));
//        System.out.println("result => " + JsonUtil.toJsonStr(merchant));
//    }


//    @Test //ok
//    public void reSaveStoreForDbCrypto(){
//        adminService.reSaveStoreForDbCrypto("001f11a1-ad6b-4e55-aa06-10e836b50888");
//    }

//    @Test //ok
//    public void reSaveAccountForDbCrypto(){
//        adminService.reSaveAccountForDbCrypto("420b8214-cc61-461a-b67b-************");
//    }

}
