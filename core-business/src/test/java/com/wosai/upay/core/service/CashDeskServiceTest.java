package com.wosai.upay.core.service;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.CashDesk;
import com.wosai.upay.core.model.CashDeskDevice;
import com.wosai.upay.core.model.CashDeskOpLog;
import com.wosai.upay.core.service.base.BaseTest;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2022/10/17
 */
public class CashDeskServiceTest extends BaseTest {

    @Autowired
    private CashDeskService cashDeskService;

    @MockBean
    private TradeConfigService tradeConfigService;

    @MockBean
    private ChangeShiftsService changeShiftsService;
    @MockBean
    private SupportService supportService;
    @MockBean
    private BusinssCommonService businessCommonService;
    @MockBean
    private ApplicationContext applicationContext;
    @MockBean
    private SnGenerator snGenerator;

    /**
     * 创建的终端信息和已有的没有重复
     */
    @Test
    public void testCreateCashDesk01() {
        Mockito.doReturn(changeShiftsService).when(applicationContext).getBean(ChangeShiftsService.class);
        Mockito.doReturn(supportService).when(applicationContext).getBean(SupportService.class);
        String merchantId = "merchant_id";
        String storeId = "store_id";
        // 创建收银台信息
        Map save = CollectionUtil.hashMap("name", "测试收银台",
                "merchant_id", merchantId, "store_id", storeId,
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save);

        // 查询收银台信息
        Map cashDesk = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(merchantId, "terminal1");
        Assert.assertEquals("测试收银台", WosaiMapUtils.getString(cashDesk, CashDesk.NAME));
        cashDesk = cashDeskService.getSimpleCashDeskByMerchantIdAndName(merchantId, "测试收银台");
        Assert.assertTrue(WosaiMapUtils.isNotEmpty(cashDesk));

        cashDesk = cashDeskService.getCashDeskWithDevicesById(WosaiMapUtils.getString(cashDesk, DaoConstants.ID));
        List<Map> cashDeskDevices = (List<Map>) cashDesk.get("devices");
        Assert.assertEquals(4, cashDeskDevices.size());

        ListResult listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap("store_ids", Arrays.asList(storeId)));
        Assert.assertEquals(1, listResult.getTotal());
        ListResult cashDeskOpLogs = cashDeskService.findCashDeskOpLogs(new PageInfo(1, 10), MapUtils.getString(cashDesk, DaoConstants.ID));
        Assert.assertEquals(1, listResult.getTotal());
        Assert.assertEquals("创建", cashDeskOpLogs.getRecords().get(0).get(CashDeskOpLog.CHANGE_CONTENT));
    }

    @Test
    public void testCreateCashDesk02() {
        Mockito.doReturn(changeShiftsService).when(applicationContext).getBean(ChangeShiftsService.class);
        Mockito.doReturn(supportService).when(applicationContext).getBean(SupportService.class);
        String merchantId = "merchant_id";
        String storeId = "store_id";
        // 创建收银台信息
        Map save = CollectionUtil.hashMap("name", "测试收银台01",
                "merchant_id", merchantId, "store_id", storeId,
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save);
        // 查询收银台信息
        Map cashDesk01 = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(merchantId, "terminal1");
        Assert.assertEquals("测试收银台01", WosaiMapUtils.getString(cashDesk01, CashDesk.NAME));
        cashDesk01 = cashDeskService.getSimpleCashDeskByMerchantIdAndName(merchantId, "测试收银台01");
        Assert.assertTrue(WosaiMapUtils.isNotEmpty(cashDesk01));

        cashDesk01 = cashDeskService.getCashDeskWithDevicesById(WosaiMapUtils.getString(cashDesk01, DaoConstants.ID));
        List<Map> cashDeskDevices01 = (List<Map>) cashDesk01.get("devices");
        Assert.assertEquals(4, cashDeskDevices01.size());


        // 创建收银台信息 将terminal1和operator_id1绑定到这个收银台上
        save = CollectionUtil.hashMap("name", "测试收银台02",
                "merchant_id", merchantId, "store_id", storeId,
                "terminals", Arrays.asList("terminal1", "terminal3"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save);
        // 查询收银台信息
        Map cashDesk02 = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(merchantId, "terminal1");
        Assert.assertEquals("测试收银台02", WosaiMapUtils.getString(cashDesk02, CashDesk.NAME));
        cashDesk02 = cashDeskService.getSimpleCashDeskByMerchantIdAndName(merchantId, "测试收银台02");
        Assert.assertTrue(WosaiMapUtils.isNotEmpty(cashDesk02));

        cashDesk02 = cashDeskService.getCashDeskWithDevicesById(WosaiMapUtils.getString(cashDesk02, DaoConstants.ID));
        List<Map> cashDeskDevices02 = (List<Map>) cashDesk02.get("devices");
        Assert.assertEquals(4, cashDeskDevices02.size());

        // 收银台01中的两个terminal1和operator_id1被解绑了
        cashDesk01 = cashDeskService.getCashDeskWithDevicesById(WosaiMapUtils.getString(cashDesk01, DaoConstants.ID));
        cashDeskDevices01 = (List<Map>) cashDesk01.get("devices");
        Assert.assertEquals(1, cashDeskDevices01.size());

        ListResult listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap("store_ids", Arrays.asList(storeId)));
        Assert.assertEquals(2, listResult.getTotal());
    }

    @Test
    public void testUpdateCashDesk() {
        ThreadPoolExecutor threadPoolExecutor = Mockito.mock(ThreadPoolExecutor.class);
        ReflectionTestUtils.setField(cashDeskService, "threadPoolExecutor", threadPoolExecutor);

        String merchantId = "merchant_id";
        String storeId = "store_id";
        // 创建收银台信息
        Map save = CollectionUtil.hashMap("name", "测试收银台01",
                "merchant_id", merchantId, "store_id", storeId,
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        Map cashDesk01 = cashDeskService.createCashDesk(save);

        save = CollectionUtil.hashMap("name", "测试收银台02",
                "merchant_id", merchantId, "store_id", storeId,
                "terminals", Arrays.asList("terminal3", "terminal4"),
                "uc_user_ids", Arrays.asList("uc_user_id3", "uc_user_id4"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        Map cashDesk02 = cashDeskService.createCashDesk(save);

        cashDeskService.updateCashDesk(CollectionUtil.hashMap(DaoConstants.ID, WosaiMapUtils.getString(cashDesk01, DaoConstants.ID), CashDesk.NAME, "测试收银台01",
                "terminals", Arrays.asList("terminal2", "terminal3"), "uc_user_ids", Arrays.asList("uc_user_id2", "uc_user_id3"),
                "operator", "operator"));

        cashDesk01 = cashDeskService.getCashDeskWithDevicesById(WosaiMapUtils.getString(cashDesk01, DaoConstants.ID));
        Assert.assertEquals(4, ((List<String>) cashDesk01.get("devices")).size());
        cashDesk02 = cashDeskService.getCashDeskWithDevicesById(WosaiMapUtils.getString(cashDesk02, DaoConstants.ID));
        Assert.assertEquals(2, ((List<String>) cashDesk02.get("devices")).size());
        Map terminal1 = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(merchantId, "terminal1");
        Assert.assertTrue(WosaiMapUtils.isEmpty(terminal1));
        Map operatorId1 = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(merchantId, "uc_user_id1");
        Assert.assertTrue(WosaiMapUtils.isEmpty(operatorId1));
    }

    @Test
    public void testDeleteCashDesk() {
        String merchantId = "merchant_id";
        String storeId = "store_id";
        // 创建收银台信息
        Map save = CollectionUtil.hashMap("name", "测试收银台",
                "merchant_id", merchantId, "store_id", storeId,
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        Map cashDesk = cashDeskService.createCashDesk(save);
        cashDeskService.deleteCashDesk(BeanUtil.getPropString(cashDesk, DaoConstants.ID));

        // 软删除cash_desk，硬删除cash_desk_device
        ListResult listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap("merchant_id", merchantId));
        Assert.assertEquals(1, listResult.getTotal());
        cashDesk = cashDeskService.getCashDeskWithDevicesById(BeanUtil.getPropString(cashDesk, DaoConstants.ID));
        List<Map> cashDeskDevices = (List<Map>) cashDesk.get("devices");
        Assert.assertTrue(WosaiCollectionUtils.isEmpty(cashDeskDevices));

    }

    @Test
    public void testFindCashDesks() {
        String merchantId = "merchant_id";
        Map save = CollectionUtil.hashMap("name", "测试收银台01",
                "merchant_id", merchantId, "store_id", "store_id1",
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        String sn1 = System.currentTimeMillis() + "";
        Mockito.doReturn(sn1).when(snGenerator).nextCashDeskSn();
        Map cashDesk1 = cashDeskService.createCashDesk(save);
        save = CollectionUtil.hashMap("name", "测试收银台02",
                "merchant_id", merchantId, "store_id", "store_id2",
                "terminals", Arrays.asList("terminal3", "terminal4"),
                "uc_user_ids", Arrays.asList("uc_user_id3"),
                "operator", "operator");
        String sn2 = System.currentTimeMillis() + "";
        Mockito.doReturn(sn2).when(snGenerator).nextCashDeskSn();
        Map cashDesk2 = cashDeskService.createCashDesk(save);

        ListResult listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap("merchant_id", merchantId));
        Assert.assertEquals(2, listResult.getTotal());
        listResult = cashDeskService.findCashDesks(new PageInfo(1, 1), CollectionUtil.hashMap("merchant_id", merchantId));
        Assert.assertEquals(1, listResult.getRecords().size());
        listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap("store_ids", Arrays.asList("store_id1")));
        Assert.assertEquals(1, listResult.getRecords().size());
        Assert.assertEquals(4, ((List<Map>) listResult.getRecords().get(0).get("devices")).size());

        listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap("store_ids", Arrays.asList("store_id2")));
        Assert.assertEquals(1, listResult.getRecords().size());
        Assert.assertEquals(3, ((List<Map>) listResult.getRecords().get(0).get("devices")).size());

        listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap(
                "ids", Arrays.asList(MapUtils.getString(cashDesk1, DaoConstants.ID), MapUtils.getString(cashDesk2, DaoConstants.ID)),
                CashDesk.NAME, "收银台02",
                CashDesk.SN, sn2
        ));
        Assert.assertEquals(1, listResult.getTotal());

        listResult = cashDeskService.findCashDesks(new PageInfo(1, 10), CollectionUtil.hashMap(
                "ids", Arrays.asList(MapUtils.getString(cashDesk1, DaoConstants.ID), MapUtils.getString(cashDesk2, DaoConstants.ID)),
                CashDesk.NAME, "收银台02",
                CashDesk.SN, sn1
        ));
        Assert.assertEquals(0, listResult.getTotal());
    }

    @Test
    public void testGetCashDeskDeviceByMerchantIdAndDeviceId() {
        Map save1 = CollectionUtil.hashMap("name", "测试收银台01",
                "merchant_id", "merchant_id1", "store_id", "store_id1",
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save1);

        Map save2 = CollectionUtil.hashMap("name", "测试收银台02",
                "merchant_id", "merchant_id2", "store_id", "store_id2",
                "terminals", Arrays.asList("terminal3", "terminal4"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id3"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save2);

        Map result = cashDeskService.getCashDeskDeviceByMerchantIdAndDeviceId("merchant_id1", "uc_user_id1");
        Assert.assertEquals("uc_user_id1", WosaiMapUtils.getString(result, CashDeskDevice.DEVICE_ID));

        result = cashDeskService.getCashDeskDeviceByMerchantIdAndDeviceId("merchant_id2", "uc_user_id1");
        Assert.assertEquals("uc_user_id1", WosaiMapUtils.getString(result, CashDeskDevice.DEVICE_ID));
    }

    @Test
    public void testGetCashDesksSimpleByDeviceIds01() {
        Map save = CollectionUtil.hashMap("name", "测试收银台01",
                "merchant_id", "merchant_id", "store_id", "store_id",
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save);

        Map result = cashDeskService.getSimpleCashDesksByMerchantIdAndDeviceIds("merchant_id", Arrays.asList("terminal1", "terminal2", "uc_user_id2"));
        Assert.assertEquals(3, result.keySet().size());

        result = cashDeskService.getSimpleCashDesksByMerchantIdAndDeviceIds("merchant_id", Arrays.asList("terminal1", "terminal2"));
        Assert.assertEquals(2, result.keySet().size());

    }

    @Test
    public void testGetCashDesksSimpleByDeviceIds02() {
        Map save1 = CollectionUtil.hashMap("name", "测试收银台01",
                "merchant_id", "merchant_id1", "store_id", "store_id1",
                "terminals", Arrays.asList("terminal1", "terminal2"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id2"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save1);

        Map save2 = CollectionUtil.hashMap("name", "测试收银台02",
                "merchant_id", "merchant_id2", "store_id", "store_id2",
                "terminals", Arrays.asList("terminal3", "terminal4"),
                "uc_user_ids", Arrays.asList("uc_user_id1", "uc_user_id3"),
                "operator", "operator");
        Mockito.doReturn(System.currentTimeMillis() + "").when(snGenerator).nextCashDeskSn();
        cashDeskService.createCashDesk(save2);

        Map result = cashDeskService.getSimpleCashDesksByMerchantIdAndDeviceIds("merchant_id1", Arrays.asList("terminal1", "terminal2", "uc_user_id1"));
        Assert.assertEquals(3, result.keySet().size());
        Assert.assertEquals("测试收银台01", BeanUtil.getPropString(result, "uc_user_id1.name"));

        result = cashDeskService.getSimpleCashDesksByMerchantIdAndDeviceIds("merchant_id1", Arrays.asList("uc_user_id1"));
        Assert.assertEquals(1, result.keySet().size());

        result = cashDeskService.getSimpleCashDesksByMerchantIdAndDeviceIds("merchant_id1", Arrays.asList("terminal1", "terminal2"));
        Assert.assertEquals(2, result.keySet().size());

    }
}
