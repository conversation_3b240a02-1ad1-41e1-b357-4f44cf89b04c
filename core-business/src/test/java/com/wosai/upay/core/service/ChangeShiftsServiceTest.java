package com.wosai.upay.core.service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.dao.Filter;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.bean.request.ChangeShiftsBatchQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckInRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCheckOutRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsQueryRequest;
import com.wosai.upay.core.bean.request.HasCashDeskChangeShiftsRequest;
import com.wosai.upay.core.bean.request.UpdateChangeShiftsExtraRequest;
import com.wosai.upay.core.bean.response.ChangeShiftsBatchQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckInResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCheckOutResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsInfo;
import com.wosai.upay.core.bean.response.ChangeShiftsQueryResponse;
import com.wosai.upay.core.model.CashDeskDevice;
import com.wosai.upay.core.model.ChangeShifts;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.util.DateUtil;

public class ChangeShiftsServiceTest extends BaseTest{

    @Autowired
    ChangeShiftsService changeShiftsService;

    @Autowired
    DataRepository repository;

    /**
     * 设备做签到和签退（正常业务流程)
     */
    @Test
    public void testTerminalChangeShifts() {
        Criteria criteria = Criteria.where(Terminal.CURRENT_CHECKIN_STATUS).is(null).with(Terminal.STATUS).is(Terminal.STATUS_ACTIVATED);
        Filter<Map<String, Object>> filter = repository.getTerminalDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(100);
        List<Map<String, Object>> terminals = CollectionUtil.iterator2list(filter.fetchAll());
        Map<String, Object> terminal = terminals.get(ThreadLocalRandom.current().nextInt(terminals.size()));
        String sn = MapUtil.getString(terminal, Terminal.SN);
        String id = MapUtil.getString(terminal, DaoConstants.ID);
        // 签到
        ChangeShiftsCheckInRequest request = new ChangeShiftsCheckInRequest(sn);
        ChangeShiftsCheckInResponse response = changeShiftsService.changeShiftsCheckIn(request);
        String batchNo = response.getBatchSn();
        Long startDate = response.getStartDate();

        // 数据对比
        criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(id).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL);
        filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map<String, Object> changeShifts = filter.fetchOne();
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(startDate, MapUtil.getLongValue(changeShifts, ChangeShifts.START_DATE));

        // 签到后终端checkin状态发生变化
        terminal = repository.getTerminalDao().get(id);
        assert terminal != null
                && Objects.equals(Terminal.CURRENT_CHECKIN_STATUS_CHECKIN, MapUtil.getInteger(terminal, Terminal.CURRENT_CHECKIN_STATUS));

        // 再次签到
        response = changeShiftsService.changeShiftsCheckIn(request);
        batchNo = response.getBatchSn();
        startDate = response.getStartDate();

        // 数据对比
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(startDate, MapUtil.getLongValue(changeShifts, ChangeShifts.START_DATE));

        // 签退
        ChangeShiftsCheckOutRequest checkoutRequest = new ChangeShiftsCheckOutRequest();
        checkoutRequest.setTerminalSn(sn);
        checkoutRequest.setCashierNo("abc");
        ChangeShiftsCheckOutResponse checkOutResult = changeShiftsService.changeShiftsCheckOut(checkoutRequest);
        Long endDate = checkOutResult.getEndDate();
        changeShifts = repository.getChangeShiftsDao().get(MapUtil.getString(changeShifts, DaoConstants.ID));
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(endDate, MapUtil.getLongValue(changeShifts, ChangeShifts.END_DATE));

        // 签退后终端checkin状态发生变化
        terminal = repository.getTerminalDao().get(id);
        assert terminal != null
                && Objects.equals(Terminal.CURRENT_CHECKIN_STATUS_CHECKOUT, MapUtil.getInteger(terminal, Terminal.CURRENT_CHECKIN_STATUS));

        // 相同收银员编号再次签退时，不报错
        checkOutResult = changeShiftsService.changeShiftsCheckOut(checkoutRequest);
        endDate = checkOutResult.getEndDate();
        changeShifts = repository.getChangeShiftsDao().get(MapUtil.getString(changeShifts, DaoConstants.ID));
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(endDate, MapUtil.getLongValue(changeShifts, ChangeShifts.END_DATE));

        // 不同收银员编号再次签退时，报错
        checkoutRequest.setCashierNo("cba");
        Throwable th = null;
        try {
            checkOutResult = changeShiftsService.changeShiftsCheckOut(checkoutRequest);
        } catch (Exception e){
            th = e;
        }
        assert th != null;
    }

    /**
     * 收银台做签到和签退（正常业务流程)
     */
    @Test
    public void testCashDeskChangeShifts() {
        Criteria criteria = Criteria.where(CashDeskDevice.DEVICE_TYPE).is(CashDeskDevice.DEVICE_TYPE_TERMINAL);
        Filter<Map<String, Object>> filter = repository.getCashDeskDeviceDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(100);
        List<Map<String, Object>> cashDeskDevices = CollectionUtil.iterator2list(filter.fetchAll());
        if (cashDeskDevices.isEmpty()){
            return;
        }
        Map<String, Object> cashDeskDevice = cashDeskDevices.get(ThreadLocalRandom.current().nextInt(cashDeskDevices.size()));
        Map<String, Object> terminal = repository.getTerminalDao().get(MapUtil.getString(cashDeskDevice, CashDeskDevice.DEVICE_ID));
        String sn = MapUtil.getString(terminal, Terminal.SN);
        String cashDeskId = MapUtil.getString(cashDeskDevice, CashDeskDevice.CASH_DESK_ID);
        // 签到
        ChangeShiftsCheckInRequest request = new ChangeShiftsCheckInRequest();
        request.setTerminalSn(sn);
        request.setAccessCashDesk(true);
        ChangeShiftsCheckInResponse response = changeShiftsService.changeShiftsCheckIn(request);
        String batchNo = response.getBatchSn();
        Long startDate = response.getStartDate();

        // 数据对比
        criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(cashDeskId).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK);
        filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map<String, Object> changeShifts = filter.fetchOne();
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(startDate, MapUtil.getLongValue(changeShifts, ChangeShifts.START_DATE));

        // 再次签到
        response = changeShiftsService.changeShiftsCheckIn(request);
        batchNo = response.getBatchSn();
        startDate = response.getStartDate();

        // 数据对比
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(startDate, MapUtil.getLongValue(changeShifts, ChangeShifts.START_DATE));

        // 签退
        ChangeShiftsCheckOutRequest checkoutRequest = new ChangeShiftsCheckOutRequest(sn, null, "abc");
        checkoutRequest.setAccessCashDesk(true);
        ChangeShiftsCheckOutResponse checkOutResult = changeShiftsService.changeShiftsCheckOut(checkoutRequest);
        Long endDate = checkOutResult.getEndDate();
        changeShifts = repository.getChangeShiftsDao().get(MapUtil.getString(changeShifts, DaoConstants.ID));
        assert changeShifts != null
                && Objects.equals(batchNo, MapUtil.getString(changeShifts, ChangeShifts.BATCH_SN))
                && Objects.equals(endDate, MapUtil.getLongValue(changeShifts, ChangeShifts.END_DATE));
        String nextBatchNo = checkOutResult.getNextBatchSn();
        assert null != nextBatchNo;

        // 校验下一批次
        criteria = Criteria.where(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK).with(ChangeShifts.SERVICE_ID).is(cashDeskId);
        filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map<String, Object> nextChangeShifts = filter.fetchOne();
        assert null != nextChangeShifts && Objects.equals(MapUtil.getLongValue(changeShifts, ChangeShifts.END_DATE) + 1, MapUtil.getLongValue(nextChangeShifts, ChangeShifts.START_DATE) );
    }

    /**
     * 设备做签到和签退（商户签到后被移到收银台，进行签退)
     */
    @Test
    public void testTerminalChangeShifts2() {
        Criteria criteria = Criteria.where(Terminal.CURRENT_CHECKIN_STATUS).is(null).with(Terminal.STATUS).is(Terminal.STATUS_ACTIVATED);
        Filter<Map<String, Object>> filter = repository.getTerminalDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(100);
        List<Map<String, Object>> terminals = CollectionUtil.iterator2list(filter.fetchAll());
        Map<String, Object> terminal = terminals.get(ThreadLocalRandom.current().nextInt(terminals.size()));
        String sn = MapUtil.getString(terminal, Terminal.SN);
        String id = MapUtil.getString(terminal, DaoConstants.ID);

        criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(id).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL);
        filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        Map<String, Object> changeShifts = filter.fetchOne();
        if (changeShifts != null && MapUtil.getLong(changeShifts, ChangeShifts.END_DATE) == null){
            repository.getChangeShiftsDao().delete(MapUtil.getString(changeShifts, DaoConstants.ID));
        }
        HasCashDeskChangeShiftsRequest hasCashDeskChangeShiftsRequest = new HasCashDeskChangeShiftsRequest();
        hasCashDeskChangeShiftsRequest.setTerminalSns(Arrays.asList(sn));
        changeShiftsService.hasCashDeskChangeShifts(hasCashDeskChangeShiftsRequest);

        // 商户不存在班次信息，签到报错
        Throwable th = null;
        try {
            ChangeShiftsCheckOutRequest request = new ChangeShiftsCheckOutRequest();
            request.setTerminalSn(sn);
            changeShiftsService.changeShiftsCheckOut(request);
        }catch (Exception e){
            th = e;
        }
        assert th != null;

        // 添加班次信息
        String batchNo = DateUtil.formatDate(new Date(), "yyyyMMdd") + "0001";
        repository.getChangeShiftsDao().save(MapUtil.hashMap(
                ChangeShifts.MERCHANT_ID, BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID),
                ChangeShifts.TYPE, ChangeShifts.TYPE_TERMINAL,
                ChangeShifts.SERVICE_ID, id,
                ChangeShifts.BATCH_SN, batchNo,
                ChangeShifts.START_DATE, System.currentTimeMillis()));

        th = null;
        // 设备状态不为签到状态，签到报错
        try {
            ChangeShiftsCheckOutRequest request = new ChangeShiftsCheckOutRequest();
            request.setTerminalSn(sn);
            changeShiftsService.changeShiftsCheckOut(request);
        }catch (Exception e){
            th = e;
        }
        assert th != null;

        th = null;
        // 变更终端签到状态
        repository.getTerminalDao().updatePart(MapUtil.hashMap(DaoConstants.ID, MapUtil.getString(terminal, DaoConstants.ID),
                    Terminal.CURRENT_CHECKIN_STATUS, Terminal.CURRENT_CHECKIN_STATUS_CHECKIN
                ));
        ChangeShiftsCheckOutResponse checkOutResult = null;
        try {
            ChangeShiftsCheckOutRequest request = new ChangeShiftsCheckOutRequest();
            request.setTerminalSn(sn);
            checkOutResult = changeShiftsService.changeShiftsCheckOut(request);
        }catch (Exception e){
            th = e;
        }
        assert th == null
                && checkOutResult != null
                && Objects.equals(batchNo, checkOutResult.getBatchSn());
        
        changeShiftsService.hasCashDeskChangeShifts(hasCashDeskChangeShiftsRequest);
    }

    @Test
    public void testGetTerminalChangeShiftsInfo(){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).ne(null).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL);
        Filter<Map<String, Object>> filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        List<Map<String, Object>> changeShifts = CollectionUtil.iterator2list(filter.fetchAll());
        if (changeShifts.isEmpty()){
            return;
        }
        Map<String, Object> changeShift = changeShifts.get(ThreadLocalRandom.current().nextInt(changeShifts.size()));
        Map<String, Object> terminal = repository.getTerminalDao().get(MapUtil.getString(changeShift, ChangeShifts.SERVICE_ID));
        ChangeShiftsQueryRequest request = new ChangeShiftsQueryRequest();
        request.setTerminalSn(MapUtil.getString(terminal, Terminal.SN));
        request.setBatchSn(MapUtil.getString(changeShift, ChangeShifts.BATCH_SN));
        request.setUseBatchSn(true);
        ChangeShiftsQueryResponse response =  changeShiftsService.getChangeShiftsInfo(request);
        assert Objects.equals(MapUtil.getLongValue(changeShift, DaoConstants.ID), response.getId())
                && Objects.equals(MapUtil.getString(changeShift, ChangeShifts.BATCH_SN), response.getBatchSn())
                && Objects.equals(MapUtil.getLongValue(changeShift, ChangeShifts.END_DATE), response.getEndDate());

    }

    @Test
    public void testGetCashDeskChangeShiftsInfo(){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).ne(null).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK);
        Filter<Map<String, Object>> filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        List<Map<String, Object>> changeShiftsList = CollectionUtil.iterator2list(filter.fetchAll());
        Map findChangeShifts = null;
        Map<String, Object> terminal = null;
        for (Map<String, Object> changeShifts: changeShiftsList) {
            criteria = Criteria.where(CashDeskDevice.DEVICE_TYPE).is(CashDeskDevice.DEVICE_TYPE_TERMINAL)
                    .with(CashDeskDevice.CASH_DESK_ID).is(MapUtil.getString(changeShifts, ChangeShifts.SERVICE_ID));
            Map<String, Object> cashDeskDevice = repository.getCashDeskDeviceDao().filter(criteria).fetchOne();
            if (cashDeskDevice != null){
                terminal = repository.getTerminalDao().get(MapUtil.getString(cashDeskDevice, CashDeskDevice.DEVICE_ID));
                findChangeShifts = changeShifts;
            }
        }
        if (findChangeShifts == null) {
            return;
        }
        ChangeShiftsQueryRequest request = new ChangeShiftsQueryRequest();
        request.setTerminalSn(MapUtil.getString(terminal, Terminal.SN));
        ChangeShiftsQueryResponse response =  changeShiftsService.getChangeShiftsInfo(request);
        assert response == null
                || (!Objects.equals(MapUtil.getLongValue(findChangeShifts, DaoConstants.ID), response.getId())
                    && !Objects.equals(MapUtil.getString(findChangeShifts, ChangeShifts.BATCH_SN), response.getBatchSn()));

        request.setBatchSn(MapUtil.getString(findChangeShifts, ChangeShifts.BATCH_SN));
        request.setUseBatchSn(true);
        request.setAccessCashDesk(true);
        response =  changeShiftsService.getChangeShiftsInfo(request);
        assert response != null
                && Objects.equals(MapUtil.getLongValue(findChangeShifts, DaoConstants.ID), response.getId())
                && Objects.equals(MapUtil.getString(findChangeShifts, ChangeShifts.BATCH_SN), response.getBatchSn());


    }

    @Test
    public void testGetTerminalChangeShiftsList(){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).ne(null).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL);
        Filter<Map<String, Object>> filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        List<Map<String, Object>> changeShifts = CollectionUtil.iterator2list(filter.fetchAll());
        if (changeShifts.isEmpty()){
            return;
        }
        Map<String, Object> changeShift = changeShifts.get(ThreadLocalRandom.current().nextInt(changeShifts.size()));
        Map<String, Object> terminal = repository.getTerminalDao().get(MapUtil.getString(changeShift, ChangeShifts.SERVICE_ID));
        ChangeShiftsBatchQueryRequest request = new ChangeShiftsBatchQueryRequest();
        request.setTerminalSn(MapUtil.getString(terminal, Terminal.SN));
        request.setReturnUnCheckout(true);
        ChangeShiftsBatchQueryResponse queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        assert queryChangeShift != null && queryChangeShift.getRecords().size() >=1;

        criteria = Criteria.where(ChangeShifts.SERVICE_ID).ne(null).with(ChangeShifts.CASHIER_ID).ne(null);
        filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        changeShifts = CollectionUtil.iterator2list(filter.fetchAll());
        if (changeShifts.isEmpty()){
            return;
        }
        Map<String, Object> cashierChangeShifts = changeShifts.get(ThreadLocalRandom.current().nextInt(changeShifts.size()));
        long id = MapUtil.getLongValue(cashierChangeShifts, DaoConstants.ID);
        long start = (Long)cashierChangeShifts.get(ChangeShifts.START_DATE);
        long end = (Long)cashierChangeShifts.get(ChangeShifts.END_DATE);
        String merchantId = (String) cashierChangeShifts.get(ChangeShifts.MERCHANT_ID);
        Consumer<ChangeShiftsBatchQueryResponse> cs = (rp) -> {
            assert rp != null
                    && rp.getRecords() != null
                    && rp.getRecords().size() >= 1
                    && rp.getRecords().stream().map(ChangeShiftsInfo::getId).collect(Collectors.toList()).contains(id);
        };
        // 收银员查询
        request = new ChangeShiftsBatchQueryRequest();
        request.setCsMerchantId(merchantId);
        request.setPageSize(5000);
        request.setAccessCashDesk(true);
        request.setTradeCashierQuery(true);
        request.setStartDate(start);
        request.setEndDate(end);
        // 查询开始时间和结束时间在区间范围内
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        cs.accept(queryChangeShift);

        // 查询开始时间小于开始时间
        request.setStartDate(start - 10 * 60 * 1000);
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        cs.accept(queryChangeShift);

        // 查询开始时间小于开始时间，查询结束时间大于开始时间
        request.setEndDate((end - 10 * 60 * 1000) <  start ? end - 1 : end - 10 * 60 * 1000);
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        cs.accept(queryChangeShift);

        // 查询开始时间小于开始时间，查询结束时间大于结束时间
        request.setEndDate(end + 10 * 60 * 1000);
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        cs.accept(queryChangeShift);

        // 查询开始时间大于开始时间，小于结束时间，查询结束时间大于结束时间
        request.setStartDate((start + 10 * 60 * 1000) > end ? start + 1 : start + 10 * 60 * 1000);
        request.setEndDate(end + 10 * 60 * 1000);
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        cs.accept(queryChangeShift);

        // 查询开始和结束时间都小于开始时间
        request.setStartDate(start - 10 * 60 * 1000);
        request.setEndDate(start - 1);
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        assert queryChangeShift != null
                && queryChangeShift.getRecords() != null
                && !queryChangeShift.getRecords().stream().map(ChangeShiftsInfo::getId).collect(Collectors.toList()).contains(id);

        // 查询开始和结束时间都大于结束时间
        request.setStartDate(end + 1);
        request.setEndDate(end + 10 * 60 * 1000);
        queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        assert queryChangeShift != null
                && queryChangeShift.getRecords() != null
                && !queryChangeShift.getRecords().stream().map(ChangeShiftsInfo::getId).collect(Collectors.toList()).contains(id);

    }

    @Test
    public void testGetCashDeskChangeShiftsList(){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).ne(null).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_CASHDESK);
        Filter<Map<String, Object>> filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        List<Map<String, Object>> changeShiftsList = CollectionUtil.iterator2list(filter.fetchAll());
        Map findChangeShifts = null;
        Map<String, Object> terminal = null;
        for (Map<String, Object> changeShifts: changeShiftsList) {
            criteria = Criteria.where(CashDeskDevice.DEVICE_TYPE).is(CashDeskDevice.DEVICE_TYPE_TERMINAL)
                    .with(CashDeskDevice.CASH_DESK_ID).is(MapUtil.getString(changeShifts, ChangeShifts.SERVICE_ID));
            Map<String, Object> cashDeskDevice = repository.getCashDeskDeviceDao().filter(criteria).fetchOne();
            if (cashDeskDevice != null){
                terminal = repository.getTerminalDao().get(MapUtil.getString(cashDeskDevice, CashDeskDevice.DEVICE_ID));
                findChangeShifts = changeShifts;
            }
        }
        if (findChangeShifts == null) {
            return;
        }
        ChangeShiftsBatchQueryRequest request = new ChangeShiftsBatchQueryRequest();
        request.setTerminalSn(MapUtil.getString(terminal, Terminal.SN));
        request.setAccessCashDesk(true);
        request.setReturnUnCheckout(true);
        ChangeShiftsBatchQueryResponse queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        assert queryChangeShift != null && queryChangeShift.getRecords().size() >=1
                && queryChangeShift
                    .getRecords()
                    .stream()
                    .map(r -> r.getBatchSn())
                    .collect(Collectors.toList())
                    .contains(MapUtil.getString(findChangeShifts, ChangeShifts.BATCH_SN));
    }

    @Test
    public void getUnCheckoutChangeShiftsList(){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).is(null).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL);
        Filter<Map<String, Object>> filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        List<Map<String, Object>> changeShifts = CollectionUtil.iterator2list(filter.fetchAll());
        if (changeShifts.isEmpty()){
            return;
        }
        Map<String, Object> changeShift = changeShifts.get(ThreadLocalRandom.current().nextInt(changeShifts.size()));
        Map<String, Object> terminal = repository.getTerminalDao().get(MapUtil.getString(changeShift, ChangeShifts.SERVICE_ID));
        ChangeShiftsBatchQueryRequest request = new ChangeShiftsBatchQueryRequest();
        request.setTerminalSn(MapUtil.getString(terminal, Terminal.SN));
        request.setReturnUnCheckout(true);
        ChangeShiftsBatchQueryResponse queryChangeShift =  changeShiftsService.getChangeShiftsList(request);
        assert queryChangeShift != null && queryChangeShift.getRecords().size() >=1 && queryChangeShift.getRecords().get(0).getEndDate() == null;
    }

    @Test
    public void updateChangeShiftsExtra(){
        Criteria criteria = Criteria.where(ChangeShifts.SERVICE_ID).ne(null).with(ChangeShifts.TYPE).is(ChangeShifts.TYPE_TERMINAL);
        Filter<Map<String, Object>> filter = repository.getChangeShiftsDao().filter(criteria);
        filter.orderBy(DaoConstants.CTIME, Filter.DESC);
        filter.limit(10);
        List<Map<String, Object>> changeShifts = CollectionUtil.iterator2list(filter.fetchAll());
        if (changeShifts.isEmpty()){
            return;
        }
        Map<String, Object> updateExtra = MapUtil.hashMap(
                "pay_count", 10,
                "pay_amount", 10000,
                "refund_count", 1,
                "refund_amount", 2000
        );
        
        Map<String, Object> changeShift = changeShifts.get(ThreadLocalRandom.current().nextInt(changeShifts.size()));
        UpdateChangeShiftsExtraRequest request = new UpdateChangeShiftsExtraRequest();
        request.setId(MapUtil.getLong(changeShift, DaoConstants.ID));
        request.setExtra(updateExtra);
        changeShiftsService.updateChangeShiftsExtra(request);
        changeShift = repository.getChangeShiftsDao().get(MapUtil.getString(changeShift, DaoConstants.ID));
        assert MapUtil.getMap(changeShift, ChangeShifts.EXTRA) != null;
        Map<String, Object> extra = MapUtil.getMap(changeShift, ChangeShifts.EXTRA);
        updateExtra.forEach((k, v) ->{
            assert Objects.equals(v, MapUtil.getIntValue(extra, k));
        });
    }
}
