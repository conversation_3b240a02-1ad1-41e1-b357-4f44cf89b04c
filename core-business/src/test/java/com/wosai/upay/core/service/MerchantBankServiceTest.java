package com.wosai.upay.core.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.exception.CoreMerchantNotBindBankAccountException;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.service.base.BaseTest;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.UUID;

public class MerchantBankServiceTest extends BaseTest {
    @Autowired
    MerchantBankService merchantBankService;

    @Autowired
    MerchantService merchantService;

    private String getNumber() {
        // cardbin是中国农业银行借记卡,19位
        try {
            Thread.sleep(1);
        } catch (Exception ignored) {
        }
        return "622848" + System.currentTimeMillis();
    }

    /**
     * 新增银行卡
     */
    @Test
    public void testSaveMerchantBankAccountPre() {
        savePre(UUID.randomUUID().toString(), getNumber());
    }

    /**
     * 同一个商户超过10张银行卡
     */
    @Test(expected = CoreMerchantNotBindBankAccountException.class)
    public void testSaveMerchantBankAccountPreMoreThan10() {
        String merchantId = UUID.randomUUID().toString();
        for (int i = 0; i < 11; i++) {
            savePre(merchantId, getNumber());
        }
    }


    /**
     * 新增时 银行卡已存在，执行更新逻辑
     */
    @Test
    public void testSaveMerchantBankAccountForExist() {
        String merchantId = UUID.randomUUID().toString();
        String number = getNumber();
        savePre(merchantId, number);
        Map<String, Object> param = Maps.newHashMap();
        Integer type = 2;
        String holder = "holder" + "55";
        Integer idType = 4;
        String identity = "332203198205220031" + "55";
        String holder_id_front_photo = "front" + "55";
        String holder_id_back_photo = "back" + "55";
        Integer holder_id_front_ocr_status = 1;
        Integer holder_id_back_ocr_status = 1;
        Integer holder_id_status = 1;
        String tax_payer_id = "xxx" + "55";
        String verify_status = "1";
        String opening_number = "xxx" + "55";
        String cellphone = "kkk" + "55";
        String bank_card_image = "image" + "55";
        param.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
        param.put(MerchantBankAccountPre.TYPE, type);
        param.put(MerchantBankAccountPre.HOLDER, holder);
        param.put(MerchantBankAccountPre.ID_TYPE, idType);
        param.put(MerchantBankAccountPre.IDENTITY, identity);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, holder_id_front_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, holder_id_back_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS, holder_id_front_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS, holder_id_back_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_STATUS, holder_id_status);
        param.put(MerchantBankAccountPre.TAX_PAYER_ID, tax_payer_id);
        param.put(MerchantBankAccountPre.NUMBER, number);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, verify_status);
        param.put(MerchantBankAccountPre.OPENING_NUMBER, opening_number);
        param.put(MerchantBankAccountPre.CELLPHONE, cellphone);
        param.put(MerchantBankAccountPre.BANK_CARD_IMAGE, bank_card_image);
        Map result = merchantBankService.saveMerchantBankAccountPre(param);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.MERCHANT_ID), merchantId);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TYPE), type + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER), holder);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.ID_TYPE), idType + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.IDENTITY), identity);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO), holder_id_front_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO), holder_id_back_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS), holder_id_front_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS), holder_id_back_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_STATUS), holder_id_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TAX_PAYER_ID), tax_payer_id);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.NUMBER), number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.VERIFY_STATUS), verify_status);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.OPENING_NUMBER), opening_number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.CELLPHONE), cellphone);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.BANK_CARD_IMAGE), bank_card_image);
    }


    private String savePre(String merchantId, String number) {
        Map<String, Object> param = Maps.newHashMap();
        Integer type = 2;
        String holder = "holder";
        Integer idType = 4;
        String identity = "332203198205220031";
        String holder_id_front_photo = "front";
        String holder_id_back_photo = "back";
        Integer holder_id_front_ocr_status = 1;
        Integer holder_id_back_ocr_status = 1;
        Integer holder_id_status = 1;
        String tax_payer_id = "xxx";
        String verify_status = "1";
        String opening_number = "xxx";
        String cellphone = "kkk";
        String bank_card_image = "image";
        param.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
        param.put(MerchantBankAccountPre.TYPE, type);
        param.put(MerchantBankAccountPre.HOLDER, holder);
        param.put(MerchantBankAccountPre.ID_TYPE, idType);
        param.put(MerchantBankAccountPre.IDENTITY, identity);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, holder_id_front_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, holder_id_back_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS, holder_id_front_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS, holder_id_back_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_STATUS, holder_id_status);
        param.put(MerchantBankAccountPre.TAX_PAYER_ID, tax_payer_id);
        param.put(MerchantBankAccountPre.NUMBER, number);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, verify_status);
        param.put(MerchantBankAccountPre.OPENING_NUMBER, opening_number);
        param.put(MerchantBankAccountPre.CELLPHONE, cellphone);
        param.put(MerchantBankAccountPre.BANK_CARD_IMAGE, bank_card_image);
        Map result = merchantBankService.saveMerchantBankAccountPre(param);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.MERCHANT_ID), merchantId);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TYPE), type + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER), holder);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.ID_TYPE), idType + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.IDENTITY), identity);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO), holder_id_front_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO), holder_id_back_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS), holder_id_front_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS), holder_id_back_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_STATUS), holder_id_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TAX_PAYER_ID), tax_payer_id);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.NUMBER), number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.VERIFY_STATUS), verify_status);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.OPENING_NUMBER), opening_number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.CELLPHONE), cellphone);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.BANK_CARD_IMAGE), bank_card_image);
        return BeanUtil.getPropString(result, DaoConstants.ID);
    }

    @Test
    public void testUpdateMerchantBankAccountPre() {
        String merchantId = UUID.randomUUID().toString();
        String number = getNumber();
        savePre(merchantId, number);
        Map<String, Object> param = Maps.newHashMap();
        Integer type = 2;
        String holder = "holder" + "55";
        Integer idType = 4;
        String identity = "332203198205220031" + "55";
        String holder_id_front_photo = "front" + "55";
        String holder_id_back_photo = "back" + "55";
        Integer holder_id_front_ocr_status = 1;
        Integer holder_id_back_ocr_status = 1;
        Integer holder_id_status = 1;
        String tax_payer_id = "xxx" + "55";
        String verify_status = "1";
        String opening_number = "xxx" + "55";
        String cellphone = "kkk" + "55";
        String bank_card_image = "image" + "55";
        param.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
        param.put(MerchantBankAccountPre.TYPE, type);
        param.put(MerchantBankAccountPre.HOLDER, holder);
        param.put(MerchantBankAccountPre.ID_TYPE, idType);
        param.put(MerchantBankAccountPre.IDENTITY, identity);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, holder_id_front_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, holder_id_back_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS, holder_id_front_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS, holder_id_back_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_STATUS, holder_id_status);
        param.put(MerchantBankAccountPre.TAX_PAYER_ID, tax_payer_id);
        param.put(MerchantBankAccountPre.NUMBER, number);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, verify_status);
        param.put(MerchantBankAccountPre.OPENING_NUMBER, opening_number);
        param.put(MerchantBankAccountPre.CELLPHONE, cellphone);
        param.put(MerchantBankAccountPre.BANK_CARD_IMAGE, bank_card_image);
        Map result = merchantBankService.saveMerchantBankAccountPre(param);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.MERCHANT_ID), merchantId);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TYPE), type + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER), holder);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.ID_TYPE), idType + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.IDENTITY), identity);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO), holder_id_front_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO), holder_id_back_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS), holder_id_front_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS), holder_id_back_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_STATUS), holder_id_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TAX_PAYER_ID), tax_payer_id);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.NUMBER), number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.VERIFY_STATUS), verify_status);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.OPENING_NUMBER), opening_number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.CELLPHONE), cellphone);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.BANK_CARD_IMAGE), bank_card_image);
    }

    @Test
    public void testGetMerchantBankAccountPre() {
        String merchantId = UUID.randomUUID().toString();
        String number = getNumber();
        Map<String, Object> param = Maps.newHashMap();
        Integer type = 2;
        String holder = "holder";
        Integer idType = 4;
        String identity = "332203198205220031";
        String holder_id_front_photo = "front";
        String holder_id_back_photo = "back";
        Integer holder_id_front_ocr_status = 1;
        Integer holder_id_back_ocr_status = 1;
        Integer holder_id_status = 1;
        String tax_payer_id = "xxx";
        String verify_status = "1";
        String opening_number = "xxx";
        String cellphone = "kkk";
        String bank_card_image = "image";
        param.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
        param.put(MerchantBankAccountPre.TYPE, type);
        param.put(MerchantBankAccountPre.HOLDER, holder);
        param.put(MerchantBankAccountPre.ID_TYPE, idType);
        param.put(MerchantBankAccountPre.IDENTITY, identity);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, holder_id_front_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, holder_id_back_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS, holder_id_front_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS, holder_id_back_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_STATUS, holder_id_status);
        param.put(MerchantBankAccountPre.TAX_PAYER_ID, tax_payer_id);
        param.put(MerchantBankAccountPre.NUMBER, number);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, verify_status);
        param.put(MerchantBankAccountPre.OPENING_NUMBER, opening_number);
        param.put(MerchantBankAccountPre.CELLPHONE, cellphone);
        param.put(MerchantBankAccountPre.BANK_CARD_IMAGE, bank_card_image);
        Map result = merchantBankService.saveMerchantBankAccountPre(param);
        String id = BeanUtil.getPropString(result, DaoConstants.ID);
        result = merchantBankService.getMerchantBankAccountPre(id);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.MERCHANT_ID), merchantId);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TYPE), type + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER), holder);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.ID_TYPE), idType + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.IDENTITY), identity);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO), holder_id_front_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO), holder_id_back_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS), holder_id_front_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS), holder_id_back_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_STATUS), holder_id_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TAX_PAYER_ID), tax_payer_id);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.NUMBER), number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.VERIFY_STATUS), verify_status);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.OPENING_NUMBER), opening_number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.CELLPHONE), cellphone);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.BANK_CARD_IMAGE), bank_card_image);
    }


    @Test
    public void testGetMerchantBankAccountPreByMerchantIdAndNumber() {
        String merchantId = UUID.randomUUID().toString();
        String number = getNumber();
        Map<String, Object> param = Maps.newHashMap();
        Integer type = 2;
        String holder = "holder";
        Integer idType = 4;
        String identity = "332203198205220031";
        String holder_id_front_photo = "front";
        String holder_id_back_photo = "back";
        Integer holder_id_front_ocr_status = 1;
        Integer holder_id_back_ocr_status = 1;
        Integer holder_id_status = 1;
        String tax_payer_id = "xxx";
        String verify_status = "1";
        String opening_number = "xxx";
        String cellphone = "kkk";
        String bank_card_image = "image";
        param.put(MerchantBankAccountPre.MERCHANT_ID, merchantId);
        param.put(MerchantBankAccountPre.TYPE, type);
        param.put(MerchantBankAccountPre.HOLDER, holder);
        param.put(MerchantBankAccountPre.ID_TYPE, idType);
        param.put(MerchantBankAccountPre.IDENTITY, identity);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO, holder_id_front_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO, holder_id_back_photo);
        param.put(MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS, holder_id_front_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS, holder_id_back_ocr_status);
        param.put(MerchantBankAccountPre.HOLDER_ID_STATUS, holder_id_status);
        param.put(MerchantBankAccountPre.TAX_PAYER_ID, tax_payer_id);
        param.put(MerchantBankAccountPre.NUMBER, number);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, verify_status);
        param.put(MerchantBankAccountPre.OPENING_NUMBER, opening_number);
        param.put(MerchantBankAccountPre.CELLPHONE, cellphone);
        param.put(MerchantBankAccountPre.BANK_CARD_IMAGE, bank_card_image);
        merchantBankService.saveMerchantBankAccountPre(param);
        Map result = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.MERCHANT_ID), merchantId);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TYPE), type + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER), holder);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.ID_TYPE), idType + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.IDENTITY), identity);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_PHOTO), holder_id_front_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_PHOTO), holder_id_back_photo);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_FRONT_OCR_STATUS), holder_id_front_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_BACK_OCR_STATUS), holder_id_back_ocr_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.HOLDER_ID_STATUS), holder_id_status + "");
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.TAX_PAYER_ID), tax_payer_id);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.NUMBER), number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.VERIFY_STATUS), verify_status);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.OPENING_NUMBER), opening_number);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.CELLPHONE), cellphone);
        Assert.assertEquals("查询结果不一致", BeanUtil.getPropString(result, MerchantBankAccountPre.BANK_CARD_IMAGE), bank_card_image);
    }


    @Test
    public void testFindMerchantBankAccountPres() {
        String merchantId = UUID.randomUUID().toString();
        String number1 = getNumber();
        String number2 = getNumber();
        String number3 = getNumber();
        savePre(merchantId, number1);
        savePre(merchantId, number2);
        savePre(merchantId, number3);
        ListResult result = merchantBankService.findMerchantBankAccountPres(null, ImmutableMap.of(MerchantBankAccountPre.MERCHANT_ID, merchantId));
        Assert.assertEquals(3, result.getRecords().size());
        result = merchantBankService.findMerchantBankAccountPres(null,
                ImmutableMap.of(MerchantBankAccountPre.MERCHANT_ID, merchantId,
                        MerchantBankAccountPre.NUMBER, number1
                ));
        Assert.assertEquals(1, result.getRecords().size());
    }


    @Test(expected = CoreMerchantNotBindBankAccountException.class)
    public void testUpdateMerchantBankAccountForNotExist() {
        merchantBankService.updateMerchantBankAccount(
                ImmutableMap.of(MerchantBankAccount.MERCHANT_ID, UUID.randomUUID().toString(),
                        MerchantBankAccount.CELLPHONE, "123"));
    }

    //    INSERT INTO `merchant_bank_account` (`id`, `merchant_id`, `type`, `holder`, `id_type`, `identity`, `holder_id_front_photo`, `holder_id_back_photo`, `holder_id_front_ocr_status`, `holder_id_back_ocr_status`, `holder_id_status`, `tax_payer_id`, `number`, `verify_status`, `bank_name`, `branch_name`, `card_validity`, `extend`, `clearing_number`, `opening_number`, `city`, `cellphone`, `extra`, `change_time`, `ctime`, `mtime`, `deleted`, `version`, `bank_card_image`, `transfer_voucher`, `id_validity`, `letter_of_authorization`, `hand_letter_of_authorization`, `bank_card_status`)
//    VALUES
//            ('000047a8-7c9b-4e79-a7e6-ed452dd1e433', '1631cbe35f25-4569-bd04-7d74-92dfd522', 1, '齐天大圣', 1, '1:z9S6aPcuEP0d5hAYMRLY00ON5Y6mxZ5L5jL3CA0JDMw=', '1:Dq4qLQfPP1HG0AoqV4vlcr85GVUmKVcvNRp/ay5hZ9Jr3MX8Hbdz/rephb22Ymf07+Wvce7FMKAgAR5ZG94zQwjErP42fo9yOzKtDhkzyj4=', '1:Dq4qLQfPP1HG0AoqV4vlcr85GVUmKVcvNRp/ay5hZ9Jr3MX8Hbdz/rephb22Ymf0rPk1zzeNPafmhmnK+tz3iAjErP42fo9yOzKtDhkzyj4=', 1, 1, 1, '', '1:xX3b2Lmx50j1DMb0Su4BeRT/q4lnXxwnUqeSNRMCmx4=', 2, '中国建设银行', '中国建设银行长沙马王堆支行', '', X'7B226269727468646179223A223139383030383130222C22736578223A2231222C226C6F63616C223A22343332353232227D', '', '', '湖南省 长沙市 雨花区', '', X'7B22636F6E74726163744D656D6F223A22227D', 0, *************, *************, 0, 1, '', '', '', '', NULL, 0);
    //需要执行上面这条sql
    @Test
    public void testUpdateMerchantBankAccount() {
        String merchantId = "1631cbe35f25-4569-bd04-7d74-92dfd522";
        String cellphone = UUID.randomUUID().toString().substring(0, 10);
        Map<String, Object> param = Maps.newHashMap();
        param.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        param.put(MerchantBankAccount.CELLPHONE, cellphone);
        merchantBankService.updateMerchantBankAccount(param);
        Map bankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        Assert.assertEquals("cellphone fail ", cellphone, MapUtils.getString(bankAccount, MerchantBankAccount.CELLPHONE));
    }


    @Test(expected = CoreMerchantNotBindBankAccountException.class)
    public void testDeletedMerchantBankAccountPreNotExist() {
        merchantBankService.deletedMerchantBankAccountPre(UUID.randomUUID().toString());
    }


    /**
     * 删除非默认卡
     */
    @Test
    public void testDeletedMerchantBankAccountPre() {
        String merchantId = UUID.randomUUID().toString();
        String number1 = getNumber();
        String id = savePre(merchantId, number1);
        Map<String, Object> param = Maps.newHashMap();
        param.put(DaoConstants.ID, id);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS_NOT);
        merchantBankService.updateMerchantBankAccountPre(param);
        merchantBankService.deletedMerchantBankAccountPre(id);
        Map result = merchantBankService.getMerchantBankAccountPreByMerchantIdAndNumber(merchantId, number1);
        Assert.assertNull(result);
    }

    /**
     * 删除非默认卡(多次)
     */
    @Test
    public void testDeletedMerchantBankAccountPreTwice() {
        String merchantId = UUID.randomUUID().toString();
        String number1 = getNumber();
        String id = savePre(merchantId, number1);
        Map<String, Object> param = Maps.newHashMap();
        param.put(DaoConstants.ID, id);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS_NOT);
        merchantBankService.updateMerchantBankAccountPre(param);
        merchantBankService.deletedMerchantBankAccountPre(id);
        merchantBankService.deletedMerchantBankAccountPre(id);
    }


    /**
     * 删除默认卡，抛错
     */
    @Test(expected = CoreMerchantNotBindBankAccountException.class)
    public void testDeleteMerchantBankAccountPre() {
        String merchantId = UUID.randomUUID().toString();
        String number1 = getNumber();
        String id = savePre(merchantId, number1);

        Map<String, Object> param = Maps.newHashMap();
        param.put(DaoConstants.ID, id);
        param.put(MerchantBankAccountPre.DEFAULT_STATUS, MerchantBankAccountPre.DEFAULT_STATUS_TRUE);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS_SUCC);
        merchantBankService.updateMerchantBankAccountPre(param);
        merchantBankService.deletedMerchantBankAccountPre(id);
    }


    /**
     * 删除验证中的卡，报错
     */
    @Test(expected = CoreMerchantNotBindBankAccountException.class)
    public void testDeleteMerchantBankAccountPreInvalidStatus2() {
        String merchantId = UUID.randomUUID().toString();
        String number1 = getNumber();
        String id = savePre(merchantId, number1);

        Map<String, Object> param = Maps.newHashMap();
        param.put(DaoConstants.ID, id);
        param.put(MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS_INPROGRESS);
        merchantBankService.updateMerchantBankAccountPre(param);
        merchantBankService.deletedMerchantBankAccountPre(id);

    }
}
