package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.base.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class MerchantBusinessLicenseServiceImplTest extends BaseTest {

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Test
    public void testGetBusinessLicenseByLegalPersonIdNumberOrNumber() {
        ListResult legal_person_id_number = merchantBusinessLicenseService.getBusinessLicenseByLegalPersonIdNumberOrNumber(new PageInfo(1,5,null, null, Collections.singletonList(new OrderBy("mtime", OrderBy.OrderType.DESC))), CollectionUtil.hashMap(
                "legal_person_id_number", "132626197509273554"
        ));
        List<Map> records = legal_person_id_number.getRecords();
        System.out.println(JSON.toJSONString(records));
    }
}