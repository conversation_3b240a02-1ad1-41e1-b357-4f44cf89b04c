package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.constant.BusinessLogConstant;
import com.wosai.upay.core.exception.CoreInvalidParameterException;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.base.AbstractNewMerchantTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * MerchantServiceTest
 *
 * <AUTHOR>
 * @date 2019-08-28 10:04
 * <p>
 * <p>
 */
@Slf4j
public class MerchantServiceTest extends AbstractNewMerchantTest {

    @Autowired
    MerchantService merchantService;

    @Autowired
    StoreService storeService;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    TradeConfigService tradeConfigService;

    private String getNumber() {
        // cardbin是中国农业银行借记卡,19位
        try {
            Thread.sleep(1);
        } catch (Exception ignored) {
        }
        return "622848" + System.currentTimeMillis();
    }


    @Test
    public void testMerchantCases() {
        Map merchant = createAndQueryMerchant();
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        String merchantSn = MapUtils.getString(merchant, Merchant.SN);
        //findMerchant
        testFindMerchant(merchant);

        testDisableMerchant(merchantId);
        testEnableMerchant(merchantId);
        testCloseMerchant(merchantId);
        testUpdateMerchantWithdrawModeById(merchantId, 3);
        testUpdateMerchant(merchantId);

        //根据id或者sn查询merchant
        Map merchantById = merchantService.getMerchantByMerchantId(merchantId);
        Map merchantById2 = merchantService.getMerchant(merchantId);
        Map merchantBySn = merchantService.getMerchantByMerchantSn(merchantSn);
        Map merchantBySn2 = merchantService.getMerchantBySn(merchantSn);
        Assert.assertEquals("merchantById,merchantById2查询不一致", JSON.toJSONString(merchantById), JSON.toJSONString(merchantById2));
        Assert.assertEquals("merchantById2,merchantBySn", JSON.toJSONString(merchantById2), JSON.toJSONString(merchantBySn));
        Assert.assertEquals("merchantBySn,merchantBySn2", JSON.toJSONString(merchantBySn), JSON.toJSONString(merchantBySn2));
        merchantId = null;
        merchantSn = null;
        merchantById = merchantService.getMerchantByMerchantId(merchantId);
        merchantById2 = merchantService.getMerchant(merchantId);
        merchantBySn = merchantService.getMerchantByMerchantSn(merchantSn);
        merchantBySn2 = merchantService.getMerchantBySn(merchantSn);
        Assert.assertNull(merchantById);
        Assert.assertNull(merchantById2);
        Assert.assertNull(merchantBySn);
        Assert.assertNull(merchantBySn2);
        merchantId = "";
        merchantSn = "";
        merchantById = merchantService.getMerchantByMerchantId(merchantId);
        merchantById2 = merchantService.getMerchant(merchantId);
        merchantBySn = merchantService.getMerchantByMerchantSn(merchantSn);
        merchantBySn2 = merchantService.getMerchantBySn(merchantSn);
        Assert.assertNull(merchantById);
        Assert.assertNull(merchantById2);
        Assert.assertNull(merchantBySn);
        Assert.assertNull(merchantBySn2);
    }

    private void testFindMerchant(Map merchant) {
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        String merchantSn = MapUtils.getString(merchant, Merchant.SN);
        long ctime = BeanUtil.getPropLong(merchant, DaoConstants.CTIME);

        PageInfo pageInfo = new PageInfo(1, 5);
        pageInfo.setDateStart(ctime);
        pageInfo.setDateEnd(ctime + 1);
        Map queryFilter = new HashMap();
        queryFilter.put("merchant_sn", merchantSn);
        queryFilter.put("merchant_sns", Lists.newArrayList(merchantSn));
        queryFilter.put("merchant_id", merchantId);
        queryFilter.put("merchant_ids", Lists.newArrayList(merchantId));
//        queryFilter.put("merchant_name", MapUtils.getString(merchant, Merchant.NAME));
        queryFilter.put(Merchant.VENDOR_ID, MapUtils.getString(merchant, Merchant.VENDOR_ID));
//        queryFilter.put("merchant_alias", MapUtils.getString(merchant, Merchant.ALIAS));
        queryFilter.put(Merchant.CONTACT_PHONE, MapUtils.getString(merchant, Merchant.CONTACT_PHONE));
        queryFilter.put(Merchant.CONTACT_CELLPHONE, MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE));
        queryFilter.put(Merchant.OWNER_CELLPHONE, MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        queryFilter.put(Merchant.SOLICITOR_ID, MapUtils.getString(merchant, Merchant.SOLICITOR_ID));
        queryFilter.put("cellphone", MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        queryFilter.put("solicitor_name", "测试你好啊1");
        queryFilter.put(Merchant.STATUS, MapUtils.getIntValue(merchant, Merchant.STATUS));

        ListResult listResult = merchantService.findMerchants(pageInfo, queryFilter);
        ListResult slavelistResult = merchantService.findMerchantsFromSlaveDb(pageInfo, queryFilter);
        Assert.assertEquals("findMerchantsFromSlaveDb diff from findMerchants", JSON.toJSONString(listResult), JSON.toJSONString(slavelistResult));
        Assert.assertNotNull("商户列表不能为空", listResult);
        Assert.assertEquals("testFindMerchant error", merchantId, MapUtils.getString(listResult.getRecords().get(0), DaoConstants.ID));
        queryFilter = new HashMap();
        queryFilter.put("merchant_id", merchantId);
        queryFilter.put(Merchant.NAME, "");
        queryFilter.put(Merchant.VENDOR_ID, "");
        queryFilter.put("merchant_alias", "");
        queryFilter.put(Merchant.CONTACT_PHONE, "");
        queryFilter.put(Merchant.CONTACT_CELLPHONE, "");
        queryFilter.put(Merchant.OWNER_CELLPHONE, "");
        queryFilter.put(Merchant.SOLICITOR_ID, "");
        queryFilter.put("cellphone", "");
        queryFilter.put("solicitor_name", "测试你好啊1");
        listResult = merchantService.findMerchants(pageInfo, queryFilter);
        Assert.assertNotNull("商户列表不能为空", listResult);
        Assert.assertEquals("testFindMerchant error", merchantId, MapUtils.getString(listResult.getRecords().get(0), DaoConstants.ID));
    }


    private void testEnableMerchant(String merchantId) {
        log.info("start testEnableMerchant");
        merchantService.enableMerchant(merchantId);
        Map merchantInfo = merchantService.getMerchant(merchantId);
        Assert.assertEquals("启用商户失败", new Integer(Merchant.STATUS_ENABLED), MapUtils.getInteger(merchantInfo, Merchant.STATUS));
        log.info("fin testEnableMerchant");
    }

    private void testDisableMerchant(String merchantId) {
        log.info("start testDisableMerchant");
        merchantService.disableMerchant(merchantId);
        Map merchantInfo = merchantService.getMerchant(merchantId);
        Assert.assertEquals("禁用商户失败", new Integer(Merchant.STATUS_DISABLED), MapUtils.getInteger(merchantInfo, Merchant.STATUS));
        log.info("fin testDisableMerchant");
    }

    private void testCloseMerchant(String merchantId) {
        log.info("start testCloseMerchant");
        merchantService.closeMerchant(merchantId);
        Map merchantInfo = merchantService.getMerchant(merchantId);
        Assert.assertEquals("关闭商户失败", new Integer(Merchant.STATUS_CLOSED), MapUtils.getInteger(merchantInfo, Merchant.STATUS));
        log.info("fin testCloseMerchant");
    }

    private void testUpdateMerchantWithdrawModeById(String merchantId, int mode) {
        Map<String, Object> param = ImmutableMap.of(DaoConstants.ID, merchantId, Merchant.WITHDRAW_MODE, mode);
        Map<String, Object> param2 = ImmutableMap.of(BusinessLogConstant.PLATFORM, "osp", BusinessLogConstant.OPERATOR, "单元测试");
        merchantService.updateMerchantWithdrawModeById(param, param2);
        Map merchantInfo = merchantService.getMerchant(merchantId);
        Assert.assertTrue("商户提现模式不正确", mode == MapUtils.getInteger(merchantInfo, Merchant.WITHDRAW_MODE));
        //再次更新提现模式，结果一样（代码有判断return）
        merchantService.updateMerchantWithdrawModeById(param, param2);
        Assert.assertTrue("商户提现模式不正确", mode == MapUtils.getInteger(merchantInfo, Merchant.WITHDRAW_MODE));
    }

    private void testUpdateMerchant(String merchantId) {
        Map merchantInfo = merchantService.getMerchant(merchantId);
        String merchantSn = MapUtils.getString(merchantInfo, Merchant.SN);

        //根据id更新
        Assert.assertNotNull(
                "被更新的商户不存在", merchantInfo);
        int randomNumber = new Random().nextInt(9000) + 1000;
        String merchantName = "两元店" + randomNumber;
        String longitude = "121.32" + randomNumber;
        String latitude = "31.23" + randomNumber;
        String alias = "商户ALIAS" + randomNumber;
        String businessName = "行业分类为餐饮" + randomNumber;
        String province = "江苏省";
        String city = "苏州市";
        String district = "姑苏区";
        String streetAddress = "大渡河路S90" + randomNumber;
        String contactName = "联系人姓名SSS" + randomNumber;
        String contactPhone = "010-0000" + randomNumber;
        String contactCellphone = "188888" + randomNumber;
        String contactEmail = randomNumber + "<EMAIL>";
        String legalPersonIdCardFrontPhoto = "法人身份证正面照" + randomNumber;
        String legalPersonIdCardBackPhoto = "法人身份证反面照" + randomNumber;
        String businessLicensePhoto = "营业执照照片" + randomNumber;
        String business = "经营内容小吃" + randomNumber;
        String ownerName = "所有人姓名" + randomNumber;
        String ownerCellphone = "18888" + randomNumber;
        String customerPhone = "166666" + randomNumber;
        String logo = "门店码LOGO" + randomNumber;
        String clientSn = "商户外部商户号" + randomNumber;
        Map merchant = new HashMap(20);
        merchant.put(DaoConstants.ID, merchantId);
        merchant.put(Merchant.NAME, merchantName);
        merchant.put(Merchant.LONGITUDE, longitude);
        merchant.put(Merchant.LATITUDE, latitude);
        merchant.put(Merchant.ALIAS, alias);
        merchant.put(Merchant.BUSINESS_NAME, businessName);
        merchant.put(Merchant.PROVINCE, province);
        merchant.put(Merchant.CITY, city);
        merchant.put(Merchant.DISTRICT, district);
        merchant.put(Merchant.STREET_ADDRESS, streetAddress);
        merchant.put(Merchant.CONTACT_NAME, contactName);
        merchant.put(Merchant.CONTACT_PHONE, contactPhone);
        merchant.put(Merchant.CONTACT_CELLPHONE, contactCellphone);
        merchant.put(Merchant.CONTACT_EMAIL, contactEmail);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, legalPersonIdCardFrontPhoto);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO, legalPersonIdCardBackPhoto);
        merchant.put(Merchant.BUSINESS_LICENSE_PHOTO, businessLicensePhoto);
        merchant.put(Merchant.BUSINESS, business);
        merchant.put(Merchant.OWNER_NAME, ownerName);
        merchant.put(Merchant.OWNER_CELLPHONE, ownerCellphone);
        merchant.put(Merchant.CUSTOMER_PHONE, customerPhone);
        merchant.put(Merchant.LOGO, logo);
        merchant.put(Merchant.CLIENT_SN, clientSn);
        Map result = merchantService.updateMerchant(merchant);
        Assert.assertNotNull("更新商户返回值不能为 null", result);
        Assert.assertEquals("更新商户后的数据与预期数据不一致", merchantName, MapUtils.getString(merchant, Merchant.NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", longitude, MapUtils.getString(merchant, Merchant.LONGITUDE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", latitude, MapUtils.getString(merchant, Merchant.LATITUDE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", alias, MapUtils.getString(merchant, Merchant.ALIAS));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", businessName, MapUtils.getString(merchant, Merchant.BUSINESS_NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", province, MapUtils.getString(merchant, Merchant.PROVINCE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", city, MapUtils.getString(merchant, Merchant.CITY));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", district, MapUtils.getString(merchant, Merchant.DISTRICT));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", streetAddress, MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactName, MapUtils.getString(merchant, Merchant.CONTACT_NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactPhone, MapUtils.getString(merchant, Merchant.CONTACT_PHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactCellphone, MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactEmail, MapUtils.getString(merchant, Merchant.CONTACT_EMAIL));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", legalPersonIdCardFrontPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", legalPersonIdCardBackPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", businessLicensePhoto, MapUtils.getString(merchant, Merchant.BUSINESS_LICENSE_PHOTO));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", business, MapUtils.getString(merchant, Merchant.BUSINESS));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", ownerName, MapUtils.getString(merchant, Merchant.OWNER_NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", ownerCellphone, MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", customerPhone, MapUtils.getString(merchant, Merchant.CUSTOMER_PHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", clientSn, MapUtils.getString(merchant, Merchant.CLIENT_SN));
        //根据sn更新
        Assert.assertNotNull(
                "被更新的商户不存在", merchantInfo);
        randomNumber = new Random().nextInt(9000) + 1000;
        merchantName = "两元店" + randomNumber;
        longitude = "121.32" + randomNumber;
        latitude = "31.23" + randomNumber;
        alias = "商户ALIAS" + randomNumber;
        businessName = "行业分类为餐饮" + randomNumber;
        province = "江苏省" + randomNumber;
        city = "苏州市" + randomNumber;
        district = "姑苏区" + randomNumber;
        streetAddress = "大渡河路S90" + randomNumber;
        contactName = "联系人姓名SSS" + randomNumber;
        contactPhone = "010-0000" + randomNumber;
        contactCellphone = "188888" + randomNumber;
        contactEmail = randomNumber + "<EMAIL>";
        legalPersonIdCardFrontPhoto = "法人身份证正面照" + randomNumber;
        legalPersonIdCardBackPhoto = "法人身份证反面照" + randomNumber;
        businessLicensePhoto = "营业执照照片" + randomNumber;
        business = "经营内容小吃" + randomNumber;
        ownerName = "所有人姓名" + randomNumber;
        ownerCellphone = "18888" + randomNumber;
        customerPhone = "166666" + randomNumber;
        logo = "门店码LOGO" + randomNumber;
        clientSn = "商户外部商户号" + randomNumber;
        merchant = new HashMap(20);

        merchant.put(Merchant.SN, merchantSn);
        merchant.put(Merchant.NAME, merchantName);
        merchant.put(Merchant.LONGITUDE, longitude);
        merchant.put(Merchant.LATITUDE, latitude);
        merchant.put(Merchant.ALIAS, alias);
        merchant.put(Merchant.BUSINESS_NAME, businessName);
        merchant.put(Merchant.PROVINCE, province);
        merchant.put(Merchant.CITY, city);
        merchant.put(Merchant.DISTRICT, district);
        merchant.put(Merchant.STREET_ADDRESS, streetAddress);
        merchant.put(Merchant.CONTACT_NAME, contactName);
        merchant.put(Merchant.CONTACT_PHONE, contactPhone);
        merchant.put(Merchant.CONTACT_CELLPHONE, contactCellphone);
        merchant.put(Merchant.CONTACT_EMAIL, contactEmail);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, legalPersonIdCardFrontPhoto);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO, legalPersonIdCardBackPhoto);
        merchant.put(Merchant.BUSINESS_LICENSE_PHOTO, businessLicensePhoto);
        merchant.put(Merchant.BUSINESS, business);
        merchant.put(Merchant.OWNER_NAME, ownerName);
        merchant.put(Merchant.OWNER_CELLPHONE, ownerCellphone);
        merchant.put(Merchant.CUSTOMER_PHONE, customerPhone);
        merchant.put(Merchant.LOGO, logo);
        merchant.put(Merchant.CLIENT_SN, clientSn);
        result = merchantService.updateMerchant(merchant);
        Assert.assertNotNull("更新商户返回值不能为 null", result);
        Assert.assertEquals("更新商户后的数据与预期数据不一致", merchantName, MapUtils.getString(merchant, Merchant.NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", longitude, MapUtils.getString(merchant, Merchant.LONGITUDE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", latitude, MapUtils.getString(merchant, Merchant.LATITUDE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", alias, MapUtils.getString(merchant, Merchant.ALIAS));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", businessName, MapUtils.getString(merchant, Merchant.BUSINESS_NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", province, MapUtils.getString(merchant, Merchant.PROVINCE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", city, MapUtils.getString(merchant, Merchant.CITY));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", district, MapUtils.getString(merchant, Merchant.DISTRICT));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", streetAddress, MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactName, MapUtils.getString(merchant, Merchant.CONTACT_NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactPhone, MapUtils.getString(merchant, Merchant.CONTACT_PHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactCellphone, MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", contactEmail, MapUtils.getString(merchant, Merchant.CONTACT_EMAIL));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", legalPersonIdCardFrontPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", legalPersonIdCardBackPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", businessLicensePhoto, MapUtils.getString(merchant, Merchant.BUSINESS_LICENSE_PHOTO));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", business, MapUtils.getString(merchant, Merchant.BUSINESS));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", ownerName, MapUtils.getString(merchant, Merchant.OWNER_NAME));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", ownerCellphone, MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", customerPhone, MapUtils.getString(merchant, Merchant.CUSTOMER_PHONE));
        Assert.assertEquals("更新商户后的数据与预期数据不一致", clientSn, MapUtils.getString(merchant, Merchant.CLIENT_SN));

        String exMsg = "";
        try {
            Map m = new HashMap<>();
            merchantService.updateMerchant(m);
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("更新商户id,sn同时为空异常", "id、sn不能同时为空", exMsg);
    }


    @Test
    public void testCreateMerchantComplete() {
        String merchantName = "两元店商户";
        String longitude = "121.323649";
        String latitude = "31.239252";
        String alias = "商户ALIAS";
        String businessName = "行业分类为餐饮";
        String province = "江苏省";
        String city = "苏州市";
        String district = "姑苏区";
        String streetAddress = "大渡河路S90";
        String contactName = "联系人姓名SSS";
        String contactPhone = "010-00001111";
        String contactCellphone = "1888888";
        String contactEmail = "<EMAIL>";
        String legalPersonIdCardFrontPhoto = "法人身份证正面照";
        String legalPersonIdCardBackPhoto = "法人身份证反面照";
        String businessLicensePhoto = "营业执照照片";
        String business = "经营内容小吃";
        String ownerName = "所有人姓名为xxx";
        String ownerCellphone = "188888AAAA";
        String customerPhone = "166666AAAA";
        String logo = "门店码LOGO";
        String clientSn = "商户外部商户号";
        String vendorId = "00ae96e3-9354-4430-acee-aad4ec82ba59";
        String solicitorId = "c97fcf9b-b300-11e5-9987-6c92bf21bb99";
        String industry = "45570e40-d8c4-4ca1-a0ad-7a2a635a8e06";
        Map merchant = new HashMap();
        merchant.put(Merchant.NAME, merchantName);
        merchant.put(Merchant.ALIAS, alias);
        merchant.put(Merchant.LONGITUDE, longitude);
        merchant.put(Merchant.LATITUDE, latitude);
        merchant.put(Merchant.BUSINESS_NAME, businessName);
        merchant.put(Merchant.PROVINCE, province);
        merchant.put(Merchant.CITY, city);
        merchant.put(Merchant.DISTRICT, district);
        merchant.put(Merchant.STREET_ADDRESS, streetAddress);
        merchant.put(Merchant.CONTACT_NAME, contactName);
        merchant.put(Merchant.CONTACT_PHONE, contactPhone);
        merchant.put(Merchant.CONTACT_CELLPHONE, contactCellphone);
        merchant.put(Merchant.CONTACT_EMAIL, contactEmail);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, legalPersonIdCardFrontPhoto);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO, legalPersonIdCardBackPhoto);
        merchant.put(Merchant.BUSINESS_LICENSE_PHOTO, businessLicensePhoto);
        merchant.put(Merchant.BUSINESS, business);
        merchant.put(Merchant.OWNER_NAME, ownerName);
        merchant.put(Merchant.OWNER_CELLPHONE, ownerCellphone);
        merchant.put(Merchant.CUSTOMER_PHONE, customerPhone);
        merchant.put(Merchant.INDUSTRY, industry);
        merchant.put(Merchant.LOGO, logo);
        merchant.put(Merchant.CLIENT_SN, clientSn);
        merchant.put(Merchant.VENDOR_ID, vendorId);
        merchant.put(Merchant.SOLICITOR_ID, solicitorId);
        merchant.put("submitter", "d73b37e5-ec9c-44fd-8b9f-a099048e9529");


        Map store = new HashMap();
        String storeName = "广州大拇指信息科技有限公司";
        String storeContactName = "贺深书";
        String storeCellPhone = "***********";
        String storeProvince = "广东省";
        String storeCity = "广东省";
        String storeDistrict = "广东省";
        String storeStreetAddress = "番禺区恒然创意园D座1梯";
        String storeLongitude = "113.386259";
        String storeLatitude = "22.975204";
        String storeSolicitorLd = "c97fcf9b-b300-11e5-9987-6c92bf21bb99";
        store.put(Store.NAME, storeName);
        store.put(Store.CONTACT_NAME, storeContactName);
        store.put(Store.CONTACT_CELLPHONE, storeCellPhone);
        store.put(Store.PROVINCE, storeProvince);
        store.put(Store.CITY, storeCity);
        store.put(Store.DISTRICT, storeDistrict);
        store.put(Store.STREET_ADDRESS, storeStreetAddress);
        store.put(Store.LONGITUDE, storeLongitude);
        store.put(Store.LATITUDE, storeLatitude);
        store.put(Store.SOLICITOR_ID, storeSolicitorLd);
        merchant.put("store", store);

        String holder = "账户持有人名称";
        String number = getNumber();
        String identity = "300000119901010001";
        String taxPayerId = "taxId11111";
        String bankName = "中国农业银行";
        String branchName = "中国农业银行苏州干将路支行";
        String bankCity = "上海";
        String cellphone = "10086-0000";
        Map merchantBankAccount = new HashMap();
        merchantBankAccount.put(MerchantBankAccount.HOLDER, holder);
        merchantBankAccount.put(MerchantBankAccount.NUMBER, number);
        merchantBankAccount.put(MerchantBankAccount.IDENTITY, identity);
        merchantBankAccount.put(MerchantBankAccount.TYPE, 1);
        merchantBankAccount.put(MerchantBankAccount.ID_TYPE, 3);
        merchantBankAccount.put(MerchantBankAccount.TAX_PAYER_ID, taxPayerId);
        merchantBankAccount.put(MerchantBankAccount.VERIFY_STATUS, 1);
        merchantBankAccount.put(MerchantBankAccount.BANK_NAME, bankName);
        merchantBankAccount.put(MerchantBankAccount.BRANCH_NAME, branchName);
        merchantBankAccount.put(MerchantBankAccount.CITY, bankCity);
        merchantBankAccount.put(MerchantBankAccount.CELLPHONE, cellphone);
        merchant.put("merchant_bank_account", merchantBankAccount);


        Map merchantAudit = new HashMap();
        Map m1 = new HashMap();
        m1.put("photo", "http://images.wosaimg.com/b6/fd5318d95526dc231fea8304d63949.png");
        Map m2 = new HashMap();
        m2.put("photo", "http://images.wosaimg.com/b6/fd5318d95526dc231fea8304d63949.png");
        Map m3 = new HashMap();
        m3.put("photo", "http://images.wosaimg.com/b6/fd5318d95526dc231fea8304d63949.png");
        merchantAudit.put("brand", m1);
        merchantAudit.put("indoor_material", m2);
        merchantAudit.put("outdoor_material", m3);
        merchant.put("merchant_audit", merchantAudit);


        Map p2Map = new HashMap();
        String feeRate = "0.38";
        Integer status = 0;
        p2Map.put("payway", 2);
        p2Map.put("b2c_status", status);
        p2Map.put("b2c_fee_rate", feeRate);
        p2Map.put("c2b_status", status);
        p2Map.put("c2b_fee_rate", feeRate);
        p2Map.put("wap_status", status);
        p2Map.put("wap_fee_rate", feeRate);
        merchant.put("merchant_config", Lists.newArrayList(p2Map));
        merchant.put("account_id", UUID.randomUUID().toString());
        Map response = merchantService.createMerchantComplete(merchant);
        String merchantId = BeanUtil.getPropString(response, DaoConstants.ID);
        merchant = merchantService.getMerchantByMerchantId(merchantId);
        Assert.assertEquals("获取商户数据与预期数据不一致", merchantName, MapUtils.getString(merchant, Merchant.NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", longitude, MapUtils.getString(merchant, Merchant.LONGITUDE));
        Assert.assertEquals("获取商户数据与预期数据不一致", latitude, MapUtils.getString(merchant, Merchant.LATITUDE));
        Assert.assertEquals("获取商户数据与预期数据不一致", alias, MapUtils.getString(merchant, Merchant.ALIAS));
        Assert.assertEquals("获取商户数据与预期数据不一致", businessName, MapUtils.getString(merchant, Merchant.BUSINESS_NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", province, MapUtils.getString(merchant, Merchant.PROVINCE));
        Assert.assertEquals("获取商户数据与预期数据不一致", city, MapUtils.getString(merchant, Merchant.CITY));
        Assert.assertEquals("获取商户数据与预期数据不一致", district, MapUtils.getString(merchant, Merchant.DISTRICT));
        Assert.assertEquals("获取商户数据与预期数据不一致", streetAddress, MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactName, MapUtils.getString(merchant, Merchant.CONTACT_NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactPhone, MapUtils.getString(merchant, Merchant.CONTACT_PHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactCellphone, MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactEmail, MapUtils.getString(merchant, Merchant.CONTACT_EMAIL));
        Assert.assertEquals("获取商户数据与预期数据不一致", legalPersonIdCardFrontPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
        Assert.assertEquals("获取商户数据与预期数据不一致", legalPersonIdCardBackPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        Assert.assertEquals("获取商户数据与预期数据不一致", businessLicensePhoto, MapUtils.getString(merchant, Merchant.BUSINESS_LICENSE_PHOTO));
        Assert.assertEquals("获取商户数据与预期数据不一致", business, MapUtils.getString(merchant, Merchant.BUSINESS));
        Assert.assertEquals("获取商户数据与预期数据不一致", ownerName, MapUtils.getString(merchant, Merchant.OWNER_NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", ownerCellphone, MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", customerPhone, MapUtils.getString(merchant, Merchant.CUSTOMER_PHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", clientSn, MapUtils.getString(merchant, Merchant.CLIENT_SN));
        Assert.assertEquals("获取商户数据与预期数据不一致", vendorId, MapUtils.getString(merchant, Merchant.VENDOR_ID));
        Assert.assertEquals("获取商户数据与预期数据不一致", solicitorId, MapUtils.getString(merchant, Merchant.SOLICITOR_ID));
        Assert.assertEquals("商户状态不正确", Merchant.STATUS_ENABLED, (int) MapUtils.getInteger(merchant, Merchant.STATUS));

        ListResult storeList = storeService.getStoreListByMerchantId(merchantId, null, null);
        Assert.assertTrue(storeList != null && storeList.getRecords() != null && storeList.getRecords().size() == 1);
        Map queryStore = storeList.getRecords().get(0);
        Assert.assertEquals("获取门店数据与预期数据不一致", storeName, MapUtils.getString(queryStore, Store.NAME));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeContactName, MapUtils.getString(queryStore, Store.CONTACT_NAME));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeCellPhone, MapUtils.getString(queryStore, Store.CONTACT_CELLPHONE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeProvince, MapUtils.getString(queryStore, Store.PROVINCE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeCity, MapUtils.getString(queryStore, Store.CITY));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeDistrict, MapUtils.getString(queryStore, Store.DISTRICT));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeStreetAddress, MapUtils.getString(queryStore, Store.STREET_ADDRESS));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeLongitude, MapUtils.getString(queryStore, Store.LONGITUDE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeLatitude, MapUtils.getString(queryStore, Store.LATITUDE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeSolicitorLd, MapUtils.getString(queryStore, Store.SOLICITOR_ID));
        Assert.assertEquals("获取门店数据与预期数据不一致", vendorId, MapUtils.getString(queryStore, Store.VENDOR_ID));
        Map config = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, 2);
        Assert.assertEquals("获取merchant-config与预期数据不一致", feeRate, MapUtils.getString(config, MerchantConfig.B2C_FEE_RATE));
        Assert.assertEquals("获取merchant-config与预期数据不一致", feeRate, MapUtils.getString(config, MerchantConfig.C2B_FEE_RATE));
        Assert.assertEquals("获取merchant-config与预期数据不一致", feeRate, MapUtils.getString(config, MerchantConfig.WAP_FEE_RATE));
        Assert.assertEquals("获取merchant-config与预期数据不一致", status, MapUtils.getInteger(config, MerchantConfig.WAP_STATUS));
        Assert.assertEquals("获取merchant-config与预期数据不一致", status, MapUtils.getInteger(config, MerchantConfig.B2C_STATUS));
        Assert.assertEquals("获取merchant-config与预期数据不一致", status, MapUtils.getInteger(config, MerchantConfig.C2B_STATUS));

        Map bankAccountInfo = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        Assert.assertNotNull("获取商户银行账户信息返回不能为空", bankAccountInfo);
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", merchantId, MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", holder, MapUtils.getString(bankAccountInfo, MerchantBankAccount.HOLDER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", number, MapUtils.getString(bankAccountInfo, MerchantBankAccount.NUMBER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", 1, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.TYPE));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", 3, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.ID_TYPE));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", 1, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.VERIFY_STATUS));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", identity, MapUtils.getString(bankAccountInfo, MerchantBankAccount.IDENTITY));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", taxPayerId, MapUtils.getString(bankAccountInfo, MerchantBankAccount.TAX_PAYER_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", bankName, MapUtils.getString(bankAccountInfo, MerchantBankAccount.BANK_NAME));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", branchName, MapUtils.getString(bankAccountInfo, MerchantBankAccount.BRANCH_NAME));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", cellphone, MapUtils.getString(bankAccountInfo, MerchantBankAccount.CELLPHONE));

    }

    @Test
    public void testGetMerchantDeveloperByMerchantId() {
        Map developer = merchantService.getMerchantDeveloperByMerchantId("213");
        Assert.assertNull("testGetMerchantDeveloperByMerchantId", developer);
    }


    @Test
    public void testGetMerchantListByCtimeOrMtimeOrWithdrawModeList() {
        Map merchant = createAndQueryMerchant();
        long mtime = BeanUtil.getPropLong(merchant, DaoConstants.MTIME);
        long ctime = BeanUtil.getPropLong(merchant, DaoConstants.CTIME);
        //getChangeMerchant
        List<Map<String, Object>> merchantList = merchantService.getChangeMerchant(mtime, mtime);
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        Assert.assertEquals("getChangeMerchant有误", 1, merchantList.size());
        String id = BeanUtil.getPropString(merchantList.get(0), DaoConstants.ID);
        Assert.assertEquals("getChangeMerchant有误", merchantId, id);
        //getMerchantRollListByCtime
        List<Map<String, Object>> rollList = merchantService.getMerchantRollListByCtime(ctime, ctime + 1, 10);
        Assert.assertEquals("getMerchantRollListByCtime有误", 1, rollList.size());
        id = BeanUtil.getPropString(rollList.get(0), DaoConstants.ID);
        Assert.assertEquals("getMerchantRollListByCtime有误", merchantId, id);
        String exMsg = null;
        try {
            merchantService.getMerchantRollListByCtime(null, null, 10);
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("getMerchantRollListByCtime单元测试异常", "时间不可为空", exMsg);
        exMsg = null;
        try {
            merchantService.getMerchantRollListByCtime(ctime, null, 10);
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("getMerchantRollListByCtime单元测试异常", "时间不可为空", exMsg);

        //getAutoWithdrawMerchantRollListByCtime
        testUpdateMerchantWithdrawModeById(merchantId, Merchant.WITHDRAW_MODE_AUTO);
        List<Map<String, Object>> autoWithdrawList = merchantService.getAutoWithdrawMerchantRollListByCtime(ctime, 10);
        Assert.assertEquals("getMerchantRollListByCtime有误", 1, autoWithdrawList.size());
        id = BeanUtil.getPropString(autoWithdrawList.get(0), DaoConstants.ID);
        Assert.assertEquals("getMerchantRollListByCtime有误", merchantId, id);
        exMsg = null;
        try {
            merchantService.getAutoWithdrawMerchantRollListByCtime(null, 10);
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("getAutoWithdrawMerchantRollListByCtime单元测试异常", "时间不可为空", exMsg);

    }

    @Test
    public void testBankAccountCases() {
        //1.创建商户
        Map merchant = createAndQueryMerchant();
        //2.为商户绑定银行卡
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        String holder = "账户持有人名称";
        String number = getNumber();
        String identity = "300000119901010001";
        String taxPayerId = "taxId11111";
        String bankName = "中国农业银行";
        String branchName = "中国农业银行苏州干将路支行";
        String city = "上海";
        String cellphone = "10086-0000";
        Map merchantBankAccount = new HashMap(10);
        merchantBankAccount.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        merchantBankAccount.put(MerchantBankAccount.HOLDER, holder);
        merchantBankAccount.put(MerchantBankAccount.NUMBER, number);
        merchantBankAccount.put(MerchantBankAccount.IDENTITY, identity);
        merchantBankAccount.put(MerchantBankAccount.TYPE, 1);
        merchantBankAccount.put(MerchantBankAccount.ID_TYPE, 3);
        merchantBankAccount.put(MerchantBankAccount.TAX_PAYER_ID, taxPayerId);
        merchantBankAccount.put(MerchantBankAccount.VERIFY_STATUS, 1);
        merchantBankAccount.put(MerchantBankAccount.BANK_NAME, bankName);
        merchantBankAccount.put(MerchantBankAccount.BRANCH_NAME, branchName);
        merchantBankAccount.put(MerchantBankAccount.CITY, city);
        merchantBankAccount.put(MerchantBankAccount.CELLPHONE, cellphone);
        Map bankAccountInfo = merchantService.bindMerchantBankAccount(merchantBankAccount);
        Assert.assertNotNull("商户绑定银行账户返回不能为空", bankAccountInfo);
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", merchantId, MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", holder, MapUtils.getString(bankAccountInfo, MerchantBankAccount.HOLDER));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", 1, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.TYPE));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", 3, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.ID_TYPE));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", 1, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.VERIFY_STATUS));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", number, MapUtils.getString(bankAccountInfo, MerchantBankAccount.NUMBER));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", identity, MapUtils.getString(bankAccountInfo, MerchantBankAccount.IDENTITY));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", taxPayerId, MapUtils.getString(bankAccountInfo, MerchantBankAccount.TAX_PAYER_ID));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", bankName, MapUtils.getString(bankAccountInfo, MerchantBankAccount.BANK_NAME));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", branchName, MapUtils.getString(bankAccountInfo, MerchantBankAccount.BRANCH_NAME));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", city, MapUtils.getString(bankAccountInfo, MerchantBankAccount.CITY));
        Assert.assertEquals("商户绑定银行账户返回结果与期望值不同", cellphone, MapUtils.getString(bankAccountInfo, MerchantBankAccount.CELLPHONE));
        String bankAccountId = MapUtils.getString(merchantBankAccount, DaoConstants.ID);
        //3.查询银行卡
        bankAccountInfo = merchantService.getMerchantBankAccount(bankAccountId);
        Map bankAccountByMerchantId = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        bankAccountByMerchantId.remove("app_change_in_twoday");
        Assert.assertEquals("bankAccountInfo,bankAccountByMerchantId查询不一致", JSON.toJSONString(bankAccountInfo), JSON.toJSONString(bankAccountByMerchantId));
        Assert.assertNotNull("获取商户银行账户信息返回不能为空", bankAccountInfo);
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", merchantId, MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", holder, MapUtils.getString(bankAccountInfo, MerchantBankAccount.HOLDER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", number, MapUtils.getString(bankAccountInfo, MerchantBankAccount.NUMBER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", 1, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.TYPE));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", 3, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.ID_TYPE));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", 1, MapUtils.getIntValue(bankAccountInfo, MerchantBankAccount.VERIFY_STATUS));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", identity, MapUtils.getString(bankAccountInfo, MerchantBankAccount.IDENTITY));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", taxPayerId, MapUtils.getString(bankAccountInfo, MerchantBankAccount.TAX_PAYER_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", bankName, MapUtils.getString(bankAccountInfo, MerchantBankAccount.BANK_NAME));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", branchName, MapUtils.getString(bankAccountInfo, MerchantBankAccount.BRANCH_NAME));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", cellphone, MapUtils.getString(bankAccountInfo, MerchantBankAccount.CELLPHONE));
        //4.查询银行卡与商户信息
        testGetMerchantAndBankAccount(merchant, bankAccountInfo);
        testFindMerchantBankAccounts(merchant, bankAccountInfo);
        testGetBankAccountVerifyStatus(merchant);
        testUpdateMerchantBankInnerMethod(bankAccountInfo);
        testUpdateMerchantEdgeInfo(bankAccountInfo);
        tesSyncMerchantBankAccountInfoAfterVerifiedSuccess(bankAccountInfo);
        testUpdateMerchantBankAccountInfo(bankAccountInfo);
    }

    private void testUpdateMerchantBankAccountInfo(Map bankAccountInfo) {
        String merchantId = MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID);
        Map update = Maps.newHashMap();
        String number = getNumber();
        update.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        update.put(MerchantBankAccount.NUMBER, number);
        update.put("change_way", "APP");
        merchantService.updateMerchantBankAccountInfo(update);
        Boolean hasKey = redisTemplate.hasKey("APP_REBIND_BANK_PRE_FLAG_" + merchantId);
        Assert.assertTrue("redis-hasKey-error", hasKey);
        update = Maps.newHashMap();
        update.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        update.put(MerchantBankAccount.NUMBER, number);
        update.put("change_way", "CRM");
        merchantService.updateMerchantBankAccountInfo(update);
        hasKey = redisTemplate.hasKey("APP_REBIND_BANK_PRE_FLAG_" + merchantId);
        Assert.assertFalse("redis-hasKey-error", hasKey);
    }

    private void tesSyncMerchantBankAccountInfoAfterVerifiedSuccess(Map bankAccountInfo) {
        String merchantId = MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID);
        Map update = Maps.newHashMap();
        update.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        int randomNumber = new Random().nextInt(9000) + 1000;
        String number = "账户卡号100001" + randomNumber;
        update.put(MerchantBankAccount.NUMBER, number);
        //正常跑通 (tesUpdateInnerMethod有校验）
        merchantService.syncMerchantBankAccountInfoAfterVerifiedSuccess(update);
        String exMsg = null;
        try {
            update = Maps.newHashMap();
            update.put(MerchantBankAccount.MERCHANT_ID, new Random().nextInt(9000) + 1000 + "");
            merchantService.syncMerchantBankAccountInfoAfterVerifiedSuccess(update);
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("syncMerchantBankAccountInfoAfterVerifiedSuccess单元测试异常", "该商户未绑定过银行卡信息", exMsg);
    }

    private void testUpdateMerchantEdgeInfo(Map bankAccountInfo) {
        String id = MapUtils.getString(bankAccountInfo, DaoConstants.ID);
        String merchantId = MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID);
        int randomNumber = new Random().nextInt(9000) + 1000;
        String frontPhoto = "front-photo" + randomNumber;
        String backPhoto = "back-photo" + randomNumber;
        int ocrStatus = 4;
        int holderIdStatus = 4;
        String taxPayerId = "taxId11111" + randomNumber;
        String bankCardImage = "bankCardImage-photo" + randomNumber;
        String transferVoucher = "voucher" + randomNumber;
        String letterOfAuthorization = "letterOfAuthorization" + randomNumber;
        int bankCardStatus = 3;
        Map merchantBankAccount = new HashMap();
        merchantBankAccount.put(DaoConstants.ID, id);
        merchantBankAccount.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_FRONT_PHOTO, frontPhoto);
        merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_BACK_PHOTO, backPhoto);
        merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS, ocrStatus);
        merchantBankAccount.put(MerchantBankAccount.HOLDER_ID_STATUS, holderIdStatus);
        merchantBankAccount.put(MerchantBankAccount.TAX_PAYER_ID, taxPayerId);
        merchantBankAccount.put(MerchantBankAccount.BANK_CARD_IMAGE, bankCardImage);
        merchantBankAccount.put(MerchantBankAccount.TRANSFER_VOUCHER, transferVoucher);
        merchantBankAccount.put(MerchantBankAccount.LETTER_OF_AUTHORIZATION, letterOfAuthorization);
        merchantBankAccount.put(MerchantBankAccount.BANK_CARD_STATUS, bankCardStatus);
        merchantService.updateBankAccountEdgeInfo(merchantBankAccount);
        merchantBankAccount = merchantService.getMerchantBankAccount(id);
        Assert.assertNotNull("获取商户银行账户信息返回不能为空", merchantBankAccount);
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", merchantId, MapUtils.getString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", frontPhoto, MapUtils.getString(merchantBankAccount, MerchantBankAccount.HOLDER_ID_FRONT_PHOTO));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", backPhoto, MapUtils.getString(merchantBankAccount, MerchantBankAccount.HOLDER_ID_BACK_PHOTO));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", ocrStatus, MapUtils.getIntValue(merchantBankAccount, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", holderIdStatus, MapUtils.getIntValue(merchantBankAccount, MerchantBankAccount.HOLDER_ID_STATUS));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", taxPayerId, MapUtils.getString(merchantBankAccount, MerchantBankAccount.TAX_PAYER_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", bankCardImage, MapUtils.getString(merchantBankAccount, MerchantBankAccount.BANK_CARD_IMAGE));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", transferVoucher, MapUtils.getString(merchantBankAccount, MerchantBankAccount.TRANSFER_VOUCHER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", letterOfAuthorization, MapUtils.getString(merchantBankAccount, MerchantBankAccount.LETTER_OF_AUTHORIZATION));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", bankCardStatus, MapUtils.getIntValue(merchantBankAccount, MerchantBankAccount.BANK_CARD_STATUS));
    }

    public void testUpdateMerchantBankInnerMethod(Map bankAccountInfo) {
        String id = MapUtils.getString(bankAccountInfo, DaoConstants.ID);
        String merchantId = MapUtils.getString(bankAccountInfo, MerchantBankAccount.MERCHANT_ID);
        int randomNumber = new Random().nextInt(9000) + 1000;
        String holder = "账户持有人名称" + randomNumber;
        String number = "账户卡号100001" + randomNumber;
        String identity = "300000119901010001" + randomNumber;
        String taxPayerId = "taxId11111" + randomNumber;
        String bankName = "中国农业银行";
        String branchName = "中国农业银行苏州干将路支行";
        String city = "上海" + randomNumber;
        String cellphone = "10086-" + randomNumber;
        Map merchantBankAccount = new HashMap(10);
        merchantBankAccount.put(DaoConstants.ID, id);
        merchantBankAccount.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        merchantBankAccount.put(MerchantBankAccount.HOLDER, holder);
        merchantBankAccount.put(MerchantBankAccount.NUMBER, number);
        merchantBankAccount.put(MerchantBankAccount.IDENTITY, identity);
        merchantBankAccount.put(MerchantBankAccount.TAX_PAYER_ID, taxPayerId);
        merchantBankAccount.put(MerchantBankAccount.BANK_NAME, bankName);
        merchantBankAccount.put(MerchantBankAccount.BRANCH_NAME, branchName);
        merchantBankAccount.put(MerchantBankAccount.CITY, city);
        merchantBankAccount.put(MerchantBankAccount.CELLPHONE, cellphone);
        merchantService.updateBankAccountInnerMethod(merchantBankAccount);
        merchantBankAccount = merchantService.getMerchantBankAccount(id);
        Assert.assertNotNull("获取商户银行账户信息返回不能为空", merchantBankAccount);
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", merchantId, MapUtils.getString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", holder, MapUtils.getString(merchantBankAccount, MerchantBankAccount.HOLDER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", number, MapUtils.getString(merchantBankAccount, MerchantBankAccount.NUMBER));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", identity, MapUtils.getString(merchantBankAccount, MerchantBankAccount.IDENTITY));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", taxPayerId, MapUtils.getString(merchantBankAccount, MerchantBankAccount.TAX_PAYER_ID));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", bankName, MapUtils.getString(merchantBankAccount, MerchantBankAccount.BANK_NAME));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", branchName, MapUtils.getString(merchantBankAccount, MerchantBankAccount.BRANCH_NAME));
        Assert.assertEquals("获取商户银行账户信息返回结果与期望值不同", cellphone, MapUtils.getString(merchantBankAccount, MerchantBankAccount.CELLPHONE));
    }


    private void testGetBankAccountVerifyStatus(Map merchant) {
        String exMsg = null;
        try {
            merchantService.getBankAccountVerifyStatus(null);
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("getBankAccountVerifyStatus单元测试异常", "商户号不能为空", exMsg);
        try {
            merchantService.getBankAccountVerifyStatus("not exist");
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertTrue("getBankAccountVerifyStatus单元测试异常", exMsg.contains("不存在"));

        String merchantSn = MapUtils.getString(merchant, Merchant.SN);
        Map result = merchantService.getBankAccountVerifyStatus(merchantSn);
        Assert.assertEquals("testGetBankAccountVerifyStatus错误", "10", BeanUtil.getPropString(result, "code"));

    }

    private void testFindMerchantBankAccounts(Map merchant, Map insertInfo) {
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        String bankId = MapUtils.getString(insertInfo, DaoConstants.ID);
        Long ctime = MapUtils.getLong(insertInfo, DaoConstants.CTIME);

        PageInfo pageInfo = new PageInfo(0, 2);
        pageInfo.setDateStart(ctime);
        pageInfo.setDateEnd(ctime + 1);

        Map queryFilter = new HashMap();
        queryFilter.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        queryFilter.put("merchant_ids", Lists.newArrayList(merchantId));
        queryFilter.put(MerchantBankAccount.TYPE, MapUtils.getString(insertInfo, MerchantBankAccount.TYPE));
        queryFilter.put(MerchantBankAccount.HOLDER, MapUtils.getString(insertInfo, MerchantBankAccount.HOLDER));
        queryFilter.put(MerchantBankAccount.ID_TYPE, MapUtils.getString(insertInfo, MerchantBankAccount.ID_TYPE));
        queryFilter.put(MerchantBankAccount.IDENTITY, MapUtils.getString(insertInfo, MerchantBankAccount.IDENTITY));
        queryFilter.put(MerchantBankAccount.NUMBER, MapUtils.getString(insertInfo, MerchantBankAccount.NUMBER));
        queryFilter.put(MerchantBankAccount.VERIFY_STATUS, MapUtils.getString(insertInfo, MerchantBankAccount.VERIFY_STATUS));
        queryFilter.put(MerchantBankAccount.BANK_NAME, MapUtils.getString(insertInfo, MerchantBankAccount.BANK_NAME));
        queryFilter.put(MerchantBankAccount.BRANCH_NAME, MapUtils.getString(insertInfo, MerchantBankAccount.BRANCH_NAME));
        queryFilter.put(MerchantBankAccount.TAX_PAYER_ID, MapUtils.getString(insertInfo, MerchantBankAccount.TAX_PAYER_ID));
        queryFilter.put(MerchantBankAccount.CELLPHONE, MapUtils.getString(insertInfo, MerchantBankAccount.CELLPHONE));
        ListResult listResult = merchantService.findMerchantBankAccounts(pageInfo, queryFilter);
        Assert.assertNotNull("testFindMerchantBankAccounts查询结果不可能为空", listResult);
        Assert.assertEquals("testFindMerchantBankAccounts有误", 1, listResult.getRecords().size());
        String id = BeanUtil.getPropString(listResult.getRecords().get(0), DaoConstants.ID);
        Assert.assertEquals("testFindMerchantBankAccounts有误", bankId, id);

        //只有1个查询条件，测试其它参数为null场景
        queryFilter = new HashMap();
        queryFilter.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        listResult = merchantService.findMerchantBankAccounts(pageInfo, queryFilter);
        Assert.assertNotNull("testFindMerchantBankAccounts查询结果不可能为空", listResult);
        Assert.assertEquals("testFindMerchantBankAccounts有误", 1, listResult.getRecords().size());
        id = BeanUtil.getPropString(listResult.getRecords().get(0), DaoConstants.ID);
        Assert.assertEquals("testFindMerchantBankAccounts有误", bankId, id);
        //只有一个查询参数，测试其它参数为""场景
        queryFilter = new HashMap();
        queryFilter.put(MerchantBankAccount.MERCHANT_ID, merchantId);
        queryFilter.put("merchant_ids", null);
        queryFilter.put(MerchantBankAccount.TYPE, "");
        queryFilter.put(MerchantBankAccount.HOLDER, "");
        queryFilter.put(MerchantBankAccount.ID_TYPE, "");
        queryFilter.put(MerchantBankAccount.IDENTITY, "");
        queryFilter.put(MerchantBankAccount.NUMBER, "");
        queryFilter.put(MerchantBankAccount.VERIFY_STATUS, "");
        queryFilter.put(MerchantBankAccount.BANK_NAME, "");
        queryFilter.put(MerchantBankAccount.BRANCH_NAME, "");
        queryFilter.put(MerchantBankAccount.TAX_PAYER_ID, "");
        queryFilter.put(MerchantBankAccount.CELLPHONE, "");
        listResult = merchantService.findMerchantBankAccounts(pageInfo, queryFilter);
        Assert.assertNotNull("testFindMerchantBankAccounts查询结果不可能为空", listResult);
        Assert.assertEquals("testFindMerchantBankAccounts有误", 1, listResult.getRecords().size());
        id = BeanUtil.getPropString(listResult.getRecords().get(0), DaoConstants.ID);
        Assert.assertEquals("testFindMerchantBankAccounts有误", bankId, id);


    }


    public void testGetMerchantAndBankAccount(Map merchant, Map insertInfo) {
        //1.正常场景
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        Map merchantAndBankAccountInfo = merchantService.getMerchantAndBankAccount(merchantId);
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.NAME), MapUtils.getString(merchantAndBankAccountInfo, Merchant.NAME));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.ALIAS), MapUtils.getString(merchantAndBankAccountInfo, Merchant.ALIAS));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.INDUSTRY), MapUtils.getString(merchantAndBankAccountInfo, Merchant.INDUSTRY));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getIntValue(merchant, Merchant.STATUS), MapUtils.getIntValue(merchantAndBankAccountInfo, Merchant.STATUS));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getIntValue(merchant, Merchant.RANK), MapUtils.getIntValue(merchantAndBankAccountInfo, Merchant.RANK));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getIntValue(merchant, Merchant.WITHDRAW_MODE), MapUtils.getIntValue(merchantAndBankAccountInfo, Merchant.WITHDRAW_MODE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.LONGITUDE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.LONGITUDE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.LATITUDE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.LATITUDE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.COUNTRY), MapUtils.getString(merchantAndBankAccountInfo, Merchant.COUNTRY));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.PROVINCE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.PROVINCE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CITY), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CITY));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.DISTRICT), MapUtils.getString(merchantAndBankAccountInfo, Merchant.DISTRICT));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.STREET_ADDRESS), MapUtils.getString(merchantAndBankAccountInfo, Merchant.STREET_ADDRESS));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CONTACT_NAME), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CONTACT_NAME));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CONTACT_PHONE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CONTACT_PHONE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CONTACT_CELLPHONE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CONTACT_EMAIL), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CONTACT_EMAIL));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.BUSINESS_LICENSE_PHOTO), MapUtils.getString(merchantAndBankAccountInfo, Merchant.BUSINESS_LICENSE_PHOTO));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.BUSINESS), MapUtils.getString(merchantAndBankAccountInfo, Merchant.BUSINESS));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CURRENCY), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CURRENCY));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.OWNER_NAME), MapUtils.getString(merchantAndBankAccountInfo, Merchant.OWNER_NAME));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.OWNER_CELLPHONE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.CUSTOMER_PHONE), MapUtils.getString(merchantAndBankAccountInfo, Merchant.CUSTOMER_PHONE));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getIntValue(merchant, Merchant.BANK_ACCOUNT_VERIFY_STATUS), MapUtils.getIntValue(merchantAndBankAccountInfo, Merchant.BANK_ACCOUNT_VERIFY_STATUS));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.VENDOR_ID), MapUtils.getString(merchantAndBankAccountInfo, Merchant.VENDOR_ID));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.SOLICITOR_ID), MapUtils.getString(merchantAndBankAccountInfo, Merchant.SOLICITOR_ID));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getIntValue(merchant, Merchant.PLATFORM), MapUtils.getIntValue(merchantAndBankAccountInfo, Merchant.PLATFORM));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.BUSINESS_NAME), MapUtils.getString(merchantAndBankAccountInfo, Merchant.BUSINESS_NAME));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.NAME), MapUtils.getString(merchantAndBankAccountInfo, Merchant.NAME));
        Assert.assertEquals("商户信息与预期值不匹配", MapUtils.getString(merchant, Merchant.NAME), MapUtils.getString(merchantAndBankAccountInfo, Merchant.NAME));
        Assert.assertNotNull("获取商户及商户的银行账号信息返回为空，请使用一个已存在的商户id", merchantAndBankAccountInfo);

        Map backAccountInfo = MapUtils.getMap(merchantAndBankAccountInfo, "bank_account");
        Assert.assertNotNull("商户银行账户信息不能为空", backAccountInfo);
        Assert.assertEquals("商户银行账户信息与预期值不匹配", merchantId, MapUtils.getString(backAccountInfo, MerchantBankAccount.MERCHANT_ID));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getIntValue(insertInfo, MerchantBankAccount.TYPE), MapUtils.getIntValue(backAccountInfo, MerchantBankAccount.TYPE));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.HOLDER), MapUtils.getString(backAccountInfo, MerchantBankAccount.HOLDER));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getIntValue(insertInfo, MerchantBankAccount.ID_TYPE), MapUtils.getIntValue(backAccountInfo, MerchantBankAccount.ID_TYPE));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.IDENTITY), MapUtils.getString(backAccountInfo, MerchantBankAccount.IDENTITY));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.HOLDER_ID_FRONT_PHOTO), MapUtils.getString(backAccountInfo, MerchantBankAccount.HOLDER_ID_FRONT_PHOTO));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.HOLDER_ID_BACK_PHOTO), MapUtils.getString(backAccountInfo, MerchantBankAccount.HOLDER_ID_BACK_PHOTO));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getIntValue(insertInfo, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS), MapUtils.getIntValue(backAccountInfo, MerchantBankAccount.HOLDER_ID_FRONT_OCR_STATUS));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getIntValue(insertInfo, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS), MapUtils.getIntValue(backAccountInfo, MerchantBankAccount.HOLDER_ID_BACK_OCR_STATUS));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getIntValue(insertInfo, MerchantBankAccount.HOLDER_ID_STATUS), MapUtils.getIntValue(backAccountInfo, MerchantBankAccount.HOLDER_ID_STATUS));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.TAX_PAYER_ID), MapUtils.getString(backAccountInfo, MerchantBankAccount.TAX_PAYER_ID));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getIntValue(insertInfo, MerchantBankAccount.VERIFY_STATUS), MapUtils.getIntValue(backAccountInfo, MerchantBankAccount.VERIFY_STATUS));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.BANK_NAME), MapUtils.getString(backAccountInfo, MerchantBankAccount.BANK_NAME));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.BRANCH_NAME), MapUtils.getString(backAccountInfo, MerchantBankAccount.BRANCH_NAME));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.CITY), MapUtils.getString(backAccountInfo, MerchantBankAccount.CITY));
        Assert.assertEquals("商户银行账户信息与预期值不匹配", MapUtils.getString(insertInfo, MerchantBankAccount.CELLPHONE), MapUtils.getString(backAccountInfo, MerchantBankAccount.CELLPHONE));
        //2.商户不存在时，查询抛异常
        String exMsg = null;
        try {
            merchantService.getMerchantAndBankAccount("xxxxx");
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertEquals("getMerchantAndBankAccount单元测试异常", "商户不存在", exMsg);
    }


    @Test
    public void getIndustriesNameMapFromBankInfoService() {
        Map<String, String> result = merchantService.getIndustriesNameMapFromBankInfoService();
        Assert.assertNotNull("行业类目不能为空", result);
    }

    @Rule
    public ExpectedException thrown = ExpectedException.none();
    @Test
    @Transactional(value = "transactionManager")
    public void createMerchantForMerchantCenter() {
        Map<String, Object> merchantInfo = JSON.parseObject("{\n" +
                "    \"business_name\": \"万象健康板材5S店\",\n" +
                "    \"street_address\": \"详细地址\",\n" +
                "    \"legal_person_id_card_back_photo\": \"http://baidu.com\",\n" +
                "    \"contact_name\": \"徐欣\",\n" +
                "    \"owner_name\": \"徐欣\",\n" +
                "    \"business\": \"万象健康板材5S店\",\n" +
                "    \"city\": \"长沙市\",\n" +
                "    \"latitude\": \"28.279130\",\n" +
                "    \"customer_phone\": \"180****2033\",\n" +
                "    \"solicitor_id\": \"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\n" +
                "    \"industry\": \"0260c061-c759-47c7-ac2d-dddd353f1649\",\n" +
                "    \"contact_cellphone\": \"180****2033\",\n" +
                "    \"owner_cellphone\": \"180****2033\",\n" +
                "    \"province\": \"湖南省\",\n" +
                "    \"district\": \"望城区\",\n" +
                "    \"extra\": {\n" +
                "        \"poi_address\": \"湖南省长沙市望城区月亮岛建材市场01栋193号\"\n" +
                "    },\n" +
                "    \"merchant_scale\": 2,\n" +
                "    \"vendor_id\": \"859d9f5f-af99-11e5-9ec3-00163e00625b\",\n" +
                "    \"name\": \"万象健康板材5S店\",\n" +
                "    \"alias\": \"万象健康板材5S店\",\n" +
                "    \"id\": \"6b1d1e6f-92c5-47ee-8845-1528d72e29e4\",\n" +
                "    \"legal_person_id_card_front_photo\": \"***\",\n" +
                "    \"longitude\": \"112.909420\"\n" +
                "}");
        Map merchant = merchantService.createMerchantForMerchantCenter(merchantInfo);
        Assert.assertEquals("430112", BeanUtil.getPropString(merchant, Merchant.DISTRICT_CODE));
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        merchant = merchantService.updateMerchant(CollectionUtil.hashMap(
                DaoConstants.ID, merchantId,
                Merchant.NAME, "test",
                Merchant.PROVINCE, "test"
        ));
        Assert.assertEquals("湖南省", BeanUtil.getPropString(merchant, Merchant.PROVINCE));
        Assert.assertEquals("test", BeanUtil.getPropString(merchant, Merchant.NAME));

        thrown.expect(CoreInvalidParameterException.class);
        merchantService.updateMerchant(CollectionUtil.hashMap(
                DaoConstants.ID, merchantId,
                Merchant.NAME, "test",
                Merchant.DISTRICT_CODE, "test"
        ));
    }


}
