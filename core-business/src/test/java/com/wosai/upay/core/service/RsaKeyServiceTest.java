package com.wosai.upay.core.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.RsaKey;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.base.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

public class RsaKeyServiceTest extends BaseTest {

    @Autowired
    private RsaKeyService rsaKeyService;

    @Autowired
    private DataRepository repository;

//
//    @Test
//    public void testStoreRsaKey(){
//        String uuidData = "a6f34d7b-54fc-48e3-beab-d37e5ece3a59";
//        System.out.println(uuidData);
//        String rsaKey = rsaKeyService.storeRsaKey(uuidData);
//        System.out.println(rsaKey);
//        Map<String, Object> map = rsaKeyService.getRsaKey(rsaKey);
//        assert BeanUtil.getPropString(map,RsaKey.DATA).equals(uuidData);
//    }



}
