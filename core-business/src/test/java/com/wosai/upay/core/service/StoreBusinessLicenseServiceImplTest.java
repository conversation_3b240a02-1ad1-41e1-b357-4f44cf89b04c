package com.wosai.upay.core.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.base.BaseTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StoreBusinessLicenseServiceImplTest extends BaseTest {

    @Autowired
    private StoreBusinessLicenseService storeBusinessLicenseService;
    @Autowired
    private LicenseService licenseService;

    @Test
    public void save() {
        String store_id = "12355661";

        //营业执照信息
        Map storeBusinessLicense = CollectionUtil.hashMap("store_id", store_id, "merchant_id", "123", "trade_license_list", Arrays.asList(CollectionUtil.hashMap("license_name", "一起存")));
        //一个许可证信息
        Map license = CollectionUtil.hashMap("license_name", "名称");
        //保存营业执照信息
        storeBusinessLicenseService.saveStoreBusinessLicense(storeBusinessLicense);
        //根据门店id查询存入的营业执照信息(包括许可证)
        Map<String, Object> storeBusinessLicenseByStoreId = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id);
        Assert.assertNotNull(storeBusinessLicenseByStoreId);

        //保存之前的许可证 (许可证必须对应一个营业执照,即有  business_license_id )
        String businessLicenseId = (String) storeBusinessLicenseByStoreId.get("id");
        license.put("business_license_id", businessLicenseId);
        licenseService.saveLicense(license);
        //根据营业执照id 获取保存的所有许可证信息
        List list = licenseService.getLicenseByBusinessLicenseId(businessLicenseId);
        Assert.assertEquals(2, list.size());

        for (Object o : list) {
            //根据主键id,获取许可证信息
            Map map = (Map) o;
            String id = (String) map.get("id");
            Map<String, Object> licenseById = licenseService.getLicenseById(id);
            Assert.assertNotNull(licenseById);

            //根据id删除许可证
            licenseService.deleteLicenseById(id);
            break;
        }
        //删除营业执照信息(会连带许可证一起删除)
        storeBusinessLicenseService.deleteStoreBusinessLicenseById(businessLicenseId);

        storeBusinessLicenseByStoreId = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id);
        Assert.assertNull(storeBusinessLicenseByStoreId);

    }

    @Test
    public void update() {
        String store_id = "202112061920";
        //营业执照信息
        Map storeBusinessLicense = CollectionUtil.hashMap("store_id", store_id, "merchant_id", "123", "trade_license_list", Arrays.asList(CollectionUtil.hashMap("license_name", "一起存")));
        storeBusinessLicenseService.saveStoreBusinessLicense(storeBusinessLicense);

        Map<String, Object> businessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id);
        String id = (String) businessLicense.get("id");
        List storeLicense = licenseService.getLicenseByBusinessLicenseId(id);
        Map o = (Map) storeLicense.get(0);
        String licenseId = (String) o.get("id");

        businessLicense.put("photo", "1.png");
        o.put("license_name", "更新");
        businessLicense.remove("trade_license_list");
        storeBusinessLicenseService.updateStoreBusinessLicense(businessLicense);
        licenseService.updateLicense(o);

        Map<String, Object> licenseById = licenseService.getLicenseById(licenseId);
        businessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id);

        String license_name = (String) licenseById.get("license_name");
        Assert.assertEquals("更新", license_name);
        String photo = (String) businessLicense.get("photo");
        Assert.assertEquals("1.png", photo);

        storeBusinessLicenseService.deleteStoreBusinessLicenseByStoreId(store_id);

        businessLicense = storeBusinessLicenseService.getStoreBusinessLicenseByStoreId(store_id);
        Assert.assertNull(businessLicense);

        List licenseByStoreBusinessLicenseId = licenseService.getLicenseByBusinessLicenseId(id);
        Assert.assertEquals(0, licenseByStoreBusinessLicenseId.size());


    }

    @Test
    public void testException() {
//        Map data1 = new HashMap();
//        data1.put("id","123456");
//        storeBusinessLicenseService.updateStoreBusinessLicense(data1);

    }
}