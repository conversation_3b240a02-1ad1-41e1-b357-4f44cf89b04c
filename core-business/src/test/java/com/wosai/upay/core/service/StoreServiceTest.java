package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.exception.CoreClientSnNotUniqueException;
import com.wosai.upay.core.exception.CoreOnlyStatusDisabledCouldEnableException;
import com.wosai.upay.core.exception.CoreStoreNotExistsException;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.base.AbstractNewMerchantTest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
public class StoreServiceTest extends AbstractNewMerchantTest {

    @Autowired
    StoreService storeService;
    @Autowired
    StoreExtService storeExtService;


    @Test
    public void testCreateStore() {
        createStoreAndMerchant();
    }


    /**
     * 相同商户下 不允许两个client_sn相同的门店
     */
    @Test(expected = CoreClientSnNotUniqueException.class)
    public void testCreateStoreWithSameClientSn() {
        Map store = createStoreAndMerchant();
        String clientSn = BeanUtil.getPropString(store, Store.CLIENT_SN);
        createStoreByMerchantId(MapUtils.getString(store, Store.MERCHANT_ID), clientSn);
    }

    @Test
    public void testDisableAndEnableStore() {
        Map store = createStoreAndMerchant();
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        storeService.disableStore(storeId);
        Map storeV2 = storeService.getStoreByStoreId(storeId);
        Assert.assertEquals("禁用门店状态异常", Store.STATUS_DISABLED, MapUtils.getIntValue(storeV2, Store.STATUS));
        storeService.enableStore(storeId);
        Map storeV3 = storeService.getStoreByStoreId(storeId);
        Assert.assertEquals("禁用门店状态异常", Store.STATUS_ENABLED, MapUtils.getIntValue(storeV3, Store.STATUS));
    }

    @Test
    public void testCloseStore() {
        Map store = createStoreAndMerchant();
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        storeService.closeStore(storeId);
        Map storeV4 = storeService.getStoreByStoreId(storeId);
        Assert.assertEquals("关闭门店状态异常", Store.STATUS_CLOSED, MapUtils.getIntValue(storeV4, Store.STATUS));
    }

    @Test(expected = CoreStoreNotExistsException.class)
    public void testDisableStoreNotExist() {
        storeService.disableStore(UUID.randomUUID().toString());
    }

    @Test(expected = CoreStoreNotExistsException.class)
    public void testEnableStoreNotExist() {
        storeService.enableStore(UUID.randomUUID().toString());
    }

    @Test(expected = CoreStoreNotExistsException.class)
    public void testCloeseStoreNotExist() {
        storeService.closeStore(UUID.randomUUID().toString());
    }

    @Test(expected = CoreOnlyStatusDisabledCouldEnableException.class)
    public void testEnableAlreadyEnableStore() {
        Map store = createStoreAndMerchant();
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        storeService.enableStore(storeId);
    }

    @Test
    public void testGetStore() {
        Map store = createStoreAndMerchant();
        String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
        String storeSN = BeanUtil.getPropString(store, Store.SN);
        String clientSn = BeanUtil.getPropString(store, Store.CLIENT_SN);
        Map store3 = storeService.getStore(storeId);
        Map store1 = storeService.getStoreByStoreId(storeId);
        Map store2 = storeService.getStoreByStoreSn(storeSN);
        Map store4 = storeService.getStoreByClientSn(BeanUtil.getPropString(store, Store.MERCHANT_ID), clientSn);
        Assert.assertEquals("store1,store2不相等", JSON.toJSONString(store1), JSON.toJSONString(store2));
        Assert.assertEquals("store2,store3不相等", JSON.toJSONString(store2), JSON.toJSONString(store3));
        Assert.assertEquals("store4,store4不相等", JSON.toJSONString(store3), JSON.toJSONString(store4));
        //为null时的判断
        Map store11 = storeService.getStore(null);
        Map store13 = storeService.getStoreByStoreId(null);
        Map store12 = storeService.getStoreByStoreSn(null);
        Assert.assertNull("store11不为空", store11);
        Assert.assertNull("store12不为空", store12);
        Assert.assertNull("store13不为空", store13);

        Map store21 = storeService.getStore("");
        Map store23 = storeService.getStoreByStoreId("");
        Map store22 = storeService.getStoreByStoreSn("");
        Assert.assertNull("store11不为空", store21);
        Assert.assertNull("store12不为空", store22);
        Assert.assertNull("store13不为空", store23);


    }

    @Test
    public void testUpdateStore() {
        Map origin = createStoreAndMerchant();
        String storeId = BeanUtil.getPropString(origin, DaoConstants.ID);
        String merchantId = BeanUtil.getPropString(origin, Store.MERCHANT_ID);
        int randomNumber = new Random().nextInt(9000) + 1000;
        Map store = new HashMap();
        String storeName = "广州大拇指信息科技有限公司" + randomNumber;
        String storeContactName = "贺深书" + randomNumber;
        String storeCellPhone = "15218022949" + randomNumber;
        String storeProvince = "广东省" + randomNumber;
        String storeCity = "广东省" + randomNumber;
        String storeDistrict = "广东省" + randomNumber;
        String storeStreetAddress = "番禺区恒然创意园D座1梯" + randomNumber;
        String storeLongitude = "113.999999";
        String storeLatitude = "22.999999";
        store.put(DaoConstants.ID, storeId);
        store.put(Store.NAME, storeName);
        store.put(Store.CONTACT_NAME, storeContactName);
        store.put(Store.CONTACT_CELLPHONE, storeCellPhone);
        store.put(Store.PROVINCE, storeProvince);
        store.put(Store.CITY, storeCity);
        store.put(Store.DISTRICT, storeDistrict);
        store.put(Store.STREET_ADDRESS, storeStreetAddress);
        store.put(Store.LONGITUDE, storeLongitude);
        store.put(Store.LATITUDE, storeLatitude);
        store.put(Store.MERCHANT_ID, UUID.randomUUID().toString());
        storeService.updateStore(store);
        Map queryStore = storeService.getStoreByStoreId(storeId);
        Assert.assertEquals("获取门店数据与预期数据不一致", storeName, MapUtils.getString(queryStore, Store.NAME));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeContactName, MapUtils.getString(queryStore, Store.CONTACT_NAME));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeCellPhone, MapUtils.getString(queryStore, Store.CONTACT_CELLPHONE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeProvince, MapUtils.getString(queryStore, Store.PROVINCE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeCity, MapUtils.getString(queryStore, Store.CITY));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeDistrict, MapUtils.getString(queryStore, Store.DISTRICT));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeStreetAddress, MapUtils.getString(queryStore, Store.STREET_ADDRESS));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeLongitude, MapUtils.getString(queryStore, Store.LONGITUDE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeLatitude, MapUtils.getString(queryStore, Store.LATITUDE));
        Assert.assertEquals("商户ID不可变更，与预期不一致", merchantId, MapUtils.getString(queryStore, Store.MERCHANT_ID));
    }

    @Test
    public void testGetStoreListByMerchantId() {
        Map origin = createStoreAndMerchant();
        String storeSn = BeanUtil.getPropString(origin, Store.SN);
        String merchantId = BeanUtil.getPropString(origin, Store.MERCHANT_ID);
        Map queryFilter = Maps.newHashMap();
        queryFilter.put("store_name", "广州大拇指信息科技有限公司");
        queryFilter.put("store_sn", storeSn);
        queryFilter.put("merchantId", merchantId);
        ListResult list = storeService.getStoreListByMerchantId(merchantId, null, queryFilter);
        Assert.assertTrue(list != null && list.getRecords().size() == 1 && storeSn.equalsIgnoreCase(MapUtils.getString(list.getRecords().get(0), Store.SN)));
        ListResult list2 = storeService.getStoreListByMerchantIdFromSlaveDb(merchantId, null, queryFilter);
        Assert.assertEquals("from slave-db diff ", JSON.toJSONString(list), JSON.toJSONString(list2));
    }


    @Test
    public void testGetChangeStore() {
        Map origin = createStoreAndMerchant();
        long mtime = BeanUtil.getPropLong(origin, DaoConstants.MTIME);
        String storeSn = BeanUtil.getPropString(origin, Store.SN);
        List<Map<String, Object>> list = storeService.getChangeStore(mtime, mtime + 1);
        Assert.assertTrue(list != null && list.size() == 1 && storeSn.equalsIgnoreCase(MapUtils.getString(list.get(0), Store.SN)));
    }


    @Test
    public void testFindStores() {
        Map origin = createStoreAndMerchant();
        String storeSn = BeanUtil.getPropString(origin, Store.SN);
        String merchantId = BeanUtil.getPropString(origin, Store.MERCHANT_ID);
        String storeId = BeanUtil.getPropString(origin, DaoConstants.ID);
        int status = BeanUtil.getPropInt(origin, Store.STATUS);
        Map queryFilter = Maps.newHashMap();
//        queryFilter.put("store_name", "广州大拇指信息科技有限公司"); 查询es 暂时无法覆盖
        queryFilter.put("store_sn", storeSn);
        queryFilter.put("store_ids", Lists.newArrayList(storeId));
        queryFilter.put("merchantId", merchantId);
        queryFilter.put("status", status);
        ListResult list = storeService.findStores(null, queryFilter);
        Assert.assertTrue(list != null && list.getRecords().size() == 1 && storeSn.equalsIgnoreCase(MapUtils.getString(list.getRecords().get(0), Store.SN)));
    }

    @Test
    public void testSimpleFindStores() {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(1);
        pageInfo.setPageSize(1);
        pageInfo.setOrderBy(new ArrayList<>(Arrays.asList(new OrderBy("ctime", OrderBy.OrderType.ASC))));

        Map queryFilter = CollectionUtil.hashMap("merchant_id", "b9fc12fb29c6-b108-5e11-b45d-7a76dfa9");
        ListResult list = storeService.findStores(pageInfo, queryFilter);
    }


    @Test
    public void testBatchFindStoreExt() {

        ArrayList<String> strings = new ArrayList<>(Arrays.asList("5bfad2-0dfe-4a56-beef-","c15e8c29-f6b6-4489-8c5b-499d9c9ec608"));

        List<Map> storeExtBatchByStoreIds = storeExtService.findStoreExtBatchByStoreIds(strings);

    }
}
