package com.wosai.upay.core.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;

import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.exception.CoreMerchantConfigAbnormalException;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.util.ApolloConfigurationCenterUtil;
//
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "sun.security.*", "javax.net.*", "javax.net.ssl.*", "javax.crypto.*"}) //为了解决使用powermock后，提示classloader错误
//@ContextConfiguration(locations = {"classpath:spring/business-config.xml", "classpath:spring/tools-config.xml"})
//@PrepareForTest({ApolloConfigurationCenterUtil.class})
//public class SupportServiceTest {
//    // 测试终端
//    private String terminalSn = "tsn-100000330007352422";
//
//    @Autowired
//    private SupportService supportService;
//    
//    @Autowired
//    private TradeConfigService tradeConfigService;
//
//    /**
//     * 获取支付业务交易参数
//     */
//    @Test
//    public void testGetBasicTradeAppConfig() {
//        Map<String, Object> result = supportService.getAllParamsWithTradeApp(null, terminalSn, 3, 1, null);
//        assertTrue("获取支付业务交易参数", null != result);
//        supportService.removeCachedParams(MapUtil.getString(result, TransactionParam.MERCHANT_SN));
//        result = supportService.getAllParamsWithTradeApp(null, terminalSn, 3, 1, TransactionParam.TRADE_APP_BASIC_PAY);
//        assertTrue("获取支付业务交易参数", null != result);
//        supportService.removeCachedParams(MapUtil.getString(result, TransactionParam.MERCHANT_SN));
//    }
//
//    /**
//     * 不支持的业务方交易参数获取
//     */
//    @Test
//    public void testTradeAppConfigNotSupport() {
//        CoreMerchantConfigAbnormalException ex = null;
//        try {
//            Map<String, Object> result = supportService.getAllParamsWithTradeApp(null, terminalSn, 3, 1, "2");
//        }catch (CoreMerchantConfigAbnormalException e) {
//            ex = e;
//        }
//        assertNotNull("不支持的业务方交易参数获取", ex);
//        assertEquals("不支持的业务方交易参数获取", "终端归属服务商无该业务权限", ex.getMessage());
//    }
//
//    /**
//     * 业务方交易参数未配置
//     */
//    @Test
//    public void testGetTradeAppConfigFail() {
//        MockitoAnnotations.initMocks(this);
//        PowerMockito.mockStatic(ApolloConfigurationCenterUtil.class);
//        Mockito.when(ApolloConfigurationCenterUtil.getVendorAppSupportTradeApp(Mockito.anyString())).thenReturn(Arrays.asList("2"));
//        CoreMerchantConfigAbnormalException ex = null;
//        try {
//            Map<String, Object> result = supportService.getAllParamsWithTradeApp(null, terminalSn, 3, 1, "2");
//        }catch (CoreMerchantConfigAbnormalException e) {
//            ex = e;
//        }
//        assertNotNull("不支持的业务方交易参数获取", ex);
//        assertEquals("不支持的业务方交易参数获取", "业务方未配置交易参数", ex.getMessage());
//    }
//
//    /**
//     * 获取久久折支付业务交易参数
//     */
//    @Test
//    public void testGetJJZTradeAppConfig() {
//        String appId = "2";
//        MockitoAnnotations.initMocks(this);
//        PowerMockito.mockStatic(ApolloConfigurationCenterUtil.class);
//        Mockito.when(ApolloConfigurationCenterUtil.getVendorAppSupportTradeApp(Mockito.anyString())).thenReturn(Arrays.asList(appId));
//        
//        Map<String, Object> result = supportService.getBasicParams(null, terminalSn);
//        assertTrue("获取久久折支付业务交易参数", null != result);
//        
//        Map<String, Object> weixinTradeParam = CollectionUtil.hashMap(TransactionParam.WEIXIN_APP_ID, "app_id",
//                    TransactionParam.WEIXIN_SUB_APP_ID, "sub_app_id"
//                );
//        Map<String, Object> merchantAppConfig = CollectionUtil.hashMap(MerchantAppConfig.MERCHANT_ID, MapUtil.getString(result, TransactionParam.MERCHANT_ID),
//                MerchantAppConfig.APP_ID, appId,
//                MerchantAppConfig.PAYWAY, 3,
//                MerchantAppConfig.B2C_FORMAL, 1,
//                MerchantAppConfig.B2C_STATUS, MerchantAppConfig.STATUS_OPENED,
//                MerchantAppConfig.B2C_AGENT_NAME, "*_3_*_true_true_0003",
//                MerchantAppConfig.PARAMS, CollectionUtil.hashMap(TransactionParam.WEIXIN_TRADE_PARAMS, weixinTradeParam)
//            );
//        tradeConfigService.createMerchantAppConfig(merchantAppConfig);
//        merchantAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(MapUtil.getString(merchantAppConfig, MerchantAppConfig.MERCHANT_ID), MapUtil.getInteger(merchantAppConfig, MerchantAppConfig.PAYWAY), MapUtil.getString(merchantAppConfig, MerchantAppConfig.APP_ID));
//        assertTrue("获取久久折支付业务交易参数", null != merchantAppConfig);
//
//       result = supportService.getAllParamsWithTradeApp(null, terminalSn, 3, 1, appId);
//       assertTrue("获取久久折支付业务交易参数", null != result);
//       assertEquals("trade_app", appId, result.get(TransactionParam.TRADE_APP));
//       
//       Map<String, Object>  qWeixinTradeParam = (Map<String, Object> ) result.get(TransactionParam.WEIXIN_TRADE_PARAMS);
//       assertTrue("获取久久折支付业务交易参数 - weixin_trade_params", null != qWeixinTradeParam);
//       assertEquals("获取久久折支付业务交易参数 - weixin_app_id", weixinTradeParam.get(TransactionParam.WEIXIN_APP_ID), qWeixinTradeParam.get(TransactionParam.WEIXIN_APP_ID));
//       assertEquals("获取久久折支付业务交易参数 - sub_app_id", weixinTradeParam.get(TransactionParam.WEIXIN_SUB_APP_ID), qWeixinTradeParam.get(TransactionParam.WEIXIN_SUB_APP_ID));
//       tradeConfigService.deleteMerchantAppConfig(MapUtil.getString(merchantAppConfig, DaoConstants.ID));
//       supportService.removeCachedParams(MapUtil.getString(result, TransactionParam.MERCHANT_SN));
//    }
//}
