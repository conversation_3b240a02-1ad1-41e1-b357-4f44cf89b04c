package com.wosai.upay.core.service;

import com.wosai.upay.core.service.base.BaseTest;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * @Auther: hrx
 * @Date: 2019-11-04
 * @Description: com.wosai.upay.core.service
 * @version: 1.0
 */
public class TerminalActivationCodeServiceTest{

//    @Autowired
//    private TerminalActivationCodeService terminalActivationCodeService;
//
//    @Before
//    public void before() {
//        System.out.println("--------------------开始测试：--------------------");
//    }
//
//    @After
//    public void after() {
//        System.out.println("--------------------测试结束。--------------------");
//    }
//
//
//    @Test
//    public void getActivationInfo(){
//        String code = "49179445";
//        Map map = terminalActivationCodeService.getActivationInfoByCode(code);
//        Assert.assertNotNull(map);
//    }
}
