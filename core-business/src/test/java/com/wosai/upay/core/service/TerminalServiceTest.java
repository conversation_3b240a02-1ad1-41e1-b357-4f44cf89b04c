package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TerminalActivationCode;
import com.wosai.upay.core.model.ChangeShifts;
import com.wosai.upay.core.service.base.AbstractNewMerchantTest;
import facade.ICustomerRelationValidateFacade;
import org.apache.commons.collections.MapUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 * TerminalServiceTest for TerminalService
 *
 * <AUTHOR>
 * @date time 2019-06-13 18:06
 * <p>
 * INSERT INTO `vendor_app` (`id`, `vendor_id`, `appid`, `appkey`, `name`, `type`, `ctime`, `mtime`, `deleted`, `version`)
 * VALUES
 * (X'30303037623535662D653264622D346139312D613732372D386135333536366663333633', X'38353964396635662D616639392D313165352D396563332D303031363365303036323562', X'32303137303330363030303030303833', X'6362666636636635646631396261326436353863653336663132306632303330', X'E699BAE883BD504F53', 20, 1480825912964, 1480825912964, 0, 1),
 * (X'30303166653261322D663366632D346130342D623262342D353733633532356431363637', X'38353964396635662D616639392D313165352D396563332D303031363365303036323562', X'32303137303230363030303030303639', X'6265333965363630313334633363323565366637343631646663613761373939', X'E4B893E794A8E8AEBEE5A487', 30, 1482966024482, 1482966024482, 0, 1),
 * (X'30333537633438382D383661302D346165632D626666332D366131323135666531393533', X'38353964396635662D616639392D313165352D396563332D303031363365303036323562', X'32303136303831323030303035303830', X'3238663138363932613334323762376535326532316362323939323863613861', X'4378307535373436', 11, 1470970587012, 1470970587012, 0, 1),
 * (X'30353535363932632D373465652D346337312D386464382D653066396633383536393539', X'38353964396635662D616639392D313165352D396563332D303031363365303036323562', X'32303139303930363030303231373731', X'3038326632616132663161383733376536326239393438613831663932643937', X'E7A4BCE59381E58DA1E7BABFE4B88AE6A0B8E99480', 60, 1567751567023, 1567751567023, 0, 1),
 * (X'30353966643166342D356131322D343662642D383965632D333664383838343265363764', X'30356464633666332D636565332D346462332D383865312D393861613066363437383037', X'32303139303231333030303231373339', X'3335613364646662316533643931383962626261366237343736356562633034', X'7465737430323133', 30, 1550024691481, 1550024691481, 0, 1),
 * (X'30373831333033372D666430322D343934612D383432662D356336393334366565613465', X'30356464633666332D636565332D346462332D383865312D393861613066363437383037', X'32303139303431313030303231373436', X'6539623639373533613237313765636565386234646433383738343834366664', X'3031323334353637383930313233343536373839', 30, 1554952701087, 1554952701087, 0, 1);
 * INSERT INTO `vendor` (`id`, `name`, `sn`, `cellphone`, `contact_name`, `contact_phone`, `contact_cellphone`, `contact_email`, `contact_address`, `rank`, `status`, `province`, `city`, `district`, `street_address`, `extra`, `ctime`, `mtime`, `deleted`, `version`)
 * VALUES
 * (X'38353964396635662D616639392D313165352D396563332D303031363365303036323562', X'73686F757169616E6261323233', X'31', X'3136353234333532363738', X'77616E677765693131', X'3139383732363335323637', X'373632353336373238383838', X'31314071712E636F6D', X'322E35207061726B', 1, 1, X'6A69616E677375', X'73757A686F75', X'646F6E676368616E6720726F6164', X'3838', X'7B2272656D61726B223A227465737420666F722077616E67776569227D', 1451553160705, 1562662065977, 0, 9);
 * <p>
 * * INSERT INTO `vendor` (`id`, `name`, `sn`, `cellphone`, `contact_name`, `contact_phone`, `contact_cellphone`, `contact_email`, `contact_address`, `rank`, `status`, `province`, `city`, `district`, `street_address`, `extra`, `ctime`, `mtime`, `deleted`, `version`)
 * * VALUES
 * * 	(X'30356464633666332D636565332D346462332D383865312D393861613066363437383037', X'E6B58BE8AF95E69C8DE58AA1E59586736A', X'3931383032303631', NULL, X'E6B288E5A9A7', NULL, X'3130363235353336323738', NULL, NULL, NULL, 1, X'E5A4A9E6B4A5E5B882', X'E58EBF', X'E99D99E6B5B7E58EBF', X'E7BFBBE7BFBB', NULL, 1463995064021, 1463995064021, 0, 1);
 */
public class TerminalServiceTest extends AbstractNewMerchantTest {

    @Autowired
    private TerminalService terminalService;

    @MockBean
    ICustomerRelationValidateFacade iCustomerRelationValidateFacade;

    @Autowired
    private TerminalActivationCodeService terminalActivationCodeService;

    private static Map store = null;

    private static String storeId = null;
    private static String storeSn = null;
    private static String storeName = null;
    private static String storeClientSN = null;

    private static String merchantId = null;

    private String vendorAppAppid = "2017020600000069";

    private static AtomicBoolean init = new AtomicBoolean();

    @Before
    public void before() {
        PowerMockito.when(iCustomerRelationValidateFacade.isLakalaPayMerchant(Mockito.anyString())).thenReturn(false);
        if (init.compareAndSet(false, true)) {
            //创建商户和门店
            store = createStoreAndMerchant();
            storeId = BeanUtil.getPropString(store, DaoConstants.ID);
            storeSn = BeanUtil.getPropString(store, Store.SN);
            storeName = BeanUtil.getPropString(store, Store.NAME);
            merchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            storeClientSN = BeanUtil.getPropString(store, Store.CLIENT_SN);
        }
    }

    @After
    public void after() {
        System.out.println("--------------------测试结束。--------------------");
    }

    @Test
    public void testCreateActivatedTerminal() {
        createActivateTerminal();
    }

    @Test
    public void testCreateActivateTerminalWithEmptyName() {
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        Map<String, Object> result = terminalService.createActivated(vendorAppAppid,
                storeSn, clientSn, deviceFp, null, osVer, sdkVer, longitude, latitude, extra);
        Assert.assertEquals("激活结果与预期不一致", "859d9f5f-af99-11e5-9ec3-00163e00625b", MapUtils.getString(result, Terminal.VENDOR_ID));
        Assert.assertEquals("激活结果与预期不一致", latitude, MapUtils.getString(result, Terminal.LATITUDE));
        Assert.assertEquals("激活结果与预期不一致", longitude, MapUtils.getString(result, Terminal.LONGITUDE));
        Assert.assertEquals("激活结果与预期不一致", extra, MapUtils.getString(result, Terminal.EXTRA));
        Assert.assertTrue("激活结果与预期不一致", MapUtils.getString(result, Terminal.NAME).contains("New Term (createActivated)"));
        Assert.assertEquals("激活结果与预期不一致", deviceFp, MapUtils.getString(result, Terminal.DEVICE_FINGERPRINT));
        Assert.assertEquals("激活结果与预期不一致", osVer, MapUtils.getString(result, Terminal.OS_VERSION));
        Assert.assertEquals("激活结果与预期不一致", sdkVer, MapUtils.getString(result, Terminal.SDK_VERSION));
        Assert.assertEquals("激活结果与预期不一致", vendorAppAppid, MapUtils.getString(result, Terminal.VENDOR_APP_APPID));
        Assert.assertEquals("激活结果与预期不一致", storeId, MapUtils.getString(result, Terminal.STORE_ID));
        Assert.assertEquals("激活结果与预期不一致", 30, MapUtils.getIntValue(result, Terminal.TYPE));
        Assert.assertEquals("激活结果与预期不一致", Terminal.STATUS_ACTIVATED, MapUtils.getIntValue(result, Terminal.STATUS));
        Assert.assertEquals("激活结果与预期不一致", storeName, MapUtils.getString(result, "store_name"));
        Assert.assertEquals("激活结果与预期不一致", storeSn, MapUtils.getString(result, "store_sn"));
    }

    @Test
    public void testBindQrcodeTerminal() {
        Map info = new HashMap();
        String deviceFingerPrint = UUID.randomUUID().toString();
        String clientSn = UUID.randomUUID().toString();
        String terminalName = "二维码名称啊";
        String vendorAppid = UUID.randomUUID().toString();
        String vendorAppAppid = UUID.randomUUID().toString();
        info.put(Terminal.STORE_ID, storeId);
        info.put(Terminal.NAME, terminalName);
        info.put(Terminal.DEVICE_FINGERPRINT, deviceFingerPrint);
        info.put(Terminal.CLIENT_SN, clientSn);
        info.put(Terminal.VENDOR_APP_ID, vendorAppid);
        info.put(Terminal.VENDOR_APP_APPID, vendorAppAppid);
        Map<String, Object> result = terminalService.bindQrcodeTerminal(info);
        Assert.assertEquals("激活结果与预期不一致", deviceFingerPrint, MapUtils.getString(result, Terminal.DEVICE_FINGERPRINT));
        Assert.assertEquals("激活结果与预期不一致", clientSn, MapUtils.getString(result, Terminal.CLIENT_SN));
        Assert.assertEquals("激活结果与预期不一致", storeId, MapUtils.getString(result, Terminal.STORE_ID));
        Assert.assertEquals("激活结果与预期不一致", vendorAppid, MapUtils.getString(result, Terminal.VENDOR_APP_ID));
        Assert.assertEquals("激活结果与预期不一致", vendorAppAppid, MapUtils.getString(result, Terminal.VENDOR_APP_APPID));
        Assert.assertEquals("激活结果与预期不一致", merchantId, MapUtils.getString(result, Terminal.MERCHANT_ID));
        Assert.assertEquals("激活结果与预期不一致", Terminal.STATUS_ACTIVATED, MapUtils.getIntValue(result, Terminal.STATUS));
    }


    @Test
    public void testUnBindQrcodeTerminalNotExist() {
        terminalService.unbindQrcodeTerminal(UUID.randomUUID().toString());
    }


    @Test
    public void testUnBindQrcodeTerminal() {
        Map info = new HashMap();
        String deviceFingerPrint = UUID.randomUUID().toString();
        String clientSn = UUID.randomUUID().toString();
        String terminalName = "二维码名称啊";
        String vendorAppid = UUID.randomUUID().toString();
        String vendorAppAppid = UUID.randomUUID().toString();
        info.put(Terminal.STORE_ID, storeId);
        info.put(Terminal.NAME, terminalName);
        info.put(Terminal.DEVICE_FINGERPRINT, deviceFingerPrint);
        info.put(Terminal.CLIENT_SN, clientSn);
        info.put(Terminal.VENDOR_APP_ID, vendorAppid);
        info.put(Terminal.VENDOR_APP_APPID, vendorAppAppid);
        Map<String, Object> result = terminalService.bindQrcodeTerminal(info);
        String terminalId = MapUtils.getString(result, DaoConstants.ID);
        terminalService.unbindQrcodeTerminal(terminalId);
    }


    @Test
    public void testUnbindQrcodeTerminal() {
        Map info = new HashMap();
        String deviceFingerPrint = UUID.randomUUID().toString();
        String clientSn = UUID.randomUUID().toString();
        String terminalName = "二维码名称啊";
        String vendorAppid = UUID.randomUUID().toString();
        String vendorAppAppid = UUID.randomUUID().toString();
        info.put(Terminal.STORE_ID, storeId);
        info.put(Terminal.NAME, terminalName);
        info.put(Terminal.DEVICE_FINGERPRINT, deviceFingerPrint);
        info.put(Terminal.CLIENT_SN, clientSn);
        info.put(Terminal.VENDOR_APP_ID, vendorAppid);
        info.put(Terminal.VENDOR_APP_APPID, vendorAppAppid);
        Map<String, Object> result = terminalService.bindQrcodeTerminal(info);
        String terminalId = MapUtils.getString(result, DaoConstants.ID);
        terminalService.unbindTerminal(terminalId);
        Map query = terminalService.getTerminalByTerminalId(terminalId);
        Assert.assertEquals("unbindFail", Terminal.STATUS_UNACTIVATED + "", MapUtils.getString(query, Terminal.STATUS));
        Assert.assertTrue("unbindFail", MapUtils.getString(query, Terminal.CLIENT_SN).contains("deleted"));
    }


    @Test(expected = CoreInvalidParameterException.class)
    public void testBindQrCodeTerminalRepeat() {
        Map info = new HashMap();
        String deviceFingerPrint = UUID.randomUUID().toString();
        String clientSn = UUID.randomUUID().toString();
        String terminalName = "二维码名称啊";
        String vendorAppid = UUID.randomUUID().toString();
        String vendorAppAppid = UUID.randomUUID().toString();
        info.put(Terminal.STORE_ID, storeId);
        info.put(Terminal.NAME, terminalName);
        info.put(Terminal.DEVICE_FINGERPRINT, deviceFingerPrint);
        info.put(Terminal.CLIENT_SN, clientSn);
        info.put(Terminal.VENDOR_APP_ID, vendorAppid);
        info.put(Terminal.VENDOR_APP_APPID, vendorAppAppid);
        terminalService.bindQrcodeTerminal(info);
        terminalService.bindQrcodeTerminal(info);
    }

    private Map<String, Object> createActivateTerminal() {
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String name = UUID.randomUUID().toString();
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        Map<String, Object> result = terminalService.createActivated(vendorAppAppid,
                storeSn, clientSn, deviceFp, name, osVer, sdkVer, longitude, latitude, extra);
        Assert.assertEquals("激活结果与预期不一致", "859d9f5f-af99-11e5-9ec3-00163e00625b", MapUtils.getString(result, Terminal.VENDOR_ID));
        Assert.assertEquals("激活结果与预期不一致", latitude, MapUtils.getString(result, Terminal.LATITUDE));
        Assert.assertEquals("激活结果与预期不一致", longitude, MapUtils.getString(result, Terminal.LONGITUDE));
        Assert.assertEquals("激活结果与预期不一致", extra, MapUtils.getString(result, Terminal.EXTRA));
        Assert.assertEquals("激活结果与预期不一致", name, MapUtils.getString(result, Terminal.NAME));
        Assert.assertEquals("激活结果与预期不一致", deviceFp, MapUtils.getString(result, Terminal.DEVICE_FINGERPRINT));
        Assert.assertEquals("激活结果与预期不一致", osVer, MapUtils.getString(result, Terminal.OS_VERSION));
        Assert.assertEquals("激活结果与预期不一致", sdkVer, MapUtils.getString(result, Terminal.SDK_VERSION));
        Assert.assertEquals("激活结果与预期不一致", vendorAppAppid, MapUtils.getString(result, Terminal.VENDOR_APP_APPID));
        Assert.assertEquals("激活结果与预期不一致", storeId, MapUtils.getString(result, Terminal.STORE_ID));
        Assert.assertEquals("激活结果与预期不一致", 30, MapUtils.getIntValue(result, Terminal.TYPE));
        Assert.assertEquals("激活结果与预期不一致", Terminal.STATUS_ACTIVATED, MapUtils.getIntValue(result, Terminal.STATUS));
        Assert.assertEquals("激活结果与预期不一致", storeName, MapUtils.getString(result, "store_name"));
        Assert.assertEquals("激活结果与预期不一致", storeSn, MapUtils.getString(result, "store_sn"));
        return result;
    }

    @Test(expected = CoreVendorAppAcccessDeniedException.class)
    public void testCreateActivatedDiffVendor() {
        Map origin = createStoreAndMerchant();
        String storeSn = BeanUtil.getPropString(origin, Store.SN);
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String name = UUID.randomUUID().toString();
        String vendorAppAppId = "2020062900021907";
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        terminalService.createActivated(vendorAppAppId,
                storeSn, clientSn, deviceFp, name, osVer, sdkVer, longitude, latitude, extra);
    }

    @Test(expected = CoreVendorAppNotExistsException.class)
    public void testCreateActivateNotExistVendorApp() {
        Map origin = createStoreAndMerchant();
        String storeSn = BeanUtil.getPropString(origin, Store.SN);
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String name = UUID.randomUUID().toString();
        String vendorAppAppId = UUID.randomUUID().toString();
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        terminalService.createActivated(vendorAppAppId,
                storeSn, clientSn, deviceFp, name, osVer, sdkVer, longitude, latitude, extra);
    }


    @Test
    public void testCreateActivateCode() {
        Map<String, Object> code = terminalService.createActivationCode("1", storeId, 9999);
        Assert.assertEquals("生成激活码失败", "1", MapUtils.getString(code, TerminalActivationCode.VENDOR_SN));
        Assert.assertEquals("生成激活码失败", merchantId, MapUtils.getString(code, TerminalActivationCode.MERCHANT_ID));
        Assert.assertEquals("生成激活码失败", storeId, MapUtils.getString(code, TerminalActivationCode.STORE_ID));
        Assert.assertEquals("生成激活码失败", 9999, MapUtils.getIntValue(code, TerminalActivationCode.USAGE_LIMITS));
        Assert.assertEquals("生成激活码失败", null, MapUtils.getString(code, TerminalActivationCode.DEFAULT_TERMINAL_NAME));

        code = terminalService.createActivationCode(null, storeId, 9999);
        Assert.assertEquals("生成激活码失败", "1", MapUtils.getString(code, TerminalActivationCode.VENDOR_SN));
        Assert.assertEquals("生成激活码失败", merchantId, MapUtils.getString(code, TerminalActivationCode.MERCHANT_ID));
        Assert.assertEquals("生成激活码失败", storeId, MapUtils.getString(code, TerminalActivationCode.STORE_ID));
        Assert.assertEquals("生成激活码失败", 9999, MapUtils.getIntValue(code, TerminalActivationCode.USAGE_LIMITS));
        Assert.assertEquals("生成激活码失败", null, MapUtils.getString(code, TerminalActivationCode.DEFAULT_TERMINAL_NAME));

    }


    @Test(expected = CoreVendorAccessDenied.class)
    public void testCreateActivateCodeInvalidVendorSn() {
        Map<String, Object> code = terminalService.createActivationCode("91802061", storeId, 9999);
    }


    @Test
    public void testCreateActivateCodeV2() {
        String codeName = UUID.randomUUID().toString();
        Map<String, Object> code = terminalService.createActivationCodeV2(null, null, storeSn, codeName, 9999);
        Assert.assertEquals("生成激活码失败", merchantId, MapUtils.getString(code, TerminalActivationCode.MERCHANT_ID));
        Assert.assertEquals("生成激活码失败", storeId, MapUtils.getString(code, TerminalActivationCode.STORE_ID));
        Assert.assertEquals("生成激活码失败", 9999, MapUtils.getIntValue(code, TerminalActivationCode.USAGE_LIMITS));
        Assert.assertEquals("生成激活码失败", codeName, MapUtils.getString(code, TerminalActivationCode.DEFAULT_TERMINAL_NAME));

        //相同激活码激活
        String activateCode = MapUtils.getString(code, TerminalActivationCode.CODE);
        String msg = "";
        try {
            terminalService.createActivationCodeV2(null, activateCode, storeSn, codeName, 9999);
        } catch (Exception e) {
            msg = e.getMessage();
        }
        Assert.assertEquals("相同激活码校验失败", msg, "无法使用指定的数字" + activateCode + "创建激活码");
    }


    @Test(expected = CoreSolicitorAccessDeniedException.class)
    public void testCreateActivateCodeV2WithWrongSolicitor() {
        String codeName = UUID.randomUUID().toString();
        terminalService.createActivationCodeV2(UUID.randomUUID().toString(), null, storeSn, codeName, 9999);
    }

    @Test(expected = CoreStoreNotExistsException.class)
    public void testCreateActivateCodeV2WithNoExistStore() {
        String codeName = UUID.randomUUID().toString();
        terminalService.createActivationCodeV2(UUID.randomUUID().toString(), null, UUID.randomUUID().toString(), codeName, 9999);
    }

    @Test
    public void testUpdateActivateCode() {
        Map<String, Object> result = terminalService.createActivationCodeV2(null, null, storeSn, "default-name", 9999);
        String msg = "";
        try {
            Map update = Maps.newHashMap();
            terminalService.updateActivationCode(update);
        } catch (Exception e) {
            msg = e.getMessage();
        }
        Assert.assertEquals("updateActivationCode fail", msg, "id或code至少填写一个");

        String code = MapUtils.getString(result, TerminalActivationCode.CODE);
        Map originCode = terminalActivationCodeService.getActivationInfoByCode(code);
        String id = MapUtils.getString(originCode, DaoConstants.ID);
        Map update = Maps.newHashMap();
        long expire = System.currentTimeMillis();
        int count = 20;
        update.put(DaoConstants.ID, id);
        update.put(TerminalActivationCode.REMAINING, count);
        update.put(TerminalActivationCode.EXPIRE_TIME, expire);
        update.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_EXPIRED);
        terminalService.updateActivationCode(update);

        Map queryCode = terminalActivationCodeService.getActivationInfoByCode(code);
        Assert.assertEquals("激活码更新异常", count, MapUtils.getIntValue(queryCode, TerminalActivationCode.REMAINING));
        Assert.assertEquals("激活码更新异常", expire, MapUtils.getLongValue(queryCode, TerminalActivationCode.EXPIRE_TIME));
        Assert.assertEquals("激活码更新异常", TerminalActivationCode.STATUS_EXPIRED, MapUtils.getIntValue(queryCode, TerminalActivationCode.STATUS));

        update.clear();
        expire = System.currentTimeMillis();
        count = 30;
        update.put(TerminalActivationCode.CODE, code);
        update.put(TerminalActivationCode.REMAINING, count);
        update.put(TerminalActivationCode.EXPIRE_TIME, expire);
        update.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_EXPIRED);
        terminalService.updateActivationCode(update);
        queryCode = terminalActivationCodeService.getActivationInfoByCode(code);
        Assert.assertEquals("激活码更新异常", count, MapUtils.getIntValue(queryCode, TerminalActivationCode.REMAINING));
        Assert.assertEquals("激活码更新异常", expire, MapUtils.getLongValue(queryCode, TerminalActivationCode.EXPIRE_TIME));
        Assert.assertEquals("激活码更新异常", TerminalActivationCode.STATUS_EXPIRED, MapUtils.getIntValue(queryCode, TerminalActivationCode.STATUS));


        update.clear();
        update.put(TerminalActivationCode.CODE, code);
        update.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_ACTIVE);
        terminalService.updateActivationCode(update);
        queryCode = terminalActivationCodeService.getActivationInfoByCode(code);
        Assert.assertEquals("激活码更新异常", count, MapUtils.getIntValue(queryCode, TerminalActivationCode.REMAINING));
        Assert.assertEquals("激活码更新异常", expire, MapUtils.getLongValue(queryCode, TerminalActivationCode.EXPIRE_TIME));
        Assert.assertEquals("激活码更新异常", TerminalActivationCode.STATUS_ACTIVE, MapUtils.getIntValue(queryCode, TerminalActivationCode.STATUS));

        update.clear();
        update.put(TerminalActivationCode.CODE, code);
        update.put(TerminalActivationCode.REMAINING, 30);
        terminalService.updateActivationCode(update);
        queryCode = terminalActivationCodeService.getActivationInfoByCode(code);
        Assert.assertEquals("激活码更新异常", 30, MapUtils.getIntValue(queryCode, TerminalActivationCode.REMAINING));
        Assert.assertEquals("激活码更新异常", expire, MapUtils.getLongValue(queryCode, TerminalActivationCode.EXPIRE_TIME));
        Assert.assertEquals("激活码更新异常", TerminalActivationCode.STATUS_ACTIVE, MapUtils.getIntValue(queryCode, TerminalActivationCode.STATUS));
    }


    @Test(expected = CoreInvalidParameterException.class)
    public void testUpdateActivateCodeNotFount() {
        Map update = Maps.newHashMap();
        long expire = System.currentTimeMillis();
        int count = 20;
        update.put(TerminalActivationCode.CODE, UUID.randomUUID().toString());
        update.put(TerminalActivationCode.REMAINING, count);
        update.put(TerminalActivationCode.EXPIRE_TIME, expire);
        update.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_EXPIRED);
        terminalService.updateActivationCode(update);
    }

    @Test
    public void testGetActivationCodes() {
        Map<String, Object> create = terminalService.createActivationCodeV2(null, null, storeSn, "default-name", 9999);
        String code = MapUtils.getString(create, TerminalActivationCode.CODE);
        create = terminalActivationCodeService.getActivationInfoByCode(code);
        Long ctime = MapUtils.getLong(create, DaoConstants.CTIME);
        PageInfo info = new PageInfo();
        info.setDateStart(ctime);
        info.setDateEnd(ctime + 1);
        Map queryFilter = Maps.newHashMap();
        queryFilter.put(TerminalActivationCode.STATUS, TerminalActivationCode.STATUS_ACTIVE);
        ListResult result = terminalService.getActivationCodes(merchantId, storeId, info, queryFilter);
        Assert.assertTrue(result != null && result.getRecords().size() == 1 && code.equals(MapUtils.getString(result.getRecords().get(0), TerminalActivationCode.CODE)));
        //queryFilter为空时查询
        ListResult result2 = terminalService.getActivationCodes(merchantId, storeId, info, null);
        Assert.assertEquals("testGetActivationCodes查询结果不一致", JSON.toJSONString(result), JSON.toJSONString(result2));
    }


    @Test(expected = CoreInvalidParameterException.class)
    public void testGetActivationCodesWhenInvalidParam() {
        terminalService.getActivationCodes(null, null, null, null);
    }


    @Test
    public void testGetTerminalWhenNullParam() {
        Map terminalBySn = terminalService.getTerminal(null);
        assertNull(terminalBySn);
        Map terminalById = terminalService.getTerminal(null);
        assertNull(terminalById);
    }

    @Test
    public void testGetTerminal() {
        Map<String, Object> terminal = createActivateTerminal();
        String print = MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT);
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map terminalBySn = terminalService.getTerminalBySn(terminalSn);
        Map terminalBySn1 = terminalService.getTerminalByTerminalSn(terminalSn);
        Map terminalByPrint = terminalService.getTerminalByDeviceFingerprint(print);
        Assert.assertEquals("查询结果不一致", JSON.toJSONString(terminalBySn), JSON.toJSONString(terminalBySn1));
        Assert.assertEquals("查询结果不一致", JSON.toJSONString(terminalBySn1), JSON.toJSONString(terminalByPrint));
//        Assert.assertEquals("查询结果不一致", JSON.toJSONString(terminalByPrint), JSON.toJSONString(terminalByPrint1));
        String terminalId = MapUtils.getString(terminalBySn, DaoConstants.ID);
        Map terminalById = terminalService.getTerminal(terminalId);
        Map terminalId1 = terminalService.getTerminalByTerminalId(terminalId);
        Assert.assertEquals("查询结果不一致", JSON.toJSONString(terminalByPrint), JSON.toJSONString(terminalById));
        Assert.assertEquals("查询结果不一致", JSON.toJSONString(terminalById), JSON.toJSONString(terminalId1));
        Map terminalByPrint1 = terminalService.getTerminalByDeviceFp("2017020600000069", print);
        Assert.assertEquals("查询结果不一致", terminalSn, MapUtils.getString(terminalByPrint1, Terminal.SN));
        Map response = terminalService.getTerminalActivationCodeByTerminalId(UUID.randomUUID().toString());
        assertNull(response);
        Map v1 = terminalService.getTerminal(UUID.randomUUID().toString());
        assertNull(v1);
        Map v2 = terminalService.getTerminalBySn(UUID.randomUUID().toString());
        assertNull(v2);
    }


    @Test
    public void testActivateTerminal() {
        String terminalName = UUID.randomUUID().toString();
        Map<String, Object> activateCode2 = terminalService.createActivationCode("1", storeId, 9999);
        String code2 = MapUtils.getString(activateCode2, TerminalActivationCode.CODE);
        Map<String, Object> terminal2 = terminalService.activate(null, code2, terminalName, "osx", "2.0.0", "40");
        Assert.assertEquals("激活终端不通过", storeId, MapUtils.getString(terminal2, Terminal.STORE_ID));
        Assert.assertTrue("激活终端不通过", MapUtils.getString(terminal2, Terminal.NAME).contains("New Term (activate) "));
    }

    @Test
    public void testActivateTerminalV2() {
        Map<String, Object> create = terminalService.createActivationCodeV2(null, null, storeSn, "default-name", 9999);
        String code = MapUtils.getString(create, TerminalActivationCode.CODE);
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        Map<String, Object> result = terminalService.activateV2(vendorAppAppid,
                code, clientSn, deviceFp, null, osVer, sdkVer, longitude, latitude, extra);
        Assert.assertEquals("激活结果与预期不一致", "859d9f5f-af99-11e5-9ec3-00163e00625b", MapUtils.getString(result, Terminal.VENDOR_ID));
        Assert.assertEquals("激活结果与预期不一致", latitude, MapUtils.getString(result, Terminal.LATITUDE));
        Assert.assertEquals("激活结果与预期不一致", longitude, MapUtils.getString(result, Terminal.LONGITUDE));
        Assert.assertEquals("激活结果与预期不一致", extra, MapUtils.getString(result, Terminal.EXTRA));
        Assert.assertEquals("激活结果与预期不一致", "default-name", MapUtils.getString(result, Terminal.NAME));
        Assert.assertEquals("激活结果与预期不一致", deviceFp, MapUtils.getString(result, Terminal.DEVICE_FINGERPRINT));
        Assert.assertEquals("激活结果与预期不一致", osVer, MapUtils.getString(result, Terminal.OS_VERSION));
        Assert.assertEquals("激活结果与预期不一致", sdkVer, MapUtils.getString(result, Terminal.SDK_VERSION));
        Assert.assertEquals("激活结果与预期不一致", vendorAppAppid, MapUtils.getString(result, Terminal.VENDOR_APP_APPID));
        Assert.assertEquals("激活结果与预期不一致", storeId, MapUtils.getString(result, Terminal.STORE_ID));
        Assert.assertEquals("激活结果与预期不一致", 30, MapUtils.getIntValue(result, Terminal.TYPE));
        Assert.assertEquals("激活结果与预期不一致", Terminal.STATUS_ACTIVATED, MapUtils.getIntValue(result, Terminal.STATUS));
        Assert.assertEquals("激活结果与预期不一致", storeName, MapUtils.getString(result, "store_name"));
        Assert.assertEquals("激活结果与预期不一致", storeSn, MapUtils.getString(result, "store_sn"));
    }

    @Test(expected = CoreVendorAppNotExistsException.class)
    public void testActivateTerminalV2NotExistVendorApp() {
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        terminalService.activateV2(UUID.randomUUID().toString(),
                UUID.randomUUID().toString(), clientSn, deviceFp, null, osVer, sdkVer, longitude, latitude, extra);
    }


    @Test
    public void testUnbindTerminal() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        terminal = terminalService.getTerminalBySn(terminalSn);
        String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
        terminalService.unbindTerminal(terminalId);
        terminal = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("解绑结果与预期不一致", Terminal.STATUS_UNACTIVATED, MapUtils.getIntValue(terminal, Terminal.STATUS));
        Assert.assertTrue("解绑结果与预期不一致", MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT).contains("-delete"));
        Assert.assertTrue("解绑结果与预期不一致", MapUtils.getString(terminal, Terminal.CLIENT_SN).contains("-delete"));
    }

    @Test
    public void testDisableTerminal() {
        Map<String, Object> terminal = createActivateTerminal();
        String id = MapUtils.getString(terminal, DaoConstants.ID);
        terminalService.disableTerminal(id);
        Map t = terminalService.getTerminalByTerminalId(id);
        Assert.assertEquals("解绑结果与预期不一致", Terminal.STATUS_DISABLED, MapUtils.getIntValue(t, Terminal.STATUS));
    }

    @Test
    public void testUpdateTerminal() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        terminal = terminalService.getTerminalBySn(terminalSn);
        String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
        Map update = Maps.newHashMap();
        update.put(DaoConstants.ID, terminalId);
        String terminalName = UUID.randomUUID().toString();
        update.put(Terminal.NAME, terminalName);
        terminalService.updateTerminal(update);
        terminal = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("解绑结果与预期不一致", terminalName, MapUtils.getString(terminal, Terminal.NAME));
    }

    @Test
    public void testUpdateTerminalName() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        terminal = terminalService.getTerminalBySn(terminalSn);
        String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
        Map update = Maps.newHashMap();
        update.put(ConstantUtil.KEY_TERMINAL_ID, terminalId);
        String terminalName = UUID.randomUUID().toString();
        update.put(Terminal.NAME, terminalName);
        terminalService.updateTerminalName(update);
        terminal = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("解绑结果与预期不一致", terminalName, MapUtils.getString(terminal, Terminal.NAME));
    }

    @Test
    public void testGetSecret() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        terminal = terminalService.getTerminalBySn(terminalSn);
        Map<String, Object> secret = terminalService.getSecret(terminalSn);
        Assert.assertEquals("解绑结果与预期不一致", MapUtils.getString(terminal, Terminal.SN), MapUtils.getString(secret, Terminal.SN));
        Assert.assertEquals("解绑结果与预期不一致", MapUtils.getString(terminal, Terminal.CURRENT_SECRET), MapUtils.getString(secret, Terminal.CURRENT_SECRET));
        Assert.assertEquals("解绑结果与预期不一致", MapUtils.getString(terminal, Terminal.LAST_SECRET), MapUtils.getString(secret, Terminal.LAST_SECRET));
        Assert.assertEquals("解绑结果与预期不一致", MapUtils.getString(terminal, Terminal.NEXT_SECRET), MapUtils.getString(secret, Terminal.NEXT_SECRET));
    }

    @Test
    public void testPrepareCheckIn() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        List<String> terminalSns = Arrays.asList(terminalSn, terminalSn);
        List<CompletableFuture<Map<String, Object>>> responseFuture = terminalSns.stream()
                .map(sn -> CompletableFuture.supplyAsync(
                        () -> terminalService.prepareCheckin(terminalSn)))
                .collect(Collectors.toList());
        List<String> list =  responseFuture.stream()
                .map(CompletableFuture::join) //join 操作等待所有异步操作的结果
                .map(item -> MapUtils.getString(item, Terminal.NEXT_SECRET))
                .collect(Collectors.toList());
        //判断并发情况下，两个nextSecret是否一致
        Assert.assertEquals(list.get(0), list.get(1));

        terminal = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("prepareCheckin结果与预期不一致", MapUtils.getString(terminal, Terminal.NEXT_SECRET), list.get(0));
    }

//    @Test(expected = CoreTerminalNotExistsException.class)
//    public void testPrepareCheckInWhenTerminalNotActive() {
//        Map<String, Object> terminal = createActivateTerminal();
//        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
//        terminal = terminalService.getTerminalBySn(terminalSn);
//        String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
//        terminalService.unbindTerminal(terminalId);
//        terminalService.prepareCheckin(terminalSn);
//    }

    @Test(expected = CoreTerminalNotExistsException.class)
    public void testPrepareCheckInForNotExistTerminal() {
        terminalService.prepareCheckin(UUID.randomUUID().toString());
    }

    @Test
    public void testPrepareCheckInWithTermEnvInfo() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);

        Map<String, Object> extraEnvInfo = Maps.newHashMap();
        String uuid = UUID.randomUUID().toString();
        extraEnvInfo.put("term_type", uuid);
        Map<String, Object> response = terminalService.prepareCheckinWithTermEnvInfo(terminalSn, extraEnvInfo);
        terminal = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("prepareCheckin结果与预期不一致", MapUtils.getString(terminal, Terminal.NEXT_SECRET), MapUtils.getString(response, Terminal.NEXT_SECRET));
        Assert.assertTrue("prepareCheckin结果与预期不一致 env-info不存在", JSON.toJSONString(MapUtils.getString(terminal, Terminal.EXTRA)).contains(uuid));
    }

    @Test(expected = CoreTerminalNotExistsException.class)
    public void testPrepareCheckInWithTermEnvInfoForNotExistTerminal() {
        terminalService.prepareCheckinWithTermEnvInfo(UUID.randomUUID().toString(), null);
    }


    @Test
    public void testCommitCheckIn() {
        Map<String, Object> preTerminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(preTerminal, Terminal.SN);
        Map<String, Object> response = terminalService.prepareCheckin(terminalSn);

        preTerminal = terminalService.getTerminalBySn(terminalSn);
        String nextSecret = MapUtils.getString(response, Terminal.NEXT_SECRET);

        //commit 秘钥错误时
        String msg = "";
        try {
            terminalService.commitCheckin(terminalSn, UUID.randomUUID().toString());
        } catch (Exception e) {
            msg = e.getMessage();
        }
        Assert.assertEquals("commitCheckIn错误秘钥校验异常", msg, "你传过来的终端密钥太老了");

        Map<String, Object> commitResult = terminalService.commitCheckin(terminalSn, nextSecret);
        Map<String, Object> afterTerminal = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("commitResult错误", MapUtils.getString(afterTerminal, Terminal.CURRENT_SECRET), MapUtils.getString(commitResult, Terminal.CURRENT_SECRET));
        Assert.assertEquals("commitResult错误", MapUtils.getString(preTerminal, Terminal.CURRENT_SECRET), MapUtils.getString(afterTerminal, Terminal.LAST_SECRET));
        Assert.assertEquals("commitResult错误", null, MapUtils.getString(afterTerminal, Terminal.NEXT_SECRET));
    }


    @Test(expected = CoreTerminalNotExistsException.class)
    public void testCommitCheckInNotExistTerminal() {
        terminalService.commitCheckin(UUID.randomUUID().toString(), UUID.randomUUID().toString());
    }


    @Test(expected = CoreTerminalMissingNextSecretException.class)
    public void testCommitCheckInWhenNotPrepare() {
        Map<String, Object> preTerminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(preTerminal, Terminal.SN);
        terminalService.commitCheckin(terminalSn, UUID.randomUUID().toString());
    }

    @Test
    public void testFindTerminals() {
        Map<String, Object> create = terminalService.createActivationCodeV2(null, null, storeSn, "default-name", 9999);
        String code = MapUtils.getString(create, TerminalActivationCode.CODE);
        String clientSn = UUID.randomUUID().toString();
        String deviceFp = UUID.randomUUID().toString();
        String osVer = "1.3.0";
        String sdkVer = "1.3.1";
        String longitude = "12.1";
        String latitude = "12.2";
        String extra = "1.3.1";
        Map<String, Object> terminal = terminalService.activateV2(vendorAppAppid,
                code, clientSn, deviceFp, null, osVer, sdkVer, longitude, latitude, extra);
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        terminal = terminalService.getTerminalBySn(terminalSn);
        Map filter = Maps.newHashMap();
        filter.put("statuses", Lists.newArrayList(Terminal.STATUS_ACTIVATED));
        filter.put("store_ids", Lists.newArrayList(MapUtils.getString(terminal, Terminal.STORE_ID)));
        filter.put("types", Lists.newArrayList(30));
        filter.put("types_ne", Lists.newArrayList(0));
        filter.put("vendor_app_appids", Lists.newArrayList(vendorAppAppid));
        filter.put("vendor_app_appids_ne", Lists.newArrayList("0"));
        filter.put(Terminal.NAME, MapUtils.getString(terminal, Terminal.NAME));
        filter.put(Terminal.STORE_ID, MapUtils.getString(terminal, Terminal.STORE_ID));
        filter.put(Terminal.SN, MapUtils.getString(terminal, Terminal.SN));
        filter.put(Terminal.DEVICE_FINGERPRINT, MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT));
        filter.put(Terminal.TYPE, MapUtils.getString(terminal, Terminal.TYPE));
        filter.put(Terminal.STATUS, MapUtils.getString(terminal, Terminal.STATUS));
        filter.put(Terminal.CLIENT_SN, MapUtils.getString(terminal, Terminal.CLIENT_SN));
        filter.put(Terminal.MERCHANT_ID, MapUtils.getString(terminal, Terminal.MERCHANT_ID));
        filter.put(Terminal.VENDOR_ID, MapUtils.getString(terminal, Terminal.VENDOR_ID));
        filter.put(Terminal.VENDOR_APP_ID, MapUtils.getString(terminal, Terminal.VENDOR_APP_ID));
        filter.put(Terminal.VENDOR_APP_APPID, MapUtils.getString(terminal, Terminal.VENDOR_APP_APPID));
        filter.put("showActivationCode", "yes");
        ListResult result = terminalService.findTerminals(null, filter);
        Assert.assertTrue(result != null && result.getRecords().size() == 1 && terminalSn.equals(MapUtils.getString(result.getRecords().get(0), Terminal.SN)));
        ListResult result1 = terminalService.getTerminals(merchantId, storeId, null, filter);
        Assert.assertEquals("find,get diff", JSON.toJSONString(result), JSON.toJSONString(result1));

        long count = terminalService.countTerminals(merchantId, storeId, filter);
        Assert.assertEquals("count数量有误", 1, count);
        long count2 = terminalService.countTerminals(merchantId, storeId, null);
        Assert.assertTrue("count数量有误", count2 > 0);
    }

    @Test(expected = CoreInvalidParameterException.class)
    public void testGetTerminalsInvalidArg() {
        terminalService.getTerminals("", "", null, null);
    }

    @Test(expected = CoreInvalidParameterException.class)
    public void testCountTerminalsInvalidArg() {
        terminalService.countTerminals("", "", null);
    }


    @Test(expected = CoreTerminalActivationException.class)
    public void testChangeShiftsCheckInWhenTerminalNull() {
        terminalService.changeShiftsCheckIn(UUID.randomUUID().toString());
    }


    @Test
    public void testChangeShiftsCheckIn() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map<String, Object> changeShiftsCheckIn = terminalService.changeShiftsCheckIn(terminalSn);
        String batchSn = MapUtils.getString(changeShiftsCheckIn, ChangeShifts.BATCH_SN);
        long startDate = MapUtils.getLong(changeShiftsCheckIn, ChangeShifts.START_DATE);
        Map terminalBySn = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("terminal签退签到状态有误", Terminal.CURRENT_CHECKIN_STATUS_CHECKIN, MapUtils.getIntValue(terminalBySn, Terminal.CURRENT_CHECKIN_STATUS));
        Assert.assertNotNull("bathSn不能为空", batchSn);
        Assert.assertTrue("startDate有误", startDate != 0);
        //重复签到 返回相同
        Map<String, Object> changeShiftsCheckIn2 = terminalService.changeShiftsCheckIn(terminalSn);
        Assert.assertEquals("多次签到batchSn有误", batchSn, MapUtils.getString(changeShiftsCheckIn2, ChangeShifts.BATCH_SN));
        Assert.assertEquals("startDate有误", startDate, MapUtils.getLongValue(changeShiftsCheckIn2, ChangeShifts.START_DATE));
    }


    @Test
    public void testChangeShiftsCheckInAndCheckOutAndCheckIn() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map<String, Object> changeShiftsCheckIn = terminalService.changeShiftsCheckIn(terminalSn);
        String batchSn = MapUtils.getString(changeShiftsCheckIn, ChangeShifts.BATCH_SN);
        long startDate = MapUtils.getLong(changeShiftsCheckIn, ChangeShifts.START_DATE);
        Map terminalBySn = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("terminal签退签到状态有误", Terminal.CURRENT_CHECKIN_STATUS_CHECKIN, MapUtils.getIntValue(terminalBySn, Terminal.CURRENT_CHECKIN_STATUS));
        Assert.assertNotNull("bathSn不能为空", batchSn);
        Assert.assertTrue("startDate有误", startDate != 0);
        terminalService.changeShiftsCheckOut(terminalSn, batchSn, "123");
        changeShiftsCheckIn = terminalService.changeShiftsCheckIn(terminalSn);
        Assert.assertNotEquals("签到-签退-签到 批次号未发生变化", batchSn, MapUtils.getString(changeShiftsCheckIn, ChangeShifts.BATCH_SN));
        terminalBySn = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("terminal签退签到状态有误", Terminal.CURRENT_CHECKIN_STATUS_CHECKIN, MapUtils.getIntValue(terminalBySn, Terminal.CURRENT_CHECKIN_STATUS));
    }


    @Test(expected = CoreTerminalActivationException.class)
    public void testChangeShiftsCheckOutWhenTerminalNull() {
        //终端不存在
        terminalService.changeShiftsCheckOut(UUID.randomUUID().toString(), "", "");
    }


    @Test(expected = CoreTerminalActivationException.class)
    public void testChangeShiftsCheckOutNotCheckIn() {
        //未签到，进行签退
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        terminalService.changeShiftsCheckOut(terminalSn, UUID.randomUUID().toString(), "");
    }

    @Test
    public void testChangeShiftsCheckOut() {
        //签到，签退
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map<String, Object> changeShiftsCheckIn = terminalService.changeShiftsCheckIn(terminalSn);
        String batchSn = MapUtils.getString(changeShiftsCheckIn, ChangeShifts.BATCH_SN);
        Map<String, Object> response = terminalService.changeShiftsCheckOut(terminalSn, batchSn, "123");
        Map info = terminalService.getTerminalChangeShiftsInfo(terminalSn, batchSn);
        Assert.assertEquals("endTime不一致", MapUtils.getLong(info, ChangeShifts.END_DATE), MapUtils.getLong(response, ChangeShifts.END_DATE));
        Assert.assertEquals("cashierNo不一致", "123", MapUtils.getString(info, ChangeShifts.CASHIER_NO));
        Map terminalBySn = terminalService.getTerminalBySn(terminalSn);
        Assert.assertEquals("终端签到签退状态有误", Terminal.CURRENT_CHECKIN_STATUS_CHECKOUT, MapUtils.getIntValue(terminalBySn, Terminal.CURRENT_CHECKIN_STATUS));
        //重复签退
        String exMsg = "";
        try {
            terminalService.changeShiftsCheckOut(terminalSn, batchSn, "1234"); //不同的收银员去签退，报错
        } catch (Exception e) {
            exMsg = e.getMessage();
        }
        Assert.assertTrue("终端重复签退有误", exMsg.contains("该设备已签退"));
    }

    @Test(expected = CoreTerminalActivationException.class)
    public void testGetTerminalChangeShiftsInfoWhenTerminalNotExist() {
        terminalService.getTerminalChangeShiftsInfo(UUID.randomUUID().toString(), "123");
    }

    @Test
    public void testGetTerminalChangeShiftsInfo() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map<String, Object> changeShiftsCheckIn = terminalService.changeShiftsCheckIn(terminalSn);
        String batchSn = MapUtils.getString(changeShiftsCheckIn, ChangeShifts.BATCH_SN);
        Map info = terminalService.getTerminalChangeShiftsInfo(terminalSn, batchSn);
        Assert.assertEquals(batchSn, MapUtils.getString(info, ChangeShifts.BATCH_SN));
    }

    @Test(expected = CoreTerminalActivationException.class)
    public void testGetTerminalChangeShiftsListWhenTerminalNull() {
        terminalService.getTerminalChangeShiftsList(UUID.randomUUID().toString(), null, null, null);
    }

    @Test
    public void testGetTerminalChangeShiftsList() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map<String, Object> changeShiftsCheckIn = terminalService.changeShiftsCheckIn(terminalSn);
        String batchSn = MapUtils.getString(changeShiftsCheckIn, ChangeShifts.BATCH_SN);
        ListResult result = terminalService.getTerminalChangeShiftsList(terminalSn, System.currentTimeMillis() - 1000 * 50, System.currentTimeMillis() + 1000 * 50, null);
        assertEquals(1, result.getTotal());
        terminalService.changeShiftsCheckOut(terminalSn, batchSn, "123");
        result = terminalService.getTerminalChangeShiftsList(terminalSn, System.currentTimeMillis() - 1000 * 50, System.currentTimeMillis() + 1000 * 50, null);
        assertEquals(1, result.getTotal());
    }


    @Test
    public void testGetTerminalVendorAppIdMapping() {
        Map<String, Object> mapping = terminalService.getTerminalVendorAppIdMapping();
        Assert.assertTrue("mapping size有误", mapping.size() > 0);
    }

    @Test
    public void testTransferTerminal() {
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        Map data = terminalService.getTerminalBySn(terminalSn);
        String id = MapUtils.getString(data, DaoConstants.ID);
        String newMid = UUID.randomUUID().toString();
        String newSid = UUID.randomUUID().toString();
        terminalService.transforTerminal(MapUtils.getString(data, DaoConstants.ID), newMid, newSid);
        Map result = terminalService.getTerminalByTerminalId(id);
        Assert.assertEquals("TransferTerminal error", MapUtils.getString(result, Terminal.MERCHANT_ID), newMid);
        Assert.assertEquals("TransferTerminal error", MapUtils.getString(result, Terminal.STORE_ID), newSid);
    }

    @Test
    public void testGetTerminalSnByLegacySdkParam() {
        //terminlId为null
        String nullId = terminalService.getTerminalSnByLegacySdkParam("123", "12", null);
        Assert.assertNull("terminal必须为null", nullId);
        Map<String, Object> terminal = createActivateTerminal();
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        String clientSn = MapUtils.getString(terminal, Terminal.CLIENT_SN);
        String queryTerminalSn = terminalService.getTerminalSnByLegacySdkParam(storeId, null, clientSn);
        Assert.assertEquals("testGetTerminalSnByLegacySdkParam fail", terminalSn, queryTerminalSn);
    }


    @Test
    public void testGetStoreSnByLegacySdkParam() {
        //terminlId为null
        String nullId = terminalService.getStoreSnByLegacySdkParam("123", null, null);
        Assert.assertEquals("storeSn必须为123", "123", nullId); //cacheService.getStoreMinimalInfo 不为null

        Map<String, Object> terminal = createActivateTerminal();
        String clientSn = MapUtils.getString(terminal, Terminal.CLIENT_SN);
        String queryStoreNotWithTermClientSn = terminalService.getStoreSnByLegacySdkParam(storeSn, storeClientSN, null);
        String queryStoreSnByTerminalClientSn = terminalService.getStoreSnByLegacySdkParam(storeSn, null, clientSn);
        Assert.assertEquals("queryStoreNotWithTermClientSn fail", storeSn, queryStoreNotWithTermClientSn);
        Assert.assertEquals("queryStoreSnByTerminalClientSn fail", storeSn, queryStoreSnByTerminalClientSn);
    }

    @Test
    public void testGetTerminalSnByLegacyPosSn() {
        //terminlId为null
        String nullId = terminalService.getTerminalSnByLegacyPosSn("123", null);
        Assert.assertNull("terminalSn必须为null", nullId);

        String nullId2 = terminalService.getTerminalSnByLegacyPosSn(UUID.randomUUID().toString(), null);
        Assert.assertNull("terminalSn必须为null", nullId2);

        Map<String, Object> terminal = createActivateTerminal();
        String finger = MapUtils.getString(terminal, Terminal.DEVICE_FINGERPRINT);
        String terminalSn = terminalService.getTerminalSnByLegacyPosSn(storeSn, finger);
        Assert.assertEquals("not equals", MapUtils.getString(terminal, Terminal.SN), terminalSn);
    }


    @Test
    public void testGetTerminalSnByLegacyUserId() {
        Map<String, Object> terminal = createActivateTerminal();
        String clientSn = MapUtils.getString(terminal, Terminal.CLIENT_SN);
        String terminalSn = MapUtils.getString(terminal, Terminal.SN);
        String queryTerminalSn = terminalService.getTerminalSnByLegacyUserId(storeSn, clientSn);
        Assert.assertEquals("not equals", terminalSn, queryTerminalSn);
    }

}
