package com.wosai.upay.core.service;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.bean.request.GetAnalyzedMerchantAppConfigRequest;
import com.wosai.upay.core.constant.CoreCommonConstants;
import com.wosai.upay.core.meta.Payway;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.base.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class TradeConfigServiceTest extends BaseTest {

    @Autowired
    private TradeConfigService tradeConfigService;

//    @Test
//    public void getGiftCardStatusTest(){
//        String merchantId = "";
//        assert !tradeConfigService.getGiftCardStatus(merchantId);
//        merchantId ="c1f22ed5-a095-43c3-af33-6aa3f07f2010";
//        assert tradeConfigService.getGiftCardStatus(merchantId);
//    }
//
//    @Test
//    public void updateGiftCardStatusTest(){
//        String merchantId = "00bcedba-7749-b66a-a31e-6e7ea3dd6695";
//        assert !tradeConfigService.getGiftCardStatus(merchantId);
//        tradeConfigService.updateGiftCardParams(merchantId,true);
//        assert tradeConfigService.getGiftCardStatus(merchantId);
//        tradeConfigService.updateGiftCardParams(merchantId,false);
//        assert !tradeConfigService.getGiftCardStatus(merchantId);
//    }
//
//    @Test
//    public void testAlipay(){
//        Map<String,Object> map = new HashMap<>();
//        map.put("app_id","00d38645dac40a4");
//        map.put("private_key","324a41941dd");
//        map.put("fee_rate","0.5");
//        tradeConfigService.updateAlipayV2H5TradeParams("03b1d2bf-3152-4da8-a10a-a93f6e00c90e",map);
//
//    }
//
//    @Test
//    public void testUpdateMerchantDailyPaywayMaxSumOfTrans(){
//        String merchantId = "test001";
//        int payway = 3;
//        String allSubPaywayKey = "";
//        String amount = "10020.0";
//        tradeConfigService.updateMerchantDailyPaywayMaxSumOfTrans(merchantId, payway, MapUtil.hashMap(
//                allSubPaywayKey, amount,
//                TradeConfigService.SUB_PAYWAY_WAP + "", "20000"));
//        Map<String,Object> params = tradeConfigService.getMerchantTradeValidateParams(merchantId);
//        Map<String,Object> configOfQuery = (Map<String, Object>) BeanUtil.getNestedProperty(params,TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS + "." + payway);
//        String amountOfQuery = (String) configOfQuery.get(allSubPaywayKey);
//        Assert.assertEquals(amount, amountOfQuery);
//
//    }
    @Test
    public void getAnalyzedMerchantConfigList() {
        MerchantAppConfig merchantAppConfig = new MerchantAppConfig();
        GetAnalyzedMerchantAppConfigRequest request = new GetAnalyzedMerchantAppConfigRequest();
        request.setMerchantId("7f1790b1-425e-462a-ad01");
        request.setPayWayList(Arrays.asList(2));
        List list = tradeConfigService.getAnalyzedMerchantConfigList(request);
        System.out.println(JacksonUtil.toJsonString(list));
    }
    
    @Test
    public void testMerchantAppConfigCURD() {
        // 添加业务方
        Map<String, Object> merchantAppConfig = CollectionUtil.hashMap(MerchantAppConfig.MERCHANT_ID, UUID.randomUUID().toString(),
                    MerchantAppConfig.APP_ID, "2",
                    MerchantAppConfig.PAYWAY, 3,
                    MerchantAppConfig.B2C_FORMAL, 1,
                    MerchantAppConfig.B2C_STATUS, MerchantAppConfig.STATUS_OPENED,
                    MerchantAppConfig.B2C_AGENT_NAME, "test-001"
                );
        tradeConfigService.createMerchantAppConfig(merchantAppConfig);
        assertTrue("添加业务方配置", Boolean.TRUE);
        
        Map dbMerchantAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(MapUtil.getString(merchantAppConfig, MerchantAppConfig.MERCHANT_ID), 
                MapUtil.getIntValue(merchantAppConfig, MerchantAppConfig.PAYWAY), 
                MapUtil.getString(merchantAppConfig, MerchantAppConfig.APP_ID)
                );
        assertTrue("添加业务方配置", dbMerchantAppConfig != null);

        dbMerchantAppConfig = tradeConfigService.getMerchantAppConfig(MapUtil.getString(dbMerchantAppConfig, DaoConstants.ID));
        assertTrue("查询业务方配置", dbMerchantAppConfig != null);
        assertEquals("查询业务方配置 - merchant_id", MapUtil.getString(merchantAppConfig, MerchantAppConfig.MERCHANT_ID), MapUtil.getString(dbMerchantAppConfig, MerchantAppConfig.MERCHANT_ID));
        assertEquals("查询业务方配置 - app_id", MapUtil.getString(merchantAppConfig, MerchantAppConfig.APP_ID), MapUtil.getString(dbMerchantAppConfig, MerchantAppConfig.APP_ID));
        assertEquals("查询业务方配置 - payway", MapUtil.getString(merchantAppConfig, MerchantAppConfig.PAYWAY), MapUtil.getString(dbMerchantAppConfig, MerchantAppConfig.PAYWAY));
        assertEquals("查询业务方配置 - b2c_formal", MapUtil.getBooleanValue(merchantAppConfig, MerchantAppConfig.B2C_FORMAL), MapUtil.getBooleanValue(dbMerchantAppConfig, MerchantAppConfig.B2C_FORMAL));
        assertEquals("查询业务方配置 - b2c_status", MapUtil.getBooleanValue(merchantAppConfig, MerchantAppConfig.B2C_STATUS), MapUtil.getBooleanValue(dbMerchantAppConfig, MerchantAppConfig.B2C_STATUS));
        assertEquals("查询业务方配置 - b2c_agent_name", MapUtil.getString(merchantAppConfig, MerchantAppConfig.B2C_AGENT_NAME), MapUtil.getString(dbMerchantAppConfig, MerchantAppConfig.B2C_AGENT_NAME));

        // 修改业务方
        merchantAppConfig.put(MerchantAppConfig.B2C_FORMAL, 0);
        merchantAppConfig.put(MerchantAppConfig.B2C_STATUS, MerchantAppConfig.STATUS_CLOSED);
        merchantAppConfig.put(MerchantAppConfig.B2C_AGENT_NAME, "test-001-new");
        tradeConfigService.updateMerchantAppConfig(merchantAppConfig);

        dbMerchantAppConfig = tradeConfigService.getMerchantAppConfig(MapUtil.getString(dbMerchantAppConfig, DaoConstants.ID));
        assertTrue("修改业务方配置", dbMerchantAppConfig != null);
        assertEquals("修改业务方配置 - merchant_id", MapUtil.getString(merchantAppConfig, MerchantAppConfig.MERCHANT_ID), MapUtil.getString(dbMerchantAppConfig, MerchantAppConfig.MERCHANT_ID));
        assertEquals("修改业务方配置 - b2c_formal", MapUtil.getBooleanValue(merchantAppConfig, MerchantAppConfig.B2C_FORMAL), MapUtil.getBooleanValue(dbMerchantAppConfig, MerchantAppConfig.B2C_FORMAL));
        assertEquals("修改业务方配置 - b2c_status", MapUtil.getBooleanValue(merchantAppConfig, MerchantAppConfig.B2C_STATUS), MapUtil.getBooleanValue(dbMerchantAppConfig, MerchantAppConfig.B2C_STATUS));
        assertEquals("修改业务方配置 - b2c_agent_name", MapUtil.getString(merchantAppConfig, MerchantAppConfig.B2C_AGENT_NAME), MapUtil.getString(dbMerchantAppConfig, MerchantAppConfig.B2C_AGENT_NAME));

        tradeConfigService.deleteMerchantAppConfig(MapUtil.getString(dbMerchantAppConfig, DaoConstants.ID));
        dbMerchantAppConfig = tradeConfigService.getMerchantAppConfig(MapUtil.getString(dbMerchantAppConfig, DaoConstants.ID));
        assertTrue("删除业务方配置", dbMerchantAppConfig == null);

    }

    @Test
    public void testUpdateMerchantTradeValidateParams(){
        //测试用 mch-1680001449180 1e12d53ac54c-8389-46f4-feac-ad8480af
        String merchantId = "1e12d53ac54c-8389-46f4-feac-ad8480af";
        tradeConfigService.updateMerchantTradeValidateParams(
                merchantId, MapUtil.hashMap(
                        TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS, "100"
                )
        );
        assertTrue("添加商户日限额配置", Boolean.TRUE);

        tradeConfigService.updateMerchantTradeValidateParams(
                merchantId, MapUtil.hashMap(
                        TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS, null
                )
        );
        tradeConfigService.updateMerchantTradeValidateParams(
                merchantId, MapUtil.hashMap(
                        TransactionParam.STORE_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                )
        );
        assertTrue("移除商户日限额配置", Boolean.TRUE);
        tradeConfigService.updateMerchantPaywayLimit(merchantId, Payway.UNIONPAY.getCode() + "", CoreCommonConstants.DAY, 100);
        assertTrue("修改商户云闪付日限额配置", Boolean.TRUE);

        Map merchantTradeValidateParams = tradeConfigService.getMerchantTradeValidateParams(merchantId);
        Map currentSingMap = MapUtil.getMap(merchantTradeValidateParams, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);
        Map paywayUnionLimit = MapUtil.getMap(currentSingMap, "17",new HashMap());
        assertEquals("商户云闪付日限额配置是否一致", MapUtil.getString(paywayUnionLimit,""),"100");

        tradeConfigService.updateMerchantPaywayLimit(merchantId, Payway.UNIONPAY.getCode() + "", CoreCommonConstants.DAY, null);
        assertTrue("移除商户云闪付日限额配置", Boolean.TRUE);

        merchantTradeValidateParams = tradeConfigService.getMerchantTradeValidateParams(merchantId);
        currentSingMap = MapUtil.getMap(merchantTradeValidateParams, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS);
        paywayUnionLimit = MapUtil.getMap(currentSingMap, "17",new HashMap());
        String limit = MapUtil.getString(paywayUnionLimit, "");
        assertTrue("商户云闪付日限额移除", StringUtil.isEmpty(limit) || Objects.equals(limit, Integer.MAX_VALUE+""));

    }
}
