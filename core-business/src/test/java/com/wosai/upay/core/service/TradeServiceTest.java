package com.wosai.upay.core.service;

import com.wosai.upay.core.service.base.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
public class TradeServiceTest{

//    @Autowired
//    private TradeConfigService tradeConfigService;
//
//    @Autowired
//    private SupportService supportService;
//
//    @Test
//    public void testGiftCardParams() {
//        Map basicParams = supportService.getBasicParams(null, "21000002228963");
//        System.out.println(tradeConfigService.getTradeParams(21, 1, basicParams));
//    }
//
//    @Test
//    public void testAlipayV2(){
//        tradeConfigService.updateAlipayV2AllTradeParams("d272abea-2875-11e6-bc0c-00163e00625b",null);
//    }

}
