package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.user.Account;
import com.wosai.upay.core.model.user.MerchantUser;
import com.wosai.upay.core.model.user.VendorUser;
import com.wosai.upay.core.service.base.BaseTest;
import com.wosai.upay.core.service.user.UserService;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

public class UserServiceTest extends BaseTest {

    @Autowired
    UserService userService;

    @Autowired
    JdbcTemplate userJdbcTemplate;


    private static AtomicBoolean set = new AtomicBoolean();

    private static String merchant_id = "";
    private static String account_id = "";
    private static String cellphone = "";
    private static String merchant_user_id = "";
    private static String operator_id = "";
    private static String department_id = "";

    @Before
    public void setUp() {
        if (set.compareAndSet(false, true)) {
            merchant_id = UUID.randomUUID().toString();
            account_id = UUID.randomUUID().toString();
            cellphone = UUID.randomUUID().toString().substring(0, 11);
            merchant_user_id = UUID.randomUUID().toString();
            operator_id = UUID.randomUUID().toString();
            department_id = UUID.randomUUID().toString();

            String sql1 = String.format("INSERT INTO `account` (`id`, `username`, `nickname`, `gender`, `avatar`, `password`, `cellphone`, `status`, `email`, `ctime`, `mtime`, `deleted`, `version`, `mtime_stamp`) VALUES\n" +
                            "   ('%s',   '%s',         NULL,       NULL,     NULL,     123,        '%s',         1,        NULL, *************, *************, 0, 1, null);",
                    account_id, cellphone, cellphone);
            userJdbcTemplate.update(sql1);

            String sql2 = String.format("INSERT INTO `merchant_user` (`id`, `name`, `email`, `remark`, `merchant_id`, `account_id`, `role`, `store_auth`, `operator_id`, `actived`, `status`, `ctime`, `mtime`, `deleted`, `version`)  VALUES\n" +
                    "            ( '%s', NULL, NULL, NULL, '%s', '%s', 'super_admin', 1,'%s', 1, 1, *************, *************, 0, 1);", merchant_user_id, merchant_id, account_id, operator_id);
            userJdbcTemplate.update(sql2);

            String sql3 = String.format("INSERT INTO `merchant_user_department_auth` (`id`, `merchant_id`, `merchant_user_id`, `department_id`, `ctime`, `mtime`, `deleted`, `version`) VALUES\n" +
                    " (uuid(), '%s', '%s', '%s', *************, *************, 0, 1);", merchant_id, merchant_user_id, department_id);
            userJdbcTemplate.update(sql3);

            String sn = UUID.randomUUID().toString().substring(0, 32);
            String sql4 = String.format("INSERT INTO `department` (`id`, `sn`, `name`, `parent_department_sn`, `merchant_id`, `status`, `type`, `ctime`, `mtime`, `deleted`, `version`) VALUES\n" +
                    "\t('%s', '%s', 'name1', 'psn','%s', 1, 1, *************, *************, 0, 1);", department_id, sn, merchant_id);
            userJdbcTemplate.update(sql4);
        }
    }


    @Test
    public void testGetAccount() {
        Map account = userService.getAccount(account_id);
        Assert.assertEquals("getAccountFail", account_id, MapUtils.getString(account, DaoConstants.ID));
        Assert.assertEquals("getAccountFail", cellphone, MapUtils.getString(account, Account.USERNAME));
        Assert.assertEquals("getAccountFail", "1", MapUtils.getString(account, Account.STATUS));
        Assert.assertEquals("getAccountFail", cellphone, MapUtils.getString(account, Account.CELLPHONE));
    }


    @Test
    public void testGetAccountByCellphone() {
        Map account = userService.getAccountByCellphone(cellphone);
        Assert.assertEquals("getAccountFail", account_id, MapUtils.getString(account, DaoConstants.ID));
        Assert.assertEquals("getAccountFail", cellphone, MapUtils.getString(account, Account.USERNAME));
        Assert.assertEquals("getAccountFail", "1", MapUtils.getString(account, Account.STATUS));
        Assert.assertEquals("getAccountFail", cellphone, MapUtils.getString(account, Account.CELLPHONE));
    }


    @Test
    public void testCreateAndGetVendorUser() {
        createAndGetVendorUser();
    }


    @Test
    public void testCreateAndGetVendorUserWithId() {
        String id = UUID.randomUUID().toString();
        Map<String, Object> param = Maps.newHashMap();
        param.put(VendorUser.ACCOUNT_ID, UUID.randomUUID().toString());
        param.put(VendorUser.VENDOR_ID, UUID.randomUUID().toString());
        param.put(DaoConstants.ID, id);
        userService.createVendorUser(param);
        Map user = userService.getVendorUser(id);
        Assert.assertNotNull(user);
    }


    public String createAndGetVendorUser() {
        Map<String, Object> param = Maps.newHashMap();
        param.put(VendorUser.ACCOUNT_ID, UUID.randomUUID().toString());
        param.put(VendorUser.VENDOR_ID, UUID.randomUUID().toString());
        Map vendorUser = userService.createVendorUser(param);
        String id = MapUtils.getString(vendorUser, DaoConstants.ID);
        Map user = userService.getVendorUser(id);
        Assert.assertNotNull(user);
        return id;
    }


//    createVendorUser

    @Test
    public void testDeleteVendorUser() {
        String id = createAndGetVendorUser();
        userService.deleteVendorUser(id);
        Map user = userService.getVendorUser(id);
        Assert.assertNull(user);
    }

    @Test
    public void testUpdateVendorUser() {
        String accountId = UUID.randomUUID().toString();
        String id = createAndGetVendorUser();
        Map<String, Object> param = Maps.newHashMap();
        param.put(VendorUser.ACCOUNT_ID, accountId);
        param.put(DaoConstants.ID, id);
        userService.updateVendorUser(param);
        Map user = userService.getVendorUser(id);
        Assert.assertEquals(accountId, MapUtils.getString(user, VendorUser.ACCOUNT_ID));
    }


    @Test
    public void testGetVendorUserByAccountId() {
        String id = createAndGetVendorUser();
        Map user = userService.getVendorUser(id);
        Map user2 = userService.getVendorUserByAccountId(MapUtils.getString(user, VendorUser.ACCOUNT_ID));
        Assert.assertEquals(JSON.toJSONString(user), JSON.toJSONString(user2));
    }


    @Test
    public void testDisableVendorUser() {
        String id = createAndGetVendorUser();
        Map user = userService.getVendorUser(id);
        userService.disableVendorUser(MapUtils.getString(user, DaoConstants.ID));
        user = userService.getVendorUser(id);
        Assert.assertEquals(VendorUser.STATUS_DISABLED, MapUtils.getIntValue(user, VendorUser.STATUS));
    }


    @Test
    public void testFindVendorUsers() {
        String accountId = UUID.randomUUID().toString();
        String vendorId = UUID.randomUUID().toString();
        Map<String, Object> param = Maps.newHashMap();
        param.put(VendorUser.ACCOUNT_ID, accountId);
        param.put(VendorUser.VENDOR_ID, vendorId);
        userService.createVendorUser(param);
        Map<String, Object> param2 = Maps.newHashMap();
        param2.put(VendorUser.ACCOUNT_ID, accountId);
        param2.put(VendorUser.VENDOR_ID, vendorId);
        ListResult result = userService.findVendorUsers(null, param2);
        Assert.assertEquals(1, result.getTotal());
    }

    @Test
    public void testGetMerchantUser() {
        Map user = userService.getMerchantUser(merchant_user_id);
        Assert.assertEquals(cellphone, MapUtils.getString(user, "cellphone"));
        Assert.assertEquals(merchant_id, MapUtils.getString(user, MerchantUser.MERCHANT_ID));
        Assert.assertEquals("super_admin", MapUtils.getString(user, MerchantUser.ROLE));
        Assert.assertEquals(operator_id, MapUtils.getString(user, MerchantUser.OPERATOR_ID));
        Assert.assertEquals("1", MapUtils.getString(user, MerchantUser.STATUS));
        Assert.assertEquals("1", MapUtils.getString(user, MerchantUser.STORE_AUTH));
    }

    @Test
    public void testGetMerchantUserByAccountId() {
        Map user = userService.getMerchantUserByAccountId(account_id);
        Assert.assertEquals(merchant_id, MapUtils.getString(user, MerchantUser.MERCHANT_ID));
        Assert.assertEquals("super_admin", MapUtils.getString(user, MerchantUser.ROLE));
        Assert.assertEquals(operator_id, MapUtils.getString(user, MerchantUser.OPERATOR_ID));
        Assert.assertEquals("1", MapUtils.getString(user, MerchantUser.STATUS));
        Assert.assertEquals("1", MapUtils.getString(user, MerchantUser.STORE_AUTH));
    }


    @Test
    public void testDeleteMerchantUserWhenNotExist() {
        userService.deleteMerchantUser(UUID.randomUUID().toString());
    }

    @Test
    public void testDeleteMerchantUser() {
        String id = UUID.randomUUID().toString();
        String sql2 = String.format("INSERT INTO `merchant_user` (`id`, `name`, `email`, `remark`, `merchant_id`, `account_id`, `role`, `store_auth`, `operator_id`, `actived`, `status`, `ctime`, `mtime`, `deleted`, `version`)  VALUES\n" +
                "            ( '%s', NULL, NULL, NULL, '%s', '%s', 'super_admin', 1,'%s', 1, 1, *************, *************, 0, 1);", id, UUID.randomUUID().toString(), UUID.randomUUID().toString(), UUID.randomUUID().toString());
        userJdbcTemplate.update(sql2);
        userService.deleteMerchantUser(id);
        Map result = userService.getMerchantUser(id);
        Assert.assertNull(result);
    }


    @Test
    public void testGetMerchantSuperAdminUserAccount() {
        Map account = userService.getMerchantSuperAdminUserAccount(merchant_id);
        Assert.assertNotNull(account);
        Assert.assertEquals(account_id, MapUtils.getString(account, DaoConstants.ID));
    }

    @Test
    public void testGetMerchantSuperAdminUserAccountNotExist() {
        Map account = userService.getMerchantSuperAdminUserAccount(UUID.randomUUID().toString());
        Assert.assertNull(account);
    }

    @Test
    public void testGetMerchantUserStoreAuths() {
        Map<String, Object> param = Maps.newHashMap();
        param.put(MerchantUser.ACCOUNT_ID, account_id);
        List<Map> auths = userService.getMerchantUserStoreAuths(param);
        Assert.assertEquals(0, auths.size());
    }

    @Test
    public void testGetMerchantUserStoreAuthsWhenNotExist() {
        Map<String, Object> param = Maps.newHashMap();
        param.put(MerchantUser.ACCOUNT_ID, UUID.randomUUID().toString());
        List<Map> auths = userService.getMerchantUserStoreAuths(param);
        Assert.assertEquals(0, auths.size());
    }


    @Test
    public void testFindMerchantUserStoreAuths() {
        Map<String, Object> param = Maps.newHashMap();
        param.put(MerchantUser.MERCHANT_ID, merchant_id);
        param.put("store_ids", Lists.newArrayList(UUID.randomUUID().toString()));
        ListResult result = userService.findMerchantUserStoreAuths(null, param);
        Assert.assertEquals(0, result.getTotal());
    }

    @Test
    public void testGetMerchantUserDepartmentAuths() {
        Map<String, Object> param = Maps.newHashMap();
        param.put(MerchantUser.ACCOUNT_ID, account_id);
        List<Map> auths = userService.getMerchantUserDepartmentAuths(param);
        Assert.assertEquals(1, auths.size());

        param = Maps.newHashMap();
        param.put(MerchantUser.ACCOUNT_ID, UUID.randomUUID().toString());
        auths = userService.getMerchantUserDepartmentAuths(param);
        Assert.assertEquals(0, auths.size());
    }

    @Test
    public void testFindMerchantUserDepartmentAuths() {
        Map<String, Object> param = Maps.newHashMap();
        param.put(MerchantUser.MERCHANT_ID, merchant_id);
        param.put("department_ids", Lists.newArrayList(department_id));
        ListResult result = userService.findMerchantUserDepartmentAuths(null, param);
        Assert.assertEquals(1, result.getTotal());
    }
}
