package com.wosai.upay.core.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Vendor;
import com.wosai.upay.core.model.VendorApp;
import com.wosai.upay.core.model.VendorConfig;
import com.wosai.upay.core.model.VendorDeveloper;
import com.wosai.upay.core.service.base.BaseTest;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.UUID;


public class VendorServiceTest extends BaseTest {


    @Autowired
    private VendorService vendorService;

    @Test
    public void testCreateVendorComplete() {
        createVendorComplete();
    }

    private Map createVendorComplete() {
        String street_address = "笋岗街道笋西社区桃园路260号HALO广场四期";
        String contact_name = "eric（田茂宇）";
        String account_id = UUID.randomUUID().toString();
        String province = "广东省";
        String city = "深圳市";
        String district = "罗湖区";
        Map extra = ImmutableMap.of("remark", "开发者开发语言：c#");
        String name = "深圳市阿卡索资讯股份有限公司";
        String cellphone = "175****5156";
        String contact_cellphone = "175****5156";
        String contact_email = "<EMAIL>";
        Map vendor = Maps.newHashMap();
        vendor.put(Vendor.STREET_ADDRESS, street_address);
        vendor.put(Vendor.CONTACT_NAME, contact_name);
        vendor.put(Vendor.PROVINCE, province);
        vendor.put(Vendor.CITY, city);
        vendor.put(Vendor.DISTRICT, district);
        vendor.put(Vendor.NAME, name);
        vendor.put(Vendor.EXTRA, extra);
        vendor.put(Vendor.CELLPHONE, cellphone);
        vendor.put(Vendor.CONTACT_CELLPHONE, contact_cellphone);
        vendor.put(Vendor.CONTACT_EMAIL, contact_email);
        vendor.put("account_id", account_id);
        Map rs = vendorService.createVendorComplete(vendor);
        String vendorId = MapUtils.getString(rs, DaoConstants.ID);
        Map query = vendorService.getVendor(vendorId);
        Assert.assertEquals("createCompleteVendorError ", street_address, MapUtils.getString(query, Vendor.STREET_ADDRESS));
        Assert.assertEquals("createCompleteVendorError ", contact_name, MapUtils.getString(query, Vendor.CONTACT_NAME));
        Assert.assertEquals("createCompleteVendorError ", province, MapUtils.getString(query, Vendor.PROVINCE));
        Assert.assertEquals("createCompleteVendorError ", city, MapUtils.getString(query, Vendor.CITY));
        Assert.assertEquals("createCompleteVendorError ", district, MapUtils.getString(query, Vendor.DISTRICT));
        Assert.assertEquals("createCompleteVendorError ", name, MapUtils.getString(query, Vendor.NAME));
        Assert.assertEquals("createCompleteVendorError ", JSON.toJSONString(extra), JSON.toJSONString(MapUtils.getMap(query, Vendor.EXTRA)));
        Assert.assertEquals("createCompleteVendorError ", cellphone, MapUtils.getString(query, Vendor.CELLPHONE));
        Assert.assertEquals("createCompleteVendorError ", contact_cellphone, MapUtils.getString(query, Vendor.CONTACT_CELLPHONE));
        Assert.assertEquals("createCompleteVendorError ", contact_email, MapUtils.getString(query, Vendor.CONTACT_EMAIL));
        Assert.assertEquals("createCompleteVendorError ", Vendor.STATUS_ENABLED, MapUtils.getIntValue(query, Vendor.STATUS));
        return query;
    }

    @Test
    public void testCreateAndDeleteVendorById() {
        Map vendor = createVendor();
        String id = MapUtils.getString(vendor, DaoConstants.ID);
        vendorService.deleteVendor(id);
    }

    @Test
    public void testCreateAndDeleteVendorBysn() {
        Map vendor = createVendor();
        String sn = MapUtils.getString(vendor, Vendor.SN);
        vendorService.deleteVendorBySn(sn);
    }

    @Test
    public void testUpdateVendor() {
        String street_address = "笋岗街道笋西社区桃园路260号HALO广场四期2";
        String contact_name = "eric（田茂宇）2";
        String province = "广东省2";
        String city = "深圳市2";
        String district = "罗湖区2";
        Map extra = ImmutableMap.of("remark", "开发者开发语言：c#2");
        String name = "深圳市阿卡索资讯股份有限公司2";
        String cellphone = "175****51562";
        String contact_cellphone = "175****51562";
        String contact_email = "865704613@qq.com2";
        Map vendor = Maps.newHashMap();
        vendor.put(Vendor.STREET_ADDRESS, street_address);
        vendor.put(Vendor.CONTACT_NAME, contact_name);
        vendor.put(Vendor.PROVINCE, province);
        vendor.put(Vendor.CITY, city);
        vendor.put(Vendor.DISTRICT, district);
        vendor.put(Vendor.NAME, name);
        vendor.put(Vendor.EXTRA, extra);
        vendor.put(Vendor.CELLPHONE, cellphone);
        vendor.put(Vendor.CONTACT_CELLPHONE, contact_cellphone);
        vendor.put(Vendor.CONTACT_EMAIL, contact_email);
        vendor.put(Vendor.STATUS, Vendor.STATUS_DISABLED);

        Map vendor1 = createVendor();
        vendor.put(DaoConstants.ID, MapUtils.getString(vendor1, DaoConstants.ID));
        vendorService.updateVendor(vendor);
        Map updateResult = vendorService.getVendor(MapUtils.getString(vendor1, DaoConstants.ID));

        Assert.assertEquals("update vendor error ", street_address, MapUtils.getString(updateResult, Vendor.STREET_ADDRESS));
        Assert.assertEquals("update vendor error ", contact_name, MapUtils.getString(updateResult, Vendor.CONTACT_NAME));
        Assert.assertEquals("update vendor error ", province, MapUtils.getString(updateResult, Vendor.PROVINCE));
        Assert.assertEquals("update vendor error ", city, MapUtils.getString(updateResult, Vendor.CITY));
        Assert.assertEquals("update vendor error ", district, MapUtils.getString(updateResult, Vendor.DISTRICT));
        Assert.assertEquals("update vendor error ", name, MapUtils.getString(updateResult, Vendor.NAME));
        Assert.assertEquals("update vendor error ", JSON.toJSONString(extra), JSON.toJSONString(MapUtils.getMap(updateResult, Vendor.EXTRA)));
        Assert.assertEquals("update vendor error ", cellphone, MapUtils.getString(updateResult, Vendor.CELLPHONE));
        Assert.assertEquals("update vendor error ", contact_cellphone, MapUtils.getString(updateResult, Vendor.CONTACT_CELLPHONE));
        Assert.assertEquals("update vendor error ", contact_email, MapUtils.getString(updateResult, Vendor.CONTACT_EMAIL));
        Assert.assertEquals("update vendor error ", Vendor.STATUS_ENABLED, MapUtils.getIntValue(updateResult, Vendor.STATUS));
    }


    @Test
    public void testGetVendorWhenNull() {
        Map rs = vendorService.getVendor(null);
        Assert.assertNull(rs);
        Map rs2 = vendorService.getVendorBySn(null);
        Assert.assertNull(rs2);
    }

    public void testGetVendorBySn() {
        Map vendor = createVendor();
        String id = MapUtils.getString(vendor, DaoConstants.ID);
        String sn = MapUtils.getString(vendor, Vendor.SN);
        Map vendorBySn = vendorService.getVendorBySn(sn);
        Map vendorById = vendorService.getVendor(id);
        Assert.assertEquals("getVendorBySn fail", JSON.toJSONString(vendorById), JSON.toJSONString(vendorBySn));
    }


    @Test
    public void testDisableAndEnableCloseVendor() {
        Map vendor = createVendor();
        String id = MapUtils.getString(vendor, DaoConstants.ID);
        vendorService.disableVendor(id);
        Map v = vendorService.getVendor(id);
        Assert.assertEquals("disable fail", Vendor.STATUS_DISABLED, MapUtils.getIntValue(v, Vendor.STATUS));
        vendorService.enableVendor(id);
        v = vendorService.getVendor(id);
        Assert.assertEquals("enable fail", Vendor.STATUS_ENABLED, MapUtils.getIntValue(v, Vendor.STATUS));
        vendorService.closeVendor(id);
        v = vendorService.getVendor(id);
        Assert.assertEquals("close fail", Vendor.STATUS_CLOSED, MapUtils.getIntValue(v, Vendor.STATUS));
    }


    @Test
    public void testFindVendors() {
        Map vendor = createVendor();
        Map filter = Maps.newHashMap();
        filter.put(Vendor.NAME, "阿卡索资讯股份");
        filter.put(Vendor.SN, MapUtils.getString(vendor, Vendor.SN));
        filter.put(Vendor.CONTACT_CELLPHONE, MapUtils.getString(vendor, Vendor.CONTACT_CELLPHONE));
        filter.put(Vendor.CONTACT_PHONE, MapUtils.getString(vendor, Vendor.CONTACT_PHONE));
        ListResult result = vendorService.findVendors(null, filter);
        Assert.assertNotNull(result);
        Assert.assertEquals("length fail", 1, result.getRecords().size());
    }


    @Test
    public void testResetAndGetAppKey() {
        Map vendor = createVendorComplete();
        String vendorSn = MapUtils.getString(vendor, Vendor.SN);

        Map developer = vendorService.getVendorDeveloperByVendorSn(vendorSn);
        Assert.assertNotNull(developer);

        Map developerByPk = vendorService.getVendorDeveloperByVendorId(MapUtils.getString(vendor, DaoConstants.ID));
        Assert.assertEquals("getVendorDeveloperByVendorSn fail", JSON.toJSONString(developer), JSON.toJSONString(developerByPk));

        String developKey = vendorService.getVendorAppKeyByVendorSn(vendorSn);
        Assert.assertEquals("develop key not equals", MapUtils.getString(developer, VendorDeveloper.APP_KEY), developKey);
        String developKey2 = vendorService.getAppKey(vendorSn);
        Assert.assertEquals("getAppKey key not equals", developKey, developKey2);

        String resetAppKey = vendorService.resetAppKey(vendorSn);
        String resetResultKey = vendorService.getAppKey(vendorSn);
        Assert.assertEquals("resetAppKey key not equals", resetAppKey, resetResultKey);
    }


    @Test
    public void testCreateDeveloper() {
        Map vendor = createVendor();
        String id = MapUtils.getString(vendor, DaoConstants.ID);
        Map create = Maps.newHashMap();
        create.put(VendorDeveloper.VENDOR_ID, id);
        Map vendorDeveloper = vendorService.createVendorDeveloper(create);
        Assert.assertNotNull(vendorDeveloper);
        Assert.assertNotNull(MapUtils.getString(vendorDeveloper, VendorDeveloper.APP_KEY));
    }

    @Test
    public void testUpdateDeveloper() {
        Map vendor = createVendor();
        String id = MapUtils.getString(vendor, DaoConstants.ID);
        Map create = Maps.newHashMap();
        create.put(VendorDeveloper.VENDOR_ID, id);
        Map vendorDeveloper = vendorService.createVendorDeveloper(create);

        String developId = MapUtils.getString(vendorDeveloper, DaoConstants.ID);

        Map update = Maps.newHashMap();
        update.put(DaoConstants.ID, developId);
        update.put(VendorDeveloper.APP_KEY, "123");
        vendorService.updateVendorDeveloper(update);
        Map dep = vendorService.getVendorDeveloperByVendorId(id);
        Assert.assertEquals("update fail ", "123", MapUtils.getString(dep, VendorDeveloper.APP_KEY));
    }

    @Test
    public void testFindVendorDevelopers() {
        Map vendor = createVendorComplete();
        Map filter = Maps.newHashMap();
        filter.put(VendorDeveloper.VENDOR_ID, MapUtils.getString(vendor, DaoConstants.ID));
        ListResult result = vendorService.findVendorDevelopers(null, filter);
        Assert.assertNotNull(result);
        Assert.assertEquals("length fail", 1, result.getRecords().size());
    }


    @Test
    public void testJustForQuery() {
        Map vendor = createVendor();
        vendorService.getVendorTranslateInfoById(MapUtils.getString(vendor, DaoConstants.ID));
        vendorService.getVendorTranslateInfoBySn(MapUtils.getString(vendor, Vendor.SN));
        vendorService.getVendorAppTypesAndDesc();
    }


    @Test
    public void testGetVendorConfigByIdAndFindConfigs() {
        Map vendor = createVendorComplete();
        Map configId = vendorService.getVendorConfigByVendorId(MapUtils.getString(vendor, DaoConstants.ID));
        Assert.assertNotNull("config must not null", configId);
        Map filter = Maps.newHashMap();
        filter.put(VendorDeveloper.VENDOR_ID, MapUtils.getString(vendor, DaoConstants.ID));
        ListResult result = vendorService.findVendorConfigs(null, filter);
        Assert.assertNotNull(result);
        Assert.assertEquals("length fail", 1, result.getRecords().size());
    }

    @Test
    public void testUpdateVendorConfig() {
        Map vendor = createVendorComplete();
        String vendorId = MapUtils.getString(vendor, DaoConstants.ID);
        Map configId = vendorService.getVendorConfigByVendorId(vendorId);
        Map update = Maps.newHashMap();
        update.put(DaoConstants.ID, MapUtils.getString(configId, DaoConstants.ID));
        Map<String, String> m = ImmutableMap.of("json", UUID.randomUUID().toString());
        update.put(VendorConfig.PARAMS, m);
        vendorService.updateVendorConfig(update);
        Map updateResult = vendorService.getVendorConfigByVendorId(vendorId);
        Assert.assertEquals("update vendor config fail", JSON.toJSONString(m), JSON.toJSONString(MapUtils.getMap(updateResult, VendorConfig.PARAMS)));
    }


    @Test
    public void testVendorApps() {
        Map vendor = createVendor();
        String vendorId = MapUtils.getString(vendor, DaoConstants.ID);

        Map create = Maps.newHashMap();
        String name = UUID.randomUUID().toString();
        create.put(ConstantUtil.KEY_VENDOR_ID, vendorId);
        create.put(VendorApp.NAME, name);
        create.put(VendorApp.TYPE, 40);
        Map createResult = vendorService.createVendorApp(create);
        Map queryApp = vendorService.getVendorApp(MapUtils.getString(createResult, DaoConstants.ID));
        Assert.assertEquals("create query not equals", MapUtils.getString(create, VendorApp.APPKEY), MapUtils.getString(queryApp, VendorApp.APPKEY));
        Assert.assertEquals("create query not equals", MapUtils.getString(create, VendorApp.APPID), MapUtils.getString(queryApp, VendorApp.APPID));
        Assert.assertEquals("create query not equals", MapUtils.getString(create, VendorApp.NAME), MapUtils.getString(queryApp, VendorApp.NAME));

        Map filter = Maps.newHashMap();
        filter.put(VendorApp.VENDOR_ID, vendorId);
        filter.put(VendorApp.NAME, name);
        ListResult result = vendorService.findVendorApps(null, filter);
        Assert.assertNotNull(result);
        Assert.assertEquals("length fail", 1, result.getRecords().size());


        Map update = Maps.newHashMap();
        name = UUID.randomUUID().toString();
        update.put(DaoConstants.ID, MapUtils.getString(createResult, DaoConstants.ID));
        update.put(VendorApp.NAME, name);
        vendorService.updateVendorApp(update);
        queryApp = vendorService.getVendorApp(MapUtils.getString(createResult, DaoConstants.ID));
        Assert.assertEquals("update fail", name, MapUtils.getString(queryApp, VendorApp.NAME));


        vendorService.getVendorAppTranslateInfoById(MapUtils.getString(createResult, DaoConstants.ID));
        vendorService.getVendorAppTranslateInfoByAppId(MapUtils.getString(queryApp, VendorApp.APPID));


    }


    private Map createVendor() {
        String street_address = "笋岗街道笋西社区桃园路260号HALO广场四期";
        String contact_name = "eric（田茂宇）";
        String province = "广东省";
        String city = "深圳市";
        String district = "罗湖区";
        Map extra = ImmutableMap.of("remark", "开发者开发语言：c#");
        String name = "深圳市阿卡索资讯股份有限公司";
        String cellphone = "175****5156";
        String contact_phone = "175****5157";
        String contact_cellphone = "175****5156";
        String contact_email = "<EMAIL>";
        Map vendor = Maps.newHashMap();
        vendor.put(Vendor.STREET_ADDRESS, street_address);
        vendor.put(Vendor.CONTACT_NAME, contact_name);
        vendor.put(Vendor.PROVINCE, province);
        vendor.put(Vendor.CITY, city);
        vendor.put(Vendor.DISTRICT, district);
        vendor.put(Vendor.NAME, name);
        vendor.put(Vendor.EXTRA, extra);
        vendor.put(Vendor.CELLPHONE, cellphone);
        vendor.put(Vendor.CONTACT_PHONE, contact_phone);
        vendor.put(Vendor.CONTACT_CELLPHONE, contact_cellphone);
        vendor.put(Vendor.CONTACT_EMAIL, contact_email);
        Map rs = vendorService.createVendor(vendor);
        String vendorId = MapUtils.getString(rs, DaoConstants.ID);
        Map query = vendorService.getVendor(vendorId);
        Assert.assertEquals("create vendor error", street_address, MapUtils.getString(query, Vendor.STREET_ADDRESS));
        Assert.assertEquals("create vendor error ", contact_name, MapUtils.getString(query, Vendor.CONTACT_NAME));
        Assert.assertEquals("create vendor error", province, MapUtils.getString(query, Vendor.PROVINCE));
        Assert.assertEquals("create vendor error ", city, MapUtils.getString(query, Vendor.CITY));
        Assert.assertEquals("create vendor error ", district, MapUtils.getString(query, Vendor.DISTRICT));
        Assert.assertEquals("create vendor error ", name, MapUtils.getString(query, Vendor.NAME));
        Assert.assertEquals("create vendor error ", JSON.toJSONString(extra), JSON.toJSONString(MapUtils.getMap(query, Vendor.EXTRA)));
        Assert.assertEquals("create vendor error ", cellphone, MapUtils.getString(query, Vendor.CELLPHONE));
        Assert.assertEquals("create vendor error ", contact_cellphone, MapUtils.getString(query, Vendor.CONTACT_CELLPHONE));
        Assert.assertEquals("create vendor error ", contact_phone, MapUtils.getString(query, Vendor.CONTACT_PHONE));
        Assert.assertEquals("create vendor error ", contact_email, MapUtils.getString(query, Vendor.CONTACT_EMAIL));
        return query;
    }


}
