package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 支付宝交易参数获取
 * 
 */
public class AlipayIntlAllParamsTest extends AllParamsBaseTest{
    
    /**
     * 支付宝国际版交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        String clientId = "TESTCASE_ALIPAY_INTL_CLIENT_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String privatekey = "TESTCASE_ALIPAY_INTL_PRIVATE_KEY" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String merchantId = "TESTCASE_ALIPAY_INTL_MERCHANT_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String merchantAlipayIndustry = "TESTCASE_MERCHANT_ALIPAY_INDUSTRY" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.ALIPAY_INTL_CLIENT_ID, clientId), 
                ImmutablePair.of(TransactionParam.ALIPAY_INTL_PRIVATE_KEY, privatekey),
                ImmutablePair.of(TransactionParam.ALIPAY_INTL_MERCHANT_ID, merchantId),
                ImmutablePair.of(TransactionParam.MERCHANT_ALIPAY_INDUSTRY, merchantAlipayIndustry)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchant(
                            MapUtil.hashMap(DaoConstants.ID, MERCHANT_ID,
                                    Merchant.CURRENCY, "HKD"
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY_INTL,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.B2C_AGENT_NAME, "*_20_*_true_false_0233",
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_INTL_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝国际版直连b2c交易参数", allParams);
        assertTrue("获取支付宝国际版直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_INTL_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_INTL_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝国际版直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertEquals("获取支付宝国际版直连c2b交易参数失败:currency", "HKD", MapUtil.getString(allParams, TransactionParam.CURRENCY));
    }
}
