package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 支付宝交易参数获取
 * 
 */
public class AlipayV1AllParamsTest extends AllParamsBaseTest{
    
    /**
     * 支付宝直连交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        String agentId = "TESTCASE_AGENT_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String appKey = "TESTCASE_APP_KEY" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String alipayIndustry = "TESTCASE_MERCHANT_ALIPAY_INDUSTRY" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String partner = "TESTCASE_PARTNER" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.AGENT_ID, agentId), 
                ImmutablePair.of(TransactionParam.APP_KEY, appKey),
                ImmutablePair.of(TransactionParam.MERCHANT_ALIPAY_INDUSTRY, alipayIndustry),
                ImmutablePair.of(TransactionParam.PARTNER, partner),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V1_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v1_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V1_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V1_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
    }
}
