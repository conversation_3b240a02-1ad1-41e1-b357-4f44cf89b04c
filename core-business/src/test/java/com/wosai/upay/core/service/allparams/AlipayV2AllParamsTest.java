package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MerchantConfigCustom;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 支付宝交易参数获取
 * 
 */
public class AlipayV2AllParamsTest extends AllParamsBaseTest{
    
    /**
     * 支付宝直连交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        // 1、使用merchant_config方式获取交易参数
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        
        //2、使用merchant_config_custom配置seller_id和store_id
        String newSellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String storeId = "TESTCASE_store_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        List<ImmutablePair<String, String>> newChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, newSellerId),
                ImmutablePair.of(TransactionParam.ALIPAY_STORE_ID, storeId)
        );
        allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, newChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID,
                                    MerchantConfigCustom.B2C_VALUE, newSellerId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                                    MerchantConfigCustom.B2C_VALUE, storeId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : newChecks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
    }
    
    /**
     * 支付宝直连交易c2b参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test2() throws Exception {
        // 1、使用merchant_config方式获取交易参数
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_QRCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.C2B_FORMAL, 1,
                                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.C2B_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连c2b交易参数", allParams);
        assertTrue("获取支付宝直连c2b交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连c2b交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        
        //2、使用merchant_config_custom配置seller_id和store_id
        String newSellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String storeId = "TESTCASE_store_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        List<ImmutablePair<String, String>> newChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, newSellerId),
                ImmutablePair.of(TransactionParam.ALIPAY_STORE_ID, storeId)
        );
        allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_QRCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.C2B_FORMAL, 1,
                                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.C2B_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, newChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID,
                                    MerchantConfigCustom.C2B_VALUE, newSellerId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                                    MerchantConfigCustom.C2B_VALUE, storeId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连c2b交易参数", allParams);
        assertTrue("获取支付宝直连c2b交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : newChecks) {
            assertEquals(String.format("获取支付宝直连c2b交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
    }
    
    /**
     * 支付宝直连交易wap参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test3() throws Exception {
        // 1、使用merchant_config方式获取交易参数
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_WAP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.WAP_FORMAL, 1,
                                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.WAP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连wap交易参数", allParams);
        assertTrue("获取支付宝直连wap交易参数失败:alipay_wap_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连wap交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        
        //2、使用merchant_config_custom配置sell_id和store_id
        String newSellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String storeId = "TESTCASE_store_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        List<ImmutablePair<String, String>> newChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, newSellerId),
                ImmutablePair.of(TransactionParam.ALIPAY_STORE_ID, storeId)
        );
        allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_WAP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.WAP_FORMAL, 1,
                                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.WAP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, newChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID,
                                    MerchantConfigCustom.WAP_VALUE, newSellerId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                                    MerchantConfigCustom.WAP_VALUE, storeId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连wap交易参数", allParams);
        assertTrue("获取支付宝直连wap交易参数失败:alipay_wap_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS));
        alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : newChecks) {
            assertEquals(String.format("获取支付宝直连wap交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
    }
    
    /**
     * TODO FIXME 支付宝2.0使用的是支付宝wap交易参数，后期如果有变动，对应的测试用例也需要变更
     * 支付宝直连交易mini参数获取
     * 
     * @throws Exception 
     * 
     */
    
    @Test
    public void test4() throws Exception {
        // 1、使用merchant_config方式获取交易参数
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_WAP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.WAP_FORMAL, 1,
                                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.WAP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连mini交易参数", allParams);
        assertTrue("获取支付宝直连mini交易参数失败:alipay_wap_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连mini交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        
        //2、使用merchant_config_custom配置sell_id和store_id
        String newSellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String storeId = "TESTCASE_store_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        List<ImmutablePair<String, String>> newChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, newSellerId),
                ImmutablePair.of(TransactionParam.ALIPAY_STORE_ID, storeId)
        );
        allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_WAP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.WAP_FORMAL, 1,
                                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.WAP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS, newChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_SELLER_ID,
                                    MerchantConfigCustom.WAP_VALUE, newSellerId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_ALIPAY_STORE_ID,
                                    MerchantConfigCustom.WAP_VALUE, storeId,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连mini交易参数", allParams);
        assertTrue("获取支付宝直连mini交易参数失败:alipay_wap_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS));
        alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : newChecks) {
            assertEquals(String.format("获取支付宝直连mini交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
    }

    /**
     * 支付宝直连交易app参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test5() throws Exception {
        String appId = "TESTCASE_app_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String signType = "TESTCASE_sign_type" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String privateKey = "TESTCASE_private_key" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_APP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.APP_FORMAL, 1,
                                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.APP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS, MapUtil.hashMap(
                                                            TransactionParam.APP_ID, appId,
                                                            TransactionParam.PRIVATE_KEY, privateKey,
                                                            TransactionParam.SIGN_TYPE, signType
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连app交易参数", allParams);
        assertTrue("获取支付宝直连app交易参数失败:alipay_app_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_ID, appId), 
                ImmutablePair.of(TransactionParam.PRIVATE_KEY, privateKey),
                ImmutablePair.of(TransactionParam.SIGN_TYPE, signType),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连app交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }

    /**
     * 支付宝直连交易h5参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test6() throws Exception {
        String appId = "TESTCASE_app_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String signType = "TESTCASE_sign_type" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String privateKey = "TESTCASE_private_key" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_H5), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.H5_FORMAL, 1,
                                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.H5_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS, MapUtil.hashMap(
                                                            TransactionParam.APP_ID, appId,
                                                            TransactionParam.PRIVATE_KEY, privateKey,
                                                            TransactionParam.SIGN_TYPE, signType
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连h5交易参数", allParams);
        assertTrue("获取支付宝直连h5交易参数失败:alipay_h5_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_ID, appId), 
                ImmutablePair.of(TransactionParam.PRIVATE_KEY, privateKey),
                ImmutablePair.of(TransactionParam.SIGN_TYPE, signType),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连h5交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }

    /**
     * 上海兴业支付宝交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test7() throws Exception {
        String mchiId = "TESTCASE_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.SWIFTPASS_MCH_ID, mchiId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );

        String cibshbankTradeParams = "cibshbank_trade_params";
        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                cibshbankTradeParams, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
        );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_AGENT_NAME, "1015_*_1_false_false_0003",
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CIBSHBANK,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_AGENT_NAME, "1015_*_1_false_false_0003",
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CIBSHBANK,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY2, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取上海兴业支付宝交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取上海兴业支付宝交易参数失败:cibshbank_trade_params", subpayway), allParams.containsKey(cibshbankTradeParams));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, cibshbankTradeParams);
            assertFalse(String.format("subpayway:%s 获取上海兴业支付宝交易参数失败:liquidation_next_day", subpayway), MapUtil.getBooleanValue(weixinTradeParams, TransactionParam.LIQUIDATION_NEXT_DAY));
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取上海兴业支付宝交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 拉卡拉银联支付宝交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test8() throws Exception {
        String alipaySubMchId = "TESTCASE_alipay_sub_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String serviceId = "TESTCASE_service_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_DIRECT_ALIPAY_SUB_MCH_ID, alipaySubMchId), 
                ImmutablePair.of(TransactionParam.ALIPAY_SERVICE_ID, serviceId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_MINI, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY2, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取拉卡拉银联支付宝交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取拉卡拉银联支付宝交易参数失败:lkl_up_trade_params", subpayway), allParams.containsKey(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取拉卡拉银联支付宝交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 通联银联支付宝交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test9() throws Exception {
        String alipaySubMchId = "TESTCASE_alipay_sub_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String providerMchId = "TESTCASE_PROVIDER_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_ALIPAY_SUB_MCH_ID, alipaySubMchId), 
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_PROVIDER_MCH_ID, providerMchId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.UNION_PAY_TL_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_MINI, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY2, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL,
                                                TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取通联支付宝交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取通联联支付宝交易参数失败:up_tl_trade_params", subpayway), allParams.containsKey(TransactionParam.UNION_PAY_TL_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取通联支付宝交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 支付宝交易b2c花呗参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test10() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        int huabeiLimit = ThreadLocalRandom.current().nextInt(6000000);

        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_OPENED,
                                            TransactionParam.ALIPAY_HUABEI_LIMIT, huabeiLimit
                                            
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c花呗交易参数", allParams);
        assertTrue("获取支付宝直连b2c花呗交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c花呗交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertTrue("获取支付宝直连b2c花呗交易参数失败:alipay_huabei_status", MapUtil.getBooleanValue(allParams, TransactionParam.ALIPAY_HUABEI_STATUS));
        assertEquals("获取支付宝直连b2c花呗交易参数失败:alipay_huabei_limit", huabeiLimit, MapUtil.getIntValue(allParams, TransactionParam.ALIPAY_HUABEI_LIMIT));
        assertNotNull("获取支付宝直连b2c花呗交易参数失败:alipay_huabei_params", MapUtil.getObject(allParams, TransactionParam.ALIPAY_HUABEI_PARAMS));
    }
}
