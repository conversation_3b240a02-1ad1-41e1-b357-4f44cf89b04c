package com.wosai.upay.core.service.allparams;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;

import com.wosai.data.dao.Criteria;
import com.wosai.data.dao.Dao;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MerchantConfigCustom;
import com.wosai.upay.core.model.StoreAppConfig;
import com.wosai.upay.core.model.StoreConfig;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.core.repository.DataRepository;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.core.service.base.BaseTest;


public class AllParamsBaseTest extends BaseTest{
    protected static String MERCHANT_SN = "mock-m-" +  StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
    protected static String MERCHANT_ID = UUID.randomUUID().toString();
    protected static String STORE_SN = "mock-s-" +  StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
    protected static String STORE_ID = UUID.randomUUID().toString();
    protected static String TERMINAL_SN = "mock-t-" +  StringUtils.leftPad(ThreadLocalRandom.current().nextInt(999999999)+"", 10, "0");
    protected static String TERMINAL_ID = UUID.randomUUID().toString();


    @Autowired
    protected TradeConfigService tradeConfigService;
    
    @Autowired
    @Qualifier("jdbcTemplate")
    JdbcTemplate jdbcTemplate;
    
    @Autowired
    protected DataRepository dataRepository;
    
    @Autowired
    protected SupportService supportService;
    
    protected void setup() {
        // 1. 创建商户
        jdbcTemplate.execute("INSERT INTO `merchant` (`id`, `sn`, `name`, `alias`, `industry`, `status`, `rank`, `withdraw_mode`, `longitude`, `latitude`, `country`, `province`, `city`, `district`, `street_address`, `contact_name`, `contact_phone`, `contact_cellphone`, `contact_email`, `concat_id_card_front_photo`, `legal_person_type`, `legal_person_name`, `legal_person_id_type`, `legal_person_id_number`, `legal_person_id_card_front_photo`, `legal_person_id_card_back_photo`, `legal_person_register_no`, `business_license_photo`, `business`, `currency`, `owner_name`, `owner_cellphone`, `customer_phone`, `logo`, `bank_account_verify_status`, `client_sn`, `vendor_id`, `solicitor_id`, `platform`, `extra`, `ctime`, `mtime`, `deleted`, `version`, `business_name`, `concat_identity`)\n" + 
                "VALUES\n" + 
                "    ('"+MERCHANT_ID+"', '"+ MERCHANT_SN +"', 'mock测试用例商户', 'mock测试用例商户', '769e272a-312d-11e6-aebb-ecf4bbdee2f0', 1, 0, 1, '121.402178', '31.211785', 'CHN', '上海市', '市辖区', '长宁区', '长宁区天山路762号巴黎春天一楼', '方迪', '***********', '***********', '', NULL, 0, '', 1, '', '', '', '', '', '', 'CNY', '方迪', '***********', X'', X'', -1, '', 'b52600e36100-3ce9-5e11-99fa-f5f9d958', 'b9fc12fb29c6-7899-5e11-003b-99bbf79c', 0, NULL, unix_timestamp()*1000, unix_timestamp()*1000, 0, 1, '', NULL)");
        // 2. 创建门店
        jdbcTemplate.execute("INSERT INTO `store` (`id`, `sn`, `name`, `industry`, `status`, `rank`, `longitude`, `latitude`, `province`, `city`, `district`, `street_address`, `contact_name`, `contact_phone`, `contact_cellphone`, `contact_email`, `client_sn`, `merchant_id`, `solicitor_id`, `vendor_id`, `extra`, `ctime`, `mtime`, `deleted`, `version`)\n" + 
                "VALUES\n" + 
                "    ('"+STORE_ID+"', '"+STORE_SN+"', 'mock测试用例门店', '', 1, 0, '', '', '广东省', '佛山市', '南海区', '桃园商贸城昇宝百货', '叶月珠', '', '10586585816', '', '44', '"+MERCHANT_ID+"', 'b9fc12fb29c6-7899-5e11-003b-99bbf79c', 'b52600e36100-3ce9-5e11-99fa-f5f9d958', NULL, unix_timestamp()*1000, unix_timestamp()*1000, 0, 1)");

        // 3. 创建终端
        jdbcTemplate.execute("INSERT INTO `terminal` (`id`, `sn`, `device_fingerprint`, `name`, `type`, `status`, `last_signon_time`, `sdk_version`, `os_version`, `current_secret`, `current_checkin_status`, `last_secret`, `next_secret`, `longitude`, `latitude`, `client_sn`, `extra`, `target`, `target_type`, `store_id`, `merchant_id`, `solicitor_id`, `vendor_id`, `vendor_app_id`, `vendor_app_appid`, `ctime`, `mtime`, `deleted`, `version`)\n" + 
                "VALUES\n" + 
                "    ('"+TERMINAL_ID+"', '"+TERMINAL_SN+"', '00001580000000312240_25D708AB441A4CC3B44055379073EFCD', 'New Term (activateV2) 1514481592021', 50, 1, 0, '', '', '', 0, '', '', '', '', NULL, NULL, '', 0, '"+STORE_ID+"', '"+MERCHANT_ID+"', '', 'b52600e36100-3ce9-5e11-99fa-f5f9d958', '7c9224b1-0202-4254-8258-0a8080e8b75a', '2016111100000033', unix_timestamp()*1000, unix_timestamp()*1000, 0, 1)");
    }
    
    // 清除插入的数据
    protected void down() {
        dataRepository.getMerchantDao().delete(MERCHANT_ID);
        dataRepository.getStoreDao().delete(STORE_ID);
        dataRepository.getTerminalDao().delete(TERMINAL_ID);

        deleteConfigById(dataRepository.getMerchantConfigDao(), Criteria.where(MerchantConfig.MERCHANT_ID).is(MERCHANT_ID));
        deleteConfigById(dataRepository.getMerchantAppConfigDao(), Criteria.where(MerchantAppConfig.MERCHANT_ID).is(MERCHANT_ID));
        deleteConfigById(dataRepository.getMerchantConfigCustomDao(), Criteria.where(MerchantConfigCustom.MERCHANT_ID).is(MERCHANT_ID));
        deleteConfigById(dataRepository.getStoreConfigDao(), Criteria.where(StoreConfig.STORE_ID).is(STORE_ID));
        deleteConfigById(dataRepository.getStoreAppConfigDao(), Criteria.where(StoreAppConfig.STORE_ID).is(STORE_ID));
        deleteConfigById(dataRepository.getTerminalConfigDao(), Criteria.where(TerminalConfig.TERMINAL_ID).is(TERMINAL_ID));

        supportService.removeCachedParams(MERCHANT_SN);
        supportService.removeCachedHuaBeiParams(MERCHANT_SN);
    }

    private void deleteConfigById(Dao<Map<String, Object>> dao, Criteria criteria) {
        dao.filter(criteria)
            .fetchAll()
            .forEachRemaining(mac -> dao.delete((String)mac.get(DaoConstants.ID)));
    }

    protected <T>T call(Callable<T> call) throws Exception {
        return call(call, null);
    }
    
    protected <T>T call(Callable<T> call, Runnable preCall) throws Exception {
        setup();
        if(Objects.nonNull(preCall)) {
            preCall.run();
        }
        try{
            return call.call();
        }finally {
            down();
        }
    }
    
    protected void updateMerchant(Map<String, Object> merchant) {
        if(Objects.nonNull(merchant)) {
            String id = (String)merchant.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                merchant.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getMerchantDao().save(merchant);
            }else {
                dataRepository.getMerchantDao().updatePart(merchant);
            }
        }
    }
    
    protected void updateMerchantConfig(Map<String, Object> merchantConfig) {
        if(Objects.nonNull(merchantConfig)) {
            String id = (String)merchantConfig.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                merchantConfig.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getMerchantConfigDao().save(merchantConfig);
            }else {
                dataRepository.getMerchantConfigDao().updatePart(merchantConfig);
            }
        }
    }

    protected void updateMerchantAppConfig(Map<String, Object> merchantConfig) {
        if(Objects.nonNull(merchantConfig)) {
            String id = (String)merchantConfig.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                merchantConfig.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getMerchantAppConfigDao().save(merchantConfig);
            }else {
                dataRepository.getMerchantAppConfigDao().updatePart(merchantConfig);
            }
        }
    }

    protected void updateTerminalConfig(Map<String, Object> terminalConfig) {
        if(Objects.nonNull(terminalConfig)) {
            String id = (String)terminalConfig.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                terminalConfig.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getTerminalConfigDao().save(terminalConfig);
            }else {
                dataRepository.getTerminalConfigDao().updatePart(terminalConfig);
            }
        }
    }

    protected void updateStoreConfig(Map<String, Object> storeConfig) {
        if(Objects.nonNull(storeConfig)) {
            String id = (String)storeConfig.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                storeConfig.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getStoreConfigDao().save(storeConfig);
            }else {
                dataRepository.getStoreConfigDao().updatePart(storeConfig);
            }
        }
    }

    protected void updateStoreAppConfig(Map<String, Object> storeConfig) {
        if(Objects.nonNull(storeConfig)) {
            String id = (String)storeConfig.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                storeConfig.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getStoreAppConfigDao().save(storeConfig);
            }else {
                dataRepository.getStoreAppConfigDao().updatePart(storeConfig);
            }
        }
    }

    protected void updateMerchantConfigCustom(Map<String, Object> merchantCustomConfig) {
        if(Objects.nonNull(merchantCustomConfig)) {
            String id = (String)merchantCustomConfig.get(DaoConstants.ID);
            if(StringUtil.empty(id)) {
                merchantCustomConfig.put(DaoConstants.ID, UUID.randomUUID().toString());
                dataRepository.getMerchantConfigCustomDao().save(merchantCustomConfig);
            }else {
                dataRepository.getMerchantConfigCustomDao().updatePart(merchantCustomConfig);
            }
        }
    }
}
