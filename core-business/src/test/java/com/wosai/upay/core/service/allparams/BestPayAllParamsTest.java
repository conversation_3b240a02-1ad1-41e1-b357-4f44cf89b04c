package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 翼支付交易参数获取
 * 
 */
public class BestPayAllParamsTest extends AllParamsBaseTest{
    
    /**
     * 翼支付直连交易b2c、wap参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        String merchantId = "TESTCASE_merchant_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String merchantKey = "TESTCASE_merchant_key" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String merchantPwd = "TESTCASE_merchant_pwd" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String storeId = "TESTCASE_store_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String subMerchantId = "TESTCASE_sub_merchant_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.BESTPAY_MERCHANT_ID, merchantId),
                ImmutablePair.of(TransactionParam.BESTPAY_MERCHANT_KEY, merchantKey),
                ImmutablePair.of(TransactionParam.BESTPAY_MERCHANT_PWD, merchantPwd),
                ImmutablePair.of(TransactionParam.BESTPAY_STORE_ID, storeId),
                ImmutablePair.of(TransactionParam.BESTPAY_SUB_MERCHANT_ID, subMerchantId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.BESTPAY_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
        );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 1, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_AGENT_NAME, "18_*_true_true_0001",
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 1, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_AGENT_NAME, "18_*_true_true_0001",
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_BESTPAY, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_BESTPAY,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取直连翼支付交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取直连翼支付交易参数失败:bestpay_trade_params", subpayway), allParams.containsKey(TransactionParam.BESTPAY_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.BESTPAY_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取直连翼支付交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
}
