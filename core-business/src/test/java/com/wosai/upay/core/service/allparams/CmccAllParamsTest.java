package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 翼支付交易参数获取
 * 
 */
public class CmccAllParamsTest extends AllParamsBaseTest{
    
    /**
     * 翼支付直连交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        // 1、使用merchant_config方式获取交易参数
        String cmcc_merchant_id = "TESTCASE_cmcc_merchant_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String cmcc_merchant_key = "TESTCASE_cmcc_merchant_key" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sign_type = "TESTCASE_sign_type" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String cmcc_merchant_cert = "TESTCASE_cmcc_merchant_cert" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.CMCC_MERCHANT_ID, cmcc_merchant_id), 
                ImmutablePair.of(TransactionParam.CMCC_MERCHANT_KEY, cmcc_merchant_key),
                ImmutablePair.of(TransactionParam.SIGN_TYPE, sign_type),
                ImmutablePair.of("cmcc_merchant_cert", cmcc_merchant_cert),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)

        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_CMCC, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_CMCC,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.CMCC_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取翼支付直连b2c交易参数", allParams);
        assertTrue("获取翼支付直连b2c交易参数失败:cmcc_trade_params", allParams.containsKey(TransactionParam.CMCC_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.CMCC_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取翼支付直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
    }
}
