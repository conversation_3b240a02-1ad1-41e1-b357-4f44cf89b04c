package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.exception.CoreMerchantConfigAbnormalException;
import com.wosai.upay.core.exception.CoreStoreConfigAbnormalException;
import com.wosai.upay.core.exception.CoreTerminalConfigAbnormalException;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.StoreConfig;
import com.wosai.upay.core.model.TerminalConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 交易测试用例：所有渠道都会使用的测试用例
 *
 */
public class CommonAllParamsTest extends AllParamsBaseTest{

    /**
     * 商户收款权限被关闭 merchant_config.params.swithces.pay_status = 0
     * @throws Exception 
     */
    @Test
    public void test1() throws Exception {
        Exception ex = null;
        try {
            call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, 1, 1), 
                    () -> updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_CLOSED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                   )
            );
        }catch (CoreMerchantConfigAbnormalException e) {
            ex = e;
        }
        Thread.sleep(1 * 50 * 1000);
        assertEquals("商户收款权限被关闭", "商户收款权限被关闭，请联系您的客户经理", ex.getMessage());
    }
    
    /**
     * payway交易参数未配置（merchant_config下的所有数据都不存在）
     * @throws Exception 
     */
    @Test
    public void test2() throws Exception {
        Map<Integer, String> infos = MapUtil.hashMap(TradeConfigService.PAYWAY_ALIPAY, "此商户未开通支付宝",
                    TradeConfigService.PAYWAY_ALIPAY2, "此商户未开通支付宝",
                    TradeConfigService.PAYWAY_WEIXIN, "此商户未开通微信",
                    TradeConfigService.PAYWAY_BAIFUBAO, "百度钱包业务升级暂时无法付款，请使用其他支付方式",
                    TradeConfigService.PAYWAY_JD, "京东钱包系统升级中，暂时无法交易，请使用其他方式来完成付款。",
                    TradeConfigService.PAYWAY_QQWALLET, "QQ钱包业务升级暂时无法付款，请使用其他支付方式",
                    TradeConfigService.PAYWAY_APPLEPAY, "为了更好的支持Apple pay功能，收钱吧玛雅终端进行后台系统升级，暂时无法使用该功能，恢复时间后续通知",
                    TradeConfigService.PAYWAY_LAKALAWALLET, "无拉卡拉商户号，不可发生代结算交易",
                    TradeConfigService.PAYWAY_CMCC, "移动和包支付系统升级中，暂时无法交易，请使用其他方式来完成付款。",
                    TradeConfigService.PAYWAY_BESTPAY, "无拉卡拉商户号，不可发生代结算交易",
                    TradeConfigService.PAYWAY_WEIXIN_HK, "无拉卡拉商户号，不可发生代结算交易",
                    TradeConfigService.PAYWAY_ALIPAY_INTL, "无拉卡拉商户号，不可发生代结算交易",
                    TradeConfigService.PAYWAY_BANKCARD, "无拉卡拉商户号，不可发生代结算交易",
                    TradeConfigService.PAYWAY_SODEXO, "无拉卡拉商户号，不可发生代结算交易",
                    TradeConfigService.PAYWAY_GIFT_CARD, "无拉卡拉商户号，不可发生代结算交易"
                );
        call(() -> {
            for (Map.Entry<Integer, String> config : infos.entrySet()) {
                Exception ex = null;
                try {
                    supportService.getAllParams(null, TERMINAL_SN, config.getKey(), 1);
                }catch (CoreMerchantConfigAbnormalException e) {
                    ex = e;
                }
                assertEquals(String.format("payway:%s 商户收款权限被关闭", config.getKey()), config.getValue(), ex.getMessage());
            }
            return null;
        });
    }
    
    /**
     * 验证通道不支持的payway和subpayway
     * 
     * @throws Exception 
     */
    @Test
    public void test3() throws Exception {
        Map<Integer, String> configs = MapUtil.hashMap(TradeConfigService.PROVIDER_LKLWANMA, "此商户未开通支付宝",
                TradeConfigService.PROVIDER_UNIONPAY_OPEN, "此商户未开通支付宝"
//                ,
//                TradeConfigService.PROVIDER_CHINAUMS, "此商户未开通支付宝"
            );
        for (Map.Entry<Integer, String> config : configs.entrySet()) {
            Exception ex = null;
            try {
                call(
                        () -> supportService.getAllParams(null, TERMINAL_SN, 1, 1), 
                        () -> updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        MerchantConfig.PROVIDER, config.getKey(),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                        )
                );
            }catch (CoreMerchantConfigAbnormalException e) {
                ex = e;
            }
            assertNotNull("通道收款权限被关闭", ex);
            assertEquals(String.format("provider:%s 通道收款权限被关闭", config.getKey()), config.getValue(), ex.getMessage());
        }
    }
    
    /**
     * 结算通道未配置，不允许发生交易
     * 
     * @throws Exception 
     */
    @Test
    public void test4() throws Exception {
        Map<Integer, String> configs = MapUtil.hashMap(
                TradeConfigService.PROVIDER_LAKALA_UNION_PAY, "无拉卡拉商户号，不可发生代结算交易",
                TradeConfigService.PROVIDER_UNIONPAY_TL, "无通联商户号，不可发生代结算交易"
            );
        for (Map.Entry<Integer, String> config : configs.entrySet()) {
            Exception ex = null;
            try {
                call(
                        () -> supportService.getAllParams(null, TERMINAL_SN, 1, 1), 
                        () -> updateMerchantConfig(
                                    MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                            MerchantConfig.PARAMS, MapUtil.hashMap(
                                                    TransactionParam.SWITCHES, MapUtil.hashMap(
                                                            TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                    ),
                                                    TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                    TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                            ),
                                            MerchantConfig.PROVIDER, config.getKey(),
                                            DaoConstants.CTIME, System.currentTimeMillis(),
                                            DaoConstants.MTIME, System.currentTimeMillis()
                                )
                            )
                );
            }catch (CoreMerchantConfigAbnormalException e) {
                ex = e;
            }
            assertEquals(String.format("provider:%s 结算通道未配置", config.getKey()), config.getValue(), ex.getMessage());
        }
    }
    
    /**
     * 二级支付方式状态关闭，不允许交易
     * 
     * @throws Exception 
     */
    @Test
    public void test5() throws Exception {
        List<Integer> subpayways = Arrays.asList(TradeConfigService.SUB_PAYWAY_BARCODE, TradeConfigService.SUB_PAYWAY_QRCODE, TradeConfigService.SUB_PAYWAY_WAP,
                    TradeConfigService.SUB_PAYWAY_MINI, TradeConfigService.SUB_PAYWAY_APP, TradeConfigService.SUB_PAYWAY_H5
                );
        call(
                () -> {
                    for (Integer subpayway : subpayways) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, 1, subpayway);
                        }catch (CoreMerchantConfigAbnormalException e) {
                            ex = e;
                        }
                        assertEquals(String.format("subpayway:%s 状态关闭", subpayway), "此收款通道已被关闭，请换用其他收款通道", ex.getMessage());
                    }
                    return null;
                },
                () ->{
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_CLOSED,
                                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_CLOSED,
                                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_CLOSED,
                                    MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_CLOSED,
                                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_CLOSED,
                                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_CLOSED,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                );
        });
    }
    
    /**
     * 门店二级支付方式状态关闭，不允许交易
     * 
     * @throws Exception 
     */
    @Test
    public void test6() throws Exception {
        List<Integer> subpayways = Arrays.asList(TradeConfigService.SUB_PAYWAY_BARCODE, TradeConfigService.SUB_PAYWAY_QRCODE, TradeConfigService.SUB_PAYWAY_WAP,
                    TradeConfigService.SUB_PAYWAY_MINI, TradeConfigService.SUB_PAYWAY_APP, TradeConfigService.SUB_PAYWAY_H5
                );
        call(
                () -> {
                    for (Integer subpayway : subpayways) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, 1, subpayway);
                        }catch (CoreStoreConfigAbnormalException e) {
                            ex = e;
                        }
                        assertEquals(String.format("subpayway:%s 门店二级支付方式状态关闭", subpayway), "该门店收款功能已关闭", ex.getMessage());
                    }
                    return null;
                },
                () ->{
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                        StoreConfig.B2C_STATUS, StoreConfig.STATUS_CLOSED,
                                        StoreConfig.C2B_STATUS, StoreConfig.STATUS_CLOSED,
                                        StoreConfig.WAP_STATUS, StoreConfig.STATUS_CLOSED,
                                        StoreConfig.MINI_STATUS, StoreConfig.STATUS_CLOSED,
                                        StoreConfig.APP_STATUS, StoreConfig.STATUS_CLOSED,
                                        StoreConfig.H5_STATUS, StoreConfig.STATUS_CLOSED,
                                        StoreConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
        });
    }
    
    /**
     * 终端二级支付方式状态关闭，不允许交易
     * 
     * @throws Exception 
     */
    @Test
    public void test7() throws Exception {
        List<Integer> subpayways = Arrays.asList(TradeConfigService.SUB_PAYWAY_BARCODE, TradeConfigService.SUB_PAYWAY_QRCODE, TradeConfigService.SUB_PAYWAY_WAP,
                    TradeConfigService.SUB_PAYWAY_MINI, TradeConfigService.SUB_PAYWAY_APP, TradeConfigService.SUB_PAYWAY_H5
                );
        call(
                () -> {
                    for (Integer subpayway : subpayways) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, 1, subpayway);
                        }catch (CoreTerminalConfigAbnormalException e) {
                            ex = e;
                        }
                        assertTrue(String.format("subpayway:%s 终端二级支付方式状态关闭", subpayway), ex.getMessage().endsWith("收款功能已关闭，请使用其他终端收款"));
                    }
                    return null;
                },
                () ->{
                    // 添加payway=null的merchant_config配置
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    // 添加payway=null的store_config配置
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                        StoreConfig.B2C_STATUS, StoreConfig.STATUS_OPENED,
                                        StoreConfig.C2B_STATUS, StoreConfig.STATUS_OPENED,
                                        StoreConfig.WAP_STATUS, StoreConfig.STATUS_OPENED,
                                        StoreConfig.MINI_STATUS, StoreConfig.STATUS_OPENED,
                                        StoreConfig.APP_STATUS, StoreConfig.STATUS_OPENED,
                                        StoreConfig.H5_STATUS, StoreConfig.STATUS_OPENED,
                                        StoreConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    // 添加payway=null的terminal_config配置
                    updateTerminalConfig(
                            MapUtil.hashMap(TerminalConfig.TERMINAL_ID, TERMINAL_ID,
                                        TerminalConfig.B2C_STATUS, TerminalConfig.STATUS_CLOSED,
                                        TerminalConfig.C2B_STATUS, TerminalConfig.STATUS_CLOSED,
                                        TerminalConfig.WAP_STATUS, TerminalConfig.STATUS_CLOSED,
                                        TerminalConfig.MINI_STATUS, TerminalConfig.STATUS_CLOSED,
                                        TerminalConfig.APP_STATUS, TerminalConfig.STATUS_CLOSED,
                                        TerminalConfig.H5_STATUS, TerminalConfig.STATUS_CLOSED,
                                        TerminalConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
        });
    }
    
    /**
     * 威富通渠道未设置支付宝商户号，不允许交易
     * 
     * @throws Exception 
     */
    @Test
    public void test8() throws Exception {
        List<MutableTriple<Integer, String, Map>> configs = Arrays.asList(
                    MutableTriple.of(TradeConfigService.PROVIDER_CIBBANK, TransactionParam.CIBBANK_TRADE_PARAMS, new HashedMap()),
                    MutableTriple.of(TradeConfigService.PROVIDER_CITICBANK, TransactionParam.CITICBANK_TRADE_PARAMS, new HashedMap()),
                    MutableTriple.of(TradeConfigService.PROVIDER_CIBSHBANK, "cibshbank_trade_params", new HashedMap())

                );
        for (MutableTriple<Integer, String, Map> config : configs) {
            Exception ex = null;
            try {
                call(
                        () -> supportService.getAllParams(null, TERMINAL_SN, 1, 1),
                        () ->{
                            // 添加payway=null的merchant_config配置
                            updateMerchantConfig(
                                    MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.PARAMS, MapUtil.hashMap(
                                                        TransactionParam.SWITCHES, MapUtil.hashMap(
                                                                TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                        TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                        TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                                        TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "merc_id",
                                                                                                            TransactionParam.LAKALA_TERM_ID, "term_id"
                                                        )
                                                ),
                                                DaoConstants.CTIME, System.currentTimeMillis(),
                                                DaoConstants.MTIME, System.currentTimeMillis()
                                    )
                            );
                            
                            // 添加payway!=null的merchant_config配置
                            updateMerchantConfig(
                                    MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                                MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                                MerchantConfig.PARAMS, MapUtil.hashMap(
                                                        config.getMiddle(), config.getRight()
                                                ),
                                                MerchantConfig.PROVIDER, config.getLeft(),
                                                DaoConstants.CTIME, System.currentTimeMillis(),
                                                DaoConstants.MTIME, System.currentTimeMillis()
                                    )
                            );
                        }
                );
            }catch (CoreMerchantConfigAbnormalException e) {
                ex = e;
            }
            assertEquals(String.format("provider:%s 通道支付宝未报备完成", config.getLeft()), "支付宝商户报备中，请耐心等待", ex.getMessage());
        }
    }
    
    /**
     * 商户的*_formal =1 ，但未配置对应的交易参数配置
     * 
     * @throws Exception 
     */
    @Test
    public void test9() throws Exception {
        List<ImmutablePair<Integer, Integer>> datas = Arrays.asList(
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_QRCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_WAP),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_QRCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_WAP),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_MINI),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_APP),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_H5),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_MINI),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_H5),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_APP),
                ImmutablePair.of(TradeConfigService.PAYWAY_JD, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_JD, TradeConfigService.SUB_PAYWAY_QRCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_JD, TradeConfigService.SUB_PAYWAY_WAP),
                ImmutablePair.of(TradeConfigService.PAYWAY_BAIFUBAO, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_BAIFUBAO, TradeConfigService.SUB_PAYWAY_QRCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_QQWALLET, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_QQWALLET, TradeConfigService.SUB_PAYWAY_QRCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_APPLEPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN_HK, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_CMCC, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY_INTL, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_GIFT_CARD, TradeConfigService.SUB_PAYWAY_BARCODE),
                ImmutablePair.of(TradeConfigService.PAYWAY_SODEXO, TradeConfigService.SUB_PAYWAY_BARCODE)
        );
        call(
                () -> {
                    for (ImmutablePair<Integer, Integer> data : datas) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, data.getLeft(), data.getRight());
                        }catch (CoreMerchantConfigAbnormalException e) {
                            ex = e;
                        }
                        assertEquals(String.format("payway:%s,subpayway:%s 商户正式交易参数配置错误", data.getLeft(), data.getRight()), "商户正式交易参数配置错误", ex.getMessage());
                    }
                    return null;
                },
                () ->{
                    // 添加payway=null的merchant_config配置
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FORMAL, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FORMAL, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_FORMAL, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_FORMAL, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_FORMAL, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "merc_id",
                                                                                                    TransactionParam.LAKALA_TERM_ID, "term_id"
                                                )
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
    }
    
    /**
     * 间连通道未配置商户号
     * 
     * 注意
     * 1、provider 支持的payway和subpayway需要在system_config.name = provider.payways 的params中配置
     * 2、部分agent中有配置对应的商户号，正常来说这些应该不被配置的（下面注释的代码都和此有关）
     * 
     * @throws Exception 
     */
    @Test
    public void test10() throws Exception {
        List<MutableTriple<Integer, ImmutablePair<String, Map>, List<ImmutablePair<Integer, Integer>>>> datas = Arrays.asList(
                MutableTriple.of(TradeConfigService.PROVIDER_CIBBANK, ImmutablePair.of(TransactionParam.CIBBANK_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                            ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                            ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                            ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP)
                        )), 
//                MutableTriple.of(TradeConfigService.PROVIDER_CITICBANK, ImmutablePair.of(TransactionParam.CITICBANK_TRADE_PARAMS, new HashedMap()), Arrays.asList(
//                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
//                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_QRCODE),
//                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_WAP)
//                    )),
                MutableTriple.of(TradeConfigService.PROVIDER_NUCC, ImmutablePair.of(TransactionParam.NUCC_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_MINI),
                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_WAP)
                    )),
                MutableTriple.of(TradeConfigService.PROVIDER_UNIONPAY, ImmutablePair.of(TransactionParam.UNION_PAY_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP)
                    )),
                MutableTriple.of(TradeConfigService.PROVIDER_UNIONPAY_OPEN, ImmutablePair.of(TransactionParam.UNION_PAY_OPEN_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                        ImmutablePair.of(TradeConfigService.PAYWAY_LKL_UNIONPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_LKL_UNIONPAY, TradeConfigService.SUB_PAYWAY_WAP)
                    )),
                MutableTriple.of(TradeConfigService.PROVIDER_UNIONPAY_ONLINE, ImmutablePair.of(TransactionParam.UNION_PAY_ONLINE_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                        ImmutablePair.of(TradeConfigService.PAYWAY_BANKCARD, TradeConfigService.SUB_PAYWAY_WAP)
                    )),
                MutableTriple.of(TradeConfigService.PROVIDER_LAKALA_UNION_PAY, ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS, new HashedMap()), Arrays.asList(
//                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_BARCODE),
//                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_QRCODE),
//                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_WAP),
//                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_MINI),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_MINI)
                ))
         );
  
        // 非通联渠道
        for (MutableTriple<Integer, ImmutablePair<String, Map>, List<ImmutablePair<Integer, Integer>>> config : datas) {
            call(
                () -> {
                    for (ImmutablePair<Integer, Integer> mutableTriple : config.getRight()) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, mutableTriple.getLeft(), mutableTriple.getRight());
                        }catch (CoreMerchantConfigAbnormalException e) {
                            ex = e;
                        }
                        assertNotNull(String.format("provider:%s,payway:%s,subpayway:%s 通道未配置商户号 未报错", config.getLeft(), mutableTriple.getLeft(), mutableTriple.getRight()), ex);
                        assertEquals(String.format("provider:%s,payway:%s,subpayway:%s 通道未配置商户号", config.getLeft(), mutableTriple.getLeft(), mutableTriple.getRight()), "商户交易参数配置异常", ex.getMessage());
                    }
                    return null;
                },
                () ->{
                    // 添加payway=null的merchant_config配置
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "merc_id",
                                                                                                    TransactionParam.LAKALA_TERM_ID, "term_id"
                                                ),
                                                config.getMiddle().getLeft(), config.getMiddle().getRight()
                                        ),
                                        MerchantConfig.PROVIDER, config.getLeft(),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
            );
        }
        
        // 通联渠道配置
        // TODO FIXME 通联添加非空参数校验
        /*
        datas = Arrays.asList(
                MutableTriple.of(TradeConfigService.PROVIDER_UNIONPAY_TL, ImmutablePair.of(TransactionParam.UNION_PAY_TL_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_WAP),
                        ImmutablePair.of(TradeConfigService.PAYWAY_ALIPAY2, TradeConfigService.SUB_PAYWAY_MINI),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_MINI),
                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_BESTPAY, TradeConfigService.SUB_PAYWAY_WAP)
                    ))
         );
        
        for (MutableTriple<Integer, ImmutablePair<String, Map>, List<ImmutablePair<Integer, Integer>>> config : datas) {
            call(
                () -> {
                    for (ImmutablePair<Integer, Integer> mutableTriple : config.getRight()) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, mutableTriple.getLeft(), mutableTriple.getRight());
                        }catch (CoreMerchantConfigAbnormalException e) {
                            ex = e;
                        }
                        assertNotNull(String.format("provider:%s,payway:%s,subpayway:%s 通道未配置商户号 未报错", config.getLeft(), mutableTriple.getLeft(), mutableTriple.getRight()), ex);
                        assertEquals(String.format("provider:%s,payway:%s,subpayway:%s 通道未配置商户号", config.getLeft(), mutableTriple.getLeft(), mutableTriple.getRight()), "商户交易参数配置异常", ex.getMessage());
                    }
                    return null;
                },
                () ->{
                    // 添加payway=null的merchant_config配置
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                                TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "merc_id"),
                                                config.getMiddle().getLeft(), config.getMiddle().getRight()
                                        ),
                                        MerchantConfig.PROVIDER, config.getLeft(),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
            );
        }
        */
    }
    
    /**
     * 间连微信通道走大商户模式
     * 
     * 注：大部分出现在商户未配置merchant_config
     * 
     * @throws Exception 
     */
    @Test
    public void test11() throws Exception {
        List<MutableTriple<Integer, ImmutablePair<String, Map>, List<ImmutablePair<Integer, Integer>>>> datas = Arrays.asList(
                MutableTriple.of(TradeConfigService.PROVIDER_CITICBANK, ImmutablePair.of(TransactionParam.CITICBANK_TRADE_PARAMS, new HashedMap()), Arrays.asList(
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP),
                        ImmutablePair.of(TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_MINI)
                        )
                )
         );
  
        for (MutableTriple<Integer, ImmutablePair<String, Map>, List<ImmutablePair<Integer, Integer>>> config : datas) {
            call(
                () -> {
                    for (ImmutablePair<Integer, Integer> mutableTriple : config.getRight()) {
                        Exception ex = null;
                        try {
                            supportService.getAllParams(null, TERMINAL_SN, mutableTriple.getLeft(), mutableTriple.getRight());
                        }catch (CoreMerchantConfigAbnormalException e) {
                            ex = e;
                        }
                        assertEquals(String.format("provider:%s,payway:%s,subpayway:%s 微信商户报备中，请耐心等待", config.getLeft(), mutableTriple.getLeft(), mutableTriple.getRight()), "微信商户报备中，请耐心等待", ex.getMessage());
                    }
                    return null;
                },
                () ->{
                    // 添加payway=null的merchant_config配置
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "merc_id",
                                                                                                    TransactionParam.LAKALA_TERM_ID, "term_id"
                                                ),
                                                config.getMiddle().getLeft(), config.getMiddle().getRight()
                                        ),
                                        MerchantConfig.PROVIDER, config.getLeft(),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
            );
        }
    }
    
    /**
     * 间连通道切通道时不允许交易
     * 
     * @throws Exception 
     */
    @Test
    public void test12() throws Exception {
        Exception ex = null;
        try {
            call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, 3, 1), 
                    () -> updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_SWITCH,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                            TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "merc_id",
                                                    TransactionParam.LAKALA_TERM_ID, "term_id"),
                                            TransactionParam.CITICBANK_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.CITICBANK_MCH_ID, "mch_id")
                                    ),
                                    MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CITICBANK,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                   )
            );
        }catch (CoreMerchantConfigAbnormalException e) {
            ex = e;
        }
        assertEquals("间连通道切通道时不允许交易", "商户正在切换清算通道，请耐心等待", ex.getMessage());
    }
    
    /**
     * 非通联渠道不允许使用通联结算
     * 
     * @throws Exception 
     */
    @Test
    public void test13() throws Exception {
        Exception ex = null;
        try {
            call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, 3, 1), 
                    () -> updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                            TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "merc_id",
                                                    TransactionParam.LAKALA_TERM_ID, "term_id"),
                                            TransactionParam.CITICBANK_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.CITICBANK_MCH_ID, "mch_id")
                                    ),
                                    MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CITICBANK,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                   )
            );
        }catch (CoreMerchantConfigAbnormalException e) {
            ex = e;
        }
        assertEquals("非通联渠道不允许使用通联结算", "商户清算通道参数错误", ex.getMessage());
    }
    
    /**
     * 非拉卡拉结算渠道不允许使用拉卡拉结算
     * 
     * @throws Exception 
     */
    //@Test
    public void test14() throws Exception {
        Exception ex = null;
        try {
            call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, 3, 1), 
                    () -> updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                            TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "merc_id"),
                                            TransactionParam.UNION_PAY_TL_TRADE_PARAMS, new HashMap()
                                    ),
                                    MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                   )
            );
        }catch (CoreMerchantConfigAbnormalException e) {
            ex = e;
        }
        assertEquals("非通联渠道不允许使用通联结算", "商户清算通道参数错误", ex.getMessage());
    }
    
    /**
     * 特优费率设置错误
     * 
     * @throws Exception 
     */
    @Test
    public void test15() throws Exception {
        Exception ex = null;
        try {
            call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, 3, 1), 
                    () -> updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE,
                                            TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "merc_id"),
                                            TransactionParam.UNION_PAY_TL_TRADE_PARAMS, new HashMap(),
                                            MerchantConfig.LADDER_STATUS, MerchantConfig.STATUS_OPENED,
                                            MerchantConfig.LADDER_FEE_RATES, null
                                    ),
                                    MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                   )
            );
        }catch (CoreMerchantConfigAbnormalException e) {
            ex = e;
        }
        assertEquals("错误的特优费率配置", "商户交易参数配置异常", ex.getMessage());
    }
    
    /**
     * 所有已知的开关配置
     * 
     * @throws Exception
     */
    @Test
    public void test16() throws Exception{
        int merchantDailyMaxSumOfTrans = ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        Map<String, Map<String,String>> merchantDailyPaywayMaxSumOfTrans = MapUtil.hashMap(TradeConfigService.PAYWAY_ALIPAY2+"", MapUtil.hashMap("", Integer.MAX_VALUE+""));
        Map<String, Map<String, Integer>> merchantSingleMaxOfTran = MapUtil.hashMap(TradeConfigService.PAYWAY_ALIPAY2+"", MapUtil.hashMap(TradeConfigService.SUB_PAYWAY_BARCODE + "", Integer.MAX_VALUE));
        String creditPay = Arrays.asList(TransactionParam.CREDIT_PAY_ENABLE, TransactionParam.CREDIT_PAY_DISABLE).get(ThreadLocalRandom.current().nextInt(2));
        int useClientStoreSn = Arrays.asList(TransactionParam.USE_CLIENT_STORE_SN_YES, TransactionParam.USE_CLIENT_STORE_SN_NO).get(ThreadLocalRandom.current().nextInt(2));
        Map<String, Integer> deposit = MapUtil.hashMap("alipay", TransactionParam.STATUS_OPENED, "weixin", TransactionParam.STATUS_OPENED);
        boolean isSentStoreId = Arrays.asList(Boolean.TRUE, Boolean.FALSE).get(ThreadLocalRandom.current().nextInt(2));
        boolean isNeedRefundFeeFlag = Arrays.asList(Boolean.TRUE, Boolean.FALSE).get(ThreadLocalRandom.current().nextInt(2));
        boolean isProtectPayerPrivacy = Arrays.asList(Boolean.TRUE, Boolean.FALSE).get(ThreadLocalRandom.current().nextInt(2));
        int duplicateClientSnOfOrder = Arrays.asList(TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER_ONE, TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER_INFINITE).get(ThreadLocalRandom.current().nextInt(2));
        int historTradeRefundFlag = TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_OPEN;
        boolean refundNonSqbOrder = Arrays.asList(Boolean.TRUE, Boolean.FALSE).get(ThreadLocalRandom.current().nextInt(2));
        int clearanceProvider = Arrays.asList(TransactionParam.CLEARANCE_PROVIDER_SWITCH, TransactionParam.CLEARANCE_PROVIDER_LKL, TransactionParam.CLEARANCE_PROVIDER_TL).get(ThreadLocalRandom.current().nextInt(2));
        int payStatus = TransactionParam.STATUS_OPENED;
        int acrossStoreRefundSwitch = Arrays.asList(TransactionParam.STATUS_OPENED, TransactionParam.STATUS_CLOSED).get(ThreadLocalRandom.current().nextInt(2));
        int genOrderSnSwitch = Arrays.asList(TransactionParam.STATUS_OPENED, TransactionParam.STATUS_CLOSED).get(ThreadLocalRandom.current().nextInt(2));
        int sharingSwitch = Arrays.asList(TransactionParam.STATUS_OPENED, TransactionParam.STATUS_CLOSED).get(ThreadLocalRandom.current().nextInt(2));
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, merchantDailyMaxSumOfTrans,
                                            TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS, merchantDailyPaywayMaxSumOfTrans,
                                            TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN, merchantSingleMaxOfTran,
                                            TransactionParam.ALLOW_CREDIT_PAY, creditPay,
                                            TransactionParam.USE_CLIENT_STORE_SN, useClientStoreSn,
                                            TransactionParam.DEPOSIT, deposit,
                                            TransactionParam.IS_SENT_STORE_ID, isSentStoreId,
                                            TransactionParam.IS_NEED_REFUND_FEE_FLAG, isNeedRefundFeeFlag,
                                            TransactionParam.IS_PROTECT_PAYER_PRIVACY, isProtectPayerPrivacy,
                                            TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER, duplicateClientSnOfOrder,
                                            TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, historTradeRefundFlag,
                                            TransactionParam.REFUND_NON_SQB_ORDER, refundNonSqbOrder,
                                            TransactionParam.CLEARANCE_PROVIDER, clearanceProvider,
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, payStatus,
                                                    TransactionParam.ACROSS_STORE_REFUND_SWITCH, acrossStoreRefundSwitch,
                                                    TransactionParam.GEN_ORDER_SN_SWITCH, genOrderSnSwitch,
                                                    TransactionParam.SHARING_SWITCH, sharingSwitch)
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.APP_AUTH_TOKEN, "APP_AUTH_TOKEN",
                                                        TransactionParam.ALIPAY_MCH_ID, "ALIPAY_MCH_ID",
                                                        TransactionParam.ALIPAY_MCH_CATEGORY, "ALIPAY_MCH_CATEGORY",
                                                        TransactionParam.ALIPAY_SELLER_ID, "ALIPAY_SELLER_ID"
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertEquals("获取支付宝直连b2c交易参数失败:merchant_daily_max_sum_of_trans", merchantDailyMaxSumOfTrans, MapUtil.getIntValue(allParams, TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS));
        assertNotNull("获取支付宝直连b2c交易参数失败:merchant_daily_payway_max_sum_of_trans", MapUtil.getMap(allParams, TransactionParam.MERCHANT_DAILY_PAYWAY_MAX_SUM_OF_TRANS));
//        assertNotNull("获取支付宝直连b2c交易参数失败:merchant_single_max_of_tran", MapUtil.getMap(allParams, TransactionParam.MERCHANT_SINGLE_MAX_OF_TRAN));
        assertEquals("获取支付宝直连b2c交易参数失败:credit_pay", creditPay, MapUtil.getString(allParams, TransactionParam.ALLOW_CREDIT_PAY));
        assertEquals("获取支付宝直连b2c交易参数失败:use_client_store_sn", useClientStoreSn, MapUtil.getIntValue(allParams, TransactionParam.USE_CLIENT_STORE_SN, Integer.MIN_VALUE));
        assertNotNull("获取支付宝直连b2c交易参数失败:deposit", MapUtil.getMap(allParams, TransactionParam.DEPOSIT));
        assertEquals("获取支付宝直连b2c交易参数失败:is_sent_store_id", isSentStoreId, MapUtil.getBooleanValue(allParams, TransactionParam.IS_SENT_STORE_ID, isSentStoreId));
        assertEquals("获取支付宝直连b2c交易参数失败:is_protect_payer_privacy", isProtectPayerPrivacy, MapUtil.getBooleanValue(allParams, TransactionParam.IS_PROTECT_PAYER_PRIVACY, isProtectPayerPrivacy));
        assertEquals("获取支付宝直连b2c交易参数失败:duplicate_client_sn_of_order", duplicateClientSnOfOrder, MapUtil.getIntValue(allParams, TransactionParam.MERCHANT_DUPLICATE_CLIENT_SN_OF_ORDER, Integer.MIN_VALUE));
        assertEquals("获取支付宝直连b2c交易参数失败:history_trade_refund_flag", historTradeRefundFlag, MapUtil.getIntValue(allParams, TransactionParam.MERCHANT_HISTORY_TRADE_REFUND_FLAG, Integer.MIN_VALUE));
        assertEquals("获取支付宝直连b2c交易参数失败:refund_non_sqb_order", refundNonSqbOrder, MapUtil.getBooleanValue(allParams, TransactionParam.REFUND_NON_SQB_ORDER, refundNonSqbOrder));
        assertEquals("获取支付宝直连b2c交易参数失败:clearance_provider", clearanceProvider, MapUtil.getIntValue(allParams, TransactionParam.CLEARANCE_PROVIDER, Integer.MIN_VALUE));
        assertEquals("获取支付宝直连b2c交易参数失败:pay_status", payStatus, MapUtil.getIntValue(allParams, TransactionParam.PAY_STATUS, Integer.MIN_VALUE));
        assertEquals("获取支付宝直连b2c交易参数失败:across_store_refund_switch", acrossStoreRefundSwitch, MapUtil.getIntValue(allParams, TransactionParam.ACROSS_STORE_REFUND_SWITCH, Integer.MIN_VALUE));
        assertEquals("获取支付宝直连b2c交易参数失败:gen_order_sn_switch", genOrderSnSwitch, MapUtil.getIntValue(allParams, TransactionParam.GEN_ORDER_SN_SWITCH, Integer.MIN_VALUE));
        assertEquals("获取支付宝直连b2c交易参数失败:sharing_switch", sharingSwitch, MapUtil.getIntValue(allParams, TransactionParam.SHARING_SWITCH, Integer.MIN_VALUE));
    }
    
    /**
     * 特有费率验证
     * 
     * @throws Exception
     */
    @Test
    public void test17() throws Exception{
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            MerchantConfig.LADDER_STATUS, MerchantConfig.STATUS_OPENED,
                                            MerchantConfig.LADDER_FEE_RATES, Arrays.asList(
                                                    MapUtil.hashMap("min", 0, 
                                                            "max", 300,
                                                            MerchantConfig.B2C_FEE_RATE, 0,
                                                            MerchantConfig.C2B_FEE_RATE, 0,
                                                            MerchantConfig.WAP_FEE_RATE, 0,
                                                            MerchantConfig.MINI_FEE_RATE, 0
                                                    ),
                                                    MapUtil.hashMap("min", 300, 
                                                            "max", Integer.MAX_VALUE,
                                                            MerchantConfig.B2C_FEE_RATE, 0.6,
                                                            MerchantConfig.C2B_FEE_RATE, 0.6,
                                                            MerchantConfig.WAP_FEE_RATE, 0.6,
                                                            MerchantConfig.MINI_FEE_RATE, 0.6
                                                    )
                                                )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.APP_AUTH_TOKEN, "APP_AUTH_TOKEN",
                                                        TransactionParam.ALIPAY_MCH_ID, "ALIPAY_MCH_ID",
                                                        TransactionParam.ALIPAY_MCH_CATEGORY, "ALIPAY_MCH_CATEGORY",
                                                        TransactionParam.ALIPAY_SELLER_ID, "ALIPAY_SELLER_ID"
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertNotNull("获取支付宝直连b2c特优费率交易参数", MapUtil.getObject(MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS), TransactionParam.LADDER_FEE_RATES));

    }
}
