package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import com.wosai.upay.core.model.*;
import org.apache.commons.lang3.tuple.ImmutablePair;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.service.TradeConfigService;
import org.junit.Test;

public class FeeRateTypeTest extends AllParamsBaseTest{

    // 商户层级使用新的方式生效套餐，门店未配置费率，使用商户配置
    @Test
    public void test1() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                    StoreConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为资金渠道费率", alipayv2TradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null);
    }

    // 商户层级使用新的方式生效套餐，门店旧的方式生效固定费率，使用门店配置
    @Test
    public void test2() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        String storeFeeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                    StoreConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    StoreConfig.B2C_FORMAL, 1,
                                    StoreConfig.B2C_STATUS, StoreConfig.STATUS_OPENED,
                                    StoreConfig.B2C_FEE_RATE, storeFeeRate,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertEquals("获取借贷分离费率失败，当前不为固定费率", alipayv2TradeParams.get(TransactionParam.FEE_RATE), storeFeeRate);
    }

    // 商户层级使用新的方式生效套餐，门店旧的方式生效阶梯费率，使用门店配置
    @Test
    public void test3() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                    StoreConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    StoreConfig.B2C_FORMAL, 1,
                                    StoreConfig.B2C_STATUS, StoreConfig.STATUS_OPENED,
                                    StoreConfig.PARAMS, MapUtil.hashMap(
                                            MerchantConfig.LADDER_STATUS, 1,
                                            TransactionParam.LADDER_FEE_RATES, Arrays.asList(
                                                    MapUtil.hashMap(
                                                            TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                            TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                            TransactionParam.FEE_RATE, "0"
                                                    ),
                                                    MapUtil.hashMap(
                                                            TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                            TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                            TransactionParam.FEE_RATE, "0.6"
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );

        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为阶梯费率", alipayv2TradeParams.get(TransactionParam.LADDER_FEE_RATES) != null);
    }

    // 商户层级使用新的方式生效套餐，门店旧的方式生效资金渠道费率，使用门店配置
    @Test
    public void test4() throws Exception {
        String mercId = "TESTCASE_merc_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termId = "TESTCASE_term_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termNo = "TESTCASE_term_no" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_VERSION, TransactionParam.WEIXIN_VERSION_V3),
                ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID, mercId),
                ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID, termId),
                ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO, termNo)
        );

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_BANKCARD, TradeConfigService.SUB_PAYWAY_QRCODE),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_BANKCARD,
                                    MerchantConfig.C2B_FORMAL, 0,
                                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.C2B_AGENT_NAME, "1034_21_false_true_0001",
                                    MerchantConfig.C2B_FEE_RATE, feeRate,
                                    MerchantConfig.PROVIDER, 1034,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_LADDER,
                                            TransactionParam.LADDER_FEE_RATES, Arrays.asList(
                                                    MapUtil.hashMap(
                                                            TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                            TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                            TransactionParam.FEE_RATE, "0"
                                                    ),
                                                    MapUtil.hashMap(
                                                            TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                            TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                            TransactionParam.FEE_RATE, "0.6"
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                    StoreConfig.PAYWAY, TradeConfigService.PAYWAY_BANKCARD,
                                    StoreConfig.C2B_FORMAL, 0,
                                    StoreConfig.C2B_STATUS, StoreConfig.STATUS_OPENED,
                                    MerchantConfig.C2B_AGENT_NAME, "1034_21_false_true_0001",
                                    MerchantConfig.PROVIDER, 1034,
                                    StoreConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.CHANNEL_STATUS, 1,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(
                                                            TransactionParam.FEE, "0.38"
                                                    ),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(
                                                            TransactionParam.FEE_RATE, "0.2"
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );

        assertNotNull("获取拉卡拉银行卡交易参数", allParams);
        assertTrue("获取拉卡拉银行卡b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS));
        Map<String, Object> lakalaTradeParams = MapUtil.getMap(allParams, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取拉卡拉银行卡b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(lakalaTradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为银行卡费率", lakalaTradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null);
    }

    // 商户层级使用新的方式生效套餐，门店层级也使用新的，使用门店配置
    @Test
    public void test5() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken), 
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                    StoreConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER,
                                            TransactionParam.CHANNEL_LADDER_FEE_RATES, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, Arrays.asList(
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                                    TransactionParam.FEE_RATE, "0"
                                                            ),
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                                    TransactionParam.FEE_RATE, "0.6"
                                                            )
                                                    ),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, Arrays.asList(
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                                    TransactionParam.FEE_RATE, "0.2"
                                                            ),
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                                    TransactionParam.FEE_RATE, "0.2"
                                                            )
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为固定费率", alipayv2TradeParams.get(TransactionParam.CHANNEL_LADDER_FEE_RATES) != null);
    }

    // 商户层级使用新的方式生效套餐，门店层级也使用新的，终端层级使用老的，使用终端配置
    @Test
    public void test6() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateStoreConfig(
                            MapUtil.hashMap(StoreConfig.STORE_ID, STORE_ID,
                                    StoreConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER,
                                            TransactionParam.CHANNEL_LADDER_FEE_RATES, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, Arrays.asList(
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                                    TransactionParam.FEE_RATE, "0"
                                                            ),
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                                    TransactionParam.FEE_RATE, "0.6"
                                                            )
                                                    ),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, Arrays.asList(
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                                    TransactionParam.FEE_RATE, "0.2"
                                                            ),
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                                    TransactionParam.FEE_RATE, "0.2"
                                                            )
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateTerminalConfig(
                            MapUtil.hashMap(TerminalConfig.TERMINAL_ID, TERMINAL_ID,
                                    TerminalConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    TerminalConfig.B2C_FORMAL, 1,
                                    TerminalConfig.B2C_STATUS, TerminalConfig.STATUS_OPENED,
                                    TerminalConfig.PARAMS, MapUtil.hashMap(
                                            MerchantConfig.LADDER_STATUS, 1,
                                            TransactionParam.LADDER_FEE_RATES, Arrays.asList(
                                                    MapUtil.hashMap(
                                                            TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                            TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                            TransactionParam.FEE_RATE, "0"
                                                    ),
                                                    MapUtil.hashMap(
                                                            TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                            TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                            TransactionParam.FEE_RATE, "0.6"
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );

        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为阶梯费率", alipayv2TradeParams.get(TransactionParam.LADDER_FEE_RATES) != null);
    }

    // 商户层级使用新的方式生效套餐，业务方商户旧的方式生效固定费率，使用业务方配置
    @Test
    public void test7() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        String appFeeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );

        Map<String, Object> allParams = call(
                () -> supportService.getAllParamsWithTradeApp(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE, "2"),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantAppConfig(
                            MapUtil.hashMap(MerchantAppConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantAppConfig.APP_ID, "2",
                                    MerchantAppConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantAppConfig.B2C_FORMAL, 1,
                                    MerchantAppConfig.B2C_STATUS, StoreConfig.STATUS_OPENED,
                                    MerchantAppConfig.B2C_FEE_RATE, appFeeRate,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );

        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertEquals("获取借贷分离费率失败，当前不为固定费率", alipayv2TradeParams.get(TransactionParam.FEE_RATE), appFeeRate);
    }

    // 商户层级使用新的方式生效套餐，业务方商户新的方式生效固定费率，使用业务方配置
    @Test
    public void test8() throws Exception {
        String appAuthToken = "TESTCASE_app_auth_token" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String category = "TESTCASE_category" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String sellerId = "TESTCASE_seller_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        String appFeeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.APP_AUTH_TOKEN, appAuthToken),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_ID, mchId),
                ImmutablePair.of(TransactionParam.ALIPAY_MCH_CATEGORY, category),
                ImmutablePair.of(TransactionParam.ALIPAY_SELLER_ID, sellerId)
        );

        Map<String, Object> allParams = call(
                () -> supportService.getAllParamsWithTradeApp(null, TERMINAL_SN, TradeConfigService.PAYWAY_ALIPAY, TradeConfigService.SUB_PAYWAY_BARCODE, "2"),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.ALIPAY_V2_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantAppConfig(
                            MapUtil.hashMap(MerchantAppConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantAppConfig.APP_ID, "2",
                                    MerchantAppConfig.PAYWAY, TradeConfigService.PAYWAY_ALIPAY2,
                                    MerchantAppConfig.B2C_FORMAL, 1,
                                    MerchantAppConfig.B2C_STATUS, StoreConfig.STATUS_OPENED,
                                    MerchantAppConfig.B2C_FEE_RATE, appFeeRate,
                                    MerchantAppConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER,
                                            TransactionParam.CHANNEL_LADDER_FEE_RATES, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, Arrays.asList(
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                                    TransactionParam.FEE_RATE, "0"
                                                            ),
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                                    TransactionParam.FEE_RATE, "0.6"
                                                            )
                                                    ),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, Arrays.asList(
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 0,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, 100,
                                                                    TransactionParam.FEE_RATE, "0.2"
                                                            ),
                                                            MapUtil.hashMap(
                                                                    TransactionParam.LADDER_FEE_RATE_MIN, 100,
                                                                    TransactionParam.LADDER_FEE_RATE_MAX, null,
                                                                    TransactionParam.FEE_RATE, "0.2"
                                                            )
                                                    )
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );

        assertNotNull("获取支付宝直连b2c交易参数", allParams);
        assertTrue("获取支付宝直连b2c交易参数失败:alipay_v2_trade_params", allParams.containsKey(TransactionParam.ALIPAY_V2_TRADE_PARAMS));
        Map<String, Object> alipayv2TradeParams = MapUtil.getMap(allParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取支付宝直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(alipayv2TradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为资金渠道阶梯费率", alipayv2TradeParams.get(TransactionParam.CHANNEL_LADDER_FEE_RATES) != null);
    }

 // 商户层级使用新的方式生效套餐
    @Test
    public void test9() throws Exception {
        String mercId = "TESTCASE_merc_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termId = "TESTCASE_term_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termNo = "TESTCASE_term_no" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_VERSION, TransactionParam.WEIXIN_VERSION_V3),
                ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_OPEN_MERC_ID, mercId),
                ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_ID, termId),
                ImmutablePair.of(TransactionParam.LAKALA_UNION_PAY_OPEN_TERM_NO, termNo)
        );

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_BANKCARD, TradeConfigService.SUB_PAYWAY_QRCODE),
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_BANKCARD,
                                    MerchantConfig.C2B_FORMAL, 0,
                                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.C2B_AGENT_NAME, "1034_21_false_true_0001",
                                    MerchantConfig.C2B_FEE_RATE, feeRate,
                                    MerchantConfig.PROVIDER, 1034,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight)),
                                            TransactionParam.FEE_RATE_TYPE, MerchantConfig.FEE_RATE_TYPE_CHANNEL,
                                            TransactionParam.PARAMS_BANKCARD_FEE, MapUtil.hashMap(
                                                    TransactionParam.PARAMS_BANKCARD_FEE_CREDIT, MapUtil.hashMap(TransactionParam.FEE, "0.38"),
                                                    TransactionParam.PARAMS_BANKCARD_FEE_DEBIT, MapUtil.hashMap(TransactionParam.FEE, "0.25")
                                            )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );

        assertNotNull("获取拉卡拉银行卡交易参数", allParams);
        assertTrue("获取拉卡拉银行卡b2c交易参数失败:lakala_open_trade_params", allParams.containsKey(TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS));
        Map<String, Object> lakalaTradeParams = MapUtil.getMap(allParams, TransactionParam.LAKALA_UNION_PAY_OPEN_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取拉卡拉银行卡b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(lakalaTradeParams, check.getLeft()));
        }
        assertTrue("获取借贷分离费率失败，当前不为银行卡费率", lakalaTradeParams.get(TransactionParam.PARAMS_BANKCARD_FEE) != null);
    }
}
