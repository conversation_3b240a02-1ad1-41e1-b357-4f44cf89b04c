package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 索迪斯交易参数获取
 * 
 */
public class SodexoAllParamsTest extends AllParamsBaseTest{
    
    /**
     * 索迪斯直连交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String tid = "TESTCASE_tid" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String clientId = "TESTCASE_client_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String clientSecret = "TESTCASE_client_secret" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        final List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.SODEXO_MID, mchId), 
                ImmutablePair.of(TransactionParam.SODEXO_TID, tid),
                ImmutablePair.of(TransactionParam.SODEXO_CLIENT_ID, clientId),
                ImmutablePair.of(TransactionParam.SODEXO_CLIENT_SECRET, clientSecret)
        );
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_SODEXO, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_SODEXO,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SODEXO_TRADE_PARAMS, checks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取索迪斯直连b2c交易参数", allParams);
        assertTrue("获取索迪斯直连b2c交易参数失败:sodexo_trade_params", allParams.containsKey(TransactionParam.SODEXO_TRADE_PARAMS));
        Map<String, Object> sodexoTradeParams = MapUtil.getMap(allParams, TransactionParam.SODEXO_TRADE_PARAMS);
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取索迪斯直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(sodexoTradeParams, check.getLeft()));
        }
    }
}
