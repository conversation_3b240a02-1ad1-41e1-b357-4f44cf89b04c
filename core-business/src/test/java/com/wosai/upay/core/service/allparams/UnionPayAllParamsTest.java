package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MerchantConfigCustom;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;

/**
 * 
 * 银联云闪付交易参数获取
 * 
 */
public class UnionPayAllParamsTest extends AllParamsBaseTest{
    
    /**
     * 银联云闪付直连交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        String upoMchId = "TESTCASE_upo_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termId = "TESTCASE_term_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_OPEN_MCH_ID, upoMchId),
                ImmutablePair.of(TransactionParam.UNION_PAY_OPEN_TERM_ID, termId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.UNION_PAY_OPEN_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
        );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_AGENT_NAME, "1017_17_*_false_false_0235",
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_OPEN,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_AGENT_NAME, "1017_17_*_false_false_0235",
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_OPEN,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_UNIONPAY, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_UNIONPAY,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取银联开发平台银联云闪付交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取银联开发平台银联云闪付交易参数失败:upo_trade_params", subpayway), allParams.containsKey(TransactionParam.UNION_PAY_OPEN_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.UNION_PAY_OPEN_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取银联开发平台银联云闪付交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 银联商务银联云闪付交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test2() throws Exception {
        String appId = "TESTCASE_app_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String appKey = "TESTCASE_app_key" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchCode = "TESTCASE_mch_code" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termId = "TESTCASE_term_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.CHINAUMS_APP_ID, appId),
                ImmutablePair.of(TransactionParam.CHINAUMS_APP_KEY, appKey),
                ImmutablePair.of(TransactionParam.CHINAUMS_MCH_CODE, mchCode),
                ImmutablePair.of(TransactionParam.CHINAUMS_TERM_CODE, termId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );

        // 
        Map chinaumsTradeParams = valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight));
        chinaumsTradeParams.put(TransactionParam.LIQUIDATION_NEXT_DAY, false);
        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(TransactionParam.CHINAUMS_TRADE_PARAMS, chinaumsTradeParams);
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_AGENT_NAME, "1018_17_*_false_false_0235",
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CHINAUMS,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_UNIONPAY, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_YS,
                                                TransactionParam.YS_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.YS_MCH_CODE, "TESTCASE_MERC_ID", TransactionParam.YS_CSB_MCH_CODE, "TESTCASE_MERC_ID"),

                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_UNIONPAY,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取银联商务银联云闪付交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取银联商务银联云闪付交易参数失败:upo_trade_params", subpayway), allParams.containsKey(TransactionParam.CHINAUMS_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.CHINAUMS_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取银联商务银联云闪付交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 通联银联云闪付交易参数获取
     * @throws Exception 
     * 
     */
    //@Test
    public void test3() throws Exception {
        String mchId = "TESTCASE_mch_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String termId = "TESTCASE_term_id" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchName = "TESTCASE_mch_name" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String mchCatCode = "TESTCASE_mch_cat_code" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String acqCode = "TESTCASE_acq_code" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_UNION_MCH_ID, mchId), 
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_UNION_TERM_ID, termId),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_UNION_MCH_NAME, mchName),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_UNION_MCH_CAT_CODE, mchCatCode),
                ImmutablePair.of("acq_code", acqCode),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.UNION_PAY_TL_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.B2C_AGENT_NAME, "1020_17_*_false_false_0243",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.C2B_AGENT_NAME, "1020_17_*_false_false_0243",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.WAP_AGENT_NAME, "1020_17_*_false_false_0243",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_UNIONPAY, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL,
                                                TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_UNIONPAY,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取通联银联云闪付交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取通联联银联云闪付交易参数失败:up_tl_trade_params", subpayway), allParams.containsKey(TransactionParam.UNION_PAY_TL_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取通联银联云闪付交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
}
