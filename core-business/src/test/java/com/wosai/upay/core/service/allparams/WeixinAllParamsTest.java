package com.wosai.upay.core.service.allparams;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import com.wosai.upay.core.model.MerchantAppConfig;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;

import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.MerchantConfigCustom;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.core.service.TradeConfigServiceImpl;

/**
 * 
 * 微信交易参数获取
 * 
 */
public class WeixinAllParamsTest extends AllParamsBaseTest{
    
    /**
     * 微信直连交易b2c参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test1() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
        
        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_BARCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                    MerchantConfig.B2C_FORMAL, 1,
                                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.B2C_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.WEIXIN_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                                                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                    MerchantConfigCustom.B2C_VALUE, goodsTag,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取微信直连b2c交易参数", allParams);
        assertTrue("获取微信直连b2c交易参数失败:weixin_trade_params", allParams.containsKey(TransactionParam.WEIXIN_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.WEIXIN_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)

        );
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取微信直连b2c交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }
    
    /**
     * 微信直连交易c2b参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test2() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_QRCODE), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                    MerchantConfig.C2B_FORMAL, 1,
                                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.C2B_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.WEIXIN_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                                                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                    MerchantConfigCustom.C2B_VALUE, goodsTag,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取微信直连c2b交易参数", allParams);
        assertTrue("获取微信直连c2b交易参数失败:weixin_trade_params", allParams.containsKey(TransactionParam.WEIXIN_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.WEIXIN_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取微信直连c2b交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }
    
    /**
     * 微信直连交易wap参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test3() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppSecret = "TESTCASE_WEIXIN_SUB_APPSECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_WAP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                    MerchantConfig.WAP_FORMAL, 1,
                                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,                                    
                                    MerchantConfig.WAP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.WEIXIN_WAP_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                                                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId,
                                                        TransactionParam.WEIXIN_SUB_APP_SECRET, weixinSubAppSecret
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                    MerchantConfigCustom.WAP_VALUE, goodsTag,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取微信直连b2c交易参数", allParams);
        assertTrue("获取微信直连b2c交易参数失败:weixin_wap_trade_params", allParams.containsKey(TransactionParam.WEIXIN_WAP_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.WEIXIN_WAP_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_SECRET, weixinSubAppSecret),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取微信直连wap交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }
    
    /**
     * 微信直连交易mini参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test4() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_MINI), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                    MerchantConfig.MINI_FORMAL, 1,
                                    MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.MINI_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.WEIXIN_MINI_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                                                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfigCustom(
                            MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                    MerchantConfigCustom.MINI_VALUE, goodsTag,
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                            )
                    );
                }
        );
        
        assertNotNull("获取微信直连mini交易参数", allParams);
        assertTrue("获取微信直连mini交易参数失败:weixin_mini_trade_params", allParams.containsKey(TransactionParam.WEIXIN_MINI_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取微信直连mini交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }

    /**
     * 微信直连交易app参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test5() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_APP), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                    MerchantConfig.APP_FORMAL, 1,
                                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.APP_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.WEIXIN_APP_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                                                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取微信直连app交易参数", allParams);
        assertTrue("获取微信直连app交易参数失败:weixin_app_trade_params", allParams.containsKey(TransactionParam.WEIXIN_APP_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.WEIXIN_APP_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取微信直连app交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }

    /**
     * 微信直连交易h5参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test6() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        Map<String, Object> allParams = call(
                () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, TradeConfigService.SUB_PAYWAY_H5), 
                () -> {
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.SWITCHES, MapUtil.hashMap(
                                                    TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                            ),
                                            TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                            TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                    updateMerchantConfig(
                            MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                    MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                    MerchantConfig.H5_FORMAL, 1,
                                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                                    MerchantConfig.H5_FEE_RATE, feeRate,
                                    MerchantConfig.PARAMS, MapUtil.hashMap(
                                            TransactionParam.WEIXIN_H5_TRADE_PARAMS, MapUtil.hashMap(
                                                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                                                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                                                    )
                                    ),
                                    DaoConstants.CTIME, System.currentTimeMillis(),
                                    DaoConstants.MTIME, System.currentTimeMillis()
                        )
                    );
                }
        );
        
        assertNotNull("获取微信直连h5交易参数", allParams);
        assertTrue("获取微信直连h5交易参数失败:weixin_h5_trade_params", allParams.containsKey(TransactionParam.WEIXIN_H5_TRADE_PARAMS));
        Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.WEIXIN_H5_TRADE_PARAMS);
        List<ImmutablePair<String, String>> checks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));
        for (ImmutablePair<String, String> check : checks) {
            assertEquals(String.format("获取微信直连h5交易参数失败:%s", check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
        }
    }

    /**
     * 网联微信交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test7() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Object> params = MapUtil.hashMap(
                TransactionParam.NUCC_TRADE_PARAMS, MapUtil.hashMap(
                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                    )
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_NUCC,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_NUCC,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_NUCC,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_MINI, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.MINI_FORMAL, 0, 
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_NUCC,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                        updateMerchantConfigCustom(
                                MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                        TradeConfigServiceImpl.subPaywaysValueColName.get(checkConfig.getKey() + ""), goodsTag,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取网联微信交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取网联微信交易参数失败:nucc_trade_params", subpayway), allParams.containsKey(TransactionParam.NUCC_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.NUCC_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取网联微信交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 银联微信交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test8() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Object> params = MapUtil.hashMap(
                TransactionParam.UNION_PAY_TRADE_PARAMS, MapUtil.hashMap(
                        TransactionParam.WEIXIN_SUB_MCH_ID, weixinSubMchId,
                        TransactionParam.WEIXIN_SUB_APP_ID, weixinSubAppId
                    )
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.B2C_AGENT_NAME, "1014_3_*_false_false_0206",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.C2B_AGENT_NAME, "1014_3_*_false_false_0206",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_AGENT_NAME, "1014_3_*_false_false_0206",
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                        updateMerchantConfigCustom(
                                MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                        TradeConfigServiceImpl.subPaywaysValueColName.get(checkConfig.getKey() + ""), goodsTag,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取网联微信交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取网联微信交易参数失败:up_trade_params", subpayway), allParams.containsKey(TransactionParam.UNION_PAY_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.UNION_PAY_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取网联微信交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 上海兴业微信交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test9() throws Exception {
        String mchiId = "TESTCASE_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.SWIFTPASS_MCH_ID, mchiId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate)
        );

        String cibshbankTradeParams = "cibshbank_trade_params";
        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                cibshbankTradeParams, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
        );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_AGENT_NAME, "1015_3_*_false_false_0002",
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CIBSHBANK,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_AGENT_NAME, "1015_3_*_false_false_0002",
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_CIBSHBANK,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                        updateMerchantConfigCustom(
                                MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                        TradeConfigServiceImpl.subPaywaysValueColName.get(checkConfig.getKey() + ""), goodsTag,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取上海兴业微信交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取上海兴业微信交易参数失败:cibshbank_trade_params", subpayway), allParams.containsKey(cibshbankTradeParams));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, cibshbankTradeParams);
            assertFalse(String.format("subpayway:%s 获取上海兴业微信交易参数失败:liquidation_next_day", subpayway), MapUtil.getBooleanValue(weixinTradeParams, TransactionParam.LIQUIDATION_NEXT_DAY));
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取上海兴业微信交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 拉卡拉银联微信交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test10() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppSecret = "TESTCASE_WEIXIN_SUB_APP_SECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinMiniSubAppId = "TESTCASE_WEIXIN_MINI_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinMiniSubAppSecret = "TESTCASE_WEIXIN_MINI_SUB_APP_SECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String providerMchId = "TESTCASE_PROVIDER_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.UNION_PAY_WEIXIN_SUB_APP_SECRET, weixinSubAppSecret),
                ImmutablePair.of(TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniSubAppId),
                ImmutablePair.of(TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniSubAppSecret),
                ImmutablePair.of(TransactionParam.UNION_PAY_DIRECT_PROVIDER_MCH_ID, providerMchId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_MINI, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.MINI_FORMAL, 0, 
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_FEE_RATE, feeRate,
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_LAKALA_UNION_PAY,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.LAKALA_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.LAKALA_MERC_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                        updateMerchantConfigCustom(
                                MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                        TradeConfigServiceImpl.subPaywaysValueColName.get(checkConfig.getKey() + ""), goodsTag,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取拉卡拉银联微信交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取拉卡拉银联微信交易参数失败:lkl_up_trade_params", subpayway), allParams.containsKey(TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.LAKALA_UNION_PAY_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取拉卡拉银联微信交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }
    
    /**
     * 通联银联微信交易参数获取
     * @throws Exception 
     * 
     */
    @Test
    public void test11() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppSecret = "TESTCASE_WEIXIN_SUB_APP_SECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinMiniSubAppId = "TESTCASE_WEIXIN_MINI_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinMiniSubAppSecret = "TESTCASE_WEIXIN_MINI_SUB_APP_SECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String providerMchId = "TESTCASE_PROVIDER_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID, weixinSubMchId), 
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_SECRET, weixinSubAppSecret),
                ImmutablePair.of(TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniSubAppId),
                ImmutablePair.of(TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniSubAppSecret),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_PROVIDER_MCH_ID, providerMchId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.UNION_PAY_TL_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
                );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0, 
                                        MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.B2C_FEE_RATE, feeRate,
                                        MerchantConfig.B2C_AGENT_NAME, "1020_3_*_false_true_0210",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0, 
                                        MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.C2B_FEE_RATE, feeRate,
                                        MerchantConfig.C2B_AGENT_NAME, "1020_3_*_false_true_0210",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0, 
                                        MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.WAP_FEE_RATE, feeRate,
                                        MerchantConfig.WAP_AGENT_NAME, "1020_3_*_false_true_0210",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_MINI, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.MINI_FORMAL, 0, 
                                        MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                        MerchantConfig.MINI_FEE_RATE, feeRate,
                                        MerchantConfig.MINI_AGENT_NAME, "1020_3_*_false_true_0210",
                                        MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                        MerchantConfig.PARAMS, params
                        ), valueChecks)
            );
        
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParams(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, checkConfig.getKey()), 
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                        ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_TL,
                                                TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                                ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                );
                        Map paywayConfig = 
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                        )
                                ;
                        paywayConfig.putAll(validateConfig.getLeft());
                        updateMerchantConfig(paywayConfig);
                        updateMerchantConfigCustom(
                                MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                        TradeConfigServiceImpl.subPaywaysValueColName.get(checkConfig.getKey() + ""), goodsTag,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );
                    }
            );
            
            assertNotNull(String.format("subpayway:%s 获取通联微信交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取通联联微信交易参数失败:up_tl_trade_params", subpayway), allParams.containsKey(TransactionParam.UNION_PAY_TL_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取通联微信交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
        }
    }

    /**
     * 支付业务参数获取
     * @throws Exception
     *
     */
    @Test
    public void test_merchant_trade_app() throws Exception {
        String weixinSubMchId = "TESTCASE_WEIXIN_SUB_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppId = "TESTCASE_WEIXIN_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinSubAppSecret = "TESTCASE_WEIXIN_SUB_APP_SECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinMiniSubAppId = "TESTCASE_WEIXIN_MINI_SUB_APP_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String weixinMiniSubAppSecret = "TESTCASE_WEIXIN_MINI_SUB_APP_SECRET" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String providerMchId = "TESTCASE_PROVIDER_MCH_ID" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String goodsTag = "TESTCASE_GOODS_TAG" + ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE);
        String feeRate = new BigDecimal(ThreadLocalRandom.current().nextDouble()).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();

        // 需要校验的列
        List<ImmutablePair<String, String>> valueChecks = Arrays.asList(
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID, weixinSubMchId),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_ID, weixinSubAppId),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_WEIXIN_SUB_APP_SECRET, weixinSubAppSecret),
                ImmutablePair.of(TransactionParam.WEIXIN_MINI_SUB_APP_ID, weixinMiniSubAppId),
                ImmutablePair.of(TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, weixinMiniSubAppSecret),
                ImmutablePair.of(TransactionParam.UNION_PAY_TL_PROVIDER_MCH_ID, providerMchId),
                ImmutablePair.of(TransactionParam.GOODS_TAG, goodsTag),
                ImmutablePair.of(TransactionParam.FEE_RATE, feeRate));

        // 配置
        Map<String, Map<String, String>> params = MapUtil.hashMap(
                TransactionParam.UNION_PAY_TL_TRADE_PARAMS, valueChecks.stream().collect(Collectors.toMap(ImmutablePair::getLeft, ImmutablePair::getRight))
        );
        Map<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfigs = MapUtil.hashMap(
                TradeConfigService.SUB_PAYWAY_BARCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.B2C_FORMAL, 0,
                                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                                MerchantConfig.B2C_FEE_RATE, feeRate,
                                MerchantConfig.B2C_AGENT_NAME, "1020_3_*_false_true_0210",
                                MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_QRCODE, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.C2B_FORMAL, 0,
                                MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                                MerchantConfig.C2B_FEE_RATE, feeRate,
                                MerchantConfig.C2B_AGENT_NAME, "1020_3_*_false_true_0210",
                                MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_WAP, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.WAP_FORMAL, 0,
                                MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                                MerchantConfig.WAP_FEE_RATE, feeRate,
                                MerchantConfig.WAP_AGENT_NAME, "1020_3_*_false_true_0210",
                                MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                MerchantConfig.PARAMS, params
                        ), valueChecks),
                TradeConfigService.SUB_PAYWAY_MINI, ImmutablePair.of(
                        MapUtil.hashMap(MerchantConfig.MINI_FORMAL, 0,
                                MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                                MerchantConfig.MINI_FEE_RATE, feeRate,
                                MerchantConfig.MINI_AGENT_NAME, "1020_3_*_false_true_0210",
                                MerchantConfig.PROVIDER, TradeConfigService.PROVIDER_UNIONPAY_TL,
                                MerchantConfig.PARAMS, params
                        ), valueChecks)
        );
        String tradeApp = "3";
        for (Map.Entry<Integer, ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>>> checkConfig : checkConfigs.entrySet()) {
            Integer subpayway = checkConfig.getKey();
            ImmutablePair<Map<String, Object>, List<ImmutablePair<String, String>>> validateConfig = checkConfig.getValue();
            Map<String, Object> allParams = call(
                    () -> supportService.getAllParamsWithTradeApp(null, TERMINAL_SN, TradeConfigService.PAYWAY_WEIXIN, checkConfig.getKey(), tradeApp),
                    () -> {
                        updateMerchantConfig(
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PARAMS, MapUtil.hashMap(
                                                TransactionParam.SWITCHES, MapUtil.hashMap(
                                                        TransactionParam.PAY_STATUS, TransactionParam.STATUS_OPENED
                                                ),
                                                TransactionParam.CLEARANCE_PROVIDER, TransactionParam.CLEARANCE_PROVIDER_LKL,
                                                TransactionParam.TL_TRADE_PARAMS, MapUtil.hashMap(TransactionParam.TL_MCH_ID, "TESTCASE_MERC_ID"),
                                                TransactionParam.MERCHANT_DAILY_MAX_SUM_OF_TRANS, Integer.MAX_VALUE
                                        ),
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );
                        Map paywayConfig =
                                MapUtil.hashMap(MerchantConfig.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfig.PAYWAY, TradeConfigService.PAYWAY_WEIXIN,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                );
                        paywayConfig.putAll(validateConfig.getLeft());
                        Integer provider = (Integer)paywayConfig.remove(MerchantConfig.PROVIDER);
                        Map<String, Object> pm = (Map)paywayConfig.remove(MerchantConfig.PARAMS);
                        updateMerchantConfig(paywayConfig);

                        updateMerchantConfigCustom(
                                MapUtil.hashMap(MerchantConfigCustom.MERCHANT_ID, MERCHANT_ID,
                                        MerchantConfigCustom.TYPE, MerchantConfigCustom.TYPE_WEIXIN_GOODS_TAG,
                                        TradeConfigServiceImpl.subPaywaysValueColName.get(checkConfig.getKey() + ""), goodsTag,
                                        DaoConstants.CTIME, System.currentTimeMillis(),
                                        DaoConstants.MTIME, System.currentTimeMillis()
                                )
                        );

                        paywayConfig.remove(DaoConstants.ID);
                        paywayConfig.put(MerchantAppConfig.PROVIDER, provider);
                        paywayConfig.put(MerchantAppConfig.PARAMS, pm);
                        paywayConfig.put(MerchantAppConfig.APP_ID, tradeApp);
                        updateMerchantAppConfig(paywayConfig);
                    }
            );

            assertNotNull(String.format("subpayway:%s 获取通联微信交易参数", subpayway), allParams);
            assertTrue(String.format("subpayway:%s 获取通联联微信交易参数失败:up_tl_trade_params", subpayway), allParams.containsKey(TransactionParam.UNION_PAY_TL_TRADE_PARAMS));
            Map<String, Object> weixinTradeParams = MapUtil.getMap(allParams, TransactionParam.UNION_PAY_TL_TRADE_PARAMS);
            for (ImmutablePair<String, String> check : validateConfig.getRight()) {
                assertEquals(String.format("subpayway:%s 获取通联微信交易参数失败:%s", subpayway, check.getLeft()), check.getRight(), MapUtil.getString(weixinTradeParams, check.getLeft()));
            }
            assertEquals(String.format("subpayway:%s 获取通联微信结算通道失败", subpayway), TransactionParam.CLEARANCE_PROVIDER_TL, MapUtil.getObject(allParams, TransactionParam.CLEARANCE_PROVIDER));
        }
    }
}
