package com.wosai.upay.core.service.base;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.base.BaseTest;
import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * solicitor 表依赖
 * INSERT INTO `solicitor` (`id`, `sn`, `name`, `category`, `status`, `cellphone`, `contact_name`, `contact_phone`, `contact_cellphone`, `contact_email`, `contact_address`, `province`, `city`, `district`, `street_address`, `bank_account_verify_status`, `open_apply_form_photo`, `check_list_photo`, `extra`, `ctime`, `mtime`, `deleted`, `version`)
 * VALUES
 * ('c97fcf9b-b300-11e5-9987-6c92bf21bb99', 'sctc-**********', '测试你好啊1', 0, 1, '***********', '测试', '', '***********', '<EMAIL>', '', '山西省', '太原市', '小店区', '淡淡的', -1, '74/6d7c568afc769aa153ff12d4357d8f96a17087.jpeg', '6e/58347b18a440f1063ec8ca6325a2f267f438ee.png', X'7B2272656D61726B223A22E696B9E88AB3E88AB3227D', *************, *************, 0, 1);
 * <p>
  INSERT INTO `solicitor` (`id`, `sn`, `name`, `category`, `status`, `cellphone`, `contact_name`, `contact_phone`, `contact_cellphone`, `contact_email`, `contact_address`, `province`, `city`, `district`, `street_address`, `bank_account_verify_status`, `open_apply_form_photo`, `check_list_photo`, `extra`, `ctime`, `mtime`, `deleted`, `version`)
  VALUES
  ('5637f900b18f-590a-8b34-88ee-c1225f73', 'sct-**********', '测试好', 0, 1, '***********', '荣', '', '***********', '<EMAIL>', '', '河北省', '秦皇岛市', '海港区', '点点滴滴', -1, '74/6d7c568afc769aa153ff12d4357d8f96a17087.jpeg', '6e/58347b18a440f1063ec8ca6325a2f267f438ee.png', X'7B2272656D61726B223A22E6B7A1E6B7A1E79A84227D', *************, *************, 0, 1);
 *
 *
 * // solicitor_config表依赖
 * INSERT INTO `solicitor_config` (`id`, `solicitor_id`, `payway`, `b2c_formal`, `b2c_status`, `b2c_fee_rate`, `b2c_agent_name`, `c2b_formal`, `c2b_fee_rate`, `c2b_agent_name`, `c2b_status`, `wap_formal`, `wap_status`, `wap_fee_rate`, `wap_agent_name`, `mini_formal`, `mini_status`, `mini_fee_rate`, `mini_agent_name`, `extend2_formal`, `extend2_status`, `extend2_fee_rate`, `extend2_agent_name`, `provider`, `params`, `ctime`, `mtime`, `deleted`, `version`)
 * VALUES
 * ('165c5754-624b-48a3-a65e-8b4cd8bc5981', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 1, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806869969, 1461743643705, 0, 1),
 * ('4c0d8d06-ce6f-4e15-a9eb-1ce62820ed41', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 3, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806869983, 1479269143134, 0, 5),
 * ('508568d8-f70f-4367-b2c3-540f71ba833b', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', NULL, NULL, NULL, '0.2', NULL, NULL, '0.2', NULL, NULL, NULL, NULL, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1003, X'7B2273746F72655F6461696C795F6D61785F73756D5F6F665F7472616E73223A2235303030222C226D65726368616E745F6461696C795F6D61785F73756D5F6F665F7472616E73223A2235303030227D', 1458806869902, 1458806869902, 0, 1),
 * ('59fa1d98-0502-4786-b2fa-f93bf6ba3761', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 2, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806869977, 1461744592285, 0, 1),
 * ('9639dd42-7351-4e2b-b72f-6e815b960333', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 6, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806870007, 1458806870007, 0, 1),
 * ('9639dd42-7351-4e2b-b72f-6e815b960334', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 8, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806870007, 1458806870007, 0, 1),
 * ('9639dd42-7351-4e2b-b72f-6e815b960335', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 17, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806870007, 1458806870007, 0, 1),
 * ('9639dd42-7351-4e2b-b72f-6e815b960338', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 7, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806870007, 1458806870007, 0, 1),
 * ('9639dd42-7351-4e2b-b72f-6e815b960bb2', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 5, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806870007, 1458806870007, 0, 1),
 * ('dfbd483b-4e35-42b7-8c67-7dbd50de4692', 'c97fcf9b-b300-11e5-9987-6c92bf21bb99', 4, 0, 1, '0.2', NULL, 0, '0.2', NULL, 1, 0, 1, '0.2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1458806869998, 1461653032365, 0, 1);
 *
 */
public class AbstractNewMerchantTest extends BaseTest {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    StoreService storeService;

    /**
     * 创建一个商户，并从数据库中查询出这个商户信息
     */
    protected Map createAndQueryMerchant() {
        String merchantName = "两元店商户";
        String longitude = "121.323649";
        String latitude = "31.239252";
        String alias = "商户ALIAS";
        String businessName = "行业分类为餐饮";
        String province = "江苏省";
        String city = "苏州市";
        String district = "姑苏区";
        String streetAddress = "大渡河路S90";
        String contactName = "联系人姓名SSS";
        String contactPhone = "010-00001111";
        String contactCellphone = "1888888";
        String contactEmail = "<EMAIL>";
        String legalPersonIdCardFrontPhoto = "法人身份证正面照";
        String legalPersonIdCardBackPhoto = "法人身份证反面照";
        String businessLicensePhoto = "营业执照照片";
        String business = "经营内容小吃";
        String ownerName = "所有人姓名为xxx";
        String ownerCellphone = "188888AAAA";
        String customerPhone = "166666AAAA";
        String logo = "门店码LOGO";
        String clientSn = "商户外部商户号";
        String vendorId = "859d9f5f-af99-11e5-9ec3-00163e00625b";
        String solicitorId = "c97fcf9b-b300-11e5-9987-6c92bf21bb99";
        String industry = "45570e40-d8c4-4ca1-a0ad-7a2a635a8e06";
        Map merchant = new HashMap();
        merchant.put(Merchant.NAME, merchantName);
        merchant.put(Merchant.ALIAS, alias);
        merchant.put(Merchant.LONGITUDE, longitude);
        merchant.put(Merchant.LATITUDE, latitude);
        merchant.put(Merchant.BUSINESS_NAME, businessName);
        merchant.put(Merchant.PROVINCE, province);
        merchant.put(Merchant.CITY, city);
        merchant.put(Merchant.DISTRICT, district);
        merchant.put(Merchant.STREET_ADDRESS, streetAddress);
        merchant.put(Merchant.CONTACT_NAME, contactName);
        merchant.put(Merchant.CONTACT_PHONE, contactPhone);
        merchant.put(Merchant.CONTACT_CELLPHONE, contactCellphone);
        merchant.put(Merchant.CONTACT_EMAIL, contactEmail);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO, legalPersonIdCardFrontPhoto);
        merchant.put(Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO, legalPersonIdCardBackPhoto);
        merchant.put(Merchant.BUSINESS_LICENSE_PHOTO, businessLicensePhoto);
        merchant.put(Merchant.BUSINESS, business);
        merchant.put(Merchant.OWNER_NAME, ownerName);
        merchant.put(Merchant.OWNER_CELLPHONE, ownerCellphone);
        merchant.put(Merchant.CUSTOMER_PHONE, customerPhone);
        merchant.put(Merchant.INDUSTRY, industry);
        merchant.put(Merchant.LOGO, logo);
        merchant.put(Merchant.CLIENT_SN, clientSn);
        merchant.put(Merchant.VENDOR_ID, vendorId);
        merchant.put(Merchant.SOLICITOR_ID, solicitorId);
        Map result = merchantService.createMerchant(merchant);
        Assert.assertNotNull("创建商户返回值不能为 null", result);
        Assert.assertEquals("新建商户的前后数据不一致", merchantName, MapUtils.getString(merchant, Merchant.NAME));
        Assert.assertEquals("新建商户的前后数据不一致", longitude, MapUtils.getString(merchant, Merchant.LONGITUDE));
        Assert.assertEquals("新建商户的前后数据不一致", latitude, MapUtils.getString(merchant, Merchant.LATITUDE));
        Assert.assertEquals("新建商户的前后数据不一致", alias, MapUtils.getString(merchant, Merchant.ALIAS));
        Assert.assertEquals("新建商户的前后数据不一致", businessName, MapUtils.getString(merchant, Merchant.BUSINESS_NAME));
        Assert.assertEquals("新建商户的前后数据不一致", province, MapUtils.getString(merchant, Merchant.PROVINCE));
        Assert.assertEquals("新建商户的前后数据不一致", city, MapUtils.getString(merchant, Merchant.CITY));
        Assert.assertEquals("新建商户的前后数据不一致", district, MapUtils.getString(merchant, Merchant.DISTRICT));
        Assert.assertEquals("新建商户的前后数据不一致", streetAddress, MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        Assert.assertEquals("新建商户的前后数据不一致", contactName, MapUtils.getString(merchant, Merchant.CONTACT_NAME));
        Assert.assertEquals("新建商户的前后数据不一致", contactPhone, MapUtils.getString(merchant, Merchant.CONTACT_PHONE));
        Assert.assertEquals("新建商户的前后数据不一致", contactCellphone, MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE));
        Assert.assertEquals("新建商户的前后数据不一致", contactEmail, MapUtils.getString(merchant, Merchant.CONTACT_EMAIL));
        Assert.assertEquals("新建商户的前后数据不一致", legalPersonIdCardFrontPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
        Assert.assertEquals("新建商户的前后数据不一致", legalPersonIdCardBackPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        Assert.assertEquals("新建商户的前后数据不一致", businessLicensePhoto, MapUtils.getString(merchant, Merchant.BUSINESS_LICENSE_PHOTO));
        Assert.assertEquals("新建商户的前后数据不一致", business, MapUtils.getString(merchant, Merchant.BUSINESS));
        Assert.assertEquals("新建商户的前后数据不一致", ownerName, MapUtils.getString(merchant, Merchant.OWNER_NAME));
        Assert.assertEquals("新建商户的前后数据不一致", ownerCellphone, MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        Assert.assertEquals("新建商户的前后数据不一致", customerPhone, MapUtils.getString(merchant, Merchant.CUSTOMER_PHONE));
        Assert.assertEquals("新建商户的前后数据不一致", clientSn, MapUtils.getString(merchant, Merchant.CLIENT_SN));
        Assert.assertEquals("新建商户的前后数据不一致", industry, MapUtils.getString(merchant, Merchant.INDUSTRY));
        Assert.assertEquals("新建商户的前后数据不一致", vendorId, MapUtils.getString(merchant, Merchant.VENDOR_ID));
        Assert.assertEquals("新建商户的前后数据不一致", solicitorId, MapUtils.getString(merchant, Merchant.SOLICITOR_ID));
        Assert.assertNotNull("获取商户返回值为空，初始化商户信息有问题", result);
        String merchantId = MapUtils.getString(merchant, DaoConstants.ID);
        merchant = merchantService.getMerchantByMerchantId(merchantId);
        Assert.assertEquals("获取商户数据与预期数据不一致", merchantName, MapUtils.getString(merchant, Merchant.NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", longitude, MapUtils.getString(merchant, Merchant.LONGITUDE));
        Assert.assertEquals("获取商户数据与预期数据不一致", latitude, MapUtils.getString(merchant, Merchant.LATITUDE));
        Assert.assertEquals("获取商户数据与预期数据不一致", alias, MapUtils.getString(merchant, Merchant.ALIAS));
        Assert.assertEquals("获取商户数据与预期数据不一致", businessName, MapUtils.getString(merchant, Merchant.BUSINESS_NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", province, MapUtils.getString(merchant, Merchant.PROVINCE));
        Assert.assertEquals("获取商户数据与预期数据不一致", city, MapUtils.getString(merchant, Merchant.CITY));
        Assert.assertEquals("获取商户数据与预期数据不一致", district, MapUtils.getString(merchant, Merchant.DISTRICT));
        Assert.assertEquals("获取商户数据与预期数据不一致", streetAddress, MapUtils.getString(merchant, Merchant.STREET_ADDRESS));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactName, MapUtils.getString(merchant, Merchant.CONTACT_NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactPhone, MapUtils.getString(merchant, Merchant.CONTACT_PHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactCellphone, MapUtils.getString(merchant, Merchant.CONTACT_CELLPHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", contactEmail, MapUtils.getString(merchant, Merchant.CONTACT_EMAIL));
        Assert.assertEquals("获取商户数据与预期数据不一致", legalPersonIdCardFrontPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_FRONT_PHOTO));
        Assert.assertEquals("获取商户数据与预期数据不一致", legalPersonIdCardBackPhoto, MapUtils.getString(merchant, Merchant.LEGAL_PERSON_ID_CARD_BACK_PHOTO));
        Assert.assertEquals("获取商户数据与预期数据不一致", businessLicensePhoto, MapUtils.getString(merchant, Merchant.BUSINESS_LICENSE_PHOTO));
        Assert.assertEquals("获取商户数据与预期数据不一致", business, MapUtils.getString(merchant, Merchant.BUSINESS));
        Assert.assertEquals("获取商户数据与预期数据不一致", ownerName, MapUtils.getString(merchant, Merchant.OWNER_NAME));
        Assert.assertEquals("获取商户数据与预期数据不一致", ownerCellphone, MapUtils.getString(merchant, Merchant.OWNER_CELLPHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", customerPhone, MapUtils.getString(merchant, Merchant.CUSTOMER_PHONE));
        Assert.assertEquals("获取商户数据与预期数据不一致", clientSn, MapUtils.getString(merchant, Merchant.CLIENT_SN));
        Assert.assertEquals("获取商户数据与预期数据不一致", vendorId, MapUtils.getString(merchant, Merchant.VENDOR_ID));
        Assert.assertEquals("获取商户数据与预期数据不一致", solicitorId, MapUtils.getString(merchant, Merchant.SOLICITOR_ID));
        Assert.assertEquals("商户状态不正确", Merchant.STATUS_ENABLED, (int) MapUtils.getInteger(merchant, Merchant.STATUS));
        return merchant;
    }

    protected Map createStoreAndMerchant() {
        Map merchant = createAndQueryMerchant();
        return createStoreByMerchantId(MapUtils.getString(merchant, DaoConstants.ID), null);
    }

    protected Map createStoreByMerchantId(String merchantId, String clientSn) {
        Map store = new HashMap();
        String storeName = "广州大拇指信息科技有限公司";
        String storeContactName = "贺深书";
        String storeCellPhone = "15218022949";
        String storeProvince = "广东省";
        String storeCity = "广东市";
        String storeDistrict = "";
        String storeStreetAddress = "恒然创意园D座1梯";

        String storeLongitude = "113.386259";
        String storeLatitude = "22.975204";
        String storeSolicitorLd = "c97fcf9b-b300-11e5-9987-6c92bf21bb99";
        store.put(Store.NAME, storeName);
        store.put(Store.CONTACT_NAME, storeContactName);
        store.put(Store.CONTACT_CELLPHONE, storeCellPhone);
        store.put(Store.PROVINCE, storeProvince);
        store.put(Store.CITY, storeCity);
        store.put(Store.DISTRICT, storeDistrict);
        store.put(Store.STREET_ADDRESS, storeStreetAddress);
        store.put(Store.LONGITUDE, storeLongitude);
        if (!StringUtil.empty(clientSn)) {
            store.put(Store.CLIENT_SN, clientSn);
        } else {
            store.put(Store.CLIENT_SN, UUID.randomUUID().toString());
        }
        store.put(Store.LATITUDE, storeLatitude);
        store.put(Store.SOLICITOR_ID, storeSolicitorLd);
        store.put(Store.MERCHANT_ID, merchantId);
        Map result = storeService.createStore(store);
        String storeId = BeanUtil.getPropString(result, DaoConstants.ID);
        Map queryStore = storeService.getStoreByStoreId(storeId);
        Assert.assertEquals("获取门店数据与预期数据不一致", storeName, MapUtils.getString(queryStore, Store.NAME));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeContactName, MapUtils.getString(queryStore, Store.CONTACT_NAME));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeCellPhone, MapUtils.getString(queryStore, Store.CONTACT_CELLPHONE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeProvince, MapUtils.getString(queryStore, Store.PROVINCE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeCity, MapUtils.getString(queryStore, Store.CITY));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeDistrict, MapUtils.getString(queryStore, Store.DISTRICT));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeStreetAddress, MapUtils.getString(queryStore, Store.STREET_ADDRESS));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeLongitude, MapUtils.getString(queryStore, Store.LONGITUDE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeLatitude, MapUtils.getString(queryStore, Store.LATITUDE));
        Assert.assertEquals("获取门店数据与预期数据不一致", storeSolicitorLd, MapUtils.getString(queryStore, Store.SOLICITOR_ID));
        Assert.assertEquals("获取门店数据与预期数据不一致", Store.STATUS_ENABLED, MapUtils.getIntValue(queryStore, Store.STATUS));
        return queryStore;

    }

}
