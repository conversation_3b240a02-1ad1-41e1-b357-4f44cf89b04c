package com.wosai.upay.core.service.base;

import com.wosai.upay.core.CoreBusinessApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringRunner.class)
@PowerMockIgnore({"javax.management.*", "sun.security.*", "javax.net.*", "javax.net.ssl.*","javax.crypto.*"}) //为了解决使用powermock后，提示classloader错误
@ContextConfiguration(classes = {CoreBusinessApplication.class})
@SpringBootTest
public class BaseTest {


    @Test
    public void test() {
    }
}
