package com.wosai.upay.core.service.biz;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.info.api.model.BankInfo;
import com.wosai.upay.core.service.base.BaseTest;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
@Ignore
public class BankInfoBizTest extends BaseTest{

    @Autowired
    private BankInfoBiz bankInfoBiz;

//    @Test
//    public void appendOpeningClearingNum() throws Exception {
//        Map<String, Object> map = CollectionUtil.hashMap(
////                BankInfo.BANK_NAME,"中国工商银行",
//                BankInfo.BRANCH_NAME,"中国工商银行长沙岳麓山支行市政中心分理处"
//        );
//        bankInfoBiz.appendOpeningClearingNum(map);
//        Assert.assertNotNull(map.get(BankInfo.OPENING_NUMBER));
//        Assert.assertNotNull(map.get(BankInfo.CLEARING_NUMBER));
//
//    }
//
//    @Test
//    public void appendBankBranchName() throws Exception {
//        Map<String, Object> map = CollectionUtil.hashMap(
//                BankInfo.OPENING_NUMBER,"************"
//        );
//        bankInfoBiz.appendBankBranchName(map);
//        Assert.assertNotNull(map.get(BankInfo.BANK_NAME));
//        Assert.assertNotNull(map.get(BankInfo.BRANCH_NAME));
//        bankInfoBiz.appendBankBranchName(map);
//    }

}