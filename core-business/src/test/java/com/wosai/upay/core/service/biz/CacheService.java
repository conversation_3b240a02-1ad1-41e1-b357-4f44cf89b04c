package com.wosai.upay.core.service.biz;

import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.constant.PublicConstants;
import com.wosai.upay.core.model.Solicitor;
import com.wosai.upay.core.model.Vendor;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SolicitorService;
import com.wosai.upay.core.service.VendorService;
import com.wosai.upay.core.service.base.BaseTest;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class CacheService extends BaseTest {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private CacheManager cacheManage;

    @Autowired
    private SolicitorService solicitorService;

    @Autowired
    private VendorService vendorService;

    @Test
    @SuppressWarnings("unchecked")
    public void getMerchantInfo() throws InterruptedException {
        Map<String,Object> map1 = merchantService.getMerchantBySn("mch-1680001623759");
        Map<String,Object> map2 = merchantService.getMerchantBySn("mch-1680001623759");
        if (map1 != null) {
            assert map1.size() == map2.size();
        }
        for (String key : map1.keySet()) {
            System.out.println(key + map2.get(key));
            if(map1.get(key)!=null){
                assert map2.get(key).equals(map1.get(key));
            }
        }
        solicitorService.updateSolicitor(new HashMap(){{
            put(DaoConstants.ID,"00f1754f-9c57-4212-bee6-22520beaa679");
            put(Solicitor.NAME,"王薇1");
        }});

        vendorService.updateVendor(new HashMap(){{
            put(DaoConstants.ID,"859d9f5f-af99-11e5-9ec3-00163e00625b");
            put(Vendor.NAME,"shouqianba223");
        }});

        Thread.sleep(1000);
        Map<String,Object> map3 = merchantService.getMerchantBySn("1680002783135");
        if (map3 != null) {
            System.out.println(map3);
            assert "王薇1".equals(String.valueOf(map3.get("solicitor_name")));
            assert "shouqianba223".equals(String.valueOf(map3.get("vendor_name")));
        }
    }

    @Test
    public void testCache() throws InterruptedException {
        Cache cache = cacheManage.getCache(PublicConstants.VENDOR_APP_INFO_BY_ID);
        for (int i=0;i<100;i++){
            int j= i;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    cache.put(new Element("1", j));
                }
            }).start();
        }
        Thread.sleep(5000);
        Element element = cache.get("1");
        System.out.println(element.getObjectValue());
    }

}
