package com.wosai.upay.core.util;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class ApolloConfigurationCenterUtilTest {

    @Test
    public void test() throws Exception {
        ApolloConfigurationCenterUtil util = new ApolloConfigurationCenterUtil();
        
        assertEquals("1562900", util.getMerchantDistrictCode("上海市", "市辖区"));
        assertEquals("1569020", util.getMerchantDistrictCode("新疆维吾尔自治区", "阿勒泰地区"));
        assertEquals("1567710", util.getMerchantDistrictCode("西藏自治区", "拉萨市"));
        assertEquals("1566580", util.getMerchantDistrictCode("四川省", "德阳市"));
        assertEquals("1562900", util.getMerchantDistrictCode("四川省", "测试"));
        assertEquals("1563320", util.getMerchantDistrictCode("浙江省", "宁波市"));


    }

}
