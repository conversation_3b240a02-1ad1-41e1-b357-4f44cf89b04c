package com.wosai.upay.core.util;

import com.wosai.upay.core.exception.CoreInvalidParameterException;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public class EffectiveTimeUtilTest {

    @Rule
    public ExpectedException thrown = ExpectedException.none();

    /**
     * 等于7天
     */
    @Test
    public void testCheckoutEffectiveTime01() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        String endDay = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
        System.out.println(endDay);
        thrown.expect(CoreInvalidParameterException.class);
        EffectiveTimeUtil.checkoutEffectiveTime("20051112-" + endDay);
    }

    /**
     * 大于7天
     */
    @Test
    public void testCheckoutEffectiveTime02() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 8);
        String endDay = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
        EffectiveTimeUtil.checkoutEffectiveTime("20051112-" + endDay);
    }

    /**
     * 日期格式错误
     */
    @Test
    public void testCheckoutEffectiveTime03() {
        thrown.expect(CoreInvalidParameterException.class);
        EffectiveTimeUtil.checkoutEffectiveTime("12341234-20231314");
    }

    /**
     * 有效期格式错误
     */
    @Test
    public void testCheckoutEffectiveTime04() {
        thrown.expect(CoreInvalidParameterException.class);
        EffectiveTimeUtil.checkoutEffectiveTime("1234123420231314");
    }

    /**
     * 结束日期格式错误
     */
    @Test
    public void testCheckoutEffectiveTime05() {
        thrown.expect(CoreInvalidParameterException.class);
        EffectiveTimeUtil.checkoutEffectiveTime("12341234-2023110");
    }
}