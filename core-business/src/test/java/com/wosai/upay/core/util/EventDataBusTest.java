package com.wosai.upay.core.util;

import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.audit.MerchantAuditStatusChangeEvent;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.terminal.basic.TerminalBasicUpdateEvent;
import org.junit.Assert;
import org.junit.Test;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/8/12.
 */
public class EventDataBusTest {

    @Test
    public void testConcurrentCreateEvent(){
        ThreadPoolExecutor service = (ThreadPoolExecutor) Executors.newFixedThreadPool(10);
        int taskCount = 100;
        for (int i = 0; i < taskCount; i++) {
            service.submit(() -> {
                if(ThreadLocalRandom.current().nextBoolean()){
                    System.out.println(String.format("开始创建事件, 线程%s, 事件 TerminalBasicUpdateEvent", Thread.currentThread().getId()));
                    TerminalBasicUpdateEvent event = new TerminalBasicUpdateEvent();
                    Assert.assertNotNull(event);
                    System.out.println(String.format("结束创建事件, 线程%s, 事件 TerminalBasicUpdateEvent", Thread.currentThread().getId()));
                }else if(ThreadLocalRandom.current().nextBoolean()){
                    System.out.println(String.format("开始创建事件, 线程%s, 事件 FeeRateEvent", Thread.currentThread().getId()));
                    FeeRateEvent event = new FeeRateEvent();
                    Assert.assertNotNull(event);
                    System.out.println(String.format("结束创建事件, 线程%s, 事件 FeeRateEvent", Thread.currentThread().getId()));
                }else {
                    System.out.println(String.format("开始查找事件, 线程%s, 事件 MerchantAuditStatusChangeEvent", Thread.currentThread().getId()));
                    Class<? extends AbstractEvent> event = AbstractEvent.classFor(MerchantAuditStatusChangeEvent.module(), MerchantAuditStatusChangeEvent.objectType(), MerchantAuditStatusChangeEvent.eventType());
                    Assert.assertNotNull(event);
                    System.out.println(String.format("结束查找事件, 线程%s, 事件 MerchantAuditStatusChangeEvent", Thread.currentThread().getId()));
                }
            });
        }
        int waitMills = 60 * 1000; //60s， 60s内处理不完则报错
        long start = System.currentTimeMillis();
        while (true){
            if(service.getCompletedTaskCount() == taskCount){
                service.shutdownNow();
                break;
            }else{
                if(System.currentTimeMillis() - start > waitMills){
                    throw new RuntimeException(String.format("并发创建查找事件异常, 应处理数量%d, 已成功处理数量%d", taskCount, service.getCompletedTaskCount()));
                }
            }
        }
    }

}
