```mermaid
classDiagram
    class Merchant {
        +String id
        +String sn
        +String name
        +String alias
        +String business_name
        +String industry
        +int status
        +String vendor_id
        +String solicitor_id
        +String currency
        +bindBankAccount()
        +updateStatus()
    }
    
    class Store {
        +String id
        +String merchant_id
        +String sn
        +String name
        +String address
        +String district_code
        +int status
        +createTerminal()
        +updateStatus()
    }
    
    class Terminal {
        +String id
        +String store_id
        +String sn
        +String name
        +String device_fingerprint
        +String category
        +int status
        +activate()
        +updateStatus()
    }
    
    class MerchantBankAccount {
        +String id
        +String merchant_id
        +String account_name
        +String account_no
        +String bank_name
        +String branch_name
        +int verify_status
        +verify()
        +update()
    }
    
    class MerchantConfig {
        +String id
        +String merchant_id
        +String store_id
        +String terminal_id
        +int payway
        +int sub_payway
        +String trade_app
        +String provider
        +Map trade_params
        +int status
        +updateStatus()
        +updateTradeParams()
    }
    
    class TransactionParam {
        +static String PROVIDER
        +static String ACTIVE
        +static String WEIXIN_TRADE_PARAMS
        +static String ALIPAY_V2_TRADE_PARAMS
        +static String PROVIDER_MCH_ID
        +static String FEE_RATE
    }
    
    class Payway {
        +static Payway ALIPAY
        +static Payway WEIXIN
        +static Payway UNIONPAY
        +Integer code
        +String name
        +getNameByCode()
        +getAll()
    }
    
    class SubPayway {
        +static SubPayway BARCODE
        +static SubPayway QRCODE
        +static SubPayway WAP
        +static SubPayway MINI
        +Integer code
        +String name
        +getNameByCode()
        +getAll()
    }
    
    class Provider {
        +static Provider WEIXIN
        +static Provider ALIPAY
        +static Provider UNIONPAY
        +static Provider LAKALA
        +Integer code
        +String name
        +getNameByCode()
        +getAll()
    }
    
    class Account {
        +String id
        +String username
        +String password
        +String phone
        +String email
        +int status
        +authenticate()
        +updateStatus()
    }
    
    Merchant "1" -- "n" Store : owns
    Store "1" -- "n" Terminal : has
    Merchant "1" -- "n" MerchantBankAccount : binds
    Merchant "1" -- "n" MerchantConfig : configures
    Store "1" -- "n" MerchantConfig : configures
    Terminal "1" -- "n" MerchantConfig : configures
    MerchantConfig -- TransactionParam : uses
    MerchantConfig -- Payway : references
    MerchantConfig -- SubPayway : references
    MerchantConfig -- Provider : references
    Account "n" -- "1" Merchant : manages
```
