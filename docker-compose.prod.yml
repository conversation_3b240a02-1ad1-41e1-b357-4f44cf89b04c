version: '2'
services:
  upay-core-business:
    build: .
    ports:
      - "19800:8080"
      - "8719:8719"
    volumes:
      - /app/log/core-business:/var/lib/jetty/logs
      - /app/conf/core-business:/var/lib/jetty/resources
      - /opt/data:/opt/data
      - /opt/settings:/opt/settings

    {% if inventory_hostname.startswith('sz')  %}

    extra_hosts:
      - "zipkin-kafka-001.shouqianba.com:************"
      - "zipkin-kafka-002.shouqianba.com:**************"
      - "zipkin-kafka-003.shouqianba.com:*************"

    {% endif %}

    environment:
      - JAVA_TOOL_OPTIONS=-Xms512m -Xmx8g --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.net.util=ALL-UNNAMED -DisK8s=false -XX:+UnlockExperimentalVMOptions -XX:+UseZGC -XX:ConcGCThreads=2 -XX:ParallelGCThreads=8 -XX:ZCollectionInterval=60 -XX:ZAllocationSpikeTolerance=5 -XX:+UnlockDiagnosticVMOptions -XX:-ZProactive -Xlog:safepoint,classhisto*=trace,age*,gc*=info:file=/var/lib/jetty/logs/gc.log:time -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/var/lib/jetty/logs/heapdump.hpro -Denv=pro -Djetty.threadPool.maxThreads=500 -Djetty.http.idleTimeout=3000 -Dhera.meta.host_name=hera-meta.vpc.shouqianba.com -Dhera.meta.http_port=80 -Dhera.meta.grpc_port=8082 -Dhera.jaeger.collector_endpoint=http://hera-otel-collector.vpc.shouqianba.com/api/traces -javaagent:/app/hera-agent/agent/hera-agent.jar=agent.service_name=upay-core-business -Dskywalking.plugin.kafka.enable_inject_kafka_header=false -Dvault.project.name=core-business -Dhera.jaeger.baggage_items=fake -Dhera.sentinel.switch_flag=true -DlogDir=/var/lib/jetty/logs {% if not inventory_hostname.startswith('sz')  %} -Dspring.profiles.active=prod -Dshouqianba.region=hangzhou {% else %} -Dspring.profiles.active=prodhuanan -Dshouqianba.region=shenzhen {% endif %}
