```mermaid
sequenceDiagram
    participant Client as 客户端/终端
    participant API as API网关
    participant Core as 核心业务系统
    participant Risk as 风控系统
    participant Provider as 支付提供商
    participant DB as 数据库
    participant Cache as Redis缓存
    
    Client->>API: 发起支付请求
    API->>Core: 转发请求
    
    Core->>Cache: 获取商户/终端信息
    Cache-->>Core: 返回缓存数据
    
    Core->>DB: 查询商户配置
    DB-->>Core: 返回商户配置
    
    Core->>Risk: 风控检查
    Risk-->>Core: 风控结果
    
    alt 风控通过
        Core->>Core: 根据支付方式和模式选择支付通道
        Core->>Core: 构建支付参数
        Core->>Provider: 调用支付提供商接口
        Provider-->>Core: 返回支付结果
        
        Core->>DB: 记录交易信息
        DB-->>Core: 保存成功
        
        Core-->>API: 返回支付结果
        API-->>Client: 返回支付结果
    else 风控拒绝
        Core-->>API: 返回风控拒绝
        API-->>Client: 返回风控拒绝
    end
    
    alt 异步通知
        Provider->>API: 支付结果通知
        API->>Core: 转发通知
        Core->>DB: 更新交易状态
        DB-->>Core: 更新成功
        Core-->>API: 确认接收通知
        API-->>Provider: 确认接收通知
    end
```
