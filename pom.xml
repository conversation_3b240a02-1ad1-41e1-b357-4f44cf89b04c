<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai</groupId>
        <artifactId>common-parent</artifactId>
        <version>2.0-SNAPSHOT</version>
    </parent>

    <groupId>com.wosai.upay</groupId>
    <artifactId>core-business-parent</artifactId>
    <version>3.9.40-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <nextgen.version>2.0-RC1</nextgen.version>
        <spring4-boot.version>1.0-SNAPSHOT</spring4-boot.version>
        <confluent.version>4.0.0</confluent.version>
        <avro.version>1.8.2</avro.version>
    </properties>

    <modules>
        <module>core-business-api</module>
        <module>core-business</module>
        <module>core-business-util</module>
        <module>core-business-meta</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.2-RC3</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wosai-brave-api</artifactId>
                    <groupId>com.wosai</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wosai-brave153-api</artifactId>
                    <groupId>com.wosai</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>spring4-boot-dependencies</artifactId>
                <version>${spring4-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.32</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>4.1.116.Final</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <configuration>
                    <destFile>${project.build.directory}/coverage-reports/jacoco-unit.exec</destFile>
                    <dataFile>${project.build.directory}/coverage-reports/jacoco-unit.exec</dataFile>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-prepare</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>**/model/**.class</exclude>
                                <exclude>wosai/upay/core/model/*</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>jacoco-report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>


            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

</project>
