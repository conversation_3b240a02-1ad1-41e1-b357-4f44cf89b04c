-- <PERSON><PERSON><PERSON> Workbench Forward Engineering

SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='TRADITIONAL,ALLOW_INVALID_DATES';

-- -----------------------------------------------------
-- Schema upay_core
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema upay_core
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `upay_core` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
-- -----------------------------------------------------
-- Schema upay_user
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema upay_user
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `upay_user`  DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
-- -----------------------------------------------------
-- Schema upay
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema upay
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `upay`  DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
-- -----------------------------------------------------
-- Schema upay_log
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema upay_log
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `upay_log`  DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
-- -----------------------------------------------------
-- Schema upay_message
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema upay_message
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `upay_message`  DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
-- -----------------------------------------------------
-- Schema upay_application
-- -----------------------------------------------------

-- -----------------------------------------------------
-- Schema upay_application
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `upay_application`  DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
USE `upay_core` ;

-- -----------------------------------------------------
-- Table `upay_core`.`group`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`group` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `sn` VARCHAR(32) NOT NULL COMMENT '集团编号',
  `name` VARCHAR(128) NOT NULL COMMENT '集团名称',
  `alias` VARCHAR(128) NULL COMMENT '集团简称',
  `industry` varchar(36) NULL DEFAULT NULL COMMENT '',
  `status` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '',
  `manager_merchant_num` INT(11) NOT NULL DEFAULT 0 COMMENT '管理商户数',
  `longitude` VARCHAR(16) NULL COMMENT '经度',
  `latitude` VARCHAR(16) NULL DEFAULT NULL COMMENT '纬度',
  `province` VARCHAR(32) NULL DEFAULT NULL COMMENT '省',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '市',
  `district` VARCHAR(32) NULL DEFAULT NULL COMMENT '区',
  `street_address` VARCHAR(255) NULL COMMENT '街道门牌号',
  `contact_name` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系固定电话号码',
  `contact_cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系移动电话号码',
  `client_sn` VARCHAR(50) NULL COMMENT '集团外部集团号\n',
  `vendor_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '服务商UUID',
  `solicitor_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '推广者UUID',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_sn` (`sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '集团';

-- -----------------------------------------------------
-- Table `upay_core`.`merchant`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`merchant` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `sn` VARCHAR(32) NOT NULL COMMENT '商户可见的商户号',
  `name` VARCHAR(128) NOT NULL COMMENT '商户名',
  `alias` VARCHAR(128) NULL COMMENT '商户别名/常用名',
  `industry` varchar(36) NULL DEFAULT NULL COMMENT '',
  `status` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '',
  `rank` INT(11) NULL COMMENT '商户信用等级，初始值从推广者继承过来。',
  `withdraw_mode` INT(11) NOT NULL DEFAULT '1' COMMENT '1：人工提现；2：智能提现，其他值根据业务定义',
  `longitude` VARCHAR(16) NULL COMMENT '经度',
  `latitude` VARCHAR(16) NULL DEFAULT NULL COMMENT '纬度',
  `province` VARCHAR(32) NULL DEFAULT NULL COMMENT '省',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '市',
  `district` VARCHAR(32) NULL DEFAULT NULL COMMENT '区',
  `street_address` VARCHAR(255) NULL COMMENT '街道门牌号',
  `contact_name` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系固定电话号码',
  `contact_cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系移动电话号码',
  `contact_email` VARCHAR(64) NULL DEFAULT NULL COMMENT '联系邮箱',
  `legal_person_type` INT(11) NULL COMMENT '法人类型 1:个人 2:企业',
  `legal_person_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人姓名',
  `legal_person_id_type` INT(11) NULL DEFAULT 1 COMMENT '法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；',
  `legal_person_id_number` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人证件号码',
  `legal_person_id_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证正面照',
  `legal_person_id_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证反面照',
  `legal_person_register_no` VARCHAR(100) NULL DEFAULT NULL COMMENT '营业执照注册号/个体户注册号',
  `business_license_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '营业执照照片',
  `business` VARCHAR(255) NULL DEFAULT NULL COMMENT '经营内容',
  `owner_name` VARCHAR(50) NULL COMMENT '所有人姓名',
  `owner_cellphone` VARCHAR(32) NULL COMMENT '所有人手机',
  `bank_account_verify_status` INT(11) NOT NULL DEFAULT -1 COMMENT '卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败',
  `client_sn` VARCHAR(50) NULL COMMENT '商户外部商户号\n',
  `vendor_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '服务商UUID',
  `solicitor_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '推广者UUID',
  `platform` INT(11) NOT NULL DEFAULT '0' COMMENT '商户所属平台platform: 0: 收钱吧 1: 拉卡拉',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_sn` (`sn` ASC)  COMMENT '',
  INDEX `idx_contact_cellphone` (`contact_cellphone` ASC)  COMMENT '',
  INDEX `idx_owner_cellphone` (`owner_cellphone` ASC)  COMMENT '',
  INDEX `idx_solicitor_id` (`solicitor_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户';


-- -----------------------------------------------------
-- Table `upay_core`.`merchant_bank_account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`merchant_bank_account` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(45) NULL DEFAULT NULL COMMENT '',
  `type` INT(11) NULL DEFAULT NULL COMMENT '账户类型：1：个人账户；2：企业账户',
  `holder` VARCHAR(45) NULL DEFAULT NULL COMMENT '账户持有人名称',
  `id_type` INT(11) NULL DEFAULT 1 COMMENT '账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；',
  `identity` VARCHAR(45) NULL DEFAULT NULL COMMENT '账户持有人证件编号',
  `tax_payer_id` VARCHAR(45) NULL DEFAULT NULL COMMENT '工商税务号',
  `number` VARCHAR(45) NULL DEFAULT NULL COMMENT '账号',
  `verify_status` INT(11) NOT NULL DEFAULT '0' COMMENT '卡号(账号)真实性验证状态 0未验证 1 验证中 2验证有效 3验证失败',
  `bank_name` VARCHAR(45) NULL COMMENT '开户银行名称',
  `branch_name` VARCHAR(45) NULL DEFAULT NULL COMMENT '分支行名称',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '分支行所在城市',
  `cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '和账号绑定的手机号',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `change_time` BIGINT(20) NULL DEFAULT NULL COMMENT '换卡时间',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `merchant_id_UNIQUE` (`merchant_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '代清算商户绑定的银行账户，可以放在独立的DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`merchant_config`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`merchant_config` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(37) NOT NULL COMMENT '',
  `payway` INT(11) NULL DEFAULT NULL COMMENT '支付方式，1：支付宝1.0；2：支付宝2.0；3：微信；4：百付宝；5：京东钱包；6：QQ钱包；7：ApplePay；8：三星支付；9：小米支付；10：华为支付；11：翼支付；12：苏宁易钱包；13：银联云闪付（AndroidPay）；14银联钱包',
  `b2c_formal` TINYINT(1) NULL DEFAULT NULL COMMENT 'b扫c是否正式商户 0:否  1:是 ',
  `b2c_status` INT(11) NULL DEFAULT NULL COMMENT 'b扫c 是否开通关闭 0:关闭 1：开通',
  `b2c_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT 'b扫c 费率',
  `c2b_formal` TINYINT(1) NULL DEFAULT NULL COMMENT 'c扫b是否正式商户 0:否  1:是 ',
  `c2b_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT 'c扫b 费率',
  `c2b_status` INT(11) NULL DEFAULT NULL COMMENT 'c扫b是否开通关闭 0:关闭 1：开通',
  `wap_formal` TINYINT(1) NULL DEFAULT NULL COMMENT 'wap是否正式商户 0:否  1:是 ',
  `wap_status` INT(11) NULL DEFAULT NULL COMMENT 'wap 是否开通关闭 0:关闭 1：开通',
  `wap_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT 'wap 费率',
  `extend1_formal` TINYINT(1) NULL DEFAULT NULL COMMENT '交易模式保留字段  是否正式商户 0:否  1:是 ',
  `extend1_status` INT(11) NULL DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通',
  `extend1_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT '交易模式保留字段   费率',
  `extend1_agent_name` VARCHAR(37)  NULL DEFAULT NULL COMMENT '受理商',
  `extend2_formal` TINYINT(1) NULL DEFAULT NULL COMMENT '交易模式保留字段   是否正式商户 0:否  1:是 ',
  `extend2_status` INT(11) NULL DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通',
  `extend2_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT '交易模式保留字段  费率',
  `extend2_agent_name` VARCHAR(37) NULL DEFAULT NULL COMMENT '受理商',
  `params` BLOB NULL COMMENT '配置参数（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_payway` (`payway` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchan_id_payway` (`merchant_id` ASC, `payway` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户配置，可放在独立DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`merchant_developer`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`merchant_developer` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(37) NOT NULL COMMENT '',
  `merchant_sn` VARCHAR(32) NOT NULL COMMENT '',
  `app_key` VARCHAR(64) NULL DEFAULT NULL COMMENT '开发者密钥',
  `public_key` BLOB NULL DEFAULT NULL COMMENT '开发者公钥',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchant_sn` (`merchant_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户开发者帐号，可以放在独立的DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`rsa_key`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`rsa_key` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `name` VARCHAR(50)  NULL COMMENT '',
  `digest` VARCHAR(32) NOT NULL COMMENT '',
  `data` BLOB NOT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `remark` VARCHAR(100) NULL DEFAULT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `name_UNIQUE` (`name` ASC)  COMMENT '',
  UNIQUE INDEX `digest_UNIQUE` (`digest` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '交易参数中的私钥内容存放在这张表中。订单';


-- -----------------------------------------------------
-- Table `upay_core`.`solicitor`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`solicitor` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `sn` VARCHAR(32) NOT NULL COMMENT '推广者可见的编号',
  `name` VARCHAR(128) NULL DEFAULT NULL COMMENT '推广者名称',
  `category` INT(11) NULL DEFAULT NULL COMMENT '',
  `status` INT(11) NULL DEFAULT NULL COMMENT '',
  `cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `contact_name` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系固定电话号码',
  `contact_cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系移动电话号码',
  `contact_email` VARCHAR(64) NULL DEFAULT NULL COMMENT '联系邮箱',
  `contact_address` VARCHAR(255) NULL DEFAULT NULL COMMENT '联系地址',
  `province` VARCHAR(32) NULL DEFAULT NULL COMMENT '省',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '市',
  `district` VARCHAR(32) NULL DEFAULT NULL COMMENT '区',
  `street_address` VARCHAR(255) NULL COMMENT '街道门牌号',
  `bank_account_verify_status` INT(11) NOT NULL DEFAULT -1 COMMENT '卡号(账号)真实性验证状态 -1未录入  0未验证 1 验证中 2验证有效 3验证失败',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段\naddress\nemail\nremark\n',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_sn` (`sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '推广者';


-- -----------------------------------------------------
-- Table `upay_core`.`solicitor_bank_account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`solicitor_bank_account` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `solicitor_id` VARCHAR(45) NULL DEFAULT NULL COMMENT '',
  `type` INT(11) NULL DEFAULT NULL COMMENT '账户类型：1：个人账户；2：企业账户',
  `holder` VARCHAR(45) NULL DEFAULT NULL COMMENT '账户持有人名称',
  `id_type` INT(11) NULL DEFAULT 1 COMMENT '账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；',
  `identity` VARCHAR(45) NULL DEFAULT NULL COMMENT '账户持有人证件编号',
  `tax_payer_id` VARCHAR(45) NULL DEFAULT NULL COMMENT '工商税务号',
  `number` VARCHAR(45) NULL DEFAULT NULL COMMENT '账号',
  `verify_status` INT(11) NOT NULL DEFAULT '0' COMMENT '卡号(账号)真实性验证状态 0未验证 1 验证中 2验证有效 3验证失败',
  `bank_name` VARCHAR(45) NULL COMMENT '开户银行名称',
  `branch_name` VARCHAR(45) NULL COMMENT '分支行名称',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '分支行所在城市',
  `cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '和账号绑定的手机号',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `solicitor_id_UNIQUE` (`solicitor_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '推广者绑定的银';


-- -----------------------------------------------------
-- Table `upay_core`.`solicitor_developer`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`solicitor_developer` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `solicitor_id` VARCHAR(37) NOT NULL COMMENT '',
  `solicitor_sn` VARCHAR(32) NOT NULL COMMENT '',
  `app_key` VARCHAR(64) NULL DEFAULT NULL COMMENT '开发者密钥',
  `public_key` BLOB NULL DEFAULT NULL COMMENT '开发者公钥',
  `extra` BLOB NULL DEFAULT NULL COMMENT '',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_solicitor_id` (`solicitor_id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_solicitor_sn` (`solicitor_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '推广者开发者帐号，可以放在独立的DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`store`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`store` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `sn` VARCHAR(32) NOT NULL COMMENT '对应1.0中的wosai_store_id',
  `name` VARCHAR(128) NULL DEFAULT NULL COMMENT '',
  `industry` varchar(36) NULL DEFAULT NULL COMMENT '',
  `status` INT(11) NULL DEFAULT NULL COMMENT '',
  `rank` INT(11) NULL DEFAULT NULL COMMENT '信用等级',
  `longitude` VARCHAR(16) NULL DEFAULT NULL COMMENT '经度',
  `latitude` VARCHAR(16) NULL DEFAULT NULL COMMENT '纬度',
  `province` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `district` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `street_address` VARCHAR(255) NULL DEFAULT NULL COMMENT '',
  `contact_name` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系固定电话号码',
  `contact_cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系移动电话号码',
  `contact_email` VARCHAR(64) NULL DEFAULT NULL COMMENT '联系邮箱',
  `client_sn` VARCHAR(50) NULL COMMENT '商户外部门店号\n',
  `merchant_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '',
  `solicitor_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '',
  `vendor_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_sn` (`sn` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchant_id_client_sn` (`merchant_id` ASC, `client_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '门店';


-- -----------------------------------------------------
-- Table `upay_core`.`store_config`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`store_config` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `store_id` VARCHAR(37) NULL DEFAULT NULL COMMENT '',
  `params` BLOB NULL DEFAULT NULL COMMENT '配置参数（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '门店配置，可放在独立DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`store_developer`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`store_developer` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `store_id` VARCHAR(37) NOT NULL COMMENT '',
  `store_sn` VARCHAR(32) NOT NULL COMMENT '',
  `app_key` VARCHAR(64) NULL DEFAULT NULL COMMENT '开发者密钥',
  `public_key` BLOB NULL DEFAULT NULL COMMENT '开发者公钥',
  `extra` BLOB NULL DEFAULT NULL COMMENT '',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_store_id` (`store_id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_store_sn` (`store_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '门店开发者帐号，可以放在独立的DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`terminal`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`terminal` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `sn` VARCHAR(32) NOT NULL COMMENT '用户可见终端编号',
  `device_fingerprint` VARCHAR(128) NULL COMMENT '设备指纹',
  `name` VARCHAR(128) NULL DEFAULT NULL COMMENT '终端名',
  `type` INT(11) NULL DEFAULT NULL COMMENT '类型 \n10: Android应用\n11: iOS应用\n20: Windows桌面应用\n30: 专用设备\n40: 门店码\n50: 服务',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态',
  `last_signon_time` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `sdk_version` VARCHAR(45) NULL DEFAULT NULL COMMENT '',
  `os_version` VARCHAR(45) NULL DEFAULT NULL COMMENT '',
  `current_secret` VARCHAR(64) NULL DEFAULT NULL COMMENT '',
  `last_secret` VARCHAR(64) NULL DEFAULT NULL COMMENT '',
  `next_secret` VARCHAR(64) NULL DEFAULT NULL COMMENT '',
  `longitude` VARCHAR(16) NULL COMMENT '经度',
  `latitude` VARCHAR(16) NULL DEFAULT NULL COMMENT '纬度',
  `client_sn` VARCHAR(50) NULL COMMENT '商户外部终端号\n',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段',
  `target` VARCHAR(255) NULL COMMENT '',
  `target_type` BIGINT(11) NULL COMMENT '',
  `store_id` VARCHAR(37) NOT NULL COMMENT '',
  `merchant_id` VARCHAR(37) NOT NULL COMMENT '',
  `solicitor_id` VARCHAR(37) NULL COMMENT '',
  `vendor_id` VARCHAR(37) NOT NULL COMMENT '',
  `vendor_app_id` VARCHAR(36) NULL COMMENT '',
  `vendor_app_appid` VARCHAR(36) NULL COMMENT '',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_sn` (`sn` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchant_id_client_sn` (`merchant_id` ASC, `client_sn` ASC)  COMMENT '',
  INDEX `idx_device_fingerprint` (`device_fingerprint` ASC)  COMMENT '',
  INDEX `idx_client_sn` (`client_sn` ASC)  COMMENT '',
  INDEX `idx_vendor_app_appid` (`vendor_app_appid` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '终端，可放在独立的DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`terminal_activation_code`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`terminal_activation_code` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `vendor_sn` VARCHAR(32) NULL COMMENT '服务商序列号',
  `vendor_id` VARCHAR(36) NULL COMMENT '服务商UUID',
  `code` VARCHAR(32) NOT NULL COMMENT '激活码不要求全局唯一，但在同一个服务商下面具有唯一性。',
  `merchant_id` VARCHAR(36) NULL COMMENT '商户ID',
  `store_id` VARCHAR(36) NULL COMMENT '门店ID',
  `terminal_id` VARCHAR(36) NULL COMMENT '终端ID。如果不为空，激活已经存在的终端记录。否则根据merchant_id 和 store_id 创建终端记录并激活。',
  `default_terminal_name` VARCHAR(128) NULL COMMENT '用这个激活码激活的终端的默认名称。如果为NULL，系统会自动生成一个。',
  `usage_limits` BIGINT(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '激活码使用次数上限',
  `remaining` BIGINT(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '剩余使用次数',
  `expire_time` BIGINT(20) NOT NULL COMMENT '过期时间',
  `status` INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1: ACTIVE  2: EXPIRED 3: USED',
  `ctime` BIGINT(20) NOT NULL COMMENT '创建时间',
  `mtime` BIGINT(20) NOT NULL COMMENT '最后修改时间',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_code` (`code` ASC)  COMMENT '',
  INDEX `idx_terminal_id` (`terminal_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '终端激活码列表，可放在独立DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`vendor`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`vendor` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `name` VARCHAR(128) NOT NULL COMMENT '名称',
  `sn` VARCHAR(32) NOT NULL COMMENT '服务商可见的编号',
  `cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '绑定的手机号',
  `contact_name` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系固定电话号码',
  `contact_cellphone` VARCHAR(32) NULL DEFAULT NULL COMMENT '联系移动电话号码',
  `contact_email` VARCHAR(64) NULL DEFAULT NULL COMMENT '联系邮箱',
  `contact_address` VARCHAR(255) NULL DEFAULT NULL COMMENT '联系地址',
  `rank` INT(11) NULL DEFAULT NULL COMMENT '信用等级',
  `status` INT(11) NULL DEFAULT NULL COMMENT '',
  `province` VARCHAR(32) NULL DEFAULT NULL COMMENT '省',
  `city` VARCHAR(32) NULL DEFAULT NULL COMMENT '市',
  `district` VARCHAR(32) NULL DEFAULT NULL COMMENT '区',
  `street_address` VARCHAR(255) NULL COMMENT '街道门牌号',
  `extra` BLOB NULL DEFAULT NULL COMMENT '扩展字段\nremark\n',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_sn` (`sn` ASC)  COMMENT '')
ENGINE = InnoDB;


-- -----------------------------------------------------
-- Table `upay_core`.`vendor_config`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`vendor_config` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `vendor_id` VARCHAR(37) NOT NULL COMMENT '',
  `params` BLOB NULL DEFAULT NULL COMMENT '配置参数（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `vendor_id_UNIQUE` (`vendor_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '服务商配置，可放在独立DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`vendor_developer`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`vendor_developer` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `vendor_id` VARCHAR(37) NOT NULL COMMENT '',
  `vendor_sn` VARCHAR(32) NOT NULL COMMENT '',
  `app_key` VARCHAR(64) NULL DEFAULT NULL COMMENT '开发者密钥',
  `public_key` BLOB NULL DEFAULT NULL COMMENT '开发者公钥',
  `extra` BLOB NULL DEFAULT NULL COMMENT '',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_vendor_sn` (`vendor_sn` ASC)  COMMENT '',
  UNIQUE INDEX `vendor_id_UNIQUE` (`vendor_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '服务商开发者帐号，可以放在独立的DB实例中';


-- -----------------------------------------------------
-- Table `upay_core`.`merchant_gallery`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`merchant_gallery` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '商户id',
  `images` BLOB NULL DEFAULT NULL COMMENT '图片 json array [{url:地址, tag: [标签1,标签2]}]',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户图片库';


-- -----------------------------------------------------
-- Table `upay_core`.`vendor_app`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`vendor_app` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `vendor_id` VARCHAR(36) NOT NULL COMMENT '服务商id',
  `appid` VARCHAR(36) NOT NULL COMMENT '应用编号',
  `appkey` VARCHAR(36) NOT NULL COMMENT '应用密钥',
  `name` VARCHAR(128) NOT NULL COMMENT '应用名称',
  `type` INT(10) UNSIGNED NOT NULL COMMENT '产品类型 \n10: Android应用\n11: iOS应用\n20: Windows桌面应用\n30: 专用设备\n40: 门店码\n50: 服务',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `appid` (`appid` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '服务商产品表';


-- -----------------------------------------------------
-- Table `upay_core`.`solicitor_config`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`solicitor_config` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `solicitor_id` VARCHAR(37) NOT NULL COMMENT '',
  `payway` INT(11) NULL DEFAULT NULL COMMENT '支付方式，1：支付宝1.0；2：支付宝2.0；3：微信；4：百付宝；5：京东钱包；6：QQ钱包；7：ApplePay；8：三星支付；9：小米支付；10：华为支付；11：翼支付；12：苏宁易钱包；13：银联云闪付（AndroidPay）；14银联钱包',
  `b2c_formal` TINYINT(1) NULL DEFAULT NULL COMMENT 'b扫c是否正式商户 0:否  1:是 ',
  `b2c_status` INT(11) NULL DEFAULT NULL COMMENT 'b扫c 是否开通关闭 0:关闭 1：开通',
  `b2c_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT 'b扫c 费率',
  `b2c_agent_name` varchar(37) COLLATE DEFAULT NULL COMMENT '受理商',
  `c2b_formal` TINYINT(1) NULL DEFAULT NULL COMMENT 'c扫b是否正式商户 0:否  1:是 ',
  `c2b_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT 'c扫b 费率',
  `c2b_status` INT(11) NULL DEFAULT NULL COMMENT 'c扫b是否开通关闭 0:关闭 1：开通',
  `c2b_agent_name` varchar(37) COLLATE DEFAULT NULL COMMENT '受理商',
  `wap_formal` TINYINT(1) NULL DEFAULT NULL COMMENT 'wap是否正式商户 0:否  1:是 ',
  `wap_status` INT(11) NULL DEFAULT NULL COMMENT 'wap 是否开通关闭 0:关闭 1：开通',
  `wap_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT 'wap 费率',
  `wap_agent_name` varchar(37) COLLATE DEFAULT NULL COMMENT '受理商',
  `extend1_formal` TINYINT(1) NULL DEFAULT NULL COMMENT '交易模式保留字段  是否正式商户 0:否  1:是 ',
  `extend1_status` INT(11) NULL DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通',
  `extend1_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT '交易模式保留字段   费率',
  `extend1_agent_name` varchar(37) COLLATE DEFAULT NULL COMMENT '受理商',
  `extend2_formal` TINYINT(1) NULL DEFAULT NULL COMMENT '交易模式保留字段   是否正式商户 0:否  1:是 ',
  `extend2_status` INT(11) NULL DEFAULT NULL COMMENT '交易模式保留字段   是否开通关闭 0:关闭 1：开通',
  `extend2_fee_rate` VARCHAR(45) NULL DEFAULT NULL COMMENT '交易模式保留字段  费率',
  `extend2_agent_name` varchar(37) COLLATE DEFAULT NULL COMMENT '受理商',
  `provider` INT(11) DEFAULT NULL COMMENT '支付通道 直接对接的收款通道参考payway（0-99）, 对接第3方（100以上） 101: 兴业银行 102: 拉卡拉',
  `params` BLOB NULL COMMENT '配置参数（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_solicitor_id_payway` (`solicitor_id` ASC, `payway` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '推广者配置，可放在独立DB实例中';

-- -----------------------------------------------------
-- Table `upay_core`.`merchant_config_custom`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_core`.`merchant_config_custom` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `type` TINYINT(4) NOT NULL COMMENT '类型 1: 微信子商户号 2:京东商户号 ',
  `merchant_id` VARCHAR(37) NOT NULL COMMENT '商户id',
  `store_id` VARCHAR(37) NULL COMMENT '门店id',
  `b2c_value` VARCHAR(64) NULL COMMENT 'b扫c交易的值',
  `b2c_agent_name` varchar(37) NULL COMMENT '受理商',
  `c2b_value` VARCHAR(64) NULL COMMENT 'c扫b交易的值',
  `c2b_agent_name` VARCHAR(37) NULL COMMENT '受理商',
  `wap_value` VARCHAR(64) NULL COMMENT 'wap支付的值',
  `wap_agent_name` VARCHAR(37) NULL COMMENT '受理商',
  `value1` VARCHAR(64) NULL COMMENT '预留字段',
  `value2` VARCHAR(64) NULL COMMENT '预留字段',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `id_merchant_id` (`merchant_id` ASC)  COMMENT '',
  INDEX `id_store_id` (`store_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户配置自定义表，用于配置二清商户交易的商户号等';



CREATE TABLE IF NOT EXISTS `upay_core`.`agent` (
  `id` VARCHAR(37) NOT NULL COMMENT 'UUID',
  `name` VARCHAR(37) NOT NULL COMMENT '受理商名字',
  `details` VARCHAR(256) NOT NULL COMMENT '受理商详情',
  `provider` INT(11) NULL DEFAULT NULL COMMENT '结算通道',
  `payway` INT(11) NULL DEFAULT NULL COMMENT '支付通道',
  `sub_payway` INT(1) NULL DEFAULT NULL COMMENT '交易模式',
  `formal` tinyint(1) NOT NULL COMMENT '是否正式',
  `is_primary` tinyint(1) NOT NULL COMMENT '是否默认配置',
  `params` BLOB NULL COMMENT '配置参数（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `name_UNIQUE` (`name` ASC)  COMMENT ''
) ENGINE = InnoDB
COMMENT = '受理商表';



USE `upay_user` ;

-- -----------------------------------------------------
-- Table `upay_user`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`account` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `username` VARCHAR(128) NOT NULL COMMENT '',
  `nickname` VARCHAR(255) NULL DEFAULT NULL COMMENT '昵称',
  `gender` TINYINT(4) NULL COMMENT '性别：1：男；2：女',
  `avatar` VARCHAR(255) NULL DEFAULT NULL COMMENT '头像',
  `password` VARCHAR(64) NOT NULL COMMENT '密码',
  `cellphone` VARCHAR(32) NULL COMMENT '手机号',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：禁用；1:正常',
  `email` VARCHAR(64) NULL DEFAULT NULL COMMENT '邮箱',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  `mtime_stamp`  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `username_UNIQUE` (`username` ASC)  COMMENT '',
  UNIQUE INDEX `cellphone_UNIQUE` (`cellphone` ASC)  COMMENT ''),
  INDEX `IX_MTIME_STAMP` (`mtime_stamp`) USING BTREE
ENGINE = InnoDB
COMMENT = '用户表';


-- -----------------------------------------------------
-- Table `upay_user`.`merchant_role`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`merchant_role` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '商户id',
  `name` VARCHAR(50) NULL DEFAULT NULL COMMENT '角色名称',
  `type` INT NULL DEFAULT NULL COMMENT '角色类型 \n1: system 系统内置\n2: custom 用户添加',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户角色定义表';


-- -----------------------------------------------------
-- Table `upay_user`.`role_permission`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`role_permission` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `role_id` VARCHAR(36) NOT NULL COMMENT '角色id',
  `permission_id` VARCHAR(36) NOT NULL COMMENT '角色id',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户角色权限关联表';

-- -----------------------------------------------------
-- Table `upay_user`.`group_user`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`group_user` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `name` VARCHAR(128) DEFAULT NULL COMMENT '名称',
  `group_id` VARCHAR(36) NOT NULL COMMENT '集团id',
  `account_id` VARCHAR(36) NOT NULL COMMENT 'account表id',
  `role` VARCHAR(36) DEFAULT NULL COMMENT '角色/职务，super_admin：超级管理员，admin：管理员',
  `merchant_auth` INT(11) NOT NULL DEFAULT 1 COMMENT '商户权限，1：所有商户，2：授权商户',
  `email` VARCHAR(128) DEFAULT NULL COMMENT '邮箱',
  `remark` TEXT DEFAULT NULL COMMENT '备注',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：禁用；1:正常',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '',
  INDEX `idx_group_id` (`group_id` ASC) COMMENT '',
  INDEX `idx_account_id` (`account_id` ASC) COMMENT '',
  INDEX `idx_role` (`role` ASC) COMMENT '')
ENGINE = InnoDB
COMMENT = '集团用户表';

-- -----------------------------------------------------
-- Table `upay_user`.`group_user_merchant_auth`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`group_user_merchant_auth` (
`id` VARCHAR(36) NOT NULL COMMENT '主键',
`group_id` VARCHAR(36) NOT NULL COMMENT '集团id',
`group_user_id` VARCHAR(36) NOT NULL COMMENT '集团用户id',
`merchant_id` VARCHAR(36) NOT NULL COMMENT '商户id',
`merchant_sn` VARCHAR(36) NOT NULL COMMENT '商户编号',
`merchant_name` VARCHAR(36) NOT NULL COMMENT '商户名称',
`merchant_ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '商户创建时间',
`ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
`version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
PRIMARY KEY (`id`) COMMENT '',
UNIQUE INDEX `id` (`id` ASC) COMMENT '',
INDEX `idx_group_id` (`group_id` ASC) COMMENT '',
INDEX `idx_group_user_id` (`group_user_id` ASC) COMMENT '',
UNIQUE INDEX `idx_unique` (`group_user_id` ASC, `merchant_id` ASC) COMMENT '',
INDEX `idx_merchant_sn` (`merchant_sn` ASC) COMMENT '')
ENGINE = InnoDB
COMMENT = '集团用户商户授权表';

-- -----------------------------------------------------
-- Table `upay_user`.`merchant_user`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`merchant_user` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `name` VARCHAR(128) DEFAULT NULL COMMENT '名称',
  `merchant_id` VARCHAR(36) NOT NULL COMMENT '商户id',
  `account_id` VARCHAR(36) NOT NULL COMMENT 'user表里面的id',
  `role` VARCHAR(36) DEFAULT NULL COMMENT '角色/职务，super_admin：超级管理员/老板，admin：管理员/店长，finance：财务，cashier：收银员',
  `store_auth` INT(11) NOT NULL DEFAULT 1 COMMENT '门店权限，1：所有门店，2：授权门店',
  `email` VARCHAR(128) DEFAULT NULL COMMENT '邮箱',
  `remark` TEXT DEFAULT NULL COMMENT '备注',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：禁用；1:正常',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '',
  INDEX `idx_merchant_id` (`merchant_id` ASC) COMMENT '',
  INDEX `idx_account_id` (`account_id` ASC) COMMENT '')
ENGINE = InnoDB
COMMENT = '商户用户表';

-- -----------------------------------------------------
-- Table `upay_user`.`merchant_user_store_auth`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`merchant_user_store_auth` (
`id` VARCHAR(36) NOT NULL COMMENT '主键',
`merchant_id` VARCHAR(36) NOT NULL COMMENT '商户id',
`merchant_user_id` VARCHAR(36) NOT NULL COMMENT '商户用户id',
`store_id` VARCHAR(36) NOT NULL COMMENT '门店id',
`ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
`version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
PRIMARY KEY (`id`) COMMENT '',
UNIQUE INDEX `id` (`id` ASC) COMMENT '',
INDEX `idx_merchant_id` (`merchant_id` ASC) COMMENT '',
INDEX `idx_merchant_user_id` (`merchant_user_id` ASC) COMMENT '')
ENGINE = InnoDB
COMMENT = '商户用户门店授权表';


-- -----------------------------------------------------
-- Table `ticket`.`department_sn_ticket_2370`
-- -----------------------------------------------------
CREATE TABLE `department_sn_ticket_2370` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `stub` varchar(11) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `stub` (`stub`)
) ENGINE=InnoDB AUTO_INCREMENT=525000 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
-- -----------------------------------------------------
-- Table `upay_user`.`merchant_user_department_auth`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`merchant_user_department_auth` (
`id` VARCHAR(36) NOT NULL COMMENT '主键',
`merchant_id` VARCHAR(36) NOT NULL COMMENT '商户id',
`merchant_user_id` VARCHAR(36) NOT NULL COMMENT '商户用户id',
`department_id` VARCHAR(36) NOT NULL COMMENT '部门id',
`ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
`version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
PRIMARY KEY (`id`) COMMENT '',
UNIQUE INDEX `id` (`id` ASC) COMMENT '',
INDEX `idx_merchant_id` (`merchant_id` ASC) COMMENT '',
INDEX `idx_merchant_user_id` (`merchant_user_id` ASC) COMMENT '')
ENGINE = InnoDB
COMMENT = '商户用户部门授权表';

-- -----------------------------------------------------
-- Table `upay_user`.`department`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`department` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `sn`  VARCHAR(32) NOT NULL COMMENT '部门号',
  `name` VARCHAR(128) NOT NULL COMMENT '部门名称',
  `parent_department_sn`  VARCHAR(32) NULL COMMENT '父级部门号',
  `merchant_id` VARCHAR(36) NOT NULL COMMENT '商户id',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：关闭；1:正常,2:禁用',
  `type` INT(11) NULL DEFAULT NULL COMMENT '状态：0：无门店；1:有门店',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `sn` (`sn` asc) comment '',
  INDEX `name` (`name` ASC)  COMMENT '',
  INDEX `parent_department_sn` (`parent_department_sn` asc) comment '')
ENGINE = InnoDB
COMMENT = '部门表';

-- -----------------------------------------------------
-- Table `upay_user`.`department_store`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`department_store` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `department_sn` VARCHAR(36) NOT NULL COMMENT '部门sn',
  `department_id` VARCHAR(36) NOT NULL COMMENT '部门id',
  `store_id` VARCHAR(36) NOT NULL COMMENT 'store_id',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '',
  INDEX `idx_department_id` (`department_id` ASC)  COMMENT '',
  INDEX `idx_department_sn` (`department_sn` ASC)  COMMENT '',
  INDEX `idx_store_id` (`store_id` ASC)  COMMENT '',
ENGINE = InnoDB
COMMENT = '部门门店关系维护表';
-- -----------------------------------------------------
-- Table `upay_user`.`vendor_user`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`vendor_user` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `vendor_id` VARCHAR(36) NOT NULL COMMENT '商户id',
  `account_id` VARCHAR(36) NOT NULL COMMENT 'user表里面的id',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：禁用；1:正常',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '服务商用户表';


-- -----------------------------------------------------
-- Table `upay_user`.`solicitor_user`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`solicitor_user` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `solicitor_id` VARCHAR(36) NOT NULL COMMENT '推广者id',
  `account_id` VARCHAR(36) NOT NULL COMMENT 'user表里面的id',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：禁用；1:正常',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '推广者用户表';


-- -----------------------------------------------------
-- Table `upay_user`.`osp_user`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`osp_user` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `account_id` VARCHAR(36) NOT NULL COMMENT 'user表里面的id',
  `role` INT(11) NULL DEFAULT NULL COMMENT '角色权限',
  `status` INT(11) NULL DEFAULT NULL COMMENT '状态：0：禁用；1:正常',
  `remark` VARCHAR(255) NULL DEFAULT NULL COMMENT '备注',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = 'osp用户表';


-- -----------------------------------------------------
-- Table `upay_user`.`osp_role`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`osp_role` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `type` INT NULL DEFAULT NULL COMMENT '角色类型 \n1: system 系统内置\n2: custom 用户添加',
  `name` VARCHAR(50) NULL DEFAULT NULL COMMENT '角色名称',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = 'osp角色定义表';


-- -----------------------------------------------------
-- Table `upay_user`.`permission`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`permission` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `type` INT(10) UNSIGNED NULL DEFAULT NULL COMMENT '权限类型 1:osp系统平台权限 2：商户平台系统权限 3:服务商应用接口权限',
  `name` VARCHAR(50) NULL DEFAULT NULL COMMENT '权限名称',
  `desc` VARCHAR(50) NULL DEFAULT NULL COMMENT '权限描述',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id` (`id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '权限定义表';


-- -----------------------------------------------------
-- Table `upay_user`.`user_role`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`user_role` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `user_id` VARCHAR(36) NOT NULL COMMENT '角色id',
  `role_id` VARCHAR(36) NOT NULL COMMENT '角色id',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '')
ENGINE = InnoDB
COMMENT = '用户与角色关联表\n';


-- -----------------------------------------------------
-- Table `upay_user`.`vendor_application_permission`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_user`.`vendor_application_permission` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `vendor_application_id` VARCHAR(36) NOT NULL COMMENT '服务商应用id',
  `permission_id` VARCHAR(36) NOT NULL COMMENT '权限接口名称id',
  `ctime` BIGINT(20) NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '')
ENGINE = InnoDB
COMMENT = '服务商产品权限关联表';

USE `upay` ;

-- -----------------------------------------------------
-- Table `upay`.`withdraw`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay`.`withdraw` (
  `id` VARCHAR(36) NOT NULL COMMENT '主键',
  `sn` VARCHAR(128) NOT NULL COMMENT '提现单号',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '',
  `amount` BIGINT(20) NOT NULL COMMENT '提现金额',
  `check_status` INT(11) NOT NULL DEFAULT '1' COMMENT '审核状态：1：审核中；2运营拒绝；3.运营冻结；4.运营通过，等待财务审核；5.财务审核通过；6.打款成功；7.打款失败； 8.人工打款',
  `check_failed_reason` TEXT NULL DEFAULT NULL COMMENT '审核失败原因',
  `operators` BLOB NULL DEFAULT NULL COMMENT '审核操作人json, check_status和记录中的状态同步 [{check_status:,operator_id:,operator_name:,time:,remark:},{check_status:,operator_id:,operator_name:,time:,remark:}]',
  `expected_clearance_day` BIGINT(20) NULL DEFAULT NULL COMMENT '预期到账时间',
  `batch_no` VARCHAR(128) NULL DEFAULT NULL COMMENT '打款批次',
  `remark` TEXT NOT NULL COMMENT '备注',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  UNIQUE INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '提现记录';

USE `upay_log` ;

-- -----------------------------------------------------
-- Table `upay_log`.`op_log`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_log`.`op_log` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `request_system` INT(11) NOT NULL DEFAULT '1' COMMENT '请求系统\n1:未知;\n2:OSP;\n3:服务商;\n4:商户服务;\n5:推广者服务\n9:其他内部服务',
  `operator_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '操作人id',
  `operator_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '操作人姓名',
  `operator_login` VARCHAR(64) NULL DEFAULT NULL COMMENT '操作人账号',
  `action_class` VARCHAR(64) NULL DEFAULT NULL COMMENT '操作类',
  `action_method` VARCHAR(64) NULL DEFAULT NULL COMMENT '操作方法',
  `duration` INT(11) NULL DEFAULT NULL COMMENT '执行事件，单位：毫秒',
  `result` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '执行结果，0：失败，1：成功',
  `action_type` INT(11) NULL DEFAULT NULL COMMENT '操作类型\n1:登入;\n2:登出;\n3:查询;\n4:查看;\n5:导出;\n6:新增;\n7:编辑;\n71:修改;\n72:审核;\n8:删除;\n9:上传;\n10:下载',
  `action_date` INT(11) NULL DEFAULT NULL COMMENT '操作日期，格式yyyyMMdd',
  `client_ip` VARCHAR(32) NULL DEFAULT NULL COMMENT '操作ip',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_operate_date` (`action_date` ASC)  COMMENT '',
  INDEX `idx_operator_id` (`operator_id` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '操作日志';


-- -----------------------------------------------------
-- Table `upay_log`.`important_change_log`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_log`.`important_change_log` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '',
  `merchant_sn` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `object_type` INT(11) NULL DEFAULT NULL COMMENT '变更对象类型\n1：商户真实性审核申请\n2：微信正式商户申请',
  `object_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '变更对象id',
  `type` INT(11) NULL DEFAULT NULL COMMENT '变更类型，1：创建；2：修改；21：审核；3：删除；其他自定义',
  `post_change_status` INT(11) NULL DEFAULT NULL COMMENT '变更后的状态',
  `payload` BLOB NULL DEFAULT NULL COMMENT '变更内容（JSON）',
  `subject_type` INT(11) NULL DEFAULT NULL COMMENT '操作人类型\n1：运营用户\n2：服务商用户\n3：推广者用户\n4：商户用户',
  `subject_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '操作人用户ID',
  `subject_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '操作人姓名',
  `subject_login` VARCHAR(64) NULL DEFAULT NULL COMMENT '操作人登录账号',
  `change_description` TEXT NULL DEFAULT NULL COMMENT '事件描述（前端写入）',
  `change_time` BIGINT(20) NULL DEFAULT NULL COMMENT '操作日期时间戳',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '',
  INDEX `idx_merchant_sn` (`merchant_sn` ASC)  COMMENT '',
  INDEX `idx_operate_date` (`change_time` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '重要（资料）变动日志';

-- -----------------------------------------------------
-- Table `upay_log`.`task_apply_log`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_log`.`task_apply_log` (
`id` VARCHAR(36) NOT NULL COMMENT '主键',
`type` INT(11) NOT NULL DEFAULT '1' COMMENT '申请任务类型，1：对账单下载，2：订单下载，3：渠道分润报表下载',
`apply_system` INT(11) NOT NULL DEFAULT '1' COMMENT '请求系统，1:未知; 2:OSP; 3:服务商; 4:商户服务; 5:推广者服务 9:其他内部服务',
`payload` BLOB NULL DEFAULT NULL COMMENT '申请参数（JSON）',
`user_id` VARCHAR(36) NULL COMMENT '用户id',
`apply_status` INT(11) NOT NULL DEFAULT '0' COMMENT '任务申请状态，0：新申请，1：执行中，2：执行成功，3：执行失败',
`apply_date` DATE NOT NULL COMMENT '申请日期',
`apply_result` text NULL COMMENT '申请结果',
`ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
`deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
`version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
PRIMARY KEY (`id`) COMMENT '',
UNIQUE INDEX `id` (`id` ASC) COMMENT '',
INDEX `idx_user_id` (`user_id` ASC) COMMENT '',
INDEX `idx_apply_date` (`apply_date` ASC) COMMENT '')
ENGINE = InnoDB
COMMENT = '任务申请日志表';

USE `upay_message` ;

-- -----------------------------------------------------
-- Table `upay_message`.`message`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_message`.`message` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `type` INT(11) NULL DEFAULT NULL COMMENT '类型，1:公告通知;2:系统消息',
  `payload` BLOB NULL DEFAULT NULL COMMENT '内容（JSON）',
  `receiver_type` INT(11) NULL DEFAULT NULL COMMENT '接收者类型\n1：所有用户\n2：服务商\n3：推广者\n4：商户\n',
  `receiver_ids` BLOB NULL DEFAULT NULL COMMENT '接收者ID （JSON数组）如果为空 则所有该类型用户都包含',
  `sent` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0未发送，1已发送',
  `send_time` BIGINT(20) NULL DEFAULT NULL COMMENT '发送时间戳',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_receiver_type` (`receiver_type` ASC)  COMMENT '',
  INDEX `idx_send_date` (`send_time` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '公告通知、系统消息';

USE `upay_application` ;

-- -----------------------------------------------------
-- Table `upay_application`.`application_alipay`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_application`.`application_alipay` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '',
  `merchant_sn` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `status` INT(11) NOT NULL DEFAULT '1' COMMENT '当前审核状态，0:待提交；1:等待审核；11：审核驳回; 2：初审通过，在线签约审核中；21：已确认签约； 22：签约驳回; 3：签约已完成等待资料回填；4：资料已回填； 5：审核通过，开通成功\n',
  `status_description` VARCHAR(512) NULL DEFAULT NULL COMMENT '当前审核描述',
  `company_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '公司名称',
  `account_type` INT(11) NULL DEFAULT NULL COMMENT '账号类型，1：个人；2：企业',
  `address` VARCHAR(255) NULL DEFAULT NULL COMMENT '详细地址',
  `legal_person` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人姓名',
  `legal_person_id_number` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人身份证号码',
  `legal_person_id_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证正面照',
  `legal_person_id_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证反面照',
  `business_license_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '营业执照',
  `front_interior_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '店内正面照片',
  `interior_photos` BLOB NULL DEFAULT NULL COMMENT '店内内景照片,json',
  `street_number_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '门牌号照片',
  `cashier_registry_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '收银台照片',
  `neon_sign_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '门店招牌照片',
  `bank_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '银行卡正面照',
  `bank_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '银行卡反面照',
  `extra_licenses` VARCHAR(255) NULL DEFAULT NULL COMMENT '额外的行业证书',
  `personal_proprietary_licencse_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '个体工商户执照',
  `registered_capital` INT(11) NOT NULL DEFAULT '0' COMMENT '注册资本',
  `staff_count` INT(11) NULL DEFAULT NULL COMMENT '员工人数',
  `operating_square_meterage` VARCHAR(50) NULL DEFAULT NULL COMMENT '经营场所面积',
  `predicted_annual_revenue` INT(11) NULL DEFAULT NULL COMMENT '预计年收入',
  `apliay_account_uid` VARCHAR(255) NULL DEFAULT NULL COMMENT '支付宝账号',
  `pid_appkey_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '支付宝申请成功以后的pid和appkey照片',
  `tobacconists` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否售卖烟草',
  `payload` BLOB NULL DEFAULT NULL COMMENT '本次修改内容（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '',
  INDEX `idx_merchant_sn` (`merchant_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户支付宝资料';


-- -----------------------------------------------------
-- Table `upay_application`.`application_base`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_application`.`application_base` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '',
  `merchant_sn` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `status` INT(11) NOT NULL DEFAULT '1' COMMENT '当前审核状态，0:待提交；1:等待审核；11：审核驳回; 2：初审通过，在线签约审核中；21：已确认签约； 22：签约驳回; 3：签约已完成等待资料回填；4：资料已回填； 5：审核通过，开通成功\n',
  `status_description` VARCHAR(512) NULL DEFAULT NULL COMMENT '当前审核描述',
  `company_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '公司名称',
  `account_type` INT(11) NULL DEFAULT NULL COMMENT '账号类型，1：个人；2：企业',
  `address` VARCHAR(255) NULL DEFAULT NULL COMMENT '详细地址',
  `legal_person` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人姓名',
  `legal_person_id_type` INT(11) NULL DEFAULT 1 COMMENT '法人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；',
  `legal_person_id_number` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人证件号码',
  `legal_person_id_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证正面照',
  `legal_person_id_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证反面照',
  `business_license_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '营业执照',
  `front_interior_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '店内正面照片',
  `interior_photos` BLOB NULL DEFAULT NULL COMMENT '店内内景照片,json',
  `street_number_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '门牌号照片',
  `cashier_registry_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '收银台照片',
  `neon_sign_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '门店招牌照片',
  `bank_name` VARCHAR(512) NULL DEFAULT NULL COMMENT '开户行全名',
  `bank_account_number` VARCHAR(512) NULL DEFAULT NULL COMMENT '银行账户',
  `bank_account_holder_name` VARCHAR(512) NULL DEFAULT NULL COMMENT '账号所有人名称',
  `bank_branch_province` VARCHAR(100) NULL DEFAULT NULL COMMENT '分支行所在省份',
  `bank_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '银行卡正面照',
  `bank_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '银行卡反面照',
  `extra_licenses` VARCHAR(255) NULL DEFAULT NULL COMMENT '额外的行业证书',
  `organization_code_certificate` VARCHAR(255) NULL DEFAULT NULL COMMENT '组织机构代码证',
  `personal_proprietary_licencse_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '个体工商户执照',
  `registered_capital` INT(11) NOT NULL DEFAULT '0' COMMENT '注册资本',
  `payload` BLOB NULL DEFAULT NULL COMMENT '本次修改内容（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '',
  INDEX `idx_merchant_sn` (`merchant_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户基础资料';


-- -----------------------------------------------------
-- Table `upay_application`.`application_weixin`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `upay_application`.`application_weixin` (
  `id` VARCHAR(36) NOT NULL COMMENT 'UUID',
  `merchant_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '',
  `merchant_sn` VARCHAR(32) NULL DEFAULT NULL COMMENT '',
  `status` INT(11) NOT NULL DEFAULT '1' COMMENT '当前审核状态，0:待提交；1:等待审核；11：审核驳回; 2：初审通过，在线签约审核中；21：已确认签约； 22：签约驳回; 3：签约已完成等待资料回填；4：资料已回填； 5：审核通过，开通成功\n',
  `status_description` VARCHAR(512) NULL DEFAULT NULL COMMENT '当前审核描述',
  `company_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '公司名称',
  `account_type` INT(11) NULL DEFAULT NULL COMMENT '账号类型，1：个人；2：企业',
  `address` VARCHAR(255) NULL DEFAULT NULL COMMENT '详细地址',
  `legal_person` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人姓名',
  `legal_person_id_number` VARCHAR(100) NULL DEFAULT NULL COMMENT '法人身份证号码',
  `legal_person_id_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证正面照',
  `legal_person_id_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '法人身份证反面照',
  `business_license_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '营业执照',
  `front_interior_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '店内正面照片',
  `interior_photos` BLOB NULL DEFAULT NULL COMMENT '店内内景照片,json',
  `street_number_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '门牌号照片',
  `cashier_registry_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '收银台照片',
  `neon_sign_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '门店招牌照片',
  `bank_card_front_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '银行卡正面照',
  `bank_card_back_photo` VARCHAR(255) NULL DEFAULT NULL COMMENT '银行卡反面照',
  `extra_licenses` VARCHAR(255) NULL DEFAULT NULL COMMENT '额外的行业证书',
  `organization_code_certificate` VARCHAR(255) NULL DEFAULT NULL COMMENT '组织机构代码证',
  `bank_name` VARCHAR(512) NULL DEFAULT NULL COMMENT '开户行全名',
  `bank_account_number` VARCHAR(512) NULL DEFAULT NULL COMMENT '银行账户',
  `bank_account_holder_name` VARCHAR(512) NULL DEFAULT NULL COMMENT '账号所有人名称',
  `bank_branch_province` VARCHAR(100) NULL DEFAULT NULL COMMENT '分支行所在省份',
  `city` VARCHAR(36) NULL DEFAULT NULL COMMENT '城市',
  `payload` BLOB NULL DEFAULT NULL COMMENT '本次修改内容（JSON）',
  `ctime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `mtime` BIGINT(20) NULL DEFAULT NULL COMMENT '',
  `deleted` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '',
  `version` BIGINT(20) UNSIGNED NOT NULL COMMENT '',
  PRIMARY KEY (`id`)  COMMENT '',
  UNIQUE INDEX `id_UNIQUE` (`id` ASC)  COMMENT '',
  INDEX `idx_merchant_id` (`merchant_id` ASC)  COMMENT '',
  INDEX `idx_merchant_sn` (`merchant_sn` ASC)  COMMENT '')
ENGINE = InnoDB
COMMENT = '商户微信资料';


CREATE TABLE `upay_core.base_config` (
  `id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'UUID',
  `type` varchar(24) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类型,provider,payway,sub_payway',
  `key` int(11) DEFAULT NULL COMMENT '通道类型',
  `value` varchar(128) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道中文名称',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道具体描述',
  `version` int(11) DEFAULT '0',
  `deleted` tinyint(2) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin


insert into base_config VALUES
(uuid(),'provider',1001,'兴业银行','兴业银行',1,0),
(uuid(),'provider',1002,'拉卡拉','拉卡拉',1,0),
(uuid(),'provider',1003,'中信','中信银行深圳分行',1,0),
(uuid(),'provider',1006,'石狮','石狮银行',1,0),
(uuid(),'provider',1005,'浙商','浙商银行',1,0),
(uuid(),'provider',1004,'光大','光大银行',1,0),
(uuid(),'provider',1007,'厦门民生','厦门民生银行',1,0),
(uuid(),'provider',1008,'广州兴业','兴业银行广州分行',1,0),
(uuid(),'provider',1009,'镇江中信','中信银行镇江分行',1,0),
(uuid(),'provider',1010,'民生银行','民生银行(非威富通系统)',1,0),
(uuid(),'provider',1011,'拉卡拉万码','拉卡拉万码',1,0),
(uuid(),'payway',1,'支付宝','支付宝1.0',1,0),
(uuid(),'payway',2,'支付宝','支付宝2.0',1,0),
(uuid(),'payway',3,'微信','微信',1,0),
(uuid(),'payway',4,'百度钱包','百度钱包',1,0),
(uuid(),'payway',5,'京东钱包','京东钱包',1,0),
(uuid(),'payway',6,'QQ钱包','QQ钱包',1,0),
(uuid(),'payway',7,'NFC支付','NFC支付',1,0),
(uuid(),'payway',8,'拉卡拉钱包','拉卡拉钱包',1,0),
(uuid(),'payway',9,'和支付','移动和包支付',1,0),
(uuid(),'payway',15,'拉卡拉微信','拉卡拉微信',1,0),
(uuid(),'payway',16,'招商银行','招商银行',1,0),
(uuid(),'payway',17,'银联二维码','银联二维码',1,0),
(uuid(),'payway',18,'翼支付','翼支付',1,0),
(uuid(),'payway',19,'微信境外--香港','微信境外--香港',1,0),
(uuid(),'sub_payway',1,'条码支付','条码支付(b2c)',1,0),
(uuid(),'sub_payway',2,'二维码支付','二维码支付',1,0),
(uuid(),'sub_payway',3,'wap支付','wap支付',1,0),
(uuid(),'sub_payway',4,'小程序支付','小程序支付',1,0),
(uuid(),'sub_payway',5,'APP支付','APP支付',1,0),
(uuid(),'sub_payway',6,'H5支付','H5支付',1,0);


INSERT INTO `agent` (`id`,`name`,`details`,`provider`,`payway`,`sub_payway`,`formal`,`is_primary`,`params`,`ctime`,`mtime`,`deleted`,`version`)
VALUES(uuid(),'*_9_*_true_true_0188','移动和包支付',NULL,9,1,1,1,'{"cmcc_trade_params":{"fee_rate": "0.6", "liquidation_next_day": false,"cmcc_merchant_id":"","cmcc_merchant_key":""}}',UNIX_TIMESTAMP()*1000,UNIX_TIMESTAMP()*1000,0,1);




SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;


CREATE TABLE `upay_core`.`merchant_bank_account_pre` (
  `id` varchar(37) NOT NULL COMMENT 'UUID',
  `merchant_id` varchar(45) DEFAULT NULL,
  `type` int(11) DEFAULT NULL COMMENT '账户类型：1：个人账户；2：企业账户',
  `holder` varchar(45) DEFAULT NULL COMMENT '账户持有人名称',
  `id_type` int(11) DEFAULT '1' COMMENT '账户持有人证件类型：1 身份证；2 护照； 3 台胞证； 4 港澳通行证；',
  `identity` varchar(66) DEFAULT NULL COMMENT '账户持有人证件编号',
  `holder_id_front_photo` varchar(346) DEFAULT NULL COMMENT '开户人证件照正面照片',
  `holder_id_back_photo` varchar(346) DEFAULT NULL COMMENT '开户人证件照背面照片',
  `holder_id_front_ocr_status` int(11) DEFAULT '0' COMMENT '状态: -1未提交 0-待认证;1 - 认证成功 2-认证失败; | 身份证正面OCR认证状态',
  `holder_id_back_ocr_status` int(11) DEFAULT '0' COMMENT '状态: -1未提交 0-待认证;1 - 认证成功 2-认证失败; | 身份证反面OCR认证状态',
  `holder_id_status` int(11) DEFAULT '0' COMMENT '状态: -1未提交 0-待认证;1 - 认证成功 2-认证失败; | 身份证认证状态',
  `tax_payer_id` varchar(45) DEFAULT NULL COMMENT '工商税务号',
  `number` varchar(66) DEFAULT NULL COMMENT '账号',
  `verify_status` int(11) NOT NULL DEFAULT '0' COMMENT '卡号(账号)真实性验证状态 0未验证 1 验证中 2验证有效 3验证失败',
  `bank_name` varchar(45) DEFAULT NULL COMMENT '开户银行名称',
  `branch_name` varchar(45) DEFAULT NULL COMMENT '分支行名称',
  `city` varchar(32) DEFAULT NULL COMMENT '分支行所在城市',
  `cellphone` varchar(32) DEFAULT NULL COMMENT '账号绑定的手机号',
  `bank_card_image` varchar(255) DEFAULT NULL COMMENT '对私: 表示银行卡照片，对公: 表示开户许可证',
  `transfer_voucher` varchar(255) DEFAULT NULL COMMENT '转账凭证',
  `id_validity` varchar(128) DEFAULT NULL COMMENT '证件有效期',
  `letter_of_authorization` varchar(255) DEFAULT NULL COMMENT '营业执照授权函',
  `bank_card_status` int(11) DEFAULT NULL COMMENT '银行卡照片/开户许可证审核状态 -1: 未提交; 0: 待认证; 1: 认证成功; 2: 认证失败;',
  `legal_person_name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '法人姓名',
  `legal_person_register_no` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '营业执照注册号/个体户注册号',
  `business_license_photo` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '营业执照照片',
  `business_name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商户经营名称',
  `default_status` int(1) DEFAULT '0' COMMENT '是否为当前商户正在使用的银行卡 0:否 1:是',
  `extra` blob COMMENT '扩展字段',
  `change_time` bigint(20) DEFAULT NULL COMMENT '换卡时间',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `version` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `merchant_id` (`merchant_id`),
  UNIQUE KEY `merchant_id_number_UNIQUE` (`merchant_id`,`number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户银行信息预存储表';

CREATE TABLE `upay_core`.`sign_config` (
  `id` varchar(37) NOT NULL COMMENT 'UUID',
  `channel_id` varchar(50) DEFAULT NULL,
  `sign_type` varchar(10) NOT NULL,
  `key` varchar(1024) NOT NULL,
  `data` blob,
  `mtime` bigint(20) DEFAULT NULL,
  `version` bigint(20) unsigned NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `ctime` bigint(20) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='签名配置表';

CREATE TABLE `upay_core`.`sn_ticket` (
  `id` varchar(37) COLLATE utf8_unicode_ci NOT NULL COMMENT 'id',
  `name_prefix` varchar(48) default '' not null comment 'sn号类别名_prefix',
  `current` bigint(20) DEFAULT NULL COMMENT '当前序号',
  `ctime` bigint(20) DEFAULT NULL,
  `mtime` bigint(20) DEFAULT NULL,
  `version` bigint(20) unsigned NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_prefix_UNIQUE` (`name_prefix`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='sn号生成器表';
